<!--pages/mall/goods/goods.wxml-->
<view class="goods-container">
  <!-- 商品信息不完整时显示的提示 -->
  <view class="goods-error" wx:if="{{!goodsInfo || !goodsInfo.id}}">
    <view class="error-container">
      <view class="error-text">商品信息不完整</view>
      <button class="back-button" bindtap="goBackToList">返回商品列表</button>
    </view>
  </view>

  <!-- 商品正常显示内容 -->
  <block wx:else>
    <!-- 商品图片轮播 -->
    <swiper class="goods-swiper" indicator-dots="{{true}}" autoplay="{{true}}" interval="{{3000}}" duration="{{500}}" circular="{{true}}" bindchange="onSwiperChange">
      <block wx:if="{{goodsInfo.images && goodsInfo.images.length > 0}}">
        <swiper-item wx:for="{{goodsInfo.images}}" wx:key="*this">
          <image src="{{item}}" class="slide-image" mode="aspectFill"></image>
        </swiper-item>
      </block>
      <swiper-item wx:else>
        <image src="/assets/images/goods-placeholder.jpg" class="slide-image" mode="aspectFill"></image>
      </swiper-item>
    </swiper>

    <!-- 商品基本信息 -->
    <view class="goods-info-section">
      <view class="goods-name">{{goodsInfo.name}}</view>
      <view class="goods-price">¥{{goodsInfo.price}}</view>
      <view class="goods-stats">
        <text>销量：{{goodsInfo.sales || 0}}</text>
        <text>库存：{{goodsInfo.stock || 0}}</text>
        <text>好评率：{{goodsInfo.goodRate || '100%'}}</text>
      </view>
    </view>

    <!-- 规格选择 -->
    <view class="goods-spec-section" bindtap="onShowSpecPopup">
      <view class="section-title">选择尺寸</view>
      <view class="section-content">
        <text wx:if="{{goodsInfo.specifications && goodsInfo.specifications.length > 0}}">
          已选：{{selectedSpecs['尺寸'] || goodsInfo.specifications[currentSpecIndex].name}}
        </text>
        <text wx:else>默认尺寸</text>
        <text class="arrow-icon">></text>
      </view>
    </view>

    <!-- 商品详情 -->
    <view class="goods-detail-section">
      <view class="section-title">商品详情</view>
      <view class="detail-content">
        <text class="detail-text">{{goodsInfo.description || '暂无详情'}}</text>
        <!-- 商品详情图片 -->
        <block wx:if="{{goodsInfo.detailImages && goodsInfo.detailImages.length > 0}}">
          <image 
            wx:for="{{goodsInfo.detailImages}}" 
            wx:key="*this" 
            src="{{item}}" 
            class="detail-image" 
            mode="widthFix"
            lazy-load
          ></image>
        </block>
        <!-- 如果没有detailImages，但有images，使用商品主图作为详情图 -->
        <block wx:elif="{{goodsInfo.images && goodsInfo.images.length > 0}}">
          <image 
            wx:for="{{goodsInfo.images}}" 
            wx:key="*this" 
            src="{{item}}" 
            class="detail-image" 
            mode="widthFix"
            lazy-load
          ></image>
        </block>
      </view>
    </view>

    <!-- 商品评价 -->
    <view class="goods-review-section">
      <view class="section-title">商品评价</view>
      <view class="review-list">
        <block wx:if="{{goodsInfo.reviews && goodsInfo.reviews.length > 0}}">
          <view class="review-item" wx:for="{{goodsInfo.reviews}}" wx:key="id">
            <view class="review-user">{{item.userName || '匿名用户'}}: {{item.content}}</view>
          </view>
          <view class="view-more" bindtap="onViewMoreReviews">查看更多评价 ></view>
        </block>
        <view class="empty-reviews" wx:else>
          <text>暂无评价</text>
        </view>
      </view>
    </view>

    <!-- 相关推荐 -->
    <view class="goods-recommend-section">
      <view class="section-title">相关推荐</view>
      <view class="recommend-list">
        <view 
          class="recommend-item" 
          wx:for="{{recommendGoods}}" 
          wx:key="id" 
          bindtap="onRecommendGoodsTap" 
          data-id="{{item.id}}"
        >
          <image src="{{item.cover || '/assets/images/goods-placeholder.svg'}}" class="recommend-image" mode="aspectFill"></image>
          <view class="recommend-name">{{item.name}}</view>
        </view>
      </view>
    </view>

    <!-- 广告位 -->
    <view class="ad-container">
      <ad unit-id="adunit-id" ad-type="banner" ad-theme="white" bindload="onAdLoad" binderror="onAdError"></ad>
    </view>

    <!-- 底部操作栏 -->
    <view class="bottom-action-bar">
      <view class="action-button cart-button" bindtap="onAddToCart">加入购物车</view>
      <view class="action-button buy-button" bindtap="onBuyNow">立即购买</view>
    </view>
  </block>

  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{loading}}">
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 规格选择弹窗 -->
  <view class="spec-popup {{showSpecPopup ? 'show' : ''}}" catchtouchmove="true">
    <view class="spec-popup-mask" bindtap="onCloseSpecPopup"></view>
    <view class="spec-popup-content">
      <!-- 商品信息 -->
      <view class="spec-popup-header">
        <image class="spec-goods-image" src="{{goodsInfo.cover}}" mode="aspectFill"></image>
        <view class="spec-goods-info">
          <view class="spec-goods-price">¥{{goodsInfo.specifications[currentSpecIndex].price || goodsInfo.price}}</view>
          <view class="spec-goods-stock">库存: {{goodsInfo.specifications[currentSpecIndex].stock || goodsInfo.stock || 0}}</view>
          <view class="spec-goods-selected">
            已选: 
            <text wx:for="{{specTypes}}" wx:key="name">
              {{selectedSpecs[item.name]}}{{index < specTypes.length - 1 ? '、' : ''}}
            </text>
          </view>
        </view>
        <view class="spec-close" bindtap="onCloseSpecPopup">×</view>
      </view>
      
      <!-- 规格选择 -->
      <scroll-view scroll-y class="spec-popup-body">
        <view class="spec-group" wx:for="{{specTypes}}" wx:key="name" wx:for-item="specType">
          <view class="spec-group-title">{{specType.name}}</view>
          <view class="spec-options">
            <view 
              class="spec-option {{selectedSpecs[specType.name] === value ? 'active' : ''}}" 
              wx:for="{{specType.values}}" 
              wx:key="*this" 
              wx:for-item="value"
              data-type="{{specType.name}}"
              data-value="{{value}}"
              bindtap="onSelectSpec"
            >{{value}}</view>
          </view>
        </view>
        
        <!-- 数量选择 -->
        <view class="spec-group">
          <view class="spec-group-title">数量</view>
          <view class="quantity-selector">
            <view class="quantity-btn minus-btn" hover-class="btn-hover" catch:tap="onDecreaseQuantity">-</view>
            <view class="quantity-value">{{quantity}}</view>
            <view class="quantity-btn plus-btn" hover-class="btn-hover" catch:tap="onIncreaseQuantity">+</view>
          </view>
        </view>
      </scroll-view>
      
      <!-- 确认按钮 -->
      <view class="spec-popup-footer">
        <view class="confirm-button" bindtap="onConfirmSpec">加入购物车</view>
      </view>
    </view>
  </view>
</view> 