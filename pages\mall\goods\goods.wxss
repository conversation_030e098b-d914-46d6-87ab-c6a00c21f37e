/* pages/mall/goods/goods.wxss */
.goods-container {
  padding-bottom: 100rpx;
  background-color: #f5f5f5;
}

/* 商品轮播图 */
.goods-swiper {
  width: 100%;
  height: 750rpx;
}

.slide-image {
  width: 100%;
  height: 100%;
}

/* 商品基本信息 */
.goods-info-section {
  background-color: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.goods-name {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 15rpx;
  line-height: 1.4;
}

.goods-price {
  font-size: 36rpx;
  color: #ff4d4f;
  font-weight: bold;
  margin-bottom: 15rpx;
}

.goods-stats {
  display: flex;
  justify-content: space-between;
  font-size: 24rpx;
  color: #999;
}

/* 规格选择 */
.goods-spec-section {
  background-color: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.section-title {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
}

.section-content {
  font-size: 26rpx;
  color: #666;
  display: flex;
  align-items: center;
}

.arrow-icon {
  margin-left: 10rpx;
  color: #999;
}

/* 商品详情 */
.goods-detail-section {
  background-color: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.detail-content {
  margin-top: 20rpx;
}

.detail-text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}

.detail-image {
  width: 100%;
  margin-top: 20rpx;
}

/* 商品评价 */
.goods-review-section {
  background-color: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.review-list {
  margin-top: 20rpx;
}

.review-item {
  padding: 15rpx 0;
  border-bottom: 1rpx solid #eee;
}

.review-user {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
}

.view-more {
  text-align: center;
  color: #07c160;
  font-size: 26rpx;
  padding: 20rpx 0 0;
}

.empty-reviews {
  text-align: center;
  padding: 30rpx 0;
  color: #999;
  font-size: 26rpx;
}

/* 相关推荐 */
.goods-recommend-section {
  background-color: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.recommend-list {
  display: flex;
  overflow-x: scroll;
  margin-top: 20rpx;
  padding-bottom: 10rpx;
}

.recommend-item {
  width: 200rpx;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.recommend-image {
  width: 200rpx;
  height: 200rpx;
  border-radius: 8rpx;
  background-color: #f5f5f5;
}

.recommend-name {
  font-size: 24rpx;
  color: #666;
  margin-top: 10rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  height: 68rpx;
}

/* 广告容器 */
.ad-container {
  margin: 20rpx;
  border-radius: 12rpx;
  overflow: hidden;
}

/* 底部操作栏 */
.bottom-action-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100rpx;
  display: flex;
  background-color: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.action-button {
  flex: 1;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 30rpx;
}

.cart-button {
  background-color: #ffd591;
  color: #873800;
}

.buy-button {
  background-color: #ff4d4f;
  color: #fff;
}

/* 加载状态 */
.loading-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999;
}

.loading-text {
  color: #999;
  font-size: 28rpx;
}

/* 商品信息不完整时的错误显示 */
.goods-error {
  width: 100%;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fff;
}

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40rpx;
  text-align: center;
}

.error-text {
  font-size: 32rpx;
  color: #666;
  margin-bottom: 30rpx;
}

.back-button {
  margin-top: 30rpx;
  padding: 15rpx 40rpx;
  background-color: #07c160;
  color: #fff;
  border-radius: 40rpx;
  font-size: 28rpx;
  border: none;
} 

/* 规格选择弹窗 */
.spec-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  visibility: hidden;
  opacity: 0;
  transition: all 0.3s ease;
}

.spec-popup.show {
  visibility: visible;
  opacity: 1;
}

.spec-popup-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
}

.spec-popup-content {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  border-top-left-radius: 24rpx;
  border-top-right-radius: 24rpx;
  transform: translateY(100%);
  transition: transform 0.3s ease;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
}

.spec-popup.show .spec-popup-content {
  transform: translateY(0);
}

.spec-popup-header {
  padding: 30rpx;
  display: flex;
  position: relative;
  border-bottom: 1rpx solid #f0f0f0;
}

.spec-goods-image {
  width: 160rpx;
  height: 160rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
  background-color: #f5f5f5;
}

.spec-goods-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.spec-goods-price {
  font-size: 36rpx;
  color: #ff4d4f;
  font-weight: bold;
}

.spec-goods-stock {
  font-size: 24rpx;
  color: #999;
}

.spec-goods-selected {
  font-size: 26rpx;
  color: #666;
  margin-top: 10rpx;
}

.spec-close {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  width: 50rpx;
  height: 50rpx;
  line-height: 50rpx;
  text-align: center;
  font-size: 40rpx;
  color: #999;
}

.spec-popup-body {
  flex: 1;
  padding: 0 30rpx;
  max-height: 60vh;
  overflow-y: auto;
}

.spec-group {
  margin-bottom: 30rpx;
}

.spec-group-title {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 20rpx;
  font-weight: 500;
}

.spec-options {
  display: flex;
  flex-wrap: wrap;
}

.spec-option {
  padding: 10rpx 24rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  margin-right: 20rpx;
  margin-bottom: 20rpx;
  font-size: 26rpx;
  color: #333;
  background-color: #f8f8f8;
  min-width: 80rpx;
  text-align: center;
}

.spec-option.active {
  border-color: #ff4d4f;
  color: #ff4d4f;
  background-color: #fff0f0;
}

.quantity-selector {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  margin-top: 10rpx;
}

.quantity-btn {
  width: 60rpx;
  height: 60rpx;
  line-height: 56rpx;
  text-align: center;
  border: 1rpx solid #ddd;
  font-size: 36rpx;
  font-weight: bold;
  background-color: #f8f8f8;
  color: #333;
  border-radius: 8rpx;
}

.minus-btn {
  color: #ff4d4f;
}

.plus-btn {
  color: #07c160;
}

.btn-hover {
  opacity: 0.8;
  transform: scale(0.95);
}

.quantity-btn:first-child {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.quantity-btn:last-child {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

.quantity-value {
  width: 80rpx;
  height: 60rpx;
  line-height: 60rpx;
  text-align: center;
  border-top: 1rpx solid #ddd;
  border-bottom: 1rpx solid #ddd;
  font-size: 28rpx;
  background-color: #fff;
}

.spec-popup-footer {
  padding: 20rpx 30rpx;
  border-top: 1rpx solid #f0f0f0;
}

.confirm-button {
  width: 100%;
  height: 90rpx;
  line-height: 90rpx;
  text-align: center;
  background-color: #ff4d4f;
  color: #fff;
  font-size: 32rpx;
  font-weight: bold;
  border-radius: 45rpx;
}

.confirm-button::before {
  content: "🛒 ";
  font-size: 36rpx;
} 