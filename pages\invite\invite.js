// pages/invite/invite.js
const request = require('../../utils/request');

Page({
  /**
   * 页面的初始数据
   */
  data: {
    inviteCode: '',
    teamInfo: {},
    isLoggedIn: false,
    isMember: false,
    loading: true
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    console.log('邀请页面加载，参数:', options);
    
    // 获取邀请码
    const inviteCode = options.code || '';
    if (!inviteCode) {
      wx.showToast({
        title: '邀请码无效',
        icon: 'none'
      });
      return;
    }

    this.setData({
      inviteCode: inviteCode
    });

    // 检查登录状态
    this.checkLoginStatus();
    
    // 获取团队信息
    this.fetchTeamInfo(inviteCode);
  },

  /**
   * 检查登录状态
   */
  checkLoginStatus: function() {
    const token = wx.getStorageSync('token');
    const userInfo = wx.getStorageSync('userInfo');
    
    this.setData({
      isLoggedIn: !!(token && userInfo)
    });
  },

  /**
   * 获取团队信息
   */
  fetchTeamInfo: function(inviteCode) {
    // 通过邀请码获取团队信息
    request({
      url: '/api/v1/client/team/info-by-code',
      method: 'GET',
      data: {
        inviteCode: inviteCode
      }
    }).then(res => {
      console.log('团队信息响应:', res);
      
      if (res.status === 'success' && res.data) {
        const teamData = res.data.team || res.data;
        this.setData({
          teamInfo: {
            name: teamData.name || '未知团队',
            leaderName: teamData.leader ? teamData.leader.name : '未知',
            avatar: teamData.leader ? teamData.leader.avatar : '',
            memberCount: teamData.memberCount || 0,
            totalIncome: teamData.totalIncome || '0.00',
            monthIncome: teamData.monthIncome || '0.00'
          },
          loading: false
        });
      } else {
        // 如果获取失败，显示默认信息
        this.setData({
          teamInfo: {
            name: '团队',
            leaderName: '团长',
            avatar: '',
            memberCount: 0,
            totalIncome: '0.00',
            monthIncome: '0.00'
          },
          loading: false
        });
      }
    }).catch(err => {
      console.error('获取团队信息失败:', err);
      this.setData({
        loading: false
      });
      
      // 显示默认信息
      this.setData({
        teamInfo: {
          name: '团队',
          leaderName: '团长',
          avatar: '',
          memberCount: 0,
          totalIncome: '0.00',
          monthIncome: '0.00'
        }
      });
    });
  },

  /**
   * 复制邀请码
   */
  copyInviteCode: function() {
    wx.setClipboardData({
      data: this.data.inviteCode,
      success: function() {
        wx.showToast({
          title: '邀请码已复制',
          icon: 'success'
        });
      }
    });
  },

  /**
   * 加入团队
   */
  joinTeam: function() {
    if (!this.data.isLoggedIn) {
      this.goToLogin();
      return;
    }

    wx.showLoading({
      title: '加入中...',
      mask: true
    });

    request({
      url: '/api/v1/client/team/join',
      method: 'POST',
      data: {
        inviteCode: this.data.inviteCode
      }
    }).then(res => {
      wx.hideLoading();
      
      if (res.status === 'success') {
        wx.showToast({
          title: '加入团队成功',
          icon: 'success'
        });
        
        // 延迟跳转到团队页面
        setTimeout(() => {
          this.goToTeam();
        }, 1500);
      } else {
        wx.showToast({
          title: res.message || '加入失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      wx.hideLoading();
      console.error('加入团队失败:', err);
      wx.showToast({
        title: '加入失败，请重试',
        icon: 'none'
      });
    });
  },

  /**
   * 跳转到登录页面
   */
  goToLogin: function() {
    wx.navigateTo({
      url: '/pages/auth/auth'
    });
  },

  /**
   * 跳转到团队页面
   */
  goToTeam: function() {
    wx.switchTab({
      url: '/pages/user/team/team'
    });
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    // 重新检查登录状态
    this.checkLoginStatus();
  }
});
