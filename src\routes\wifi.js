const express = require('express');
const router = express.Router();
const { body } = require('express-validator');
const { verifyToken } = require('../middlewares/auth');
const { success, error } = require('../utils/response');
const db = require('../../config/database');
const ProfitService = require('../services/profit-service');

// 移除内存存储，改用数据库
// 检查wifi表是否存在，如果不存在则创建
const initWifiTable = async () => {
  try {
    // 检查表是否存在
    const [tables] = await db.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = DATABASE() 
      AND table_name = 'wifi'
    `);
    
    if (tables.length === 0) {
      console.log('创建wifi表...');
      await db.query(`
        CREATE TABLE wifi (
          id INT PRIMARY KEY AUTO_INCREMENT,
          title VARCHAR(100) NOT NULL,
          name VARCHAR(100) NOT NULL,
          password VARCHAR(100) NOT NULL,
          merchant_name VARCHAR(100) NOT NULL,
          qrcode VARCHAR(255),
          use_count INT DEFAULT 0,
          status TINYINT DEFAULT 1,
          user_id INT NOT NULL,
          created_at DATETIME,
          updated_at DATETIME
        )
      `);
      
      // 插入示例数据
      await db.query(`
        INSERT INTO wifi (id, title, name, password, merchant_name, qrcode, use_count, status, user_id, created_at, updated_at)
        VALUES 
        (1, 'WiFi测试1', 'Test-WiFi-1', 'password123', '测试商家1', '', 10, 1, 1, '2023-06-01 12:30:45', '2023-06-01 12:30:45'),
        (2, '星巴克WiFi', 'Starbucks_Guest', 'starbucks2024', '星巴克咖啡', '', 456, 1, 1, '2023-06-01 10:00:00', '2023-06-01 10:00:00')
      `);
      
      console.log('wifi表创建完成并插入示例数据');
    } else {
      console.log('wifi表已存在');
    }
  } catch (err) {
    console.error('初始化wifi表失败:', err);
  }
};

// 初始化表
initWifiTable();

/**
 * @route   GET /api/v1/client/wifi/qrcode
 * @desc    生成WiFi二维码（客户端）
 * @access  Public
 */
router.get('/client/wifi/qrcode', async (req, res) => {
  try {
    console.log('处理客户端WiFi二维码请求，参数：', req.query);
    const { ssid, password, encryption = 'WPA', hidden = 'false' } = req.query;
    
    if (!ssid || !password) {
      return res.status(400).json({
        status: 'error',
        message: 'SSID和密码不能为空'
      });
    }
    
    // 构建WiFi连接字符串
    // 格式: WIFI:T:<加密类型>;S:<SSID>;P:<密码>;H:<是否隐藏>;;
    const wifiString = `WIFI:T:${encryption};S:${ssid};P:${password};H:${hidden};;`;
    
    // 使用第三方服务生成二维码
    // 这里使用QR Server API作为示例
    const qrServerUrl = `https://api.qrserver.com/v1/create-qr-code/?data=${encodeURIComponent(wifiString)}&size=300x300&format=png`;
    
    console.log('生成WiFi二维码成功，URL:', qrServerUrl);
    
    // 返回二维码URL
    return res.json({
      status: 'success',
      message: '二维码生成成功',
      data: {
        qrcode_url: qrServerUrl,
        wifi_string: wifiString
      }
    });
  } catch (err) {
    console.error('生成WiFi二维码失败:', err);
    return res.status(500).json({
      status: 'error',
      message: '生成WiFi二维码失败: ' + err.message
    });
  }
});

/**
 * @route   POST /api/wifi/create
 * @desc    创建新的WiFi码
 * @access  Private
 */
router.post('/create', verifyToken, [
  body('title').notEmpty().withMessage('WiFi标题不能为空'),
  body('name').notEmpty().withMessage('WiFi名称不能为空'),
  body('password').notEmpty().withMessage('WiFi密码不能为空'),
  body('merchant_name').notEmpty().withMessage('商户名称不能为空')
], async (req, res) => {
  try {
    // 创建新的WiFi码
    const { title, name, password, merchant_name, status = 1 } = req.body;
    const user_id = req.user ? req.user.id : 1;
    
    console.log('创建WiFi码，参数:', { title, name, password, merchant_name, status, user_id });
    
    const result = await db.query(
      `INSERT INTO wifi (title, name, password, merchant_name, user_id, qrcode, use_count, status, created_at, updated_at)
       VALUES (?, ?, ?, ?, ?, '', 0, ?, NOW(), NOW())`,
      [title, name, password, merchant_name, user_id, status]
    );
    
    if (!result || !result.insertId) {
      console.error('插入WiFi记录失败，结果:', result);
      return error(res, 'WiFi码创建失败', 500);
    }
    
    const insertId = result.insertId;
    console.log('插入WiFi成功，ID:', insertId);
    
    // 获取创建的WiFi详情
    const rows = await db.query('SELECT * FROM wifi WHERE id = ?', [insertId]);

    // 确保返回的是有效对象，即使查询失败也返回一个包含ID的对象
    const wifi = rows && rows.length > 0 ? rows[0] : {
      id: insertId,
      title,
      name,
      password,
      merchant_name,
      user_id,
      status,
      created_at: new Date(),
      qrcode: '',
      use_count: 0
    };
    
    console.log('创建WiFi码成功:', wifi);
    
    return success(res, wifi, 'WiFi码创建成功');
  } catch (err) {
    console.error('创建WiFi码失败:', err);
    return error(res, '创建WiFi码失败: ' + err.message, 500);
  }
});

/**
 * @route   GET /api/wifi/list
 * @desc    获取WiFi码列表
 * @access  Private
 */
router.get('/list', verifyToken, async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const keyword = req.query.keyword || '';
    const status = req.query.status;
    
    // 构建查询条件
    const conditions = [];
    const params = [];
    
    if (keyword) {
      conditions.push('(title LIKE ? OR name LIKE ? OR merchant_name LIKE ?)');
      params.push(`%${keyword}%`, `%${keyword}%`, `%${keyword}%`);
    }
    
    if (status !== undefined && status !== '') {
      conditions.push('status = ?');
      params.push(parseInt(status));
    }
    
    const whereClause = conditions.length > 0 ? 'WHERE ' + conditions.join(' AND ') : '';
    
    // 获取总数
    const countResult = await db.query(
      `SELECT COUNT(*) as total FROM wifi ${whereClause}`,
      params
    );
    
    // 确保安全地获取total值
    let total = 0;
    if (countResult && countResult.length > 0) {
      if (countResult[0].total !== undefined) {
        total = countResult[0].total;
      } else if (countResult[0]['COUNT(*)'] !== undefined) {
        total = countResult[0]['COUNT(*)'];
      }
    }
    
    console.log('查询到WiFi总数：', total);
    
    // 获取分页数据
    const offset = (page - 1) * limit;
    // 使用直接字符串拼接方式处理LIMIT和OFFSET，避免参数化问题
    const list = await db.query(
      `SELECT * FROM wifi ${whereClause} ORDER BY created_at DESC LIMIT ${parseInt(limit)} OFFSET ${parseInt(offset)}`,
      params
    );
    
    console.log('返回WiFi列表:', list);
    
    return success(res, {
      list,
      total,
      page,
      limit
    }, '获取WiFi码列表成功');
  } catch (err) {
    console.error('获取WiFi列表失败:', err);
    return error(res, '获取WiFi列表失败: ' + err.message, 500);
  }
});

/**
 * @route   GET /api/client/wifi/list
 * @desc    获取WiFi码列表（客户端，无需认证）
 * @access  Public
 */
router.get('/client/list', async (req, res) => {
  try {
    console.log('处理客户端WiFi列表请求，参数：', req.query);
    const page = parseInt(req.query.page) || 1;
    const pageSize = parseInt(req.query.pageSize) || parseInt(req.query.limit) || 10;
    const keyword = req.query.keyword || '';
    
    // 完全移除status过滤，返回所有WiFi记录
    const conditions = [];
    const params = [];
    
    if (keyword) {
      conditions.push('(title LIKE ? OR name LIKE ? OR merchant_name LIKE ?)');
      params.push(`%${keyword}%`, `%${keyword}%`, `%${keyword}%`);
    }
    
    const whereClause = conditions.length > 0 ? 'WHERE ' + conditions.join(' AND ') : '';
    
    // 获取总数
    const countSql = `SELECT COUNT(*) as total FROM wifi ${whereClause}`;
    console.log('执行SQL查询', { sql: countSql, params });
    
    const countResult = await db.query(countSql, params);
    
    // 确保安全地获取total值
    let total = 0;
    if (Array.isArray(countResult) && countResult.length > 0) {
      if (Array.isArray(countResult[0]) && countResult[0].length > 0) {
        total = countResult[0][0]?.total || 0;
      } else if (countResult[0]?.total !== undefined) {
        total = countResult[0].total;
      }
    }
    
    console.log('查询到客户端WiFi总数：', total);
    
    // 获取分页数据
    const offset = (page - 1) * pageSize;
    const listSql = `SELECT id, title, name, password, merchant_name, qrcode, use_count, status, created_at 
       FROM wifi ${whereClause} 
       ORDER BY created_at DESC 
       LIMIT ${parseInt(pageSize)} OFFSET ${parseInt(offset)}`;
       
    console.log('执行SQL查询', { sql: listSql, params });
    
    const listResult = await db.query(listSql, params);
    
    // 处理MySQL2返回的[rows, fields]格式
    let rows = [];
    if (Array.isArray(listResult) && listResult.length > 0) {
      if (Array.isArray(listResult[0])) {
        rows = listResult[0];
      } else {
        rows = listResult;
      }
    }
    
    // 确保rows是一个数组
    const list = Array.isArray(rows) ? rows : [];
    
    console.log(`返回客户端WiFi列表: 找到${list.length}条记录`);
    console.log('WiFi列表数据:', list);
    
    return success(res, {
      list: list, // 确保是一个数组
      pagination: {
        total,
        page,
        pageSize,
        pages: Math.ceil(total / pageSize)
      }
    }, `获取WiFi码列表成功，找到${list.length}条记录`);
  } catch (err) {
    console.error('获取客户端WiFi列表失败:', err);
    console.error('错误堆栈:', err.stack);
    
    // 出错时返回空列表，而不是返回错误
    return success(res, {
      list: [],
      pagination: {
        total: 0,
        page: parseInt(req.query.page) || 1,
        pageSize: parseInt(req.query.pageSize) || parseInt(req.query.limit) || 10,
        pages: 0
      }
    }, '获取WiFi列表成功，但未找到记录');
  }
});

/**
 * @route   GET /api/wifi/detail/:id
 * @desc    获取WiFi码详情
 * @access  Public (测试用)
 */
router.get('/detail/:id', async (req, res) => {
  try {
    const { id } = req.params;
    console.log('WiFi详情请求参数:', { id });
    
    if (!id || isNaN(parseInt(id))) {
      console.log('无效的ID参数:', id);
      return error(res, '无效的ID参数', 400);
    }
    
    const results = await db.query('SELECT * FROM wifi WHERE id = ?', [id]);
    console.log('查询WiFi详情结果:', results);

    // 处理MySQL2返回的 [rows, fields] 格式
    const rows = Array.isArray(results[0]) ? results[0] : results;

    if (!rows || !Array.isArray(rows) || rows.length === 0) {
      console.log('WiFi不存在:', id);
      return error(res, 'WiFi码不存在', 404);
    }

    const wifi = rows[0];

    // 确保查询结果中至少包含必要字段
    const requiredFields = ['id', 'title', 'name', 'password', 'merchant_name', 'status'];
    const missingFields = requiredFields.filter(field => !wifi[field]);

    if (missingFields.length > 0) {
      console.log(`WiFi数据不完整，缺少字段: ${missingFields.join(', ')}`);
      return error(res, `WiFi数据不完整，缺少字段: ${missingFields.join(', ')}`, 500);
    }

    console.log('返回WiFi详情:', wifi);
    return success(res, wifi, '获取WiFi码详情成功');
  } catch (err) {
    console.error('获取WiFi详情失败:', err);
    return error(res, '获取WiFi详情失败: ' + err.message, 500);
  }
});

/**
 * @route   PUT /api/wifi/update/:id
 * @desc    修改WiFi码
 * @access  Private
 */
router.put('/update/:id', verifyToken, [
  body('title').notEmpty().withMessage('WiFi标题不能为空'),
  body('name').notEmpty().withMessage('WiFi名称不能为空'),
  body('password').notEmpty().withMessage('WiFi密码不能为空')
], async (req, res) => {
  try {
    const { id } = req.params;
    const { title, name, password, merchant_name, status } = req.body;
    
    // 检查WiFi是否存在
    const wifi = await db.query('SELECT * FROM wifi WHERE id = ?', [id]);

    if (!wifi || wifi.length === 0) {
      return error(res, 'WiFi码不存在', 404);
    }
    
    // 更新WiFi码
    const updateFields = [];
    const updateParams = [];
    
    if (title !== undefined) {
      updateFields.push('title = ?');
      updateParams.push(title);
    }
    
    if (name !== undefined) {
      updateFields.push('name = ?');
      updateParams.push(name);
    }
    
    if (password !== undefined) {
      updateFields.push('password = ?');
      updateParams.push(password);
    }
    
    if (merchant_name !== undefined) {
      updateFields.push('merchant_name = ?');
      updateParams.push(merchant_name);
    }
    
    if (status !== undefined) {
      updateFields.push('status = ?');
      updateParams.push(status);
    }
    
    updateFields.push('updated_at = NOW()');
    
    await db.query(
      `UPDATE wifi SET ${updateFields.join(', ')} WHERE id = ?`,
      [...updateParams, id]
    );
    
    // 获取更新后的WiFi详情
    const updatedWifi = await db.query('SELECT * FROM wifi WHERE id = ?', [id]);

    console.log('更新WiFi码:', updatedWifi[0]);

    return success(res, updatedWifi[0], 'WiFi码更新成功');
  } catch (err) {
    console.error('更新WiFi码失败:', err);
    return error(res, '更新WiFi码失败: ' + err.message, 500);
  }
});

/**
 * @route   DELETE /api/wifi/delete/:id
 * @desc    删除WiFi码
 * @access  Private
 */
router.delete('/delete/:id', verifyToken, async (req, res) => {
  try {
    const { id } = req.params;
    
    // 检查WiFi是否存在
    const wifi = await db.query('SELECT * FROM wifi WHERE id = ?', [id]);

    if (!wifi || wifi.length === 0) {
      return error(res, 'WiFi码不存在', 404);
    }

    // 验证所有权 - 确保用户只能删除自己的WiFi码
    if (req.user && wifi[0].user_id !== req.user.id) {
      console.log('无权限删除WiFi码:', req.user.id, '尝试删除', wifi[0].user_id, '的WiFi码');
      return error(res, '无权限删除此WiFi码', 403);
    }
    
    // 删除WiFi码
    await db.query('DELETE FROM wifi WHERE id = ?', [id]);
    
    console.log('删除WiFi码, ID:', id);
    
    return success(res, { id: parseInt(id) }, 'WiFi码删除成功');
  } catch (err) {
    console.error('删除WiFi码失败:', err);
    return error(res, '删除WiFi码失败: ' + err.message, 500);
  }
});

/**
 * @route   PUT /api/wifi/status/:id
 * @desc    更新WiFi码状态
 * @access  Private
 */
router.put('/status/:id', verifyToken, async (req, res) => {
  try {
    const { id } = req.params;
    const { status } = req.body;
    
    // 检查WiFi是否存在
    const wifi = await db.query('SELECT * FROM wifi WHERE id = ?', [id]);

    if (!wifi || wifi.length === 0) {
      return error(res, 'WiFi码不存在', 404);
    }
    
    // 更新状态
    await db.query(
      'UPDATE wifi SET status = ?, updated_at = NOW() WHERE id = ?',
      [status, id]
    );
    
    console.log('更新WiFi码状态, ID:', id, '状态:', status);
    
    return success(res, { 
      id: parseInt(id), 
      status: parseInt(status) 
    }, 'WiFi码状态更新成功');
  } catch (err) {
    console.error('更新WiFi码状态失败:', err);
    return error(res, '更新WiFi码状态失败: ' + err.message, 500);
  }
});

/**
 * @route   GET /api/wifi/stats
 * @desc    获取WiFi码整体统计数据（管理端）
 * @access  Private (Admin)
 */
router.get('/stats', verifyToken, async (req, res) => {
  try {
    const { time_range } = req.query;
    
    // 获取统计数据
    const totalResult = await db.query('SELECT COUNT(*) as total FROM wifi');
    const activeResult = await db.query('SELECT COUNT(*) as count FROM wifi WHERE status = 1');
    const inactiveResult = await db.query('SELECT COUNT(*) as count FROM wifi WHERE status = 0');
    const usageResult = await db.query('SELECT SUM(use_count) as total_use_count FROM wifi');
    
    // 确保安全地获取值
    let total = 0;
    let active = 0;
    let inactive = 0;
    let total_use_count = 0;
    
    if (totalResult && totalResult.length > 0 && totalResult[0]) {
      total = totalResult[0].total || 0;
    }
    
    if (activeResult && activeResult.length > 0 && activeResult[0]) {
      active = activeResult[0].count || 0;
    }
    
    if (inactiveResult && inactiveResult.length > 0 && inactiveResult[0]) {
      inactive = inactiveResult[0].count || 0;
    }
    
    if (usageResult && usageResult.length > 0 && usageResult[0]) {
      total_use_count = usageResult[0].total_use_count || 0;
    }
    
    const stats = {
      total,
      active,
      inactive,
      total_use_count
    };
    
    // 生成趋势数据（示例数据，实际应从数据库查询）
    const trend_data = [];
    const today = new Date();
    
    for (let i = 6; i >= 0; i--) {
      const date = new Date(today);
      date.setDate(date.getDate() - i);
      const dateStr = date.toISOString().split('T')[0];
      
      trend_data.push({
        date: dateStr,
        new_count: Math.floor(Math.random() * 10),
        use_count: Math.floor(Math.random() * 100)
      });
    }
    
    return success(res, {
      stats,
      trend_data
    }, '获取WiFi码统计数据成功');
  } catch (err) {
    console.error('获取WiFi统计数据失败:', err);
    return error(res, '获取WiFi统计数据失败: ' + err.message, 500);
  }
});

/**
 * @route   DELETE /wifi/:id
 * @desc    删除WiFi码（简化路径）
 * @access  Private
 */
router.delete('/:id', verifyToken, async (req, res) => {
  try {
    const { id } = req.params;
    
    // 检查WiFi是否存在
    const wifi = await db.query('SELECT * FROM wifi WHERE id = ?', [id]);

    if (!wifi || wifi.length === 0) {
      return error(res, 'WiFi码不存在', 404);
    }

    // 验证所有权 - 确保用户只能删除自己的WiFi码
    if (req.user && wifi[0].user_id !== req.user.id) {
      console.log('无权限删除WiFi码:', req.user.id, '尝试删除', wifi[0].user_id, '的WiFi码');
      return error(res, '无权限删除此WiFi码', 403);
    }
    
    // 删除WiFi码
    await db.query('DELETE FROM wifi WHERE id = ?', [id]);
    
    console.log('删除WiFi码, ID:', id, '(简化路径)');
    
    return success(res, { id: parseInt(id) }, 'WiFi码删除成功');
  } catch (err) {
    console.error('删除WiFi码失败:', err);
    return error(res, '删除WiFi码失败: ' + err.message, 500);
  }
});

/**
 * @route   GET /api/wifi/qrcode
 * @desc    生成WiFi二维码
 * @access  Public
 */
router.get('/qrcode', async (req, res) => {
  try {
    const { ssid, password, encryption = 'WPA', hidden = 'false' } = req.query;
    
    if (!ssid || !password) {
      return res.status(400).json({
        status: 'error',
        message: 'SSID和密码不能为空'
      });
    }
    
    // 构建WiFi连接字符串
    // 格式: WIFI:T:<加密类型>;S:<SSID>;P:<密码>;H:<是否隐藏>;;
    const wifiString = `WIFI:T:${encryption};S:${ssid};P:${password};H:${hidden};;`;
    
    // 使用第三方服务生成二维码
    // 这里使用QR Server API作为示例
    const qrServerUrl = `https://api.qrserver.com/v1/create-qr-code/?data=${encodeURIComponent(wifiString)}&size=300x300&format=png`;
    
    // 返回二维码URL
    return res.json({
      status: 'success',
      message: '二维码生成成功',
      data: {
        qrcode_url: qrServerUrl,
        wifi_string: wifiString
      }
    });
  } catch (err) {
    console.error('生成WiFi二维码失败:', err);
    return res.status(500).json({
      status: 'error',
      message: '生成WiFi二维码失败: ' + err.message
    });
  }
});

/**
 * WiFi使用记录接口
 * POST /api/v1/client/wifi/:id/use
 */
router.post('/:id/use', verifyToken, async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;

    console.log('记录WiFi使用，WiFi ID:', id, '用户ID:', userId);

    // 获取WiFi信息
    const wifiResult = await db.query('SELECT * FROM wifi WHERE id = ?', [id]);

    if (!wifiResult || wifiResult.length === 0) {
      return error(res, 'WiFi不存在', 404);
    }

    const wifi = wifiResult[0];

    // 获取用户团队信息
    const userResult = await db.query(
      'SELECT team_id, parent_id FROM user WHERE id = ?',
      [userId]
    );

    let teamId = null;
    let leaderId = null;

    if (userResult.length > 0 && userResult[0].team_id) {
      teamId = userResult[0].team_id;

      // 获取团长信息
      const teamResult = await db.query(
        'SELECT leader_id FROM team WHERE id = ?',
        [teamId]
      );

      if (teamResult.length > 0) {
        leaderId = teamResult[0].leader_id;
      }
    }

    // 处理WiFi分享分润
    try {
      const profitData = {
        wifiId: id,
        userId: userId,
        teamId: teamId,
        leaderId: leaderId,
        amount: 1.0 // 每次使用分润1元
      };

      const profitResult = await ProfitService.processWifiShareProfit(profitData);

      if (profitResult.success) {
        console.log('WiFi分享分润处理成功:', profitResult);
      } else {
        console.warn('WiFi分享分润处理失败:', profitResult.message);
      }
    } catch (profitError) {
      console.error('处理WiFi分润时出错:', profitError);
      // 分润失败不影响WiFi使用记录
    }

    return success(res, {
      wifiId: id,
      useCount: wifi.use_count + 1,
      message: 'WiFi使用记录成功'
    }, 'WiFi使用记录成功');

  } catch (err) {
    console.error('记录WiFi使用失败:', err);
    return error(res, '记录WiFi使用失败: ' + err.message, 500);
  }
});

/**
 * 获取WiFi使用统计
 * GET /api/v1/client/wifi/:id/stats
 */
router.get('/:id/stats', async (req, res) => {
  try {
    const { id } = req.params;

    // 获取WiFi基本信息
    const wifiResult = await db.query('SELECT * FROM wifi WHERE id = ?', [id]);

    if (!wifiResult || wifiResult.length === 0) {
      return error(res, 'WiFi不存在', 404);
    }

    const wifi = wifiResult[0];

    // 获取分润统计
    const profitStats = await db.query(`
      SELECT
        COUNT(*) as profit_count,
        SUM(amount) as total_profit,
        SUM(CASE WHEN status = 1 THEN amount ELSE 0 END) as settled_profit
      FROM profit_log
      WHERE source_id = ? AND source_type = 1
    `, [id]);

    const stats = {
      wifiId: id,
      title: wifi.title,
      useCount: wifi.use_count,
      profitCount: profitStats[0]?.profit_count || 0,
      totalProfit: parseFloat(profitStats[0]?.total_profit || 0),
      settledProfit: parseFloat(profitStats[0]?.settled_profit || 0),
      pendingProfit: parseFloat(profitStats[0]?.total_profit || 0) - parseFloat(profitStats[0]?.settled_profit || 0)
    };

    return success(res, stats, '获取WiFi统计成功');

  } catch (err) {
    console.error('获取WiFi统计失败:', err);
    return error(res, '获取WiFi统计失败: ' + err.message, 500);
  }
});

module.exports = router;