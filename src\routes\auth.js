const express = require('express');
const router = express.Router();
const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');
const crypto = require('crypto');
const { body } = require('express-validator');
const config = require('../../config');
const db = require('../database');
const { verifyToken, checkRole } = require('../middlewares/auth');
const { BadRequestError } = require('../middlewares/error');
const logger = require('../utils/logger');
const { success, error, unauthorized } = require('../utils/response');
const { generateToken } = require('../../config/jwt');
const axios = require('axios');

// MD5加密函数
function md5(text) {
  return crypto.createHash('md5').update(text).digest('hex');
}

/**
 * @route   POST /api/v1/auth/admin-login
 * @desc    管理员登录
 * @access  Public
 */
router.post('/admin-login', [
  body('username').notEmpty().withMessage('用户名不能为空'),
  body('password').notEmpty().withMessage('密码不能为空')
], async (req, res) => {
  try {
    const { username, password } = req.body;
    
    // 查询管理员用户
    const admin = await db.getOne('SELECT * FROM admin_user WHERE username = ?', [username]);
    
    if (!admin) {
      logger.warn(`管理员登录失败: 用户名 ${username} 不存在`);
      return unauthorized(res, '用户名或密码错误');
    }
    
    // 验证密码 - 使用MD5加密比较
    const passwordMd5 = md5(password);
    const isPasswordValid = passwordMd5 === admin.password;
    
    if (!isPasswordValid) {
      logger.warn(`管理员登录失败: 用户名 ${username} 密码错误`);
      return unauthorized(res, '用户名或密码错误');
    }
    
    // 生成JWT令牌
    const token = generateToken({
      id: admin.id,
      username: admin.username,
      role: 'admin'
    });
    
    logger.info(`管理员 ${admin.username}(ID: ${admin.id}) 登录成功`);
    
    return success(res, {
      token,
      user: {
        id: admin.id,
        username: admin.username,
        name: admin.real_name || admin.username,
        avatar: admin.avatar || '',
        roles: ['admin']
      }
    }, '登录成功');
  } catch (err) {
    logger.error(`管理员登录失败: ${err.message}`);
    return error(res, '登录失败', 500);
  }
});

/**
 * @route   GET /api/user/info
 * @desc    获取用户信息
 * @access  Private
 */
router.get('/user-info', async (req, res) => {
  try {
    // 从请求头中获取令牌
    const authHeader = req.headers.authorization;
    console.log('获取用户信息请求，Authorization:', authHeader);
    console.log('所有请求头:', req.headers);
    
    if (!authHeader) {
      console.log('未提供Authorization头');
      return unauthorized(res, '未提供访问令牌');
    }
    
    // 检查令牌格式
    const parts = authHeader.split(' ');
    if (parts.length !== 2 || parts[0] !== 'Bearer') {
      console.log('令牌格式错误:', authHeader);
      return unauthorized(res, '令牌格式错误');
    }
    
    const token = parts[1];
    console.log('提取的token:', token);
    
    try {
      // 验证令牌
      const decoded = jwt.verify(token, config.jwt.secret);
      console.log('解码后的token:', decoded);
      
      // 查询管理员用户
      const admin = await db.getOne('SELECT * FROM admin_user WHERE id = ?', [decoded.id]);
      console.log('查询到的用户:', admin);
      
      if (!admin) {
        logger.warn(`获取用户信息失败: 用户ID ${decoded.id} 不存在`);
        return unauthorized(res, '用户不存在');
      }
      
      const responseData = {
        roles: ['admin'],
        name: admin.real_name || admin.username,
        avatar: admin.avatar || '/img/default-avatar.png'
      };
      
      console.log('返回的用户信息:', responseData);
      
      return success(res, responseData, '获取用户信息成功');
    } catch (jwtError) {
      console.error('JWT验证失败:', jwtError);
      return unauthorized(res, '无效的访问令牌');
    }
  } catch (err) {
    logger.error(`获取用户信息失败: ${err.message}`);
    return error(res, '获取用户信息失败', 500);
  }
});

/**
 * @route   POST /api/v1/client/auth/login
 * @desc    用户登录（微信授权登录）
 * @access  Public
 */
router.post('/login', [
  body('code').notEmpty().withMessage('微信授权code不能为空')
], (req, res) => {
  try {
    // 此处实际应调用controller中的登录逻辑
    // 实际实现应包含微信API调用，用户信息解析等
    
    // 暂时使用模拟数据
    const user = {
      id: 1,
      openid: 'mock_openid',
      nickname: '测试用户',
      avatar: '/img/default-avatar.png',
      role: 'user'
    };
    
    // 生成JWT令牌
    const token = generateToken({
      id: user.id,
      openid: user.openid,
      role: user.role
    });
    
    logger.info(`用户 ${user.nickname}(ID: ${user.id}) 登录成功`);
    
    return success(res, {
      token,
      user: {
        id: user.id,
        nickname: user.nickname,
        avatar: user.avatar
      }
    }, '登录成功');
  } catch (err) {
    logger.error(`登录失败: ${err.message}`);
    return error(res, '登录失败', 400);
  }
});

/**
 * @route   POST /api/v1/client/auth/logout
 * @desc    用户退出登录
 * @access  Private
 */
router.post('/logout', (req, res) => {
  // 实际的JWT无法在服务端注销，但可以在客户端删除
  // 此处可以添加到黑名单或其他处理逻辑
  
  logger.info(`用户退出登录成功`);
  return success(res, null, '退出登录成功');
});

/**
 * 获取当前用户信息
 * GET /api/auth/me
 */
router.get('/me', verifyToken, (req, res) => {
  res.json({
    status: 'success',
    data: {
      user: req.user
    }
  });
});

/**
 * 刷新令牌
 * POST /api/auth/refresh
 */
router.post('/refresh', verifyToken, (req, res) => {
  // 生成新令牌
  const token = jwt.sign(
    { id: req.user.id, role: req.user.role },
    config.jwt.secret,
    { expiresIn: config.jwt.expiresIn }
  );
  
  res.json({
    status: 'success',
    data: {
      token
    }
  });
});

/**
 * 微信小程序登录（wx.login + getUserProfile 组合方式）
 * POST /api/v1/client/auth/wechat/login
 */
router.post('/wechat/login', async (req, res, next) => {
  try {
    const { code, userInfo } = req.body;
    
    if (!code) {
      logger.error('微信登录失败: 缺少必要的参数code');
      return res.status(400).json({
        status: 'error',
        message: '缺少必要的参数: code'
      });
    }
    
    // 获取微信配置
    const wxConfig = config.wechat || {
      appId: process.env.WECHAT_APPID || 'wxd57d522936cb95a1',
      appSecret: process.env.WECHAT_SECRET || 'c80e673ab03d444e7158269103cb04d0'
    };
    
    logger.info(`微信登录请求，使用AppID: ${wxConfig.appId}`);
    
    // 请求微信接口获取openid和session_key
    try {
      logger.info(`开始请求微信登录API，code: ${code}`);
      const wxUrl = 'https://api.weixin.qq.com/sns/jscode2session';
      const wxParams = {
        appid: wxConfig.appId,
        secret: wxConfig.appSecret,
        js_code: code,
        grant_type: 'authorization_code'
      };
      
      logger.info(`请求微信API: ${wxUrl}，参数: ${JSON.stringify(wxParams)}`);
      
      const wxResponse = await axios.get(wxUrl, { params: wxParams });
      
      logger.info(`微信API返回结果: ${JSON.stringify(wxResponse.data)}`);
      
      if (wxResponse.data.errcode) {
        logger.error(`微信API调用失败: ${wxResponse.data.errmsg}, errcode: ${wxResponse.data.errcode}`);
        return res.status(400).json({
          status: 'error',
          message: `微信授权失败: ${wxResponse.data.errmsg}`
        });
      }
      
      const openid = wxResponse.data.openid;
      const sessionKey = wxResponse.data.session_key;
      
      if (!openid) {
        logger.error('微信API返回数据异常，未获取到openid');
        return res.status(500).json({
          status: 'error',
          message: '微信授权失败，请稍后再试'
        });
      }
      
      // 确保用户表存在
      logger.info('检查并确保用户表存在');
      let tableName;
      
      try {
        // 检查数据库中是否存在users表或user表
        const tableCheck = await db.getOne(`
          SELECT table_name 
          FROM information_schema.tables 
          WHERE table_schema = DATABASE() AND table_name IN ('users', 'user')
        `);
        
        if (!tableCheck) {
          logger.warn('未找到用户表，使用默认表名"user"');
          tableName = 'user';
          
          // 创建用户表
          await db.query(`
            CREATE TABLE IF NOT EXISTS \`user\` (
              \`id\` int(11) NOT NULL AUTO_INCREMENT COMMENT '用户ID',
              \`openid\` varchar(50) NOT NULL COMMENT '微信openid',
              \`unionid\` varchar(50) DEFAULT NULL COMMENT '微信unionid',
              \`nickname\` varchar(50) DEFAULT NULL COMMENT '昵称',
              \`avatar\` varchar(255) DEFAULT NULL COMMENT '头像',
              \`gender\` tinyint(1) DEFAULT '0' COMMENT '性别：0未知，1男，2女',
              \`phone\` varchar(20) DEFAULT NULL COMMENT '手机号',
              \`balance\` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '账户余额',
              \`team_id\` int(11) DEFAULT NULL COMMENT '所属团队ID',
              \`parent_id\` int(11) DEFAULT NULL COMMENT '上级用户ID',
              \`is_leader\` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否团长：0否，1是',
              \`level\` tinyint(1) NOT NULL DEFAULT '0' COMMENT '用户等级',
              \`status\` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0禁用，1启用',
              \`created_at\` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
              \`updated_at\` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
              PRIMARY KEY (\`id\`),
              UNIQUE KEY \`openid\` (\`openid\`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='小程序用户表';
          `);
          logger.info('用户表创建成功');
        } else {
          tableName = tableCheck.table_name;
        }
      } catch (tableError) {
        logger.error(`检查用户表失败: ${tableError.message}`);
        // 默认使用user表
        tableName = 'user';
      }
      
      // 确保表名有值
      if (!tableName) {
        logger.warn('表名未定义，使用默认表名"user"');
        tableName = 'user';
      }
      
      logger.info(`使用表名 ${tableName} 操作用户数据`);
      
      // 查询用户是否存在
      const existUser = await db.getOne(`SELECT * FROM ${tableName} WHERE openid = ?`, [openid]);
      
      let user;
      if (!existUser) {
        // 创建新用户
        logger.info(`用户不存在，创建新用户，openid: ${openid}`);
        
        // 从请求中获取用户信息
        let nickname = '微信用户';
        let avatar = '';
        let gender = 0;
        
        if (userInfo) {
          nickname = userInfo.nickName || userInfo.nickname || '微信用户';
          avatar = userInfo.avatarUrl || userInfo.avatar || '';
          gender = userInfo.gender || 0;
        }
        
        // 插入新用户
        const insertResult = await db.query(
          `INSERT INTO ${tableName} (openid, nickname, avatar, gender, created_at, updated_at) 
           VALUES (?, ?, ?, ?, NOW(), NOW())`,
          [openid, nickname, avatar, gender]
      );
      
        if (!insertResult || !insertResult.insertId) {
          logger.error('创建新用户失败');
          return res.status(500).json({
            status: 'error',
            message: '创建用户失败，请稍后再试'
          });
        }
        
        // 查询新创建的用户
        user = await db.getOne(`SELECT * FROM ${tableName} WHERE id = ?`, [insertResult.insertId]);
      } else {
        // 使用已存在的用户
        user = existUser;
        logger.info(`用户已存在，ID: ${user.id}, openid: ${openid}`);
    }
    
    // 生成JWT令牌
      const token = generateToken({
        id: user.id,
        openid: user.openid,
        role: 'user',
        session_key: sessionKey
      });
      
      logger.info(`用户 ${user.nickname}(ID: ${user.id}) 登录成功`);
      
      // 返回用户信息和令牌
      return res.json({
      status: 'success',
      data: {
        token,
        user: {
          id: user.id,
            nickname: user.nickname || '微信用户',
            avatar: user.avatar || '',
            gender: user.gender || 0,
            phone: user.phone || '',
            isLeader: Boolean(user.is_leader)
        }
        },
        message: '登录成功'
    });
      
    } catch (wxErr) {
      logger.error(`请求微信接口失败: ${wxErr.message}`);
      return res.status(500).json({
        status: 'error',
        message: '微信授权失败，请稍后再试'
      });
    }
  } catch (err) {
    logger.error(`微信登录处理失败: ${err.message}`);
    return res.status(500).json({
      status: 'error',
      message: '登录处理失败，请稍后再试'
    });
  }
});

/**
 * 删除账号 - 重定向到system路由
 * DELETE /api/v1/admin/auth/account/delete/:id
 */
router.delete('/account/delete/:id', verifyToken, checkRole('admin'), async (req, res, next) => {
  try {
    const { id } = req.params;
    
    // 不允许删除超级管理员账号
    if (parseInt(id) === 1) {
      return res.status(400).json({
        status: 'error',
        message: '超级管理员账号不能删除'
      });
    }

    res.json({
      status: 'success',
      message: '账号删除成功'
    });
  } catch (error) {
    next(error);
  }
});

module.exports = router; 