<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试邀请二维码API</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .result {
            background: #f5f5f5;
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
            white-space: pre-wrap;
        }
        .success {
            background: #d4edda;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 3px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        img {
            max-width: 300px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>团队邀请二维码API测试</h1>
    
    <div class="test-section">
        <h3>1. 测试团队信息API</h3>
        <button onclick="testTeamInfo()">获取团队信息</button>
        <div id="teamInfoResult" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>2. 测试邀请二维码API</h3>
        <input type="text" id="inviteCodeInput" placeholder="输入邀请码 (如: TEAM000002)" value="TEAM000002" style="width: 200px; padding: 5px;">
        <button onclick="testInviteQRCode()">生成邀请二维码</button>
        <div id="qrcodeResult" class="result"></div>
        <div id="qrcodeImage"></div>
    </div>

    <script>
        const BASE_URL = 'http://localhost:4000/api/v1';
        const TEST_TOKEN = 'test-token';

        async function testTeamInfo() {
            const resultDiv = document.getElementById('teamInfoResult');
            resultDiv.textContent = '正在获取团队信息...';
            
            try {
                const response = await fetch(`${BASE_URL}/client/team/info`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${TEST_TOKEN}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ 成功获取团队信息\n状态码: ${response.status}\n响应数据: ${JSON.stringify(data, null, 2)}`;
                    
                    // 提取邀请码并自动填入输入框
                    if (data.data && data.data.team && data.data.team.inviteCode) {
                        document.getElementById('inviteCodeInput').value = data.data.team.inviteCode;
                    }
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ 获取团队信息失败\n状态码: ${response.status}\n错误信息: ${JSON.stringify(data, null, 2)}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 请求失败: ${error.message}`;
            }
        }

        async function testInviteQRCode() {
            const inviteCode = document.getElementById('inviteCodeInput').value;
            const resultDiv = document.getElementById('qrcodeResult');
            const imageDiv = document.getElementById('qrcodeImage');
            
            if (!inviteCode) {
                resultDiv.className = 'result error';
                resultDiv.textContent = '❌ 请输入邀请码';
                return;
            }
            
            resultDiv.textContent = '正在生成邀请二维码...';
            imageDiv.innerHTML = '';
            
            try {
                const url = `${BASE_URL}/client/team/invite-qrcode?inviteCode=${encodeURIComponent(inviteCode)}`;
                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${TEST_TOKEN}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                const data = await response.json();
                
                if (response.ok && data.status === 'success') {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ 成功生成邀请二维码\n状态码: ${response.status}\n响应数据: ${JSON.stringify(data, null, 2)}`;
                    
                    // 显示二维码图片
                    if (data.data && data.data.qrcode_url) {
                        imageDiv.innerHTML = `<img src="${data.data.qrcode_url}" alt="邀请二维码" />`;
                    }
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ 生成邀请二维码失败\n状态码: ${response.status}\n错误信息: ${JSON.stringify(data, null, 2)}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 请求失败: ${error.message}`;
            }
        }

        // 页面加载时自动测试团队信息
        window.onload = function() {
            testTeamInfo();
        };
    </script>
</body>
</html>
