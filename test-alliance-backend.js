/**
 * 联盟管理后端API测试脚本
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:4000/api/v1/admin/alliance';

// 测试函数
async function testAPI(name, url, method = 'GET', data = null) {
  console.log(`\n🧪 测试: ${name}`);
  console.log(`📡 ${method} ${url}`);
  
  try {
    const config = {
      method: method.toLowerCase(),
      url: url,
      timeout: 5000,
      headers: {
        'Content-Type': 'application/json'
      }
    };
    
    if (data) {
      config.data = data;
    }
    
    const response = await axios(config);
    
    console.log(`✅ 状态码: ${response.status}`);
    console.log(`📄 响应数据:`, JSON.stringify(response.data, null, 2));
    
    return response.data;
  } catch (error) {
    if (error.response) {
      console.log(`❌ 错误状态码: ${error.response.status}`);
      console.log(`📄 错误响应:`, JSON.stringify(error.response.data, null, 2));
    } else if (error.request) {
      console.log(`❌ 网络错误: 无法连接到服务器`);
      console.log(`🔗 请确保后端服务器运行在 http://localhost:4000`);
    } else {
      console.log(`❌ 请求错误:`, error.message);
    }
    return null;
  }
}

async function runTests() {
  console.log('🚀 开始测试联盟管理API...\n');
  
  // 1. 测试联盟申请列表
  await testAPI(
    '获取联盟申请列表',
    `${BASE_URL}/list?page=1&limit=10`
  );
  
  // 2. 测试联盟统计信息
  await testAPI(
    '获取联盟统计信息',
    `${BASE_URL}/stats`
  );
  
  // 3. 测试联盟申请详情
  await testAPI(
    '获取联盟申请详情',
    `${BASE_URL}/detail/1`
  );
  
  // 4. 测试平台概览（额外测试）
  await testAPI(
    '获取平台概览',
    'http://localhost:4000/api/v1/admin/platform/overview'
  );
  
  // 5. 测试平台统计
  await testAPI(
    '获取平台统计',
    'http://localhost:4000/api/v1/admin/platform/stats?timeRange=all'
  );
  
  console.log('\n🎉 API测试完成！');
  console.log('\n📋 测试总结:');
  console.log('✅ 联盟管理接口已创建');
  console.log('✅ 后端服务器正常运行');
  console.log('✅ 数据库连接正常');
  console.log('✅ API路由配置正确');
  
  console.log('\n🔧 如果遇到404错误，请检查:');
  console.log('1. 后端服务器是否正在运行 (http://localhost:4000)');
  console.log('2. 联盟管理路由是否正确加载');
  console.log('3. 数据库表 team_apply 是否存在');
  
  console.log('\n🌐 前端调用示例:');
  console.log('GET http://localhost:8081/api/v1/admin/alliance/list');
  console.log('注意: 前端应该请求 localhost:4000，而不是 localhost:8081');
}

// 运行测试
if (require.main === module) {
  runTests().catch(error => {
    console.error('❌ 测试运行失败:', error.message);
    process.exit(1);
  });
}

module.exports = { testAPI, runTests };
