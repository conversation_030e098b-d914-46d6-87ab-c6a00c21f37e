// utils/constants.js
// 常量定义

module.exports = {
  // 存储键名
  STORAGE_KEYS: {
    TOKEN: 'token',
    USER_INFO: 'userInfo',
    CART_ITEMS: 'cartItems',
    SEARCH_HISTORY: 'searchHistory'
  },

  // 页面路径
  PAGES: {
    INDEX: '/pages/index/index',
    WIFI_CREATE: '/pages/wifi/create/create',
    WIFI_LIST: '/pages/wifi/list/list',
    WIFI_DETAIL: '/pages/wifi/detail/detail',
    MALL_HOME: '/pages/mall/home/<USER>',
    MALL_CART: '/pages/mall/cart/cart',
    GOODS_DETAIL: '/pages/mall/goods/goods',
    ORDER_CONFIRM: '/pages/mall/order/confirm/confirm',
    ORDER_PAYMENT: '/pages/mall/order/payment/payment',
    ALLIANCE: '/pages/alliance/index',
    ADS: '/pages/ads/index',
    USER_PROFILE: '/pages/user/profile/profile'
  },

  // 状态码
  STATUS_CODES: {
    SUCCESS: 200,
    UNAUTHORIZED: 401,
    FORBIDDEN: 403,
    NOT_FOUND: 404,
    SERVER_ERROR: 500
  },

  // 订单状态
  ORDER_STATUS: {
    PENDING: 1,     // 待付款
    PAID: 2,        // 已付款
    SHIPPED: 3,     // 已发货
    RECEIVED: 4,    // 已收货
    COMPLETED: 5,   // 已完成
    CANCELLED: 0    // 已取消
  },

  // 支付方式
  PAYMENT_METHODS: {
    WECHAT: 'wechat',
    ALIPAY: 'alipay',
    BALANCE: 'balance'
  },

  // WiFi加密类型
  WIFI_SECURITY: {
    NONE: 'nopass',
    WEP: 'WEP',
    WPA: 'WPA'
  },

  // 广告位类型
  AD_TYPES: {
    BANNER: 'banner',
    CARD: 'card',
    POPUP: 'popup'
  },

  // 分页默认值
  PAGE_SIZE: 10,
  MAX_PAGE_SIZE: 50
} 