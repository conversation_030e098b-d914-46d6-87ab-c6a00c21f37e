const express = require('express');
const router = express.Router();
const { body } = require('express-validator');
const { verifyToken } = require('../middlewares/auth');
const { success, error } = require('../utils/response');
const db = require('../database');
// 简化的模拟支付工具
const wechatPay = {
  createMiniProgramPayment: async (orderInfo) => {
    // 模拟支付参数
    const timeStamp = Math.floor(Date.now() / 1000).toString();
    const nonceStr = Math.random().toString(36).substring(2, 15);

    return {
      timeStamp,
      nonceStr,
      package: `prepay_id=mock_${timeStamp}`,
      signType: 'RSA',
      paySign: `mock_sign_${timeStamp}_${nonceStr}`,
      orderId: orderInfo.orderId,
      orderNo: orderInfo.orderNo,
      amount: orderInfo.amount,
      isReal: false
    };
  },
  verifyNotifySignature: () => true,
  queryPaymentStatus: async (orderNo) => ({
    trade_state: 'SUCCESS',
    trade_state_desc: '支付成功',
    out_trade_no: orderNo,
    transaction_id: `mock_${orderNo}`,
    amount: { total: 0 }
  }),
  isWechatPayEnabled: () => false
};

/**
 * 创建支付订单
 * POST /api/payment/create
 */
router.post('/create', verifyToken, [
  body('orderId').notEmpty().withMessage('订单ID不能为空'),
  body('paymentMethod').notEmpty().withMessage('支付方式不能为空')
], async (req, res) => {
  try {
    const userId = req.user.id;
    const { orderId, paymentMethod } = req.body;

    console.log('📦 创建支付订单请求:', { userId, orderId, paymentMethod });

    // 查询订单信息
    const orders = await db.query(
      'SELECT * FROM orders WHERE id = ? AND user_id = ?',
      [orderId, userId]
    );

    if (!orders || orders.length === 0) {
      console.log('📦 订单不存在:', { orderId, userId });
      return error(res, '订单不存在', 404);
    }

    const order = orders[0];

    // 检查订单状态是否为待支付
    if (order.status !== 0) {
      console.log('📦 订单状态不允许支付:', { orderId, status: order.status });
      return error(res, '订单状态不允许支付', 400);
    }

    // 获取用户信息（需要openid用于微信支付）
    const users = await db.query('SELECT * FROM user WHERE id = ?', [userId]);
    const user = users[0];

    let paymentOrder;

    if (paymentMethod === 'wechat') {
      // 创建微信支付订单
      const orderInfo = {
        orderId: order.id,
        orderNo: order.order_no,
        amount: parseFloat(order.total_amount),
        description: `订单支付-${order.order_no}`,
        openid: user.openid || 'mock_openid' // 如果没有openid则使用模拟值
      };

      paymentOrder = await wechatPay.createMiniProgramPayment(orderInfo);

      console.log('📦 微信支付订单创建:', {
        orderNo: order.order_no,
        amount: orderInfo.amount,
        isReal: paymentOrder.isReal
      });
    } else {
      // 其他支付方式暂时返回错误
      return error(res, '暂不支持此支付方式', 400);
    }

    console.log('📦 支付订单创建成功:', {
      orderId: paymentOrder.orderId,
      orderNo: paymentOrder.orderNo,
      amount: paymentOrder.amount,
      isReal: paymentOrder.isReal
    });

    return success(res, paymentOrder, '创建支付订单成功');
  } catch (err) {
    console.error('📦 创建支付订单失败:', err);
    return error(res, '创建支付订单失败: ' + err.message, 500);
  }
});

/**
 * 查询支付状态
 * GET /api/payment/status/:orderNo
 */
router.get('/status/:orderNo', verifyToken, (req, res) => {
  const { orderNo } = req.params;
  
  // 模拟查询支付状态
  const paymentStatus = {
    orderNo,
    paymentNo: `PAY${orderNo}`,
    status: 'PENDING', // 'PENDING'|'SUCCESS'|'FAILED'
    amount: 99.99,
    createdAt: new Date(),
    paidAt: null
  };
  
  return success(res, paymentStatus, '获取支付状态成功');
});

/**
 * 微信支付回调
 * POST /api/payment/wechat-notify
 */
router.post('/wechat-notify', async (req, res) => {
  try {
    console.log('📦 收到微信支付回调');

    // 验证签名
    const isValid = wechatPay.verifyNotifySignature(req.headers, JSON.stringify(req.body));

    if (!isValid) {
      console.error('📦 微信支付回调签名验证失败');
      return res.status(400).json({ code: 'FAIL', message: '签名验证失败' });
    }

    const { resource } = req.body;

    // 解密回调数据（实际项目中需要解密）
    // 这里简化处理，假设已经解密
    const paymentData = resource || req.body;
    const { out_trade_no, trade_state, transaction_id } = paymentData;

    console.log('📦 微信支付回调数据:', { out_trade_no, trade_state, transaction_id });

    if (trade_state === 'SUCCESS') {
      // 查询订单
      const orders = await db.query('SELECT * FROM orders WHERE order_no = ?', [out_trade_no]);

      if (orders && orders.length > 0) {
        const order = orders[0];

        // 更新订单状态为已支付
        await db.query(
          'UPDATE orders SET status = 1, payment_time = NOW(), updated_at = NOW() WHERE id = ?',
          [order.id]
        );

        console.log('📦 订单支付成功，状态已更新:', { orderId: order.id, orderNo: out_trade_no });
      }
    }

    // 返回成功响应给微信
    return res.json({ code: 'SUCCESS', message: '成功' });
  } catch (err) {
    console.error('📦 处理微信支付回调失败:', err);
    return res.status(500).json({ code: 'FAIL', message: '处理失败' });
  }
});

/**
 * 查询支付状态
 * GET /api/payment/status/:orderNo
 */
router.get('/status/:orderNo', verifyToken, async (req, res) => {
  try {
    const { orderNo } = req.params;
    const userId = req.user.id;

    // 查询订单信息
    const orders = await db.query(
      'SELECT * FROM orders WHERE order_no = ? AND user_id = ?',
      [orderNo, userId]
    );

    if (!orders || orders.length === 0) {
      return error(res, '订单不存在', 404);
    }

    const order = orders[0];

    // 如果订单已支付，直接返回状态
    if (order.status >= 1) {
      return success(res, {
        orderNo,
        status: 'SUCCESS',
        isPaid: true,
        paymentTime: order.payment_time
      }, '订单已支付');
    }

    // 查询微信支付状态
    try {
      const paymentStatus = await wechatPay.queryPaymentStatus(orderNo);

      if (paymentStatus.trade_state === 'SUCCESS') {
        // 更新订单状态
        await db.query(
          'UPDATE orders SET status = 1, payment_time = NOW(), updated_at = NOW() WHERE id = ?',
          [order.id]
        );

        return success(res, {
          orderNo,
          status: 'SUCCESS',
          isPaid: true,
          transactionId: paymentStatus.transaction_id
        }, '支付成功');
      } else {
        return success(res, {
          orderNo,
          status: paymentStatus.trade_state,
          isPaid: false,
          statusDesc: paymentStatus.trade_state_desc
        }, '查询支付状态成功');
      }
    } catch (queryErr) {
      console.error('查询微信支付状态失败:', queryErr);
      return success(res, {
        orderNo,
        status: 'UNKNOWN',
        isPaid: false
      }, '查询支付状态失败');
    }
  } catch (err) {
    console.error('查询支付状态失败:', err);
    return error(res, '查询支付状态失败: ' + err.message, 500);
  }
});

module.exports = router; 