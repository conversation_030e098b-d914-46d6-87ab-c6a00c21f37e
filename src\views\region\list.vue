<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>地区管理</span>
        <el-button
          style="float: right; padding: 3px 0"
          type="text"
          icon="el-icon-plus"
          @click="handleCreate"
        >
          新增地区
        </el-button>
      </div>
      
      <!-- 搜索区域 -->
      <el-form :inline="true" :model="queryParams" class="demo-form-inline" size="small">
        <el-form-item label="地区名称">
          <el-input
            v-model="queryParams.name"
            placeholder="请输入地区名称"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
            <el-option label="启用" value="1" />
            <el-option label="禁用" value="0" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="handleQuery">查询</el-button>
          <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
      
      <!-- 表格区域 -->
      <el-table
        v-loading="loading"
        :data="regionList"
        border
        style="width: 100%"
        row-key="id"
        default-expand-all
      >
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="name" label="地区名称" min-width="150" />
        <el-table-column prop="code" label="地区编码" width="120" />
        <el-table-column prop="level" label="层级" width="80">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.level === 1" type="success">省级</el-tag>
            <el-tag v-else-if="scope.row.level === 2" type="warning">市级</el-tag>
            <el-tag v-else-if="scope.row.level === 3" type="info">区/县</el-tag>
            <el-tag v-else type="danger">其他</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="team_count" label="团队数量" width="100" />
        <el-table-column prop="status" label="状态" width="100">
          <template slot-scope="scope">
            <el-switch
              v-model="scope.row.status"
              :active-value="1"
              :inactive-value="0"
              @change="handleStatusChange(scope.row)"
            />
          </template>
        </el-table-column>
        <el-table-column label="创建时间" width="160">
          <template slot-scope="scope">
            <span>{{ scope.row.created_at }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-view"
              @click="handleDetail(scope.row)"
            >
              详情
            </el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleUpdate(scope.row)"
            >
              编辑
            </el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
              :disabled="scope.row.team_count > 0"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页区域 -->
      <div class="pagination-container" v-if="!loading">
        <el-pagination
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="queryParams.page"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="queryParams.limit"
          layout="total, sizes, prev, pager, next, jumper"
          :total="safeTotal"
        />
      </div>
    </el-card>
    
    <!-- 地区表单对话框 -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="500px" @close="resetForm">
      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        label-width="100px"
        size="small"
      >
        <el-form-item label="地区名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入地区名称" />
        </el-form-item>
        <el-form-item label="地区编码" prop="code">
          <el-input v-model="form.code" placeholder="请输入地区编码" />
        </el-form-item>
        <el-form-item label="上级地区" prop="parent_id">
          <el-cascader
            v-model="form.parent_id"
            :options="regionTree"
            :props="{ 
              checkStrictly: true,
              value: 'id',
              label: 'name',
              emitPath: false
            }"
            clearable
            placeholder="请选择上级地区"
          />
        </el-form-item>
        <el-form-item label="层级" prop="level">
          <el-select v-model="form.level" placeholder="请选择层级">
            <el-option label="省级" :value="1" />
            <el-option label="市级" :value="2" />
            <el-option label="区/县" :value="3" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitForm">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getRegionList, getRegionTree, createRegion, updateRegion, deleteRegion } from '@/api/region'

export default {
  name: 'RegionList',
  data() {
    return {
      // 查询参数
      queryParams: {
        page: 1,
        limit: 20,
        name: '',
        status: ''
      },
      // 地区列表
      regionList: [],
      // 地区树形数据
      regionTree: [],
      // 总数
      total: 0,
      // 加载状态
      loading: false,
      // 对话框状态
      dialogVisible: false,
      dialogTitle: '',
      // 表单数据
      form: {
        id: undefined,
        name: '',
        code: '',
        parent_id: 0,
        level: 1,
        status: 1
      },
      // 表单校验规则
      rules: {
        name: [
          { required: true, message: '请输入地区名称', trigger: 'blur' },
          { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
        ],
        code: [
          { required: true, message: '请输入地区编码', trigger: 'blur' },
          { pattern: /^[A-Za-z0-9]+$/, message: '地区编码只能包含字母和数字', trigger: 'blur' }
        ],
        level: [
          { required: true, message: '请选择层级', trigger: 'change' }
        ]
      }
    }
  },
  computed: {
    // 确保total始终是数字类型，避免分页组件报错
    safeTotal() {
      // 多重保护确保返回有效数字
      if (this.total === null || this.total === undefined) {
        return 0
      }
      if (typeof this.total === 'string') {
        const parsed = parseInt(this.total, 10)
        return isNaN(parsed) ? 0 : parsed
      }
      if (typeof this.total === 'number' && !isNaN(this.total)) {
        return Math.max(0, Math.floor(this.total))
      }
      return 0
    }
  },
  created() {
    this.getList()
    this.getRegionTree()
  },
  methods: {
    // 获取地区列表
    async getList() {
      this.loading = true
      try {
        const { data } = await getRegionList(this.queryParams)
        this.regionList = data.list || []
        // 修复分页组件total属性类型错误
        this.total = (data.pagination && data.pagination.total) || data.total || 0
      } catch (error) {
        console.error('获取地区列表失败:', error)
        this.regionList = []
        this.total = 0
        this.$message.error('获取地区列表失败')
      } finally {
        this.loading = false
      }
    },
    // 获取地区树形结构
    async getRegionTree() {
      try {
        const { data } = await getRegionTree()
        this.regionTree = [{ id: 0, name: '顶级地区', children: data }]
      } catch (error) {
        console.error('获取地区树形结构失败:', error)
      }
    },
    // 查询按钮
    handleQuery() {
      this.queryParams.page = 1
      this.getList()
    },
    // 重置按钮
    resetQuery() {
      this.queryParams = {
        page: 1,
        limit: 20,
        name: '',
        status: ''
      }
      this.getList()
    },
    // 每页条数改变
    handleSizeChange(val) {
      this.queryParams.limit = val
      this.getList()
    },
    // 当前页改变
    handleCurrentChange(val) {
      this.queryParams.page = val
      this.getList()
    },
    // 状态改变
    async handleStatusChange(row) {
      try {
        await updateRegion(row.id, { status: row.status })
        this.$message.success('状态更新成功')
      } catch (error) {
        row.status = row.status === 1 ? 0 : 1 // 恢复原状态
        this.$message.error('状态更新失败')
      }
    },
    // 新增按钮
    handleCreate() {
      this.dialogTitle = '新增地区'
      this.dialogVisible = true
      this.form = {
        id: undefined,
        name: '',
        code: '',
        parent_id: 0,
        level: 1,
        status: 1
      }
    },
    // 编辑按钮
    handleUpdate(row) {
      this.dialogTitle = '编辑地区'
      this.dialogVisible = true
      this.form = {
        id: row.id,
        name: row.name,
        code: row.code,
        parent_id: row.parent_id,
        level: row.level,
        status: row.status
      }
    },
    // 详情按钮
    handleDetail(row) {
      this.$router.push({ path: `/region/detail/${row.id}` })
    },
    // 删除按钮
    handleDelete(row) {
      this.$confirm('确认删除该地区吗？', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          await deleteRegion(row.id)
          this.$message.success('删除成功')
          this.getList()
        } catch (error) {
          this.$message.error('删除失败')
        }
      }).catch(() => {})
    },
    // 重置表单
    resetForm() {
      this.$refs.form && this.$refs.form.resetFields()
    },
    // 提交表单
    submitForm() {
      this.$refs.form.validate(async valid => {
        if (!valid) return
        
        try {
          if (this.form.id) {
            // 更新
            await updateRegion(this.form.id, this.form)
            this.$message.success('更新成功')
          } else {
            // 新增
            await createRegion(this.form)
            this.$message.success('新增成功')
          }
          this.dialogVisible = false
          this.getList()
          this.getRegionTree() // 更新树形结构
        } catch (error) {
          this.$message.error('操作失败')
        }
      })
    }
  }
}
</script>

<style scoped>
.pagination-container {
  margin-top: 20px;
  text-align: right;
}
</style>
