-- 修复数据库建表问题
-- 设置SQL模式，移除严格模式中的NO_ZERO_DATE和NO_ZERO_IN_DATE
SET SESSION sql_mode = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION';

-- 使用数据库
USE mall;

-- 检查region表是否存在，如果不存在则创建
CREATE TABLE IF NOT EXISTS `region` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '地区ID',
  `name` varchar(100) NOT NULL COMMENT '地区名称',
  `code` varchar(50) NOT NULL COMMENT '地区代码',
  `parent_id` int(11) DEFAULT NULL COMMENT '父级地区ID',
  `level` tinyint(1) NOT NULL DEFAULT '1' COMMENT '地区级别：1-省份，2-城市，3-区县',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `sort_order` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `code` (`code`),
  KEY `parent_id` (`parent_id`),
  KEY `level` (`level`),
  KEY `status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='地区表';

-- 检查team_member表是否存在，如果不存在则创建
CREATE TABLE IF NOT EXISTS `team_member` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '成员ID',
  `team_id` int(11) NOT NULL COMMENT '团队ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `role` enum('leader','member') NOT NULL DEFAULT 'member' COMMENT '角色：leader-团长，member-成员',
  `joined_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '加入时间',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `team_user` (`team_id`, `user_id`),
  KEY `team_id` (`team_id`),
  KEY `user_id` (`user_id`),
  KEY `role` (`role`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='团队成员表';

-- 检查team表是否需要添加收入字段
ALTER TABLE `team`
ADD COLUMN IF NOT EXISTS `total_income` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '总收入' AFTER `total_profit`,
ADD COLUMN IF NOT EXISTS `month_income` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '本月收入' AFTER `total_income`,
ADD COLUMN IF NOT EXISTS `today_income` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '今日收入' AFTER `month_income`;

-- 修复team表的created_at和updated_at字段默认值
ALTER TABLE `team`
MODIFY `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
MODIFY `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';

-- 插入地区数据（如果不存在）
INSERT IGNORE INTO `region` (`id`, `name`, `code`, `parent_id`, `level`, `status`, `sort_order`) VALUES
(1, '北京市', '110000', NULL, 1, 1, 1),
(2, '天津市', '120000', NULL, 1, 1, 2),
(3, '上海市', '310000', NULL, 1, 1, 3),
(4, '重庆市', '500000', NULL, 1, 1, 4),
(5, '河北省', '130000', NULL, 1, 1, 5),
(6, '山西省', '140000', NULL, 1, 1, 6),
(7, '辽宁省', '210000', NULL, 1, 1, 7),
(8, '吉林省', '220000', NULL, 1, 1, 8),
(9, '黑龙江省', '230000', NULL, 1, 1, 9),
(10, '江苏省', '320000', NULL, 1, 1, 10),
(11, '浙江省', '330000', NULL, 1, 1, 11),
(12, '安徽省', '340000', NULL, 1, 1, 12),
(13, '福建省', '350000', NULL, 1, 1, 13),
(14, '江西省', '360000', NULL, 1, 1, 14),
(15, '山东省', '370000', NULL, 1, 1, 15),
(16, '河南省', '410000', NULL, 1, 1, 16),
(17, '湖北省', '420000', NULL, 1, 1, 17),
(18, '湖南省', '430000', NULL, 1, 1, 18),
(19, '广东省', '440000', NULL, 1, 1, 19),
(20, '海南省', '460000', NULL, 1, 1, 20),
(21, '四川省', '510000', NULL, 1, 1, 21),
(22, '贵州省', '520000', NULL, 1, 1, 22),
(23, '云南省', '530000', NULL, 1, 1, 23),
(24, '陕西省', '610000', NULL, 1, 1, 24),
(25, '甘肃省', '620000', NULL, 1, 1, 25),
(26, '青海省', '630000', NULL, 1, 1, 26),
(27, '台湾省', '710000', NULL, 1, 1, 27),
(28, '内蒙古自治区', '150000', NULL, 1, 1, 28),
(29, '广西壮族自治区', '450000', NULL, 1, 1, 29),
(30, '西藏自治区', '540000', NULL, 1, 1, 30),
(31, '宁夏回族自治区', '640000', NULL, 1, 1, 31),
(32, '新疆维吾尔自治区', '650000', NULL, 1, 1, 32),
(33, '香港特别行政区', '810000', NULL, 1, 1, 33),
(34, '澳门特别行政区', '820000', NULL, 1, 1, 34);

-- 显示成功信息
SELECT 'Database tables created and data inserted successfully!' as message;
