const db = require('./src/database');

async function initTeamTables() {
  try {
    console.log('开始创建团队相关数据表...');
    
    // 创建team表
    await db.query(`
      CREATE TABLE IF NOT EXISTS team (
        id INT PRIMARY KEY AUTO_INCREMENT,
        name VARCHAR(100) NOT NULL COMMENT '团队名称',
        leader_id INT NOT NULL COMMENT '团长用户ID',
        member_count INT DEFAULT 1 COMMENT '团队成员数量',
        status TINYINT DEFAULT 1 COMMENT '状态：0-禁用，1-正常',
        level INT DEFAULT 1 COMMENT '团队等级',
        total_income DECIMAL(10, 2) DEFAULT 0.00 COMMENT '团队总收益',
        month_income DECIMAL(10, 2) DEFAULT 0.00 COMMENT '本月收益',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_leader_id (leader_id),
        INDEX idx_status (status)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='团队表'
    `);
    console.log('✓ team表创建成功');
    
    // 创建团队成员表
    await db.query(`
      CREATE TABLE IF NOT EXISTS team_member (
        id INT PRIMARY KEY AUTO_INCREMENT,
        team_id INT NOT NULL COMMENT '团队ID',
        user_id INT NOT NULL COMMENT '用户ID',
        role VARCHAR(20) DEFAULT 'member' COMMENT '角色：leader-团长，member-成员',
        joined_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_team_id (team_id),
        INDEX idx_user_id (user_id),
        UNIQUE KEY uk_team_user (team_id, user_id)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='团队成员表'
    `);
    console.log('✓ team_member表创建成功');
    
    // 检查是否有通过联盟申请的用户，自动创建团队
    const approvedApplications = await db.query(`
      SELECT ta.*, u.nickname, u.phone 
      FROM team_apply ta
      LEFT JOIN user u ON ta.user_id = u.id
      WHERE ta.status = 1
      AND NOT EXISTS (SELECT 1 FROM team WHERE leader_id = ta.user_id)
    `);
    
    if (approvedApplications && approvedApplications.length > 0) {
      console.log(`\n发现 ${approvedApplications.length} 个已批准的联盟申请需要创建团队`);
      
      for (const app of approvedApplications) {
        // 创建团队
        const result = await db.query(
          'INSERT INTO team (name, leader_id, status, member_count) VALUES (?, ?, 1, 1)',
          [app.name, app.user_id]
        );
        
        if (result.insertId) {
          // 将团长添加到团队成员表
          await db.query(
            'INSERT INTO team_member (team_id, user_id, role) VALUES (?, ?, ?)',
            [result.insertId, app.user_id, 'leader']
          );
          
          // 更新用户为团长
          await db.query(
            'UPDATE user SET is_leader = 1 WHERE id = ?',
            [app.user_id]
          );
          
          console.log(`✓ 为用户 ${app.nickname}(${app.phone}) 创建团队: ${app.name}`);
        }
      }
    }
    
    // 添加一些测试数据（如果没有团队）
    const existingTeams = await db.query('SELECT COUNT(*) as count FROM team');
    if (existingTeams[0].count === 0) {
      console.log('\n添加测试团队数据...');
      
      // 创建测试团队
      const testTeams = [
        { name: '精英团队', leader_id: 1, status: 1, level: 3 },
        { name: '新星团队', leader_id: 2, status: 1, level: 2 }
      ];
      
      for (const team of testTeams) {
        const result = await db.query(
          'INSERT INTO team (name, leader_id, status, level, member_count) VALUES (?, ?, ?, ?, 1)',
          [team.name, team.leader_id, team.status, team.level]
        );
        
        if (result.insertId) {
          // 将团长添加到成员表
          await db.query(
            'INSERT INTO team_member (team_id, user_id, role) VALUES (?, ?, ?)',
            [result.insertId, team.leader_id, 'leader']
          );
          
          console.log(`✓ 创建测试团队: ${team.name}`);
        }
      }
    }
    
    console.log('\n团队相关数据表初始化完成！');
    process.exit(0);
  } catch (error) {
    console.error('初始化失败:', error);
    process.exit(1);
  }
}

initTeamTables(); 