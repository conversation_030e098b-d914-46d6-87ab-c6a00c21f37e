/* 提现记录页面样式 */

.records-container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.records-list {
  margin-bottom: 20rpx;
}

.record-item {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.record-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.record-type {
  display: flex;
  align-items: center;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.record-type image {
  width: 40rpx;
  height: 40rpx;
  margin-right: 12rpx;
}

.record-status {
  font-size: 24rpx;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  color: white;
}

.status-0 {
  background-color: #ff9500; /* 待审核 */
}

.status-1 {
  background-color: #007aff; /* 审核通过 */
}

.status-2 {
  background-color: #ff3b30; /* 审核拒绝 */
}

.status-3 {
  background-color: #34c759; /* 处理中 */
}

.status-4 {
  background-color: #30d158; /* 已完成 */
}

.status-5 {
  background-color: #8e8e93; /* 已取消 */
}

.record-amount {
  font-size: 48rpx;
  font-weight: bold;
  color: #ff3b30;
  text-align: center;
  margin: 20rpx 0;
}

.record-details {
  border-top: 1rpx solid #f0f0f0;
  padding-top: 20rpx;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.detail-row:last-child {
  margin-bottom: 0;
}

.label {
  font-size: 28rpx;
  color: #666;
}

.value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 40rpx;
  text-align: center;
}

.empty-state image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
  opacity: 0.6;
}

.empty-state text {
  font-size: 32rpx;
  color: #999;
}

/* 加载更多样式 */
.load-more {
  text-align: center;
  padding: 30rpx;
  font-size: 28rpx;
  color: #999;
}

.loading {
  color: #007aff;
}

.no-more {
  color: #ccc;
}

/* 筛选器样式 */
.filter-bar {
  background: white;
  padding: 20rpx 30rpx;
  margin-bottom: 20rpx;
  border-radius: 16rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.filter-item {
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
  font-size: 26rpx;
  border: 1rpx solid #e0e0e0;
  color: #666;
}

.filter-item.active {
  background-color: #007aff;
  color: white;
  border-color: #007aff;
}

/* 统计信息样式 */
.stats-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  color: white;
}

.stats-title {
  font-size: 28rpx;
  opacity: 0.9;
  margin-bottom: 20rpx;
}

.stats-content {
  display: flex;
  justify-content: space-between;
}

.stats-item {
  text-align: center;
}

.stats-value {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.stats-label {
  font-size: 24rpx;
  opacity: 0.8;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .record-item {
    padding: 24rpx;
  }
  
  .record-amount {
    font-size: 42rpx;
  }
  
  .detail-row {
    margin-bottom: 10rpx;
  }
  
  .label, .value {
    font-size: 26rpx;
  }
}

/* 动画效果 */
.record-item {
  animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 下拉刷新样式 */
.refresh-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20rpx;
  color: #666;
  font-size: 28rpx;
}

.refresh-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 12rpx;
  animation: rotate 1s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
