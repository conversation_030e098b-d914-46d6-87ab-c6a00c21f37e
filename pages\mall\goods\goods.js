// pages/mall/goods/goods.js
const app = getApp()
const API = require('../../../config/api')
const { request } = require('../../../utils/request')
const { showToast, showModal } = require('../../../utils/util')
const goodsService = require('../../../services/goods')

Page({
  /**
   * 页面的初始数据
   */
  data: {
    goodsId: null,
    goodsInfo: null,
    currentSpecIndex: 0,
    quantity: 1,
    loading: true,
    isLoggedIn: false,
    recommendGoods: [],
    // 规格选择相关
    showSpecPopup: false,
    specTypes: [], // 规格类型，如颜色、尺码
    selectedSpecs: {}, // 已选规格，如 {color: '红色', size: 'XL'}
    availableSpecs: [] // 可选规格组合
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    console.log('商品详情页onLoad, 参数:', options);
    if (options && options.id) {
      this.setData({
        goodsId: options.id,
        isLoggedIn: app.globalData.isLoggedIn || false
      })
      this.fetchGoodsDetail(options.id)
      this.fetchRecommendGoods()
    } else {
      console.error('商品ID不存在', options);
      showToast('商品ID不存在')
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
    }
  },

  /**
   * 获取商品详情
   */
  fetchGoodsDetail: function (goodsId) {
    this.setData({ loading: true })
    
    // 使用服务层函数，正确使用RESTful风格请求
    goodsService.getGoodsDetail(goodsId)
      .then(res => {
      console.log('商品详情API返回数据:', res)
      if ((res.code === 0 || res.status === 'success') && res.data) {
        // 处理图片路径
        const goodsInfo = this.processImageUrls(res.data)
        
        // 处理商品规格
        this.processGoodsSpecs(goodsInfo)
        
        this.setData({
          goodsInfo: goodsInfo,
          loading: false
        })
      } else {
        showToast('获取商品详情失败')
        this.setData({ loading: false })
      }
    }).catch(err => {
      console.error('获取商品详情失败', err)
      showToast('获取商品详情失败')
      this.setData({ loading: false })
    })
  },
  
  /**
   * 处理图片URL，确保正确加载服务器上的图片
   */
  processImageUrls: function(goodsData) {
    if (!goodsData) return goodsData
    
    const app = getApp()
    const baseUrl = app.globalData.imageBaseUrl || app.globalData.baseUrl || 'http://localhost:4000'
    
    console.log('处理图片URL，使用基础URL:', baseUrl)
    
    // 处理商品封面图
    if (goodsData.cover && !goodsData.cover.startsWith('http')) {
      goodsData.cover = baseUrl + goodsData.cover
    }
    
    // 处理商品图片列表
    if (goodsData.images && Array.isArray(goodsData.images)) {
      goodsData.images = goodsData.images.map(img => {
        if (img && !img.startsWith('http')) {
          return baseUrl + img
        }
        return img
      })
    }
    
    // 处理详情图片
    if (goodsData.detailImages && Array.isArray(goodsData.detailImages)) {
      goodsData.detailImages = goodsData.detailImages.map(img => {
        if (img && !img.startsWith('http')) {
          return baseUrl + img
        }
        return img
      })
    }
    
    console.log('处理后的商品数据:', goodsData)
    return goodsData
  },

  /**
   * 处理商品规格数据
   */
  processGoodsSpecs: function(goodsInfo) {
    // 如果没有规格数据，创建默认规格
    if (!goodsInfo.specifications || !Array.isArray(goodsInfo.specifications) || goodsInfo.specifications.length === 0) {
      goodsInfo.specifications = [{ 
        id: 0, 
        name: '默认', 
        price: goodsInfo.price,
        size: 'M',
        stock: goodsInfo.stock || 1
      }]
    }
    
    // 提取规格类型
    const specTypes = {}
    const availableSpecs = []
    
    // 遍历所有规格，提取规格类型和值
    goodsInfo.specifications.forEach(spec => {
      // 创建规格组合
      const specCombination = {}
      
      // 遍历规格对象的所有属性
      Object.keys(spec).forEach(key => {
        // 排除id、name、price、stock等基础属性
        if (!['id', 'name', 'price', 'stock'].includes(key)) {
          // 如果是规格属性（如color、size等）
          if (spec[key]) {
            // 记录规格类型
            if (!specTypes[key]) {
              specTypes[key] = new Set()
            }
            specTypes[key].add(spec[key])
            
            // 添加到规格组合
            specCombination[key] = spec[key]
          }
        }
      })
      
      // 如果没有任何规格属性，添加标准尺寸规格
      if (Object.keys(specCombination).length === 0) {
        if (!specTypes['尺寸']) {
          specTypes['尺寸'] = new Set()
        }
        // 添加标准尺码选项
        specTypes['尺寸'].add('XS')
        specTypes['尺寸'].add('S')
        specTypes['尺寸'].add('M')
        specTypes['尺寸'].add('L')
        specTypes['尺寸'].add('XL')
        specTypes['尺寸'].add('XXL')
        specTypes['尺寸'].add('3XL')
        specCombination['尺寸'] = 'M'
      }
      
      // 添加到可用规格列表
      availableSpecs.push({
        id: spec.id,
        combination: specCombination,
        price: spec.price,
        stock: spec.stock || 1
      })
    })
    
    // 转换Set为数组
    const formattedSpecTypes = []
    Object.keys(specTypes).forEach(type => {
      formattedSpecTypes.push({
        name: type,
        values: Array.from(specTypes[type])
      })
    })
    
    // 初始化选中的规格
    const selectedSpecs = {}
    formattedSpecTypes.forEach(type => {
      selectedSpecs[type.name] = type.values[0]
    })
    
    // 更新数据
    this.setData({
      specTypes: formattedSpecTypes,
      selectedSpecs: selectedSpecs,
      availableSpecs: availableSpecs
    })
    
    console.log('处理后的规格数据:', {
      specTypes: formattedSpecTypes,
      selectedSpecs: selectedSpecs,
      availableSpecs: availableSpecs
    })
  },

  /**
   * 获取推荐商品
   */
  fetchRecommendGoods: function () {
    request({
      url: API.goods.list,
      method: 'GET',
      data: {
        recommend: true,
        limit: 6
      }
    }).then(res => {
      console.log('推荐商品API返回数据:', res)
      if ((res.code === 0 || res.status === 'success') && res.data) {
        // 处理每个推荐商品的图片路径
        let goodsList = res.data.list || []
        goodsList = goodsList.map(item => this.processImageUrls(item))
        
        this.setData({
          recommendGoods: goodsList
        })
      }
    }).catch(err => {
      console.error('获取推荐商品失败', err)
    })
  },

  /**
   * 轮播图切换事件
   */
  onSwiperChange: function (e) {
    // 可以在这里处理轮播图切换逻辑
  },

  /**
   * 选择规格
   */
  onSelectSpec: function (e) {
    const index = e.currentTarget.dataset.index
    this.setData({
      currentSpecIndex: index
    })
  },

  /**
   * 减少数量
   */
  onDecreaseQuantity: function () {
    console.log('减少数量按钮点击，当前数量:', this.data.quantity);
    if (this.data.quantity > 0) {
      const newQuantity = this.data.quantity - 1;
      console.log('设置新数量:', newQuantity);
      this.setData({
        quantity: newQuantity
      });
    } else {
      console.log('数量已经是最小值，无法继续减少');
      showToast('已经是最小数量');
    }
  },

  /**
   * 增加数量
   */
  onIncreaseQuantity: function () {
    console.log('增加数量按钮点击，当前数量:', this.data.quantity);
    const maxStock = this.data.goodsInfo?.stock || 999;
    if (this.data.quantity < maxStock) {
      const newQuantity = this.data.quantity + 1;
      console.log('设置新数量:', newQuantity);
      this.setData({
        quantity: newQuantity
      });
    } else {
      console.log('数量已达到最大库存:', maxStock);
      showToast('已达到最大库存');
    }
  },

  /**
   * 加入购物车
   */
  onAddToCart: function () {
    if (!this.data.isLoggedIn) {
      this.goToLogin();
      return;
    }

    if (this.data.quantity <= 0) {
      showToast('请选择商品数量');
      return;
    }

    // 获取当前选中的规格
    const selectedSpecsValues = Object.values(this.data.selectedSpecs);
    if (selectedSpecsValues.length < this.data.specTypes.length) {
      showToast('请选择商品规格');
      return;
    }
    
    const { goodsId, currentSpecIndex, quantity, goodsInfo } = this.data
    
    if (!goodsInfo) {
      showToast('商品信息不完整')
      return
    }
    
    const specs = goodsInfo.specifications || []
    const selectedSpec = specs[currentSpecIndex] || {}
    
    console.log('添加到购物车，参数:', {
      goodsId: goodsId,
      quantity: quantity,
      specificationId: selectedSpec.id || 0
    });
    
    request({
      url: API.cart.add,
      method: 'POST',
      data: {
        goodsId: goodsId,
        quantity: quantity,
        specificationId: selectedSpec.id || 0
      }
    }).then(res => {
      console.log('添加到购物车响应:', res);
      if (res.code === 0 || res.status === 'success') {
        // 显示更详细的成功提示
        wx.showModal({
          title: '添加成功',
          content: `已成功将 ${goodsInfo.name || '商品'} 添加到购物车`,
          showCancel: false,
          confirmText: '确定'
        });
      } else {
        showToast(res.message || '添加失败，请重试');
      }
    }).catch(err => {
      console.error('添加到购物车失败', err);
      showToast('添加失败，请重试');
    });
  },

  /**
   * 立即购买
   */
  onBuyNow: function () {
    if (!this.data.isLoggedIn) {
      this.goToLogin();
      return;
    }

    if (this.data.quantity <= 0) {
      showToast('请选择商品数量');
      return;
    }

    // 获取当前选中的规格
    const selectedSpecsValues = Object.values(this.data.selectedSpecs);
    if (selectedSpecsValues.length < this.data.specTypes.length) {
      showToast('请选择商品规格');
      return;
    }
    
    const { goodsId, currentSpecIndex, quantity, goodsInfo } = this.data
    
    if (!goodsInfo) {
      showToast('商品信息不完整')
      return
    }
    
    const specs = goodsInfo.specifications || []
    const selectedSpec = specs[currentSpecIndex] || {}
    
    // 构建订单预创建数据，包含完整商品信息
    const orderGoods = [{
      goodsId: Number(goodsId),
      quantity: quantity,
      specificationId: selectedSpec.id || 0,
      // 添加完整商品信息
      id: goodsInfo.id,
      name: goodsInfo.name,
      cover: goodsInfo.cover,
      price: selectedSpec.price || goodsInfo.price,
      selectedSpec: selectedSpec
    }]

    console.log('立即购买，商品数据:', orderGoods);

    // 将订单商品信息存储到全局数据
    app.globalData.tempOrderGoods = orderGoods
    
    // 跳转到订单确认页面
    wx.navigateTo({
      url: '/pages/mall/order/confirm/confirm?from=buy_now'
    })
  },

  /**
   * 查看更多评价
   */
  onViewMoreReviews: function () {
    showToast('评价功能开发中')
  },

  /**
   * 点击推荐商品
   */
  onRecommendGoodsTap: function (e) {
    const goodsId = e.currentTarget.dataset.id
    wx.redirectTo({
      url: `/pages/mall/goods/goods?id=${goodsId}`
    })
  },

  /**
   * 返回商品列表
   */
  goBackToList: function() {
    wx.switchTab({
      url: '/pages/mall/home/<USER>'
    })
  },

  /**
   * 检查登录状态
   */
  checkLogin: function () {
    if (!this.data.isLoggedIn) {
      showModal({
        title: '提示',
        content: '请先登录',
        confirmText: '去登录',
        success: (res) => {
          if (res.confirm) {
            wx.switchTab({
              url: '/pages/user/profile/profile'
            })
          }
        }
      })
      return false
    }
    return true
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {
    const { goodsInfo, goodsId } = this.data
    return {
      title: goodsInfo?.name || 'WiFi共享商城商品',
      path: `/pages/mall/goods/goods?id=${goodsId}`,
      imageUrl: goodsInfo?.images?.[0] || '/assets/images/share-mall.jpg'
    }
  },

  /**
   * 广告加载成功
   */
  onAdLoad: function () {
    console.log('广告加载成功')
  },

  /**
   * 广告加载失败
   */
  onAdError: function (err) {
    console.error('广告加载失败', err)
  },

  /**
   * 显示规格选择弹窗
   */
  onShowSpecPopup: function() {
    // 检查商品信息是否存在
    if (!this.data.goodsInfo) {
      showToast('商品信息不完整')
      return
    }
    
    this.setData({
      showSpecPopup: true
    })
  },
  
  /**
   * 关闭规格选择弹窗
   */
  onCloseSpecPopup: function() {
    this.setData({
      showSpecPopup: false
    })
  },
  
  /**
   * 选择规格
   */
  onSelectSpec: function(e) {
    const { type, value } = e.currentTarget.dataset
    
    // 更新选中的规格
    const selectedSpecs = { ...this.data.selectedSpecs }
    selectedSpecs[type] = value
    
    // 查找匹配的规格组合
    const matchedSpec = this.findMatchingSpec(selectedSpecs)
    
    // 更新当前选中的规格索引和价格
    if (matchedSpec) {
      // 查找规格在goodsInfo.specifications中的索引
      const specIndex = this.data.goodsInfo.specifications.findIndex(spec => spec.id === matchedSpec.id)
      
      this.setData({
        selectedSpecs,
        currentSpecIndex: specIndex >= 0 ? specIndex : 0
      })
    } else {
      this.setData({ selectedSpecs })
    }
  },
  
  /**
   * 查找匹配的规格组合
   */
  findMatchingSpec: function(selectedSpecs) {
    // 检查每个可用规格组合是否匹配当前选择
    return this.data.availableSpecs.find(spec => {
      // 检查每个规格类型是否匹配
      for (const type in selectedSpecs) {
        if (spec.combination[type] !== selectedSpecs[type]) {
          return false
        }
      }
      return true
    })
  },
  
  /**
   * 确认选择规格
   */
  onConfirmSpec: function() {
    // 查找匹配的规格组合
    const matchedSpec = this.findMatchingSpec(this.data.selectedSpecs)
    
    if (matchedSpec) {
      // 查找规格在goodsInfo.specifications中的索引
      const specIndex = this.data.goodsInfo.specifications.findIndex(spec => spec.id === matchedSpec.id)
      
      if (specIndex >= 0) {
        this.setData({
          currentSpecIndex: specIndex,
          showSpecPopup: false
        }, () => {
          // 确认规格后直接添加购物车
          const { goodsId, currentSpecIndex, quantity, goodsInfo } = this.data
    
          if (!goodsInfo) {
            showToast('商品信息不完整')
            return
          }
          
          const specs = goodsInfo.specifications || []
          const selectedSpec = specs[currentSpecIndex] || {}
          
          request({
            url: API.cart.add,
            method: 'POST',
            data: {
              goodsId: goodsId,
              quantity: quantity,
              specificationId: selectedSpec.id || 0
            }
          }).then(res => {
            if (res.code === 0 || res.status === 'success') {
              // 显示成功提示
              wx.showModal({
                title: '添加成功',
                content: `已成功将 ${goodsInfo.name || '商品'} 添加到购物车`,
                showCancel: false,
                confirmText: '确定'
              });
            } else {
              showToast(res.message || '添加失败，请重试');
            }
          }).catch(err => {
            console.error('添加到购物车失败', err);
            showToast('添加失败，请重试');
          });
        })
      } else {
        showToast('规格不可用')
      }
    } else {
      showToast('请选择有效的规格组合')
    }
  }
})