const { validationResult } = require('express-validator');
const logger = require('../utils/logger');

/**
 * 数据验证中间件
 * 用于验证请求参数
 */
const validate = (req, res, next) => {
  // 获取验证结果
  const errors = validationResult(req);
  
  // 如果有错误，返回400 Bad Request
  if (!errors.isEmpty()) {
    logger.warn(`请求验证失败: ${JSON.stringify(errors.array())}`);
    
    return res.status(400).json({
      success: false,
      message: '请求参数验证失败',
      errors: errors.array().map(error => ({
        field: error.param,
        message: error.msg
      }))
    });
  }
  
  // 验证通过，继续执行
  next();
};

module.exports = validate; 