const db = require('./src/database');

async function fixTeamStatus() {
  try {
    console.log('🔧 修复团队状态...\n');
    
    // 1. 查看当前团队状态
    console.log('📋 当前团队状态:');
    const teams = await db.query(`
      SELECT t.id, t.name, t.leader_id, t.status, u.nickname as leader_name
      FROM team t
      LEFT JOIN user u ON t.leader_id = u.id
      ORDER BY t.created_at DESC
    `);
    
    if (teams && teams.length > 0) {
      console.table(teams.map(team => ({
        团队ID: team.id,
        团队名称: team.name,
        团长ID: team.leader_id,
        团长昵称: team.leader_name,
        状态: team.status === 1 ? '正常' : '禁用'
      })));
    } else {
      console.log('❌ 没有找到团队记录');
      return;
    }
    
    // 2. 更新所有团队状态为正常
    console.log('\n🔄 更新团队状态为正常...');
    const updateResult = await db.query(`
      UPDATE team SET status = 1, updated_at = NOW()
      WHERE status = 0
    `);
    
    console.log(`✅ 已更新 ${updateResult.affectedRows} 个团队的状态`);
    
    // 3. 再次查看更新后的状态
    console.log('\n📋 更新后的团队状态:');
    const updatedTeams = await db.query(`
      SELECT t.id, t.name, t.leader_id, t.status, u.nickname as leader_name
      FROM team t
      LEFT JOIN user u ON t.leader_id = u.id
      ORDER BY t.created_at DESC
    `);
    
    if (updatedTeams && updatedTeams.length > 0) {
      console.table(updatedTeams.map(team => ({
        团队ID: team.id,
        团队名称: team.name,
        团长ID: team.leader_id,
        团长昵称: team.leader_name,
        状态: team.status === 1 ? '正常' : '禁用'
      })));
    }
    
    console.log('\n✅ 团队状态修复完成！');
    process.exit(0);
  } catch (error) {
    console.error('❌ 修复失败:', error);
    process.exit(1);
  }
}

fixTeamStatus();
