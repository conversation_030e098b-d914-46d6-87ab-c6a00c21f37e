/* pages/invite/invite.wxss */
.container {
  padding: 40rpx 30rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  color: #fff;
}

/* 头部 */
.header {
  text-align: center;
  margin-bottom: 60rpx;
}

.logo {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  margin-bottom: 20rpx;
}

.title {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.subtitle {
  display: block;
  font-size: 28rpx;
  opacity: 0.8;
}

/* 邀请信息 */
.invite-info {
  margin-bottom: 40rpx;
}

.team-card {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20rpx;
  padding: 40rpx;
  backdrop-filter: blur(10rpx);
}

.team-header {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}

.team-avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50rpx;
  margin-right: 30rpx;
}

.team-details {
  flex: 1;
}

.team-name {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.team-leader, .team-members {
  display: block;
  font-size: 26rpx;
  opacity: 0.8;
  margin-bottom: 5rpx;
}

.team-stats {
  display: flex;
  justify-content: space-around;
  border-top: 1rpx solid rgba(255, 255, 255, 0.2);
  padding-top: 30rpx;
}

.stat-item {
  text-align: center;
}

.stat-value {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #FFD700;
  margin-bottom: 10rpx;
}

.stat-label {
  display: block;
  font-size: 24rpx;
  opacity: 0.8;
}

/* 邀请码 */
.invite-code-section {
  margin-bottom: 40rpx;
}

.invite-code-label {
  display: block;
  font-size: 28rpx;
  margin-bottom: 20rpx;
  opacity: 0.8;
}

.invite-code-box {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 15rpx;
  padding: 20rpx 30rpx;
  backdrop-filter: blur(10rpx);
}

.invite-code {
  flex: 1;
  font-size: 32rpx;
  font-weight: bold;
  letter-spacing: 4rpx;
}

.copy-btn {
  background: #FFD700;
  color: #333;
  border: none;
  border-radius: 10rpx;
  padding: 15rpx 30rpx;
  font-size: 26rpx;
  font-weight: bold;
}

/* 操作按钮 */
.actions {
  margin-bottom: 60rpx;
}

.join-btn, .login-btn {
  width: 100%;
  background: #FFD700;
  color: #333;
  border: none;
  border-radius: 50rpx;
  padding: 30rpx;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
}

.join-btn:active, .login-btn:active {
  background: #FFC107;
}

.already-member {
  text-align: center;
  padding: 40rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20rpx;
  backdrop-filter: blur(10rpx);
}

.already-member text {
  display: block;
  font-size: 28rpx;
  margin-bottom: 20rpx;
}

.go-team-btn {
  background: transparent;
  color: #FFD700;
  border: 2rpx solid #FFD700;
  border-radius: 50rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
}

/* 底部说明 */
.footer {
  text-align: center;
  opacity: 0.8;
}

.description {
  display: block;
  font-size: 28rpx;
  margin-bottom: 30rpx;
  font-weight: bold;
}

.tips {
  display: block;
  font-size: 24rpx;
  margin-bottom: 10rpx;
  padding-left: 20rpx;
}

/* 加载状态 */
.loading {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loading text {
  color: #fff;
  font-size: 32rpx;
}
