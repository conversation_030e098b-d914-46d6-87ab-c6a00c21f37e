const mysql = require('mysql2/promise');

async function setupWithdrawData() {
  let connection;
  
  try {
    // 连接数据库
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: 'wo587129955',
      database: 'mall'
    });
    
    console.log('🔧 设置提现系统数据...\n');
    
    // 1. 插入提现方式配置
    console.log('1️⃣ 插入提现方式配置...');
    await connection.execute(`
      INSERT IGNORE INTO withdraw_method (method_code, method_name, min_amount, max_amount, fee_rate, process_time, description, is_enabled) VALUES
      ('wechat', '微信零钱', 1.00, 20000.00, 0.006, '实时到账', '提现到微信零钱，实时到账', 1),
      ('alipay', '支付宝', 1.00, 50000.00, 0.0055, '2小时内', '提现到支付宝余额，2小时内到账', 1),
      ('bank_card', '银行卡', 100.00, 50000.00, 0.01, '1-3个工作日', '提现到银行卡，1-3个工作日到账', 1)
    `);
    console.log('✅ 提现方式配置插入成功');
    
    // 2. 为测试用户插入提现账户（使用正确的字段名）
    console.log('\n2️⃣ 插入测试用户提现账户...');
    await connection.execute(`
      INSERT IGNORE INTO user_withdraw_account (user_id, method_code, account_name, account_number, account_holder, bank_name, bank_branch, id_card, phone, is_default, is_verified) VALUES
      (1, 'wechat', '微信零钱', 'oW20C7mVlW8e3W2AgUGtDTJeAbQU', '张润生', NULL, NULL, '110101199001011234', '***********', 1, 1),
      (1, 'alipay', '支付宝账户', '138****8888', '张润生', NULL, NULL, '110101199001011234', '***********', 0, 0),
      (1, 'bank_card', '工商银行储蓄卡', '6222****1234', '张润生', '中国工商银行', '北京分行', '110101199001011234', '***********', 0, 0)
    `);
    console.log('✅ 测试用户提现账户插入成功');
    
    // 3. 插入测试提现申请
    console.log('\n3️⃣ 插入测试提现申请...');
    const withdrawNo1 = 'WD' + new Date().toISOString().slice(0,10).replace(/-/g,'') + '001';
    const withdrawNo2 = 'WD' + new Date().toISOString().slice(0,10).replace(/-/g,'') + '002';
    const withdrawNo3 = 'WD' + new Date().toISOString().slice(0,10).replace(/-/g,'') + '003';
    
    await connection.execute(`
      INSERT IGNORE INTO withdraw_application (user_id, withdraw_no, amount, fee, actual_amount, account_id, account_type, account_info, status, apply_time) VALUES
      (1, ?, 100.00, 1.00, 99.00, 1, 'wechat', '{"method_code": "wechat", "account_name": "微信零钱", "account_holder": "张润生"}', 4, DATE_SUB(NOW(), INTERVAL 2 DAY)),
      (1, ?, 200.00, 2.00, 198.00, 2, 'alipay', '{"method_code": "alipay", "account_name": "支付宝账户", "account_holder": "张润生"}', 1, DATE_SUB(NOW(), INTERVAL 1 DAY)),
      (1, ?, 50.00, 0.60, 49.40, 1, 'wechat', '{"method_code": "wechat", "account_name": "微信零钱", "account_holder": "张润生"}', 0, NOW())
    `, [withdrawNo1, withdrawNo2, withdrawNo3]);
    console.log('✅ 测试提现申请插入成功');
    
    // 4. 插入银行卡数据
    console.log('\n4️⃣ 插入银行卡数据...');
    await connection.execute(`
      INSERT IGNORE INTO bank_card (user_id, bank_name, card_number, card_holder, is_default) VALUES
      (1, '中国工商银行', '6222****1234', '张润生', 1),
      (1, '中国建设银行', '4367****5678', '张润生', 0),
      (1, '招商银行', '6225****9012', '张润生', 0)
    `);
    console.log('✅ 银行卡数据插入成功');
    
    // 5. 验证数据
    console.log('\n5️⃣ 验证数据...');
    
    // 显示提现方式配置
    console.log('💰 提现方式配置:');
    const [methods] = await connection.execute(`
      SELECT 
        method_code as '方式代码',
        method_name as '方式名称',
        min_amount as '最小金额',
        max_amount as '最大金额',
        CONCAT(fee_rate * 100, '%') as '手续费率',
        process_time as '到账时间',
        description as '说明',
        is_enabled as '启用状态'
      FROM withdraw_method
      ORDER BY method_code
    `);
    console.table(methods);
    
    // 显示用户提现账户
    console.log('\n🏦 用户提现账户:');
    const [accounts] = await connection.execute(`
      SELECT 
        id,
        method_code as '提现方式',
        account_name as '账户名称',
        account_number as '账户号码',
        account_holder as '账户持有人',
        bank_name as '银行名称',
        is_default as '默认',
        is_verified as '已验证',
        status as '状态'
      FROM user_withdraw_account
      WHERE user_id = 1
      ORDER BY is_default DESC, id
    `);
    console.table(accounts);
    
    // 显示银行卡
    console.log('\n💳 用户银行卡:');
    const [cards] = await connection.execute(`
      SELECT 
        id,
        bank_name as '银行名称',
        card_number as '卡号',
        card_holder as '持卡人',
        is_default as '默认'
      FROM bank_card
      WHERE user_id = 1
      ORDER BY is_default DESC, id
    `);
    console.table(cards);
    
    // 显示提现申请
    console.log('\n📋 提现申请记录:');
    const [applications] = await connection.execute(`
      SELECT 
        id,
        withdraw_no as '提现单号',
        amount as '申请金额',
        fee as '手续费',
        actual_amount as '到账金额',
        account_type as '提现方式',
        CASE status
          WHEN 0 THEN '待审核'
          WHEN 1 THEN '审核通过'
          WHEN 2 THEN '审核拒绝'
          WHEN 3 THEN '处理中'
          WHEN 4 THEN '已完成'
          WHEN 5 THEN '已取消'
        END as '状态',
        apply_time as '申请时间'
      FROM withdraw_application
      WHERE user_id = 1
      ORDER BY apply_time DESC
    `);
    console.table(applications);
    
    // 统计信息
    const [methodCount] = await connection.execute('SELECT COUNT(*) as count FROM withdraw_method');
    const [accountCount] = await connection.execute('SELECT COUNT(*) as count FROM user_withdraw_account WHERE user_id = 1');
    const [cardCount] = await connection.execute('SELECT COUNT(*) as count FROM bank_card WHERE user_id = 1');
    const [applicationCount] = await connection.execute('SELECT COUNT(*) as count FROM withdraw_application WHERE user_id = 1');
    
    console.log('\n📊 数据统计:');
    console.log(`- 提现方式: ${methodCount[0].count} 种`);
    console.log(`- 用户提现账户: ${accountCount[0].count} 个`);
    console.log(`- 用户银行卡: ${cardCount[0].count} 张`);
    console.log(`- 提现申请: ${applicationCount[0].count} 条`);
    
    console.log('\n🎉 提现系统数据设置完成！');
    
    // 6. 生成提现流程说明
    console.log('\n📝 提现流程说明:');
    console.log('1. 用户选择提现方式（微信/支付宝/银行卡）');
    console.log('2. 输入提现金额，系统自动计算手续费');
    console.log('3. 选择或添加提现账户');
    console.log('4. 提交提现申请，状态为"待审核"');
    console.log('5. 管理员审核通过后，状态变为"审核通过"');
    console.log('6. 系统处理提现，状态变为"处理中"');
    console.log('7. 提现完成，状态变为"已完成"');
    
    console.log('\n💡 手续费说明:');
    console.log('- 微信零钱: 0.6% (最低0.6元，最高25元)');
    console.log('- 支付宝: 0.55% (最低0.55元，最高25元)');
    console.log('- 银行卡: 1% (最低1元，最高50元)');
    
  } catch (error) {
    console.error('❌ 设置失败:', error.message);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// 运行设置
setupWithdrawData();
