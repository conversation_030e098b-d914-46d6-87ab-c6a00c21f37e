const mysql = require('mysql2/promise');

async function debugUserUpdate() {
  let connection;
  
  try {
    console.log('🔍 开始诊断用户更新问题...\n');
    
    // 创建数据库连接
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: 'wo587129955',
      database: 'mall'
    });
    
    console.log('✅ 数据库连接成功');
    
    // 1. 检查用户表结构
    console.log('\n📋 检查用户表结构:');
    const [structure] = await connection.execute('DESCRIBE user');
    console.table(structure);
    
    // 2. 检查是否有用户数据
    console.log('\n👥 检查用户数据:');
    const [users] = await connection.execute('SELECT id, openid, nickname, avatar, gender, is_demote FROM user LIMIT 5');
    console.table(users);
    
    // 3. 检查是否缺少字段
    const requiredFields = ['country', 'province', 'city', 'is_demote'];
    const existingFields = structure.map(field => field.Field);
    
    console.log('\n🔍 检查必需字段:');
    requiredFields.forEach(field => {
      const exists = existingFields.includes(field);
      console.log(`${exists ? '✅' : '❌'} ${field}: ${exists ? '存在' : '缺失'}`);
    });
    
    // 4. 如果缺少字段，添加它们
    const missingFields = requiredFields.filter(field => !existingFields.includes(field));
    
    if (missingFields.length > 0) {
      console.log('\n🔧 添加缺失的字段:');
      
      for (const field of missingFields) {
        try {
          let alterSQL = '';
          switch (field) {
            case 'country':
              alterSQL = 'ALTER TABLE user ADD COLUMN country VARCHAR(50) DEFAULT NULL COMMENT "国家"';
              break;
            case 'province':
              alterSQL = 'ALTER TABLE user ADD COLUMN province VARCHAR(50) DEFAULT NULL COMMENT "省份"';
              break;
            case 'city':
              alterSQL = 'ALTER TABLE user ADD COLUMN city VARCHAR(50) DEFAULT NULL COMMENT "城市"';
              break;
            case 'is_demote':
              alterSQL = 'ALTER TABLE user ADD COLUMN is_demote TINYINT(1) NOT NULL DEFAULT 0 COMMENT "是否降级用户：0否，1是"';
              break;
          }
          
          if (alterSQL) {
            await connection.execute(alterSQL);
            console.log(`✅ 添加字段 ${field} 成功`);
          }
        } catch (error) {
          console.log(`❌ 添加字段 ${field} 失败:`, error.message);
        }
      }
    } else {
      console.log('\n✅ 所有必需字段都存在');
    }
    
    // 5. 测试更新操作
    console.log('\n🧪 测试用户更新操作:');
    
    // 查找一个测试用户
    const [testUsers] = await connection.execute('SELECT id FROM user LIMIT 1');
    
    if (testUsers.length > 0) {
      const testUserId = testUsers[0].id;
      console.log(`使用用户ID ${testUserId} 进行测试`);
      
      // 尝试更新用户信息
      const updateData = {
        nickname: '测试用户',
        avatar: 'https://example.com/avatar.jpg',
        gender: 1,
        country: '中国',
        province: '广东省',
        city: '深圳市',
        is_demote: 0
      };
      
      const setClause = Object.keys(updateData).map(key => `${key} = ?`).join(', ');
      const params = Object.values(updateData);
      params.push(testUserId);
      
      const updateSQL = `UPDATE user SET ${setClause}, updated_at = NOW() WHERE id = ?`;
      console.log('执行SQL:', updateSQL);
      console.log('参数:', params);
      
      const [result] = await connection.execute(updateSQL, params);
      console.log('更新结果:', result);
      
      if (result.affectedRows > 0) {
        console.log('✅ 用户更新测试成功');
        
        // 查询更新后的数据
        const [updatedUser] = await connection.execute('SELECT * FROM user WHERE id = ?', [testUserId]);
        console.log('更新后的用户数据:');
        console.table(updatedUser);
      } else {
        console.log('❌ 用户更新测试失败：没有影响任何行');
      }
    } else {
      console.log('❌ 没有找到测试用户');
    }
    
  } catch (error) {
    console.error('❌ 诊断过程中出现错误:', error);
  } finally {
    if (connection) {
      await connection.end();
      console.log('\n🔚 数据库连接已关闭');
    }
  }
}

// 运行诊断
debugUserUpdate().catch(console.error);
