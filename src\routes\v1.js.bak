const express = require('express');
const router = express.Router();

// 引入各个模块的路由
const systemRouter = require('./system');
const userRouter = require('./user');
const wifiRouter = require('./wifi');
const goodsRouter = require('./goods');
const orderRouter = require('./order');
const authRouter = require('./auth');
const logRouter = require('./log');
const advertisementRouter = require('./advertisement');
const uploadRouter = require('./upload');
const incomeRouter = require('./income');
const withdrawRouter = require('./withdraw');
const cartRouter = require('./cart');
const adsRouter = require('./ads');
const teamRouter = require('./team');

// 引入商品控制器（用于无认证访问）
const goodsController = require('../controllers/goods');
// 引入上传控制器
const uploadController = require('../controllers/upload');
// 引入数据库连接
const db = require('../database');
// 引入中间件和响应工具
const { verifyToken } = require('../middlewares/auth');
const { success, error } = require('../utils/response');
const config = require('../../config');

// 管理端路由
router.use('/admin/system', systemRouter);
router.use('/admin/user', userRouter);
router.use('/admin/wifi', wifiRouter);
router.use('/admin/goods', goodsRouter);
router.use('/admin/order', orderRouter);
router.use('/admin/auth', authRouter);
router.use('/admin/log', logRouter);
router.use('/admin/advertisement', advertisementRouter);
router.use('/admin/upload', uploadRouter);
router.use('/admin/income', incomeRouter);
router.use('/admin/withdraw', withdrawRouter);
router.use('/admin/team', teamRouter);

// 直接处理客户端商品公开API
router.get('/client/goods/list', (req, res) => {
  console.log('直接处理客户端商品列表请求，无需认证');
  goodsController.getGoodsList(req, res);
});

// 增加推荐商品API路由
router.get('/client/goods/recommend', (req, res) => {
  console.log('直接处理客户端推荐商品请求，无需认证');
  // 从查询参数获取limit
  const { limit = 3 } = req.query;
  
  // 修改req.query来获取推荐商品
  req.query.isRecommend = true;
  req.query.limit = limit;
  
  goodsController.getGoodsList(req, res);
});

// 增加查询参数形式的商品详情路由
router.get('/client/goods/detail', (req, res) => {
  console.log('直接处理客户端商品详情查询参数请求，无需认证');
  // 从查询参数获取ID
  const { id } = req.query;
  if (!id) {
    return res.status(400).json({
      status: 'error',
      message: '商品ID不能为空'
    });
  }
  
  // 将ID添加到params
  req.params.id = id;
  goodsController.getGoodsDetail(req, res);
});

router.get('/client/goods/detail/:id', (req, res) => {
  console.log('直接处理客户端商品详情请求，无需认证');
  goodsController.getGoodsDetail(req, res);
});

router.get('/client/goods/categories', (req, res) => {
  console.log('直接处理客户端商品分类请求，无需认证');
  goodsController.getCategories(req, res);
});

// 直接处理客户端购物车公开API
router.get('/client/cart/list', (req, res) => {
  console.log('直接处理客户端购物车列表请求，无需认证');
  cartRouter.handle({ 
    method: 'GET', 
    url: '/client/list',
    baseUrl: '',
    originalUrl: '/api/v1/client/cart/list',
    path: '/client/list',
    params: {},
    query: req.query,
    headers: req.headers
  }, res);
});

// 直接处理客户端广告公开API
router.get('/client/ads/banner', (req, res) => {
  console.log('直接处理客户端首页轮播图广告请求，无需认证');
  adsRouter.handle({ 
    method: 'GET', 
    url: '/client/banner',
    baseUrl: '',
    originalUrl: '/api/v1/client/ads/banner',
    path: '/client/banner',
    params: {},
    query: req.query,
    headers: req.headers
  }, res);
});

// 直接处理客户端WiFi列表API - 必须放在/client/wifi/:id路由之前
router.get('/client/wifi/list', (req, res) => {
  console.log('直接处理客户端WiFi列表请求，无需认证');
  // 添加调试日志，记录参数
  const { page, pageSize, keyword } = req.query;
  console.log('处理客户端WiFi列表请求，参数：', { page, pageSize, keyword });
  
  // 明确指定路由处理函数
  wifiRouter.handle({ 
    method: 'GET', 
    url: '/client/list',
    baseUrl: '',
    originalUrl: '/api/v1/client/wifi/list',
    path: '/client/list',
    params: {},
    query: req.query,
    headers: req.headers,
    user: req.user || null // 传递用户信息，如果有的话
  }, res);
});

// 处理客户端WiFi的GET请求，用于展示WiFi码
router.get('/client/wifi/:id', async (req, res) => {
  console.log('直接处理客户端WiFi码请求，ID:', req.params.id);
  
  // 确保id不是list，如果是list，直接返回错误
  if (req.params.id === 'list') {
    return error(res, '无效的请求', 400);
  }
  
  try {
    const { id } = req.params;
    
    if (!id || isNaN(parseInt(id))) {
      console.log('无效的ID参数:', id);
      return error(res, '无效的ID参数', 400);
    }
    
    // 记录查询参数
    console.log('查询WiFi数据，ID:', parseInt(id));
    
    // 确保查询返回的是正确格式 [rows, fields]
    const results = await db.query('SELECT * FROM wifi WHERE id = ?', [parseInt(id)]);
    console.log('WiFi查询结果:', results);
    
    // 检查查询结果格式
    if (!results || !Array.isArray(results)) {
      console.log('WiFi查询结果无效，非数组格式');
      return error(res, 'WiFi查询结果格式错误', 500);
    }
    
    // 处理MySQL2返回的 [rows, fields] 格式
    const rows = Array.isArray(results[0]) ? results[0] : results;
    
    // 检查是否有查询结果
    if (!rows || !Array.isArray(rows) || rows.length === 0) {
      console.log('WiFi不存在，ID:', id);
      return error(res, 'WiFi码不存在', 404);
    }
    
    // 获取第一条记录
    const wifiRecord = rows[0];
    
    // 检查记录是否有效
    if (!wifiRecord || typeof wifiRecord !== 'object') {
      console.log('WiFi记录无效:', wifiRecord);
      return error(res, 'WiFi记录格式错误', 500);
    }
    
    console.log('找到WiFi记录:', wifiRecord);
    
    // 创建默认值对象，以防某些字段为null
    const defaultRecord = {
      id: parseInt(id),
      title: '',
      name: '',
      password: '',
      merchant_name: '',
      qrcode: '',
      use_count: 0,
      user_id: 0,
      status: 1,
      created_at: new Date(),
      updated_at: new Date(),
      ad_enabled: false,
      ad_type: 'video',
      ad_content: '',
      ad_duration: 5,
      ad_title: '推荐内容',
      ad_link: '',
      ad_view_count: 0,
      ad_click_count: 0
    };
    
    // 添加广告相关字段，使用默认值对象确保不会有undefined
    // 并将字段名改为前端期望的格式
    const enhancedWifiRecord = {
      ...defaultRecord,
      ...wifiRecord,
      // 改名：将name映射为ssid，merchant_name映射为merchantName
      id: wifiRecord.id || parseInt(id),
      title: wifiRecord.title || '',
      ssid: wifiRecord.name || '', // 映射name到ssid
      password: wifiRecord.password || '',
      merchantName: wifiRecord.merchant_name || '', // 映射merchant_name到merchantName
      qrcode: wifiRecord.qrcode || '',
      use_count: wifiRecord.use_count || 0,
      user_id: wifiRecord.user_id || 0,
      status: wifiRecord.status || 0,
      createTime: wifiRecord.created_at || new Date(), // 映射created_at到createTime
      updateTime: wifiRecord.updated_at || new Date(), // 映射updated_at到updateTime
      ad_enabled: wifiRecord.ad_enabled !== undefined ? wifiRecord.ad_enabled : (wifiRecord.ad_required === 1),
      ad_type: wifiRecord.ad_type || 'video',
      ad_content: wifiRecord.ad_content || wifiRecord.ad_url || '',
      ad_duration: wifiRecord.ad_duration || 5,
      ad_title: wifiRecord.ad_title || '推荐内容',
      ad_link: wifiRecord.ad_link || '',
      ad_view_count: wifiRecord.ad_view_count || 0,
      ad_click_count: wifiRecord.ad_click_count || 0,
      // 添加统计数据
      stats: {
        scanCount: wifiRecord.use_count || 0,
        connectCount: wifiRecord.use_count || 0,
        todayCount: 0,
        totalEarnings: 0
      }
    };
    
    console.log('返回增强的WiFi详情:', enhancedWifiRecord);
    return success(res, enhancedWifiRecord, '获取WiFi码详情成功');
  } catch (err) {
    console.error('获取WiFi详情失败:', err);
    console.error('错误堆栈:', err.stack);
    
    // 确保即使出错也返回一个包含ID的基本对象
    try {
      const id = parseInt(req.params.id);
      const basicRecord = {
        id: id,
        title: 'WiFi详情',
        ssid: '',  // 使用前端期望的字段名ssid，而不是name
        password: '',
        merchantName: '', // 使用前端期望的字段名merchantName，而不是merchant_name
        status: 1,
        createTime: new Date(), // 使用前端期望的字段名createTime
        stats: {
          scanCount: 0,
          connectCount: 0,
          todayCount: 0,
          totalEarnings: 0
        }
      };
      return success(res, basicRecord, '获取WiFi码详情成功，但数据不完整');
    } catch (innerErr) {
      console.error('生成基本记录失败:', innerErr);
    }
    
    return error(res, '获取WiFi详情失败: ' + err.message, 500);
  }
});

router.delete('/client/wifi/:id', async (req, res) => {
  console.log('直接处理客户端WiFi码删除请求');
  try {
    const { id } = req.params;
    
    // 检查WiFi是否存在
    const [wifi] = await db.query('SELECT * FROM wifi WHERE id = ?', [id]);
    
    if (!wifi || wifi.length === 0) {
      return error(res, 'WiFi码不存在', 404);
    }
    
    // 删除WiFi码
    await db.query('DELETE FROM wifi WHERE id = ?', [id]);
    
    console.log('删除WiFi码, ID:', id);
    
    return success(res, { id: parseInt(id) }, 'WiFi码删除成功');
  } catch (err) {
    console.error('删除WiFi码失败:', err);
    return error(res, '删除WiFi码失败: ' + err.message, 500);
  }
});

// 添加客户端文件上传API
router.post('/client/upload', 
  require('../middlewares/auth').verifyToken, 
  uploadController.uploadMiddleware, 
  uploadController.uploadSingle
);

// 客户端API路由
router.use('/client/wifi', wifiRouter);
router.use('/client/user', userRouter);
router.use('/client/auth', authRouter);
router.use('/client/order', orderRouter);
router.use('/client/payment', orderRouter);
router.use('/client/team', teamRouter); 
router.use('/client/income', incomeRouter);
router.use('/client/withdraw', withdrawRouter);

// WiFi二维码专用路由 - 使用完全不同的路径避免冲突
router.get('/client/wifi-qrcode', async (req, res) => {
  console.log('处理独立WiFi二维码生成请求，参数:', req.query);
  try {
    const { ssid, password, encryption = 'WPA', hidden = 'false', adEnabled = 'false' } = req.query;
    
    if (!ssid || !password) {
      return res.status(400).json({
        status: 'error',
        message: 'SSID和密码不能为空'
      });
    }
    
    // 检查是否启用广告模式
    const useAdMode = adEnabled === 'true' || adEnabled === '1';
    
    let qrCodeData;
    let qrServerUrl;
    
    if (useAdMode) {
      // 广告模式：生成指向广告页面的URL
      const adPageUrl = `/pages/wifi/ad-view/ad-view?ssid=${encodeURIComponent(ssid)}&pwd=${encodeURIComponent(password)}&type=video`;
      qrCodeData = adPageUrl;
      console.log('使用广告模式生成二维码，数据:', qrCodeData);
    } else {
      // 直连模式：生成标准WiFi连接格式
      qrCodeData = `WIFI:T:${encryption};S:${ssid};P:${password};H:${hidden};;`;
      console.log('使用直连模式生成二维码，数据:', qrCodeData);
    }
    
    // 使用第三方服务生成二维码
    qrServerUrl = `https://api.qrserver.com/v1/create-qr-code/?data=${encodeURIComponent(qrCodeData)}&size=300x300&format=png`;
    
    console.log('生成WiFi二维码成功，URL:', qrServerUrl);
    
    // 返回二维码URL
    return res.json({
      status: 'success',
      message: '二维码生成成功',
      data: {
        qrcode_url: qrServerUrl,
        wifi_string: qrCodeData,
        ad_enabled: useAdMode
      }
    });
  } catch (err) {
    console.error('生成WiFi二维码失败:', err);
    return res.status(500).json({
      status: 'error',
      message: '生成WiFi二维码失败: ' + err.message
    });
  }
});

// WiFi海报生成路由
router.get('/client/wifi-poster', async (req, res) => {
  console.log('处理WiFi海报生成请求，参数:', req.query);
  try {
    const { 
      ssid, 
      password, 
      title = '免费WiFi', 
      merchant = '', 
      logo = '', 
      style = 'default',
      encryption = 'WPA', 
      hidden = 'false' 
    } = req.query;
    
    if (!ssid || !password) {
      return res.status(400).json({
        status: 'error',
        message: 'SSID和密码不能为空'
      });
    }
    
    // 构建WiFi连接字符串
    const wifiString = `WIFI:T:${encryption};S:${ssid};P:${password};H:${hidden};;`;
    
    // 构建二维码URL
    const qrcodeUrl = `https://api.qrserver.com/v1/create-qr-code/?data=${encodeURIComponent(wifiString)}&size=300x300&format=png`;
    
    // 选择海报模板 - 使用本地模板URL而不是外部API
    let posterTemplate;
    let posterBackground;
    let textColor;
    let accentColor;
    
    switch (style) {
      case 'business':
        posterTemplate = '/wifi-posters/business-template.png';
        posterBackground = '#2C3E50';
        textColor = '#FFFFFF';
        accentColor = '#3498DB';
        break;
      case 'cafe':
        posterTemplate = '/wifi-posters/cafe-template.png';
        posterBackground = '#5D4037';
        textColor = '#FFFFFF';
        accentColor = '#8D6E63';
        break;
      case 'modern':
        posterTemplate = '/wifi-posters/modern-template.png';
        posterBackground = '#212121';
        textColor = '#FFFFFF';
        accentColor = '#00BCD4';
        break;
      case 'colorful':
        posterTemplate = '/wifi-posters/colorful-template.png';
        posterBackground = '#673AB7';
        textColor = '#FFFFFF';
        accentColor = '#FFC107';
        break;
      case 'minimal':
        posterTemplate = '/wifi-posters/minimal-template.png';
        posterBackground = '#FFFFFF';
        textColor = '#212121';
        accentColor = '#607D8B';
        break;
      default:
        posterTemplate = '/wifi-posters/default-template.png';
        posterBackground = '#1976D2';
        textColor = '#FFFFFF';
        accentColor = '#BBDEFB';
    }
    
    // 构建海报数据 - 注意不包含密码
    const posterData = {
      title: title || 'WiFi连接',
      ssid: ssid,
      // 不包含密码，符合要求
      merchant: merchant,
      logo: logo,
      style: style,
      qrcode: qrcodeUrl,
      template: posterTemplate,
      background: posterBackground,
      textColor: textColor,
      accentColor: accentColor
    };
    
    // 生成海报预览URL
    // 在实际项目中，这里应该使用Canvas或图像处理库在服务器端生成真实的海报图像
    // 这里我们返回一个模拟的海报数据结构，前端将负责实际渲染
    
    console.log('生成WiFi海报成功');
    
    // 返回海报数据
    return res.json({
      status: 'success',
      message: '海报生成成功',
      data: {
        poster_data: posterData,
        available_styles: [
          { id: 'default', name: '默认蓝色', color: '#1976D2' },
          { id: 'business', name: '商务风格', color: '#2C3E50' },
          { id: 'cafe', name: '咖啡馆', color: '#5D4037' },
          { id: 'modern', name: '现代简约', color: '#212121' },
          { id: 'colorful', name: '缤纷色彩', color: '#673AB7' },
          { id: 'minimal', name: '极简风格', color: '#FFFFFF' }
        ],
        wifi_string: wifiString
      }
    });
  } catch (err) {
    console.error('生成WiFi海报失败:', err);
    return res.status(500).json({
      status: 'error',
      message: '生成WiFi海报失败: ' + err.message
    });
  }
});

// 直接处理admin WIFI详情请求（测试用）
router.get('/admin/wifi/detail/:id', async (req, res) => {
  console.log('直接处理管理端WiFi码详情请求（v1.js中）', req.params);
  try {
    const { id } = req.params;
    
    if (!id || isNaN(parseInt(id))) {
      console.log('无效的ID参数:', id);
      return error(res, '无效的ID参数', 400);
    }
    
    // 添加调试日志
    console.log('正在查询WiFi记录，ID:', id);
    
    try {
      // 修正查询和数据处理逻辑
      const connection = await db.getConnection();
      const [rows] = await connection.query('SELECT * FROM wifi WHERE id = ?', [parseInt(id)]);
      connection.release();
      
      console.log('查询结果类型:', typeof rows);
      console.log('查询结果:', rows);
      
      // 检查查询结果
      if (!rows || rows.length === 0) {
        console.log('WiFi不存在(v1.js):', id);
        return error(res, 'WiFi码不存在', 404);
      }
      
      // 获取第一条记录
      const wifiRecord = rows[0];
      console.log('返回WiFi详情(v1.js):', wifiRecord);
      
      // 添加或转换广告相关字段
      const enhancedWifiRecord = {
        id: wifiRecord.id || 0,
        title: wifiRecord.title || '',
        name: wifiRecord.name || '',
        password: wifiRecord.password || '',
        merchant_name: wifiRecord.merchant_name || '',
        qrcode: wifiRecord.qrcode || '',
        use_count: wifiRecord.use_count || 0,
        user_id: wifiRecord.user_id || 0,
        status: wifiRecord.status !== undefined ? wifiRecord.status : 1,
        created_at: wifiRecord.created_at || new Date(),
        updated_at: wifiRecord.updated_at || new Date(),
        ad_enabled: wifiRecord.ad_enabled !== undefined ? wifiRecord.ad_enabled : (wifiRecord.ad_required === 1),
        ad_type: wifiRecord.ad_type || 'video',
        ad_content: wifiRecord.ad_content || wifiRecord.ad_url || '',
        ad_duration: wifiRecord.ad_duration || 5,
        ad_title: wifiRecord.ad_title || '推荐内容',
        ad_link: wifiRecord.ad_link || '',
        ad_view_count: wifiRecord.ad_view_count || 0,
        ad_click_count: wifiRecord.ad_click_count || 0
      };
      
      return success(res, enhancedWifiRecord, '获取WiFi码详情成功');
    } catch (dbErr) {
      console.error('数据库查询出错:', dbErr);
      return error(res, '数据库查询失败: ' + dbErr.message, 500);
    }
  } catch (err) {
    console.error('获取WiFi详情失败(v1.js)，详细错误:', err);
    console.error('错误堆栈:', err.stack);
    return error(res, '获取WiFi详情失败: ' + err.message, 500);
  }
});

module.exports = router; 