// utils/util.js
// 通用工具函数

const config = require('../config/config.js')

/**
 * 格式化时间
 */
const formatTime = date => {
  const year = date.getFullYear()
  const month = date.getMonth() + 1
  const day = date.getDate()
  const hour = date.getHours()
  const minute = date.getMinutes()
  const second = date.getSeconds()

  return [year, month, day].map(formatNumber).join('/') + ' ' + [hour, minute, second].map(formatNumber).join(':')
}

/**
 * 格式化日期
 * @param {Date} date 日期对象
 * @param {String} format 格式化模式，如 'YYYY-MM-DD'
 */
const formatDate = (date, format = 'YYYY-MM-DD') => {
  const year = date.getFullYear()
  const month = date.getMonth() + 1
  const day = date.getDate()
  
  if (format === 'YYYY-MM-DD') {
    return [year, month, day].map(formatNumber).join('-')
  } else if (format === 'MM-DD') {
    return [month, day].map(formatNumber).join('-')
  } else if (format === 'YYYY/MM/DD') {
    return [year, month, day].map(formatNumber).join('/')
  }
  
  return [year, month, day].map(formatNumber).join('-')
}

/**
 * 格式化数字
 */
const formatNumber = n => {
  n = n.toString()
  return n[1] ? n : '0' + n
}

/**
 * 显示消息提示框
 * @param {String} title 提示内容
 * @param {String} icon 图标类型，可选值：success, error, loading, none
 * @param {Number} duration 提示延迟时间
 * @returns {Promise} Promise对象
 */
const showToast = (title, icon = 'none', duration = 1500) => {
  return new Promise((resolve) => {
    wx.showToast({
      title: title,
      icon: icon,
      duration: duration,
      success: resolve
    })
  })
}

/**
 * 显示模态对话框
 * @param {Object} options 配置项
 * @returns {Promise} Promise对象
 */
const showModal = (options) => {
  return new Promise((resolve) => {
    wx.showModal({
      title: options.title || '提示',
      content: options.content || '',
      showCancel: options.showCancel !== false,
      cancelText: options.cancelText || '取消',
      confirmText: options.confirmText || '确定',
      success: resolve
    })
  })
}

/**
 * 显示加载提示框
 * @param {String} title 提示内容
 * @param {Boolean} mask 是否显示透明蒙层，防止触摸穿透
 */
const showLoading = (title = '加载中...', mask = true) => {
  wx.showLoading({
    title: title,
    mask: mask
  })
}

/**
 * 隐藏加载提示框
 */
const hideLoading = () => {
  wx.hideLoading()
}

/**
 * 格式化图片URL
 * @param {string} url 图片URL
 * @returns {string} 格式化后的URL
 */
const formatImageUrl = (url) => {
  // 如果URL为空，返回默认图片
  if (!url) {
    console.log('图片URL为空，使用默认图片');
    return '/assets/images/goods-placeholder.svg';
  }
  
  // 如果已经是完整URL，直接返回
  if (url.startsWith('http://') || url.startsWith('https://')) {
    console.log('图片URL已是完整路径，无需处理:', url);
    return url;
  }
  
  // 如果是本地资源路径，直接返回
  if (url.startsWith('/assets/')) {
    console.log('本地资源路径，无需处理:', url);
    return url;
  }
  
  let formattedUrl = url;
  
  // 处理各种相对路径
  if (url.startsWith('/uploads/')) {
    // 已经有/uploads前缀，添加服务器地址
    formattedUrl = `http://localhost:4000${url}`;
    console.log('添加服务器地址到uploads路径:', formattedUrl);
  } else if (url.startsWith('/')) {
    // 其他以/开头的路径，添加服务器地址
    formattedUrl = `http://localhost:4000${url}`;
    console.log('添加服务器地址到路径:', formattedUrl);
  } else {
    // 没有前导斜杠，添加完整路径前缀
    formattedUrl = `http://localhost:4000/uploads/${url}`;
    console.log('添加完整路径前缀:', formattedUrl);
  }
  
  return formattedUrl;
}

/**
 * 价格格式化
 * @param {Number} price 价格
 * @param {Number} decimals 小数位数
 * @returns {String} 格式化后的价格
 */
const formatPrice = (price, decimals = 2) => {
  if (isNaN(price) || price === null) return '0.00'
  return parseFloat(price).toFixed(decimals)
}

/**
 * 节流函数
 * @param {Function} fn 要节流的函数
 * @param {Number} delay 延迟时间
 * @returns {Function} 节流后的函数
 */
const throttle = (fn, delay = 1000) => {
  let timer = null
  let lastTime = 0
  
  return function (...args) {
    const now = Date.now()
    
    if (now - lastTime >= delay) {
      fn.apply(this, args)
      lastTime = now
    } else {
      if (timer) clearTimeout(timer)
      timer = setTimeout(() => {
        fn.apply(this, args)
        lastTime = Date.now()
      }, delay - (now - lastTime))
    }
  }
}

/**
 * 防抖函数
 * @param {Function} fn 要防抖的函数
 * @param {Number} delay 延迟时间
 * @returns {Function} 防抖后的函数
 */
const debounce = (fn, delay = 300) => {
  let timer = null
  
  return function (...args) {
    if (timer) clearTimeout(timer)
    timer = setTimeout(() => {
      fn.apply(this, args)
    }, delay)
  }
}

/**
 * 深拷贝
 * @param {*} obj 要拷贝的对象
 * @returns {*} 拷贝后的对象
 */
const deepClone = (obj) => {
  if (obj === null || typeof obj !== 'object') return obj
  if (obj instanceof Date) return new Date(obj.getTime())
  if (obj instanceof Array) return obj.map(item => deepClone(item))
  if (typeof obj === 'object') {
    const copy = {}
    Object.keys(obj).forEach(key => {
      copy[key] = deepClone(obj[key])
    })
    return copy
  }
  return obj
}

/**
 * 生成唯一ID
 * @returns {String} 唯一ID
 */
const generateUniqueId = () => {
  return 'id_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)
}

/**
 * 验证手机号
 * @param {String} phone 手机号
 * @returns {Boolean} 是否有效
 */
const validatePhone = (phone) => {
  const reg = /^1[3-9]\d{9}$/
  return reg.test(phone)
}

/**
 * 验证邮箱
 * @param {String} email 邮箱
 * @returns {Boolean} 是否有效
 */
const validateEmail = (email) => {
  const reg = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/
  return reg.test(email)
}

/**
 * 获取图片信息
 * @param {String} src 图片路径
 * @returns {Promise} 图片信息
 */
const getImageInfo = (src) => {
  return new Promise((resolve, reject) => {
    wx.getImageInfo({
      src,
      success: resolve,
      fail: reject
    })
  })
}

/**
 * 保存图片到相册
 * @param {String} filePath 图片路径
 * @returns {Promise} 保存结果
 */
const saveImageToPhotosAlbum = (filePath) => {
  return new Promise((resolve, reject) => {
    // 检查授权
    wx.getSetting({
      success: (res) => {
        if (res.authSetting['scope.writePhotosAlbum']) {
          // 已授权，直接保存
          wx.saveImageToPhotosAlbum({
            filePath,
            success: resolve,
            fail: reject
          })
        } else {
          // 请求授权
          wx.authorize({
            scope: 'scope.writePhotosAlbum',
            success: () => {
              wx.saveImageToPhotosAlbum({
                filePath,
                success: resolve,
                fail: reject
              })
            },
            fail: () => {
              // 授权失败，引导用户手动开启
              wx.showModal({
                title: '提示',
                content: '需要您授权保存图片到相册',
                showCancel: false,
                confirmText: '去设置',
                success: () => {
                  wx.openSetting({
                    success: (settingResult) => {
                      if (settingResult.authSetting['scope.writePhotosAlbum']) {
                        wx.saveImageToPhotosAlbum({
                          filePath,
                          success: resolve,
                          fail: reject
                        })
                      } else {
                        reject(new Error('用户拒绝授权'))
                      }
                    }
                  })
                }
              })
            }
          })
        }
      }
    })
  })
}

/**
 * 生成分享海报
 * @param {Object} options 海报配置
 * @returns {Promise} 海报路径
 */
const generateSharePoster = (options) => {
  return new Promise((resolve, reject) => {
    const ctx = wx.createCanvasContext(options.canvasId, options.component)
    
    // 绘制背景
    if (options.background) {
      ctx.fillStyle = options.background
      ctx.fillRect(0, 0, options.width, options.height)
    }
    
    // 绘制内容
    if (options.content && options.content.length > 0) {
      options.content.forEach(item => {
        switch (item.type) {
          case 'text':
            ctx.fillStyle = item.color || '#000'
            ctx.font = `${item.fontSize || 14}px sans-serif`
            ctx.fillText(item.text, item.x, item.y)
            break
          case 'image':
            ctx.drawImage(item.src, item.x, item.y, item.width, item.height)
            break
        }
      })
    }
    
    ctx.draw(true, () => {
      setTimeout(() => {
        wx.canvasToTempFilePath({
          canvasId: options.canvasId,
          success: (res) => {
            resolve(res.tempFilePath)
          },
          fail: reject
        }, options.component)
      }, 1000)
    })
  })
}

/**
 * 预览图片
 * @param {String|Array} urls 图片地址
 * @param {Number} current 当前图片索引
 */
const previewImage = (urls, current = 0) => {
  const urlList = Array.isArray(urls) ? urls : [urls]
  const currentUrl = typeof current === 'number' ? urlList[current] : current
  
  wx.previewImage({
    urls: urlList,
    current: currentUrl
  })
}

/**
 * 复制到剪贴板
 * @param {String} data 要复制的内容
 * @param {String} successMsg 成功提示
 */
const copyToClipboard = (data, successMsg = '复制成功') => {
  wx.setClipboardData({
    data,
    success: () => {
      wx.showToast({
        title: successMsg,
        icon: 'success'
      })
    }
  })
}

/**
 * 获取当前页面路径
 */
const getCurrentPageUrl = () => {
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  return currentPage.route
}

/**
 * 获取当前页面带参数的路径
 */
const getCurrentPageUrlWithArgs = () => {
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  const url = currentPage.route
  const options = currentPage.options
  
  let urlWithArgs = `/${url}?`
  for (let key in options) {
    const value = options[key]
    urlWithArgs += `${key}=${value}&`
  }
  urlWithArgs = urlWithArgs.substring(0, urlWithArgs.length - 1)
  
  return urlWithArgs
}

module.exports = {
  formatTime,
  formatDate,
  formatNumber,
  formatImageUrl,
  formatPrice,
  throttle,
  debounce,
  deepClone,
  generateUniqueId,
  validatePhone,
  validateEmail,
  getImageInfo,
  saveImageToPhotosAlbum,
  generateSharePoster,
  previewImage,
  copyToClipboard,
  getCurrentPageUrl,
  getCurrentPageUrlWithArgs,
  showToast,
  showModal,
  showLoading,
  hideLoading
}