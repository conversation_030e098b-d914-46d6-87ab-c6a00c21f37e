#!/bin/bash

# WiFi共享商城后端服务器 - 宝塔面板启动脚本
# 作者：WiFi共享商城开发团队
# 日期：2025年1月31日

echo "🚀 WiFi共享商城后端服务器启动脚本"
echo "=================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目配置
PROJECT_NAME="wifi-share-server"
PROJECT_DIR="/www/wwwroot/$(basename $(pwd))"
PORT=4000

# 检查Node.js是否安装
check_nodejs() {
    echo -e "${BLUE}检查Node.js环境...${NC}"
    if ! command -v node &> /dev/null; then
        echo -e "${RED}❌ Node.js未安装，请先在宝塔面板安装Node.js${NC}"
        exit 1
    fi
    
    NODE_VERSION=$(node -v)
    echo -e "${GREEN}✅ Node.js版本: $NODE_VERSION${NC}"
}

# 检查MySQL是否运行
check_mysql() {
    echo -e "${BLUE}检查MySQL服务...${NC}"
    if ! systemctl is-active --quiet mysql; then
        echo -e "${YELLOW}⚠️  MySQL服务未运行，尝试启动...${NC}"
        systemctl start mysql
        if [ $? -eq 0 ]; then
            echo -e "${GREEN}✅ MySQL服务启动成功${NC}"
        else
            echo -e "${RED}❌ MySQL服务启动失败${NC}"
            exit 1
        fi
    else
        echo -e "${GREEN}✅ MySQL服务正在运行${NC}"
    fi
}

# 检查端口是否被占用
check_port() {
    echo -e "${BLUE}检查端口$PORT是否可用...${NC}"
    if lsof -Pi :$PORT -sTCP:LISTEN -t >/dev/null; then
        echo -e "${YELLOW}⚠️  端口$PORT已被占用，尝试释放...${NC}"
        PID=$(lsof -Pi :$PORT -sTCP:LISTEN -t)
        kill -9 $PID 2>/dev/null
        sleep 2
        if lsof -Pi :$PORT -sTCP:LISTEN -t >/dev/null; then
            echo -e "${RED}❌ 无法释放端口$PORT${NC}"
            exit 1
        fi
    fi
    echo -e "${GREEN}✅ 端口$PORT可用${NC}"
}

# 安装依赖
install_dependencies() {
    echo -e "${BLUE}安装项目依赖...${NC}"
    if [ ! -d "node_modules" ]; then
        echo -e "${YELLOW}⚠️  node_modules不存在，开始安装依赖...${NC}"
        npm install
        if [ $? -eq 0 ]; then
            echo -e "${GREEN}✅ 依赖安装成功${NC}"
        else
            echo -e "${RED}❌ 依赖安装失败${NC}"
            exit 1
        fi
    else
        echo -e "${GREEN}✅ 依赖已存在${NC}"
    fi
}

# 检查环境变量文件
check_env() {
    echo -e "${BLUE}检查环境变量配置...${NC}"
    if [ ! -f ".env" ]; then
        echo -e "${YELLOW}⚠️  .env文件不存在，创建默认配置...${NC}"
        cp .env.example .env 2>/dev/null || {
            echo -e "${RED}❌ 无法创建.env文件${NC}"
            exit 1
        }
    fi
    echo -e "${GREEN}✅ 环境变量配置就绪${NC}"
}

# 初始化数据库
init_database() {
    echo -e "${BLUE}初始化数据库...${NC}"
    if [ -f "init-database.js" ]; then
        node init-database.js
        if [ $? -eq 0 ]; then
            echo -e "${GREEN}✅ 数据库初始化成功${NC}"
        else
            echo -e "${YELLOW}⚠️  数据库初始化可能失败，但继续启动服务${NC}"
        fi
    else
        echo -e "${YELLOW}⚠️  未找到数据库初始化脚本${NC}"
    fi
}

# 启动服务器
start_server() {
    echo -e "${BLUE}启动服务器...${NC}"
    
    # 检查是否安装了PM2
    if command -v pm2 &> /dev/null; then
        echo -e "${GREEN}使用PM2启动服务...${NC}"
        
        # 停止可能存在的进程
        pm2 stop $PROJECT_NAME 2>/dev/null
        pm2 delete $PROJECT_NAME 2>/dev/null
        
        # 启动新进程
        pm2 start server.js --name $PROJECT_NAME
        
        if [ $? -eq 0 ]; then
            echo -e "${GREEN}✅ 服务启动成功${NC}"
            pm2 status
        else
            echo -e "${RED}❌ PM2启动失败，尝试直接启动${NC}"
            nohup node server.js > server.log 2>&1 &
            echo $! > server.pid
            echo -e "${GREEN}✅ 服务已在后台启动${NC}"
        fi
    else
        echo -e "${YELLOW}PM2未安装，使用nohup启动...${NC}"
        nohup node server.js > server.log 2>&1 &
        echo $! > server.pid
        echo -e "${GREEN}✅ 服务已在后台启动${NC}"
    fi
}

# 验证服务
verify_service() {
    echo -e "${BLUE}验证服务状态...${NC}"
    sleep 3
    
    # 检查端口是否监听
    if lsof -Pi :$PORT -sTCP:LISTEN -t >/dev/null; then
        echo -e "${GREEN}✅ 服务正在监听端口$PORT${NC}"
        
        # 测试API
        if command -v curl &> /dev/null; then
            echo -e "${BLUE}测试API接口...${NC}"
            HTTP_CODE=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:$PORT/api/v1/health 2>/dev/null)
            if [ "$HTTP_CODE" = "200" ]; then
                echo -e "${GREEN}✅ API接口响应正常${NC}"
            else
                echo -e "${YELLOW}⚠️  API接口可能未就绪（HTTP状态码: $HTTP_CODE）${NC}"
            fi
        fi
    else
        echo -e "${RED}❌ 服务未能正常启动${NC}"
        exit 1
    fi
}

# 显示访问信息
show_access_info() {
    echo ""
    echo -e "${GREEN}🎉 服务启动完成！${NC}"
    echo "=================================="
    echo -e "${BLUE}本地访问地址：${NC}http://localhost:$PORT"
    echo -e "${BLUE}外网访问地址：${NC}http://$(curl -s ifconfig.me 2>/dev/null || echo "您的服务器IP"):$PORT"
    echo -e "${BLUE}API文档地址：${NC}http://localhost:$PORT/api/v1/docs"
    echo ""
    echo -e "${YELLOW}管理命令：${NC}"
    if command -v pm2 &> /dev/null; then
        echo "  查看状态: pm2 status"
        echo "  查看日志: pm2 logs $PROJECT_NAME"
        echo "  重启服务: pm2 restart $PROJECT_NAME"
        echo "  停止服务: pm2 stop $PROJECT_NAME"
    else
        echo "  查看日志: tail -f server.log"
        echo "  停止服务: kill \$(cat server.pid)"
    fi
    echo ""
}

# 主函数
main() {
    echo -e "${BLUE}开始部署WiFi共享商城后端服务...${NC}"
    
    check_nodejs
    check_mysql
    check_port
    check_env
    install_dependencies
    init_database
    start_server
    verify_service
    show_access_info
    
    echo -e "${GREEN}🚀 部署完成！服务已成功启动${NC}"
}

# 错误处理
set -e
trap 'echo -e "${RED}❌ 脚本执行失败，请检查错误信息${NC}"' ERR

# 执行主函数
main
