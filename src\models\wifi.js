const db = require('../database');
const logger = require('../utils/logger');

/**
 * WiFi码模型
 */
class WifiModel {
  /**
   * 创建WiFi码
   * @param {object} wifiData WiFi码数据
   * @returns {Promise<object>} 创建的WiFi码信息
   */
  static async create(wifiData) {
    try {
      const { title, ssid, password, merchantName, userId, qrcodeUrl } = wifiData;
      
      const sql = `
        INSERT INTO wifi_codes (
          title, ssid, password, merchant_name, user_id, qrcode_url, scan_count,
          created_at, updated_at
        )
        VALUES (?, ?, ?, ?, ?, ?, 0, NOW(), NOW())
      `;
      
      const result = await db.query(sql, [
        title, ssid, password, merchantName, userId, qrcodeUrl
      ]);
      
      if (result.insertId) {
        return { id: result.insertId, ...wifiData, scan_count: 0 };
      } else {
        throw new Error('创建WiFi码失败');
      }
    } catch (error) {
      logger.error(`创建WiFi码失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 获取用户的WiFi码列表
   * @param {number} userId 用户ID
   * @returns {Promise<Array>} WiFi码列表
   */
  static async getListByUserId(userId) {
    try {
      const sql = `
        SELECT id, title, ssid, qrcode_url, scan_count, created_at
        FROM wifi_codes
        WHERE user_id = ?
        ORDER BY created_at DESC
      `;
      
      return await db.query(sql, [userId]);
    } catch (error) {
      logger.error(`获取用户WiFi码列表失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 根据ID获取WiFi码详情
   * @param {number} id WiFi码ID
   * @returns {Promise<object|null>} WiFi码信息
   */
  static async getById(id) {
    try {
      const sql = `
        SELECT id, title, ssid, password, merchant_name, user_id, qrcode_url, 
               scan_count, created_at, updated_at
        FROM wifi_codes
        WHERE id = ?
        LIMIT 1
      `;
      
      const result = await db.query(sql, [id]);
      return result.length > 0 ? result[0] : null;
    } catch (error) {
      logger.error(`根据ID获取WiFi码详情失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 更新WiFi码信息
   * @param {number} id WiFi码ID
   * @param {object} wifiData WiFi码更新数据
   * @returns {Promise<boolean>} 更新是否成功
   */
  static async update(id, wifiData) {
    try {
      const { title, ssid, password, merchantName } = wifiData;
      
      const sql = `
        UPDATE wifi_codes
        SET title = ?, ssid = ?, password = ?, merchant_name = ?, updated_at = NOW()
        WHERE id = ?
      `;
      
      const result = await db.query(sql, [title, ssid, password, merchantName, id]);
      return result.affectedRows > 0;
    } catch (error) {
      logger.error(`更新WiFi码信息失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 删除WiFi码
   * @param {number} id WiFi码ID
   * @param {number} userId 用户ID
   * @returns {Promise<boolean>} 删除是否成功
   */
  static async delete(id, userId) {
    try {
      const sql = 'DELETE FROM wifi_codes WHERE id = ? AND user_id = ?';
      const result = await db.query(sql, [id, userId]);
      return result.affectedRows > 0;
    } catch (error) {
      logger.error(`删除WiFi码失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 增加WiFi码扫描次数
   * @param {number} id WiFi码ID
   * @returns {Promise<boolean>} 操作是否成功
   */
  static async incrementScanCount(id) {
    try {
      const sql = 'UPDATE wifi_codes SET scan_count = scan_count + 1 WHERE id = ?';
      const result = await db.query(sql, [id]);
      return result.affectedRows > 0;
    } catch (error) {
      logger.error(`增加WiFi码扫描次数失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 获取WiFi码统计数据
   * @param {number} id WiFi码ID
   * @param {number} days 天数，默认7天
   * @returns {Promise<object>} 统计数据
   */
  static async getStats(id, days = 7) {
    try {
      // 获取WiFi码基本信息
      const wifiSql = `
        SELECT id, title, scan_count FROM wifi_codes WHERE id = ?
      `;
      
      // 获取每日扫描数据
      const statsSql = `
        SELECT DATE(scan_time) as date, COUNT(*) as count
        FROM wifi_scan_logs
        WHERE wifi_id = ? AND scan_time > DATE_SUB(CURDATE(), INTERVAL ? DAY)
        GROUP BY DATE(scan_time)
        ORDER BY date ASC
      `;
      
      const [wifiResult, statsResult] = await Promise.all([
        db.query(wifiSql, [id]),
        db.query(statsSql, [id, days])
      ]);
      
      if (wifiResult.length === 0) {
        return null;
      }
      
      return {
        id: wifiResult[0].id,
        title: wifiResult[0].title,
        scanCount: wifiResult[0].scan_count,
        dailyStats: statsResult
      };
    } catch (error) {
      logger.error(`获取WiFi码统计数据失败: ${error.message}`);
      throw error;
    }
  }
  
  /**
   * 记录WiFi码扫描日志
   * @param {number} wifiId WiFi码ID
   * @param {string} ip 扫描IP
   * @param {string} userAgent 用户代理
   * @returns {Promise<boolean>} 记录是否成功
   */
  static async logScan(wifiId, ip, userAgent) {
    try {
      const sql = `
        INSERT INTO wifi_scan_logs (wifi_id, ip, user_agent, scan_time)
        VALUES (?, ?, ?, NOW())
      `;
      
      const result = await db.query(sql, [wifiId, ip, userAgent]);
      return result.insertId > 0;
    } catch (error) {
      logger.error(`记录WiFi码扫描日志失败: ${error.message}`);
      throw error;
    }
  }
}

module.exports = WifiModel; 