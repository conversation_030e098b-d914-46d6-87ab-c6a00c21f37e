const axios = require('axios');

async function testAPIUpdate() {
  try {
    console.log('🧪 测试用户更新API...\n');
    
    // 1. 先获取用户信息，获取token
    console.log('1️⃣ 模拟用户登录获取token...');
    
    // 这里需要一个有效的token，我们先模拟一个
    // 实际情况下，你需要先调用登录接口获取真实的token
    const testToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6MSwib3BlbmlkIjoib1cyMEM3bVZsVzhlM1cyQWdVR3REVEplQWJRVSIsImlhdCI6MTc1MzgxMzE2NywiZXhwIjoxNzU0NDE3OTY3fQ.your-signature-here';
    
    // 2. 测试头像更新
    console.log('2️⃣ 测试头像更新API...');
    
    const updateData = {
      nickname: '润生',
      avatar: 'http://localhost:4000/uploads/avatars/test-avatar-' + Date.now() + '.jpg'
    };
    
    console.log('发送的数据:', JSON.stringify(updateData, null, 2));
    
    try {
      const response = await axios.post('http://localhost:4000/api/v1/client/user/update', updateData, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${testToken}`
        },
        timeout: 5000
      });
      
      console.log('✅ API调用成功:');
      console.log('状态码:', response.status);
      console.log('响应数据:', JSON.stringify(response.data, null, 2));
      
    } catch (apiError) {
      if (apiError.response) {
        console.log('❌ API调用失败:');
        console.log('状态码:', apiError.response.status);
        console.log('错误信息:', JSON.stringify(apiError.response.data, null, 2));
      } else if (apiError.code === 'ECONNREFUSED') {
        console.log('❌ 无法连接到服务器，请确保后端服务正在运行');
        console.log('服务器地址: http://localhost:4000');
      } else {
        console.log('❌ 请求失败:', apiError.message);
      }
    }
    
    // 3. 测试获取用户信息API
    console.log('\n3️⃣ 测试获取用户信息API...');
    
    try {
      const getUserResponse = await axios.get('http://localhost:4000/api/v1/client/user/info', {
        headers: {
          'Authorization': `Bearer ${testToken}`
        },
        timeout: 5000
      });
      
      console.log('✅ 获取用户信息成功:');
      console.log('用户数据:', JSON.stringify(getUserResponse.data, null, 2));
      
    } catch (getUserError) {
      if (getUserError.response) {
        console.log('❌ 获取用户信息失败:');
        console.log('状态码:', getUserError.response.status);
        console.log('错误信息:', JSON.stringify(getUserError.response.data, null, 2));
      } else {
        console.log('❌ 获取用户信息请求失败:', getUserError.message);
      }
    }
    
  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error.message);
  }
}

// 运行测试
testAPIUpdate();
