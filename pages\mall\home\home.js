// pages/mall/home/<USER>
// 商城首页页面逻辑

const request = require('../../../utils/request.js')
const goodsService = require('../../../services/goods.js')
const apiTest = require('../../../utils/api-test.js')
const NetworkDiagnostic = require('../../../utils/network-diagnostic.js')
const util = require('../../../utils/util.js')
const { processCategoryIcons, convertHttpIcon } = require('../../../utils/iconMapping.js')

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 微信广告流量数据
    trafficBannerData: {
      title: '微信广告流量',
      subtitle: '精准投放，高效转化',
      image: '/assets/images/wechat-ad-banner.jpg',
      link: '/pages/ads/index'
    },
    
    // 底部微信广告数据
    bottomAdData: {
      title: '微信推荐内容',
      subtitle: '发现更多精彩',
      image: '/assets/images/wechat-brand-ad.jpg',
      link: '/pages/ads/brand'
    },
    
    // 商品分类
    categories: [],
    
    // 商品列表
    goodsList: [],
    
    // 分页参数
    currentPage: 1,
    pageSize: 10,
    hasMore: true,
    loadingMore: false,
    
    // 搜索关键词
    searchKeyword: '',
    
    // 加载状态
    isLoading: false,
    
    // 刷新状态
    isRefreshing: false,
    
    // 搜索相关状态
    showSearchModal: false,
    searchFocus: false,
    searchHistory: [],
    apiAvailable: true, // 新增：用于表示API是否可用

    // 用户状态
    isLoggedIn: false,

    // 购物车数量已移除

    // 微信官方广告相关 - 真实广告位ID（需要在微信公众平台申请）
    topAdUnitId: 'adunit-2ca85b5075dd37d2',      // 顶部横幅广告位ID
    bottomAdUnitId: 'adunit-4f2c8a9b7e3d5f62',   // 底部横幅广告位ID
    interstitialAdUnitId: 'adunit-c4b8f9e2a1d6',// 插屏广告位ID
    rewardedAdUnitId: 'adunit-a7f3e8d9c2b5',     // 激励视频广告位ID
    showTopAd: false,                            // 控制顶部广告显示
    showBottomAd: false,                         // 控制底部广告显示
    adLoadRetryCount: 0,                         // 广告加载重试次数
    maxAdRetryCount: 3,                          // 最大重试次数
    adRevenue: 0,                                // 广告收益统计
    adClickCount: 0,                             // 广告点击统计
    adImpressionCount: 0                         // 广告展示统计
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    console.log('商城页面加载')
    this.initPageData()

    // 检查登录状态
    this.checkLoginStatus()

    // 直接加载真实数据
    this.loadGoodsList(true)

    // 加载商品分类
    this.loadCategories()

    // 初始化广告收益统计
    this.getAdRevenueStats()

    // 购物车功能已移除
  },

  /**
   * 已移除模拟数据加载方法
   */

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    console.log('商城页面显示')

    // 检查登录状态
    this.checkLoginStatus()

    // 购物车功能已移除

    // 尝试加载真实数据（如果失败会自动降级到模拟数据）
    this.loadGoodsList(true)
  },

  /**
   * 初始化页面数据
   */
  initPageData() {
    console.log('🔧 初始化页面数据...')
    
    // 初始化基础数据
    this.setData({
      goodsList: [],
      currentPage: 1,
      hasMore: true,
      isLoading: false,
      isRefreshing: false,
      loadingMore: false,
      searchKeyword: '',
      searchHistory: [],
      showSearchModal: false,
      searchFocus: false,
      apiAvailable: true, // 假设API可用
      
      // 微信广告数据
      trafficBannerData: {
        image: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNzUwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjVmNWY1Ii8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIyNCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPuW5v+WRiuWNoOS9jeespjwvdGV4dD48L3N2Zz4=',
        title: '微信广告流量',
        subtitle: '精准投放，高效转化',
        link: ''
      },
      bottomAdData: {
        image: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNzUwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjVmNWY1Ii8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIyNCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPuW5v+WRiuWNoOS9jeespjwvdGV4dD48L3N2Zz4=',
        title: '微信推荐内容',
        subtitle: '点击查看更多精彩内容',
        link: ''
      },
      
      // 初始化商品分类
      categories: []
    })
    
    console.log('✅ 页面数据初始化完成')
    
    // 加载搜索历史
    this.loadSearchHistory()
    
    // 先测试API连接
    this.testApiConnection()
    
    // 加载推荐商品
    this.loadGoodsList(true)
  },

  /**
   * 加载商品分类
   */
  async loadCategories() {
    console.log('🔍 开始加载商品分类...')
    
    try {
      // 先尝试从API获取商品分类
      let result
      
      try {
        result = await goodsService.getCategories()
        console.log('✅ 获取商品分类成功:', result)
      } catch (err) {
        console.error('❌ 获取商品分类失败:', err)
        result = null
      }
      
      // 如果API调用成功且返回有效数据
      if (result && result.success && result.data && Array.isArray(result.data)) {
        // 处理分类图标，将Element UI图标名称映射到本地路径
        const processedCategories = processCategoryIcons(result.data)
        console.log('🎨 处理分类图标:', processedCategories)

        this.setData({
          categories: processedCategories
        })
        console.log('📋 商品分类设置完成:', processedCategories.length)
      } else {
        // API调用失败，不使用模拟数据
        console.error('获取商品分类失败，请检查后端API')
        this.setData({
          categories: []
        })
      }
    } catch (error) {
      console.error('加载商品分类异常:', error)
      this.setData({
        categories: []
      })
    }
  },
  
  /**
   * 已移除模拟分类数据方法
   */

  /**
   * 分类点击事件
   */
  onCategoryTap(e) {
    const category = e.currentTarget.dataset.category
    console.log('点击分类:', category)
    
    if (category && category.id) {
      wx.navigateTo({
        url: `/pages/mall/category/category?id=${category.id}&name=${encodeURIComponent(category.name)}`,
        fail: (err) => {
          console.error('导航到分类页面失败:', err)
          wx.showToast({
            title: '分类页面开发中',
            icon: 'none'
          })
        }
      })
    }
  },

  /**
   * 导航到分类页面
   */
  navigateToCategory() {
    console.log('导航到分类页面')
    wx.navigateTo({
      url: '/pages/mall/category/category',
      fail: (err) => {
        console.error('导航到分类页面失败:', err)
        wx.showToast({
          title: '分类页面开发中',
          icon: 'none'
        })
      }
    })
  },

  /**
   * 测试API连接
   */
  async testApiConnection() {
    try {
      console.log('🔍 开始测试API连接...')
      
      // 运行快速连接测试
      const quickTestResult = await NetworkDiagnostic.quickConnectTest()
      
      if (quickTestResult) {
        console.log('✅ API连接测试成功')
        // 可以在这里设置一个全局状态表示API可用
        this.setData({ apiAvailable: true })
      } else {
        console.log('❌ API连接测试失败，将使用模拟数据')
        
        // 显示用户友好的提示
        wx.showModal({
          title: '连接提示',
          content: '无法连接到服务器，正在使用离线模式。请确保后端服务已启动。',
          showCancel: true,
          cancelText: '继续使用',
          confirmText: '查看帮助',
          success: (res) => {
            if (res.confirm) {
              // 运行完整诊断
              this.runFullDiagnostic()
            }
          }
        })
        
        this.setData({ apiAvailable: false })
      }
    } catch (error) {
      console.error('API连接测试异常:', error)
      this.setData({ apiAvailable: false })
    }
  },

  /**
   * 运行完整网络诊断
   */
  async runFullDiagnostic() {
    try {
      wx.showLoading({
        title: '正在诊断网络...',
        mask: true
      })
      
      const diagnosticResult = await NetworkDiagnostic.runFullDiagnostic()
      
      wx.hideLoading()
      
      // 显示诊断结果
      let content = '网络诊断完成：\n\n'
      
      if (diagnosticResult.recommendations.length > 0) {
        content += '建议：\n'
        diagnosticResult.recommendations.slice(0, 3).forEach((rec, index) => {
          content += `${index + 1}. ${rec.message}\n`
        })
      } else {
        content += '网络连接正常，但服务器可能未响应。'
      }
      
      wx.showModal({
        title: '网络诊断报告',
        content: content,
        showCancel: false,
        confirmText: '我知道了'
      })
      
    } catch (error) {
      wx.hideLoading()
      console.error('网络诊断异常:', error)
      wx.showToast({
        title: '诊断失败',
        icon: 'none'
      })
    }
  },

  /**
   * 加载搜索历史
   */
  loadSearchHistory() {
    try {
      const history = wx.getStorageSync('searchHistory') || []
      this.setData({
        searchHistory: history
      })
    } catch (error) {
      console.error('加载搜索历史失败:', error)
    }
  },

  /**
   * 加载商品列表
   */
  async loadGoodsList(refresh = false) {
    if (this.data.isLoading && !refresh) return
    
    try {
      this.setData({ 
        isLoading: true,
        isRefreshing: refresh
      })
      
      if (refresh) {
        this.setData({
          currentPage: 1,
          hasMore: true,
          loadingMore: false
        })
      }
      
      // 调用后台管理系统API
      const params = {
        page: refresh ? 1 : this.data.currentPage,
        limit: this.data.pageSize,
        keyword: this.data.searchKeyword,
        sort: 'recommend' // 推荐排序
      }
      
      let result
      try {
        // 调用后台管理系统商品列表API
        console.log('📡 正在调用API:', '/api/v1/client/goods/list', params)
        result = await request.get('/api/v1/client/goods/list', params)
        console.log('✅ API调用成功，返回数据:', result)
        
        if (!result || !result.data) {
          throw new Error('API返回数据为空')
          }
      } catch (apiError) {
        console.log('❌ API调用失败，错误详情:', apiError)
        console.log('错误消息:', apiError.message)
        
        // 显示网络错误提示
        wx.showToast({
          title: '网络连接失败，请稍后重试',
          icon: 'none',
          duration: 2000
        })
        
        // 设置加载状态为false
        this.setData({ 
          isLoading: false,
          isRefreshing: false 
        })
        
        return
      }
      
      if (result && result.data) {
        console.log('🔍 开始处理返回的数据...')
        
        // 处理不同的数据格式
        let goodsList = []
        let hasMore = false
        
        if (Array.isArray(result.data)) {
          // 如果data直接是数组
          goodsList = result.data
          hasMore = goodsList.length >= this.data.pageSize
          console.log('📋 数据格式：直接数组，商品数量:', goodsList.length)
        } else if (result.data.list && Array.isArray(result.data.list)) {
          // 如果data是对象，包含list字段
          goodsList = result.data.list
          hasMore = result.data.pagination ? 
            (result.data.pagination.page < result.data.pagination.pages) : 
            (goodsList.length >= this.data.pageSize)
          console.log('📋 数据格式：对象包含list，商品数量:', goodsList.length)
          console.log('📋 分页信息:', result.data.pagination)
        } else {
          // 其他格式，尝试使用整个data作为商品列表
          console.log('⚠️ 未知数据格式，尝试直接使用data')
          goodsList = result.data || []
          hasMore = false
        }
        
        // 确保商品列表是数组
        if (!Array.isArray(goodsList)) {
          console.log('❌ 商品数据不是数组格式，使用空数组')
          goodsList = []
        }
        
        // 处理商品图片URL
        goodsList = goodsList.map(item => {
          // 格式化图片URL
          if (item.cover) {
            item.cover = util.formatImageUrl(item.cover)
          }
          
          // 添加name字段兼容前端显示
          if (!item.name && item.title) {
            item.name = item.title
          }
          
          return item
        })
        
        console.log('🎯 最终处理的商品列表:', goodsList)
        console.log('🎯 商品数量:', goodsList.length)
        console.log('🎯 是否有更多:', hasMore)
        
        const newGoodsList = refresh ? goodsList : [...this.data.goodsList, ...goodsList]
        
        this.setData({
          goodsList: newGoodsList,
          currentPage: refresh ? 2 : this.data.currentPage + 1,
          hasMore: hasMore,
          isLoading: false,
          isRefreshing: false
        })
        
        console.log('✅ 商品列表更新完成，当前商品数量:', newGoodsList.length)
        
        // 如果刷新成功，显示提示
        if (refresh && !this.data.isRefreshing) {
          wx.showToast({
            title: newGoodsList.length > 0 ? '刷新成功' : '暂无商品数据',
            icon: newGoodsList.length > 0 ? 'success' : 'none',
            duration: 1500
          })
        }
      } else {
        console.log('❌ API返回数据为空')
        this.setData({
          isLoading: false,
          isRefreshing: false
        })
        
        if (refresh) {
          wx.showToast({
            title: '暂无商品数据',
            icon: 'none',
            duration: 1500
          })
        }
      }
    } catch (error) {
      console.error('加载商品列表失败:', error)
      
          wx.showToast({
            title: '加载失败，请重试',
            icon: 'none'
          })
      
      this.setData({ 
        isLoading: false,
        isRefreshing: false 
      })
    }
  },

  /**
   * 已移除模拟商品数据方法
   */

  /**
   * 搜索商品
   */
  async searchGoods(keyword) {
    try {
      this.setData({
        searchKeyword: keyword,
        isLoading: true
      })
      
      const params = {
        page: 1,
        limit: this.data.pageSize,
        keyword: keyword,
        sort: 'relevance'
      }
      
      let result
      try {
        // 调用后台管理系统搜索API
        result = await request.get('/api/v1/client/goods/list', params)
      } catch (apiError) {
        console.log('搜索API调用失败，使用模拟搜索:', apiError)
        result = await this.mockSearchGoods(keyword)
      }
      
      if (result && result.data) {
        this.setData({
          goodsList: result.data.list,
          currentPage: 2,
          hasMore: result.data.hasMore || false,
          isLoading: false
        })
        
        // 保存搜索历史
        this.saveSearchHistory(keyword)
      }
    } catch (error) {
      console.error('搜索商品失败:', error)
      wx.showToast({
        title: '搜索失败，请重试',
        icon: 'none'
      })
      this.setData({ isLoading: false })
    }
  },

  /**
   * 模拟搜索API
   */
  async mockSearchGoods(keyword) {
    return new Promise((resolve) => {
      setTimeout(() => {
        const allGoods = [
          {
            id: 1,
            name: '苹果iPhone 15 Pro',
            price: '7999.00',
            originalPrice: '8999.00',
            image: '/assets/images/goods1.jpg',
            sales: 1234,
            tags: ['热销', '新品']
          },
          {
            id: 2,
            name: '华为Mate 60 Pro',
            price: '6999.00',
            image: '/assets/images/goods2.jpg',
            sales: 856,
            tags: ['推荐']
          }
        ]
        
        // 简单的搜索过滤
        const filteredGoods = keyword ? 
          allGoods.filter(goods => goods.name.toLowerCase().includes(keyword.toLowerCase())) :
          allGoods
        
        resolve({
          success: true,
          data: {
            list: filteredGoods,
            hasMore: false,
            total: filteredGoods.length
          }
        })
      }, 300)
    })
  },

  /**
   * 保存搜索历史
   */
  saveSearchHistory(keyword) {
    if (!keyword || keyword.trim() === '') return
    
    try {
      let history = wx.getStorageSync('searchHistory') || []
      
      // 移除重复项
      history = history.filter(item => item !== keyword)
      
      // 添加到开头
      history.unshift(keyword)
      
      // 限制历史记录数量
      if (history.length > 10) {
        history = history.slice(0, 10)
      }
      
      wx.setStorageSync('searchHistory', history)
    } catch (error) {
      console.error('保存搜索历史失败:', error)
    }
  },

  /**
   * 广告流量横幅点击事件
   */
  onTrafficBannerTap() {
    const { trafficBannerData } = this.data
    console.log('点击广告流量横幅')
    
    // 记录广告点击
    this.recordAdClick('traffic_banner')
    
    if (trafficBannerData.link) {
      wx.navigateTo({
        url: trafficBannerData.link,
        fail: () => {
          wx.showToast({
            title: '页面暂未开放',
            icon: 'none'
          })
        }
      })
    } else {
      wx.showToast({
        title: '微信广告功能',
        icon: 'none'
      })
    }
  },

  /**
   * 搜索框点击事件
   */
  onSearchTap() {
    console.log('点击搜索框')
    this.setData({
      showSearchModal: true,
      searchFocus: true
    })
  },

  /**
   * 显示搜索输入框
   */
  showSearchInput() {
    this.setData({
      showSearchModal: true,
      searchFocus: true
    })
  },

  /**
   * 隐藏搜索模态框
   */
  hideSearchModal() {
    this.setData({
      showSearchModal: false,
      searchFocus: false
    })
  },

  /**
   * 阻止事件冒泡
   */
  stopPropagation() {
    // 阻止点击模态框内容时关闭模态框
  },

  /**
   * 搜索输入事件
   */
  onSearchInput(e) {
    this.setData({
      searchKeyword: e.detail.value
    })
  },

  /**
   * 搜索确认事件
   */
  onSearchConfirm() {
    const { searchKeyword } = this.data
    if (!searchKeyword || searchKeyword.trim() === '') {
      wx.showToast({
        title: '请输入搜索关键词',
        icon: 'none'
      })
      return
    }
    
    // 隐藏搜索模态框
    this.hideSearchModal()
    
    // 执行搜索
    this.searchGoods(searchKeyword.trim())
  },

  /**
   * 搜索历史项点击事件
   */
  onHistoryItemTap(e) {
    const keyword = e.currentTarget.dataset.keyword
    if (keyword) {
      this.setData({
        searchKeyword: keyword
      })
      this.onSearchConfirm()
    }
  },

  /**
   * 清空搜索历史
   */
  clearSearchHistory() {
    wx.showModal({
      title: '确认清空',
      content: '确定要清空所有搜索历史吗？',
      success: (res) => {
        if (res.confirm) {
          try {
            wx.removeStorageSync('searchHistory')
            this.setData({
              searchHistory: []
            })
            wx.showToast({
              title: '已清空搜索历史',
              icon: 'success'
            })
          } catch (error) {
            console.error('清空搜索历史失败:', error)
            wx.showToast({
              title: '清空失败',
              icon: 'none'
            })
          }
        }
      }
    })
  },

  /**
   * 商品项点击事件
   */
  onGoodsItemTap(e) {
    const goods = e.currentTarget.dataset.goods
    console.log('点击商品:', goods)
    
    if (goods && goods.id) {
      wx.navigateTo({
        url: `/pages/mall/goods/goods?id=${goods.id}`,
        fail: () => {
          wx.showToast({
            title: '商品详情页面开发中',
            icon: 'none'
          })
        }
      })
    }
  },

  /**
   * 停止事件冒泡
   */
  stopPropagation(e) {
    // 阻止事件冒泡
  },

  /**
   * 添加到购物车功能已移除
   */

  /**
   * 购物车功能已移除
   */

  /**
   * 加载更多
   */
  onLoadMore() {
    if (!this.data.hasMore || this.data.loadingMore) {
      return
    }
    
    this.setData({ loadingMore: true })
    this.loadGoodsList(false)
  },

  /**
   * 底部广告点击事件
   */
  onBottomAdTap() {
    const { bottomAdData } = this.data
    console.log('点击底部广告')
    
    // 记录广告点击
    this.recordAdClick('bottom_banner')
    
    if (bottomAdData.link) {
      wx.navigateTo({
        url: bottomAdData.link,
        fail: () => {
          wx.showToast({
            title: '页面暂未开放',
            icon: 'none'
          })
        }
      })
    } else {
      wx.showToast({
        title: '品牌推广功能',
        icon: 'none'
      })
    }
  },

  /**
   * 记录广告点击
   */
  recordAdClick(adType) {
    try {
      // 统计点击次数
      this.setData({
        adClickCount: this.data.adClickCount + 1
      })

      // 调用后台管理系统广告点击API
      request.post('/api/v1/client/ads/click', {
        ad_type: adType,
        page: 'mall_home',
        timestamp: Date.now(),
        user_id: wx.getStorageSync('userId') || null,
        session_id: wx.getStorageSync('sessionId') || null,
        click_count: this.data.adClickCount,
        impression_count: this.data.adImpressionCount
      }).then(res => {
        console.log('✅ 广告点击记录成功:', res)

        // 如果后台返回收益信息，更新本地统计
        if (res.data && res.data.revenue) {
          this.setData({
            adRevenue: this.data.adRevenue + res.data.revenue
          })
        }
      }).catch(err => {
        console.log('❌ 记录广告点击失败:', err)
      })
    } catch (error) {
      console.log('❌ 记录广告点击异常:', error)
    }
  },

  /**
   * 记录广告展示
   */
  recordAdImpression(adType) {
    try {
      // 调用后台管理系统广告展示API
      request.post('/api/v1/client/ads/impression', {
        ad_type: adType,
        page: 'mall_home',
        timestamp: Date.now(),
        user_id: wx.getStorageSync('userId') || null,
        session_id: wx.getStorageSync('sessionId') || null,
        device_info: {
          platform: wx.getSystemInfoSync().platform,
          version: wx.getSystemInfoSync().version,
          model: wx.getSystemInfoSync().model
        }
      }).then(res => {
        console.log('✅ 广告展示记录成功:', res)
      }).catch(err => {
        console.log('❌ 记录广告展示失败:', err)
      })
    } catch (error) {
      console.log('❌ 记录广告展示异常:', error)
    }
  },

  /**
   * 记录广告事件
   */
  recordAdEvent(adType, eventType, extraData = {}) {
    try {
      // 调用后台管理系统广告事件API
      request.post('/api/v1/client/ads/event', {
        adId: adType, // 使用后端期望的字段名
        eventType: eventType, // 使用后端期望的字段名
        adUnitId: extraData.adUnitId || null,
        errorCode: extraData.errorCode || null,
        errorMessage: extraData.errorMessage || (extraData.error ? JSON.stringify(extraData.error) : null),
        page: 'mall_home',
        timestamp: Date.now(),
        userId: wx.getStorageSync('userId') || null,
        sessionId: wx.getStorageSync('sessionId') || null,
        extraData: extraData
      }).then(res => {
        console.log('✅ 广告事件记录成功:', res)
      }).catch(err => {
        console.log('❌ 记录广告事件失败:', err)
      })
    } catch (error) {
      console.log('❌ 记录广告事件异常:', error)
    }
  },

  /**
   * 获取广告收益统计
   */
  getAdRevenueStats() {
    try {
      request.get('/api/v1/client/ads/revenue', {
        user_id: wx.getStorageSync('userId') || null,
        date_range: 'today'
      }).then(res => {
        if (res.data) {
          console.log('📊 今日广告收益:', res.data)
          this.setData({
            adRevenue: res.data.total_revenue || 0
          })
        }
      }).catch(err => {
        console.log('❌ 获取广告收益失败:', err)
      })
    } catch (error) {
      console.log('❌ 获取广告收益异常:', error)
    }
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh() {
    console.log('下拉刷新')
    this.loadGoodsList(true).finally(() => {
      wx.stopPullDownRefresh()
    })
  },

  /**
   * 上拉加载更多
   */
  onReachBottom() {
    console.log('上拉加载更多')
    this.onLoadMore()
  },

  /**
   * 分享功能
   */
  onShareAppMessage() {
    return {
      title: 'WiFi共享商城 - 精选好物',
      path: '/pages/mall/home/<USER>',
      imageUrl: '/assets/images/share-mall.jpg'
    }
  },

  /**
   * 分享到朋友圈
   */
  onShareTimeline() {
    return {
      title: 'WiFi共享商城 - 发现更多精彩',
      imageUrl: '/assets/images/share-mall.jpg'
    }
  },

  /**
   * 检查登录状态
   */
  checkLoginStatus() {
    const app = getApp()
    const isLoggedIn = app.globalData.isLogin || false
    this.setData({
      isLoggedIn: isLoggedIn
    })
  },

  /**
   * 购物车相关功能已移除
   */

  // ==================== 微信广告事件处理 ====================

  /**
   * 顶部微信广告加载成功
   */
  onTopAdLoad: function () {
    console.log('✅ 顶部微信广告加载成功')
    this.setData({
      showTopAd: true,
      adLoadRetryCount: 0
    })

    // 记录广告展示
    this.recordAdImpression('top_banner')

    // 统计广告展示次数
    this.setData({
      adImpressionCount: this.data.adImpressionCount + 1
    })
  },

  /**
   * 顶部广告加载失败
   */
  onTopAdError: function (err) {
    console.error('❌ 顶部广告加载失败', err)

    // 重试机制
    if (this.data.adLoadRetryCount < this.data.maxAdRetryCount) {
      console.log(`🔄 广告加载重试 ${this.data.adLoadRetryCount + 1}/${this.data.maxAdRetryCount}`)
      this.setData({
        adLoadRetryCount: this.data.adLoadRetryCount + 1
      })

      // 延迟重试
      setTimeout(() => {
        this.retryLoadTopAd()
      }, 2000 * (this.data.adLoadRetryCount + 1))
    } else {
      console.log('📱 显示顶部降级广告')
      this.setData({
        showTopAd: false
      })
    }
  },

  /**
   * 顶部广告关闭
   */
  onTopAdClose: function () {
    console.log('🚫 顶部广告被用户关闭')
    this.setData({
      showTopAd: false
    })

    // 记录广告关闭事件
    this.recordAdEvent('top_banner', 'close')
  },

  /**
   * 底部广告加载成功
   */
  onBottomAdLoad: function () {
    console.log('✅ 底部广告加载成功')
    this.setData({
      showBottomAd: true
    })

    // 记录广告展示
    this.recordAdImpression('bottom_banner')

    // 统计广告展示次数
    this.setData({
      adImpressionCount: this.data.adImpressionCount + 1
    })
  },

  /**
   * 底部广告加载失败
   */
  onBottomAdError: function (err) {
    console.error('❌ 底部广告加载失败', err)
    this.setData({
      showBottomAd: false
    })

    // 记录广告加载失败
    this.recordAdEvent('bottom_banner', 'load_error', { error: err })
  },

  /**
   * 底部广告关闭
   */
  onBottomAdClose: function () {
    console.log('🚫 底部广告被用户关闭')
    this.setData({
      showBottomAd: false
    })

    // 记录广告关闭事件
    this.recordAdEvent('bottom_banner', 'close')
  },

  /**
   * 重试加载顶部广告
   */
  retryLoadTopAd: function () {
    console.log('🔄 重试加载顶部广告')
    // 触发广告重新加载（通过重新设置广告位ID）
    const currentId = this.data.topAdUnitId
    this.setData({
      topAdUnitId: ''
    })

    setTimeout(() => {
      this.setData({
        topAdUnitId: currentId
      })
    }, 100)
  },

  /**
   * 中间广告加载成功
   */
  onMiddleAdLoad: function () {
    console.log('✅ 中间广告加载成功')
    this.recordAdImpression('middle_banner')
    this.setData({
      adImpressionCount: this.data.adImpressionCount + 1
    })
  },

  /**
   * 中间广告加载失败
   */
  onMiddleAdError: function (err) {
    console.error('❌ 中间广告加载失败', err)
    this.recordAdEvent('middle_banner', 'load_error', { error: err })
  },

  /**
   * 中间广告关闭
   */
  onMiddleAdClose: function () {
    console.log('🚫 中间广告被用户关闭')
    this.recordAdEvent('middle_banner', 'close')
  },

  /**
   * 顶部广告触摸开始
   */
  onTopAdTouchStart: function () {
    console.log('👆 用户开始触摸顶部广告')
    this.recordAdEvent('top_banner', 'touch_start')
  },

  /**
   * 顶部广告触摸结束
   */
  onTopAdTouchEnd: function () {
    console.log('👆 用户结束触摸顶部广告')
    this.recordAdEvent('top_banner', 'touch_end')
  },

  /**
   * 图片加载失败处理
   */
  onImageError: function (e) {
    const index = e.currentTarget.dataset.index
    console.error('商城首页图片加载失败，索引:', index, '错误:', e.detail)

    // 使用默认占位图替换失败的图片
    const goodsList = [...this.data.goodsList]  // 创建副本避免直接修改
    if (goodsList && goodsList[index]) {
      const originalUrl = goodsList[index].cover
      console.log(`图片加载失败: ${originalUrl}，使用占位图替换`)

      // 避免无限循环，如果已经是占位图了就不再替换
      if (goodsList[index].imageError) {
        console.log('占位图也加载失败，使用base64占位图')
        // 使用base64编码的简单占位图
        goodsList[index].cover = this.getBase64PlaceholderImage()
        goodsList[index].useFallback = true
      } else {
        // 首次失败，尝试使用SVG占位图
        goodsList[index].cover = '/assets/images/goods-placeholder.svg'
        goodsList[index].imageError = true  // 标记图片加载失败
      }

      this.setData({
        goodsList: goodsList
      })

      // 显示用户友好的提示（可选）
      if (originalUrl && originalUrl.includes('/uploads/')) {
        console.warn('服务器图片资源不可用，建议检查服务器配置')
      }
    }
  },

  /**
   * 获取base64占位图
   */
  getBase64PlaceholderImage: function() {
    // 返回一个简单的灰色占位图的base64编码
    return 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjVmNWY1Ii8+CiAgPHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPuWVhuWTgeWbvueJhzwvdGV4dD4KPC9zdmc+'
  }
}) 