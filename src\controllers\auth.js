const { generateToken } = require('../../config/jwt');
const UserModel = require('../models/user');
const logger = require('../utils/logger');
const { success, error, unauthorized } = require('../utils/response');
const crypto = require('../utils/crypto');
const axios = require('axios');
const config = require('../../config');

/**
 * 用户登录（微信授权登录）
 */
const login = async (req, res) => {
  try {
    const { code, isDemote } = req.body;
    
    if (!code) {
      return error(res, '微信授权code不能为空', 400);
    }
    
    // 从配置中获取微信小程序AppID和AppSecret
    const appId = config.wechat.appId;
    const appSecret = config.wechat.appSecret;
    
    if (!appId || !appSecret) {
      logger.error('微信API调用失败: appId或appSecret未配置');
      return error(res, '系统配置错误，请联系管理员', 500);
    }
    
    logger.info(`微信登录请求，使用AppID: ${appId}`);
    logger.info(`请求体内容: ${JSON.stringify(req.body)}`);
    
    // 请求微信接口获取openid和session_key
    let openid, sessionKey;
    try {
      logger.info(`开始请求微信登录API，code: ${code}`);
      logger.info(`请求微信API: https://api.weixin.qq.com/sns/jscode2session，参数: ${JSON.stringify({
        appid: appId,
        secret: appSecret,
        js_code: code,
        grant_type: 'authorization_code'
      })}`);
      
      const wxResponse = await axios.get('https://api.weixin.qq.com/sns/jscode2session', {
        params: {
          appid: appId,
          secret: appSecret,
          js_code: code,
          grant_type: 'authorization_code'
        }
      });
      
      logger.info(`微信API返回结果: ${JSON.stringify(wxResponse.data)}`);
      
      if (wxResponse.data.errcode) {
        logger.error(`微信API调用失败: ${wxResponse.data.errmsg}, errcode: ${wxResponse.data.errcode}`);
        return error(res, `微信授权失败: ${wxResponse.data.errmsg}`, 400);
      }
      
      openid = wxResponse.data.openid;
      sessionKey = wxResponse.data.session_key;
      
      if (!openid) {
        logger.error('微信API返回数据异常，未获取到openid');
        return error(res, '微信授权失败，请稍后再试', 500);
      }
    } catch (wxErr) {
      logger.error(`请求微信接口失败: ${wxErr.message}`);
      return error(res, '微信授权失败，请稍后再试', 500);
    }
    
    logger.info('检查并确保用户表存在');
    
    // 根据openid查询用户
    let user = await UserModel.findByOpenid(openid);
    
    // 获取前端传来的用户信息
    const { userInfo } = req.body;
    logger.info(`前端传来的用户信息: ${JSON.stringify(userInfo || {})}`);
    
    // 检查是否为降级登录（用户拒绝授权）
    const isUserDemote = isDemote === true;
    logger.info(`是否为降级登录: ${isUserDemote}`);
    
    // 用户不存在，创建新用户
    if (!user) {
      let nickname, avatar, gender, country, province, city;
      
      if (userInfo && !isUserDemote) {
        // 优先使用前端传来的用户信息
        nickname = userInfo.nickName || '微信用户';
        avatar = userInfo.avatarUrl || '';
        gender = userInfo.gender !== undefined ? userInfo.gender : 0;
        country = userInfo.country || '';
        province = userInfo.province || '';
        city = userInfo.city || '';
      } else {
        // 没有用户信息或降级登录时使用默认值
        nickname = '微信用户';
        avatar = '';
        gender = 0;
        country = '';
        province = '';
        city = '';
      }
      
      logger.info(`创建新用户: openid=${openid}, nickname=${nickname}, avatar=${avatar}, is_demote=${isUserDemote}`);
      user = await UserModel.create({
        openid,
        nickname,
        avatar,
        gender,
        country,
        province,
        city,
        is_demote: isUserDemote ? 1 : 0 // 添加降级标记，转换为整数
      });
      
      logger.info(`新用户创建结果: ${JSON.stringify(user)}`);
    } 
    // 用户存在但需要更新信息
    else if (userInfo && !isUserDemote) {
      logger.info(`用户已存在，ID: ${user.id}, openid: ${user.openid}`);
      logger.info(`前端传来的用户信息详情: ${JSON.stringify(userInfo)}`);
      
      // 检查是否需要更新用户信息
      const needUpdate = (
        (userInfo.nickName && userInfo.nickName !== user.nickname && userInfo.nickName !== '微信用户') ||
        (userInfo.avatarUrl && userInfo.avatarUrl !== user.avatar) ||
        (userInfo.gender !== undefined && userInfo.gender !== user.gender) ||
        (userInfo.country && userInfo.country !== user.country) ||
        (userInfo.province && userInfo.province !== user.province) ||
        (userInfo.city && userInfo.city !== user.city) ||
        (user.is_demote === 1) // 如果之前是降级用户，现在有了用户信息，需要更新
      );
      
      logger.info(`是否需要更新用户信息: ${needUpdate}, 原因: 
        昵称不同: ${userInfo.nickName && userInfo.nickName !== user.nickname && userInfo.nickName !== '微信用户'}
        头像不同: ${userInfo.avatarUrl && userInfo.avatarUrl !== user.avatar}
        性别不同: ${userInfo.gender !== undefined && userInfo.gender !== user.gender}
        国家不同: ${userInfo.country && userInfo.country !== user.country}
        省份不同: ${userInfo.province && userInfo.province !== user.province}
        城市不同: ${userInfo.city && userInfo.city !== user.city}
        之前是降级用户: ${user.is_demote === 1}
      `);
      
      if (needUpdate) {
        const updateData = {};
        if (userInfo.nickName && userInfo.nickName !== '微信用户') {
          updateData.nickname = userInfo.nickName;
          logger.info(`更新用户昵称: ${userInfo.nickName}`);
        }
        if (userInfo.avatarUrl) {
          updateData.avatar = userInfo.avatarUrl;
          logger.info(`更新用户头像: ${userInfo.avatarUrl}`);
        }
        if (userInfo.gender !== undefined) {
          updateData.gender = userInfo.gender;
          logger.info(`更新用户性别: ${userInfo.gender}`);
        }
        if (userInfo.country) updateData.country = userInfo.country;
        if (userInfo.province) updateData.province = userInfo.province;
        if (userInfo.city) updateData.city = userInfo.city;
        
        // 如果之前是降级用户，现在有了用户信息，取消降级标记
        if (user.is_demote === 1) {
          updateData.is_demote = 0;
          logger.info('用户之前是降级用户，现在取消降级标记');
        }
        
        logger.info(`更新用户信息: ID=${user.id}, 更新数据=${JSON.stringify(updateData)}`);
        const updateResult = await UserModel.update(user.id, updateData);
        logger.info(`更新结果: ${updateResult ? '成功' : '失败'}`);
        
        // 重新获取更新后的用户信息
        user = await UserModel.findById(user.id);
        logger.info(`更新后的用户信息: ${JSON.stringify(user)}`);
      } else {
        logger.info('用户信息无需更新');
      }
    } 
    // 用户存在，但是降级登录
    else if (isUserDemote && user.is_demote === 0) {
      logger.info(`用户${user.id}降级登录，标记为降级用户`);
      await UserModel.update(user.id, { is_demote: 1 });
      user.is_demote = 1;
    }
    
    // 处理头像URL
    let userAvatar = user.avatar;

    // 检查是否是失效的微信头像URL
    if (userAvatar && (userAvatar.includes('thirdwx.qlogo.cn') ||
                       userAvatar.includes('wx.qlogo.cn') ||
                       userAvatar.includes('mmopen'))) {
      logger.info(`检测到失效的微信头像URL，清除: ${userAvatar}`);
      userAvatar = '';
      // 清除数据库中的失效头像
      await UserModel.update(user.id, { avatar: '' });
    }

    if (!userAvatar && userInfo && userInfo.avatarUrl && !isUserDemote) {
      // 检查前端传来的头像是否也是微信的失效URL
      if (!userInfo.avatarUrl.includes('thirdwx.qlogo.cn') &&
          !userInfo.avatarUrl.includes('wx.qlogo.cn') &&
          !userInfo.avatarUrl.includes('mmopen')) {
        // 如果数据库中没有头像但前端传来了有效头像，使用前端的
        userAvatar = userInfo.avatarUrl;
        logger.info(`使用前端提供的有效头像: ${userAvatar}`);

        // 保存头像到数据库
        await UserModel.update(user.id, { avatar: userAvatar });
      } else {
        logger.info('前端传来的也是失效的微信头像URL，不使用');
      }
    }

    if (!userAvatar) {
      // 如果没有有效头像，返回空字符串，前端会使用默认头像
      userAvatar = '';
      logger.info('没有有效头像，返回空字符串');
    } else if (!userAvatar.startsWith('http') && !userAvatar.startsWith('/assets/')) {
      // 如果不是完整URL，添加域名
      const baseUrl = config.server.baseUrl || `http://${req.headers.host}`;
      userAvatar = userAvatar.startsWith('/')
        ? `${baseUrl}${userAvatar}`
        : `${baseUrl}/${userAvatar}`;
      logger.info(`处理后的头像URL: ${userAvatar}`);
    }
    
    logger.info(`用户头像URL: ${userAvatar}`);
    
    // 检查用户资料是否完整
    const hasNickname = !!(user.nickname && user.nickname !== '微信用户');
    const hasAvatar = !!userAvatar;
    const isProfileCompleted = hasNickname && hasAvatar;
    logger.info(`用户资料完整性检查: 昵称=${hasNickname}, 头像=${hasAvatar}, 完整=${isProfileCompleted}`);
    
    // 生成JWT令牌
    logger.info(`生成JWT令牌，payload: ${JSON.stringify({
      id: user.id,
      openid: user.openid,
      role: user.role || 'user',
      session_key: sessionKey
    })}`);
    logger.info(`JWT密钥: ${config.jwt.secret}`);
    logger.info(`JWT过期时间: ${config.jwt.expiresIn}`);
    
    const token = generateToken({
      id: user.id,
      openid: user.openid,
      role: user.role || 'user',
      session_key: sessionKey
    });
    
    logger.info(`用户 ${user.nickname}(ID: ${user.id}) 登录成功`);
    
    const responseData = {
      token,
      user: {
        id: user.id,
        nickname: user.nickname,
        avatar: userAvatar,
        gender: user.gender,
        phone: user.phone,
        isLeader: Boolean(user.is_leader),
        is_demote: Boolean(user.is_demote), // 添加降级标记到返回数据
        isProfileCompleted // 添加资料完整性标记
      }
    };
    
    logger.info(`返回给前端的数据: ${JSON.stringify(responseData)}`);
    
    return success(res, responseData, '登录成功');
  } catch (err) {
    logger.error(`登录失败: ${err.message}`);
    logger.error(`错误堆栈: ${err.stack}`);
    return error(res, '登录失败', 500);
  }
};

/**
 * 用户退出登录
 */
const logout = (req, res) => {
  // 由于JWT是无状态的，服务端无法真正"注销"令牌
  // 实际应用中可以使用令牌黑名单等机制
  // 这里简单返回成功
  logger.info(`用户ID: ${req.user?.id || '未知'} 退出登录成功`);
  return success(res, null, '退出登录成功');
};

/**
 * 刷新令牌
 */
const refreshToken = (req, res) => {
  try {
    // 获取用户信息（从认证中间件中）
    const user = req.user;
    
    // 生成新的JWT令牌
    const token = generateToken({
      id: user.id,
      openid: user.openid,
      role: user.role || 'user'
    });
    
    logger.info(`用户 ID: ${user.id} 刷新令牌成功`);
    
    return success(res, { token }, '刷新令牌成功');
  } catch (err) {
    logger.error(`刷新令牌失败: ${err.message}`);
    return error(res, '刷新令牌失败', 500);
  }
};

module.exports = {
  login,
  logout,
  refreshToken
}; 