const mysql = require('mysql2/promise');

async function checkRegionsTable() {
  let connection;
  
  try {
    // 创建数据库连接
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: 'wo587129955',
      database: 'mall'
    });

    console.log('✅ 数据库连接成功');

    // 检查regions表是否存在
    const [tables] = await connection.execute(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'mall' AND table_name = 'regions'
    `);
    
    if (tables.length === 0) {
      console.log('❌ regions表不存在，需要创建');
      
      // 创建regions表
      await connection.execute(`
        CREATE TABLE regions (
          id INT PRIMARY KEY AUTO_INCREMENT,
          name VARCHAR(100) NOT NULL COMMENT '地区名称',
          code VARCHAR(20) UNIQUE COMMENT '地区代码',
          parent_id INT DEFAULT NULL COMMENT '父级地区ID',
          level TINYINT DEFAULT 1 COMMENT '层级：1-省，2-市，3-区县',
          status TINYINT DEFAULT 1 COMMENT '状态：0-禁用，1-正常',
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          INDEX idx_parent_id (parent_id),
          INDEX idx_level (level),
          INDEX idx_status (status)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='地区表'
      `);
      
      console.log('✅ regions表创建成功');
      
      // 插入一些示例数据
      console.log('🔄 插入示例地区数据...');
      
      // 省级数据
      await connection.execute(`
        INSERT INTO regions (name, code, parent_id, level, status) VALUES
        ('北京市', '110000', NULL, 1, 1),
        ('上海市', '310000', NULL, 1, 1),
        ('广东省', '440000', NULL, 1, 1),
        ('江苏省', '320000', NULL, 1, 1),
        ('浙江省', '330000', NULL, 1, 1)
      `);
      
      // 市级数据
      await connection.execute(`
        INSERT INTO regions (name, code, parent_id, level, status) VALUES
        ('北京市', '110100', 1, 2, 1),
        ('上海市', '310100', 2, 2, 1),
        ('广州市', '440100', 3, 2, 1),
        ('深圳市', '440300', 3, 2, 1),
        ('南京市', '320100', 4, 2, 1),
        ('苏州市', '320500', 4, 2, 1),
        ('杭州市', '330100', 5, 2, 1),
        ('宁波市', '330200', 5, 2, 1)
      `);
      
      // 区县级数据
      await connection.execute(`
        INSERT INTO regions (name, code, parent_id, level, status) VALUES
        ('朝阳区', '110105', 6, 3, 1),
        ('海淀区', '110108', 6, 3, 1),
        ('黄浦区', '310101', 7, 3, 1),
        ('浦东新区', '310115', 7, 3, 1),
        ('天河区', '440106', 8, 3, 1),
        ('越秀区', '440104', 8, 3, 1),
        ('南山区', '440305', 9, 3, 1),
        ('福田区', '440304', 9, 3, 1)
      `);
      
      console.log('✅ 示例数据插入成功');
    } else {
      console.log('✅ regions表已存在');
    }
    
    // 查看表结构
    console.log('\n📋 regions表结构:');
    const [structure] = await connection.execute('DESCRIBE regions');
    console.table(structure);
    
    // 查看数据
    console.log('\n📊 regions表数据:');
    const [regions] = await connection.execute(`
      SELECT id, name, code, parent_id, level, status 
      FROM regions 
      ORDER BY level ASC, id ASC 
      LIMIT 20
    `);
    
    if (regions.length > 0) {
      console.table(regions.map(region => ({
        ID: region.id,
        名称: region.name,
        代码: region.code,
        父级ID: region.parent_id,
        层级: region.level === 1 ? '省' : region.level === 2 ? '市' : '区县',
        状态: region.status === 1 ? '正常' : '禁用'
      })));
    } else {
      console.log('❌ 没有找到地区数据');
    }
    
    console.log('\n✅ regions表检查完成！');
    
  } catch (error) {
    console.error('❌ 操作失败:', error);
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 数据库连接已关闭');
    }
  }
}

checkRegionsTable();
