// 测试钱包系统集成
const axios = require('axios');
const jwt = require('jsonwebtoken');

async function testWalletIntegration() {
  try {
    console.log('🧪 测试钱包系统集成...\n');
    
    // 1. 生成测试token
    const testUser = {
      id: 1,
      openid: 'oW20C7mVlW8e3W2AgUGtDTJeAbQU'
    };
    
    const token = jwt.sign(
      testUser,
      'wifi_share_secret_key',
      { expiresIn: '24h' }
    );
    
    console.log('1️⃣ 生成的测试token:', token.substring(0, 50) + '...\n');
    
    // 2. 测试收入统计接口（钱包数据）
    console.log('2️⃣ 测试收入统计接口（钱包数据）...');
    try {
      const statsResponse = await axios.get('http://localhost:4000/api/v1/client/income/stats', {
        headers: {
          'Authorization': `Bearer ${token}`
        },
        timeout: 10000
      });
      
      console.log('✅ 收入统计接口成功:');
      console.log('状态码:', statsResponse.status);
      console.log('响应数据:', JSON.stringify(statsResponse.data, null, 2));
      
      // 验证钱包需要的字段
      const data = statsResponse.data.data;
      console.log('\n📊 钱包字段验证:');
      console.log('- 余额 (balance):', data.balance || '缺失');
      console.log('- WiFi收入 (wifi_income):', data.wifi_income || '缺失');
      console.log('- 团队收入 (team_income):', data.team_income || '缺失');
      console.log('- 广告收入 (ad_income):', data.ad_income || '缺失');
      console.log('- 商品收入 (goods_income):', data.goods_income || '缺失');
      
    } catch (statsError) {
      console.log('❌ 收入统计接口失败:');
      if (statsError.response) {
        console.log('状态码:', statsError.response.status);
        console.log('错误信息:', JSON.stringify(statsError.response.data, null, 2));
      } else {
        console.log('请求失败:', statsError.message);
      }
    }
    
    // 3. 测试收入明细接口
    console.log('\n3️⃣ 测试收入明细接口...');
    try {
      const detailsResponse = await axios.get('http://localhost:4000/api/v1/client/income/details', {
        headers: {
          'Authorization': `Bearer ${token}`
        },
        params: {
          page: 1,
          limit: 5
        },
        timeout: 10000
      });
      
      console.log('✅ 收入明细接口成功:');
      console.log('状态码:', detailsResponse.status);
      console.log('响应数据:', JSON.stringify(detailsResponse.data, null, 2));
      
      // 验证明细字段
      const details = detailsResponse.data.data;
      if (details && details.list && details.list.length > 0) {
        console.log('\n📋 明细字段验证:');
        const firstItem = details.list[0];
        console.log('- ID:', firstItem.id || '缺失');
        console.log('- 金额 (amount):', firstItem.amount || '缺失');
        console.log('- 类型 (type):', firstItem.type || '缺失');
        console.log('- 标题 (title):', firstItem.title || '缺失');
        console.log('- 描述 (description):', firstItem.description || '缺失');
        console.log('- 创建时间 (created_at):', firstItem.created_at || '缺失');
      }
      
    } catch (detailsError) {
      console.log('❌ 收入明细接口失败:');
      if (detailsError.response) {
        console.log('状态码:', detailsError.response.status);
        console.log('错误信息:', JSON.stringify(detailsError.response.data, null, 2));
      } else {
        console.log('请求失败:', detailsError.message);
      }
    }
    
    // 4. 测试提现方式接口
    console.log('\n4️⃣ 测试提现方式接口...');
    try {
      const methodsResponse = await axios.get('http://localhost:4000/api/v1/client/withdraw/methods', {
        headers: {
          'Authorization': `Bearer ${token}`
        },
        timeout: 10000
      });
      
      console.log('✅ 提现方式接口成功:');
      console.log('状态码:', methodsResponse.status);
      console.log('响应数据:', JSON.stringify(methodsResponse.data, null, 2));
      
    } catch (methodsError) {
      console.log('❌ 提现方式接口失败:');
      if (methodsError.response) {
        console.log('状态码:', methodsError.response.status);
        console.log('错误信息:', JSON.stringify(methodsError.response.data, null, 2));
      } else {
        console.log('请求失败:', methodsError.message);
      }
    }
    
    // 5. 测试用户提现账户接口
    console.log('\n5️⃣ 测试用户提现账户接口...');
    try {
      const accountsResponse = await axios.get('http://localhost:4000/api/v1/client/withdraw/accounts', {
        headers: {
          'Authorization': `Bearer ${token}`
        },
        timeout: 10000
      });
      
      console.log('✅ 用户提现账户接口成功:');
      console.log('状态码:', accountsResponse.status);
      console.log('响应数据:', JSON.stringify(accountsResponse.data, null, 2));
      
    } catch (accountsError) {
      console.log('❌ 用户提现账户接口失败:');
      if (accountsError.response) {
        console.log('状态码:', accountsError.response.status);
        console.log('错误信息:', JSON.stringify(accountsError.response.data, null, 2));
      } else {
        console.log('请求失败:', accountsError.message);
      }
    }
    
    console.log('\n🎯 测试总结:');
    console.log('- 钱包数据接口: 需要确保返回正确的字段结构');
    console.log('- 收入明细接口: 需要确保返回列表格式');
    console.log('- 提现相关接口: 需要确保后端API已实现');
    console.log('- 前端页面路由: 已在app.json中注册');
    console.log('- 钱包页面按钮: 已修复路径问题');
    
  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error.message);
  }
}

// 运行测试
testWalletIntegration();
