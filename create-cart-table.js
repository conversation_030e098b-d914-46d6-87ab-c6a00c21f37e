// 创建购物车表的脚本
const db = require('./src/database');

async function createCartTable() {
  try {
    // 初始化数据库连接
    await db.init();
    
    console.log('开始创建购物车表...');
    
    // 创建购物车表
    await db.query(`
      CREATE TABLE IF NOT EXISTS cart (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        goods_id INT NOT NULL,
        quantity INT NOT NULL DEFAULT 1,
        specification_id INT,
        selected TINYINT(1) NOT NULL DEFAULT 1,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX (user_id),
        INDEX (goods_id)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    `);
    
    console.log('购物车表创建成功');
    process.exit(0);
  } catch (error) {
    console.error('创建购物车表失败:', error);
    process.exit(1);
  }
}

// 执行创建表的函数
createCartTable(); 