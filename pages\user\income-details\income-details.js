// pages/user/income-details/income-details.js
// 收入明细页面

const { request } = require('../../../utils/request.js')
const API = require('../../../config/api.js')

Page({
  data: {
    // 收入明细列表
    incomeList: [],
    
    // 分页信息
    page: 1,
    limit: 10,
    total: 0,
    hasMore: true,
    
    // 筛选条件
    filterType: '', // 收入类型筛选
    filterIndex: 0, // 当前选择的筛选索引
    currentFilterLabel: '全部', // 当前筛选标签
    typeOptions: [
      { value: '', label: '全部' },
      { value: 'wifi_share', label: 'WiFi分润' },
      { value: 'goods_sale', label: '商品分润' },
      { value: 'advertisement', label: '广告分润' },
      { value: 'referral', label: '推荐奖励' },
      { value: 'bonus', label: '等级奖励' }
    ],
    
    // 统计信息
    totalStats: {
      total_income: '0.00',
      today_income: '0.00',
      month_income: '0.00'
    },
    
    // 页面状态
    loading: false,
    refreshing: false,
    loadingMore: false
  },

  onLoad() {
    this.loadIncomeStats()
    this.loadIncomeDetails(true)
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.onRefresh()
  },

  // 上拉加载更多
  onReachBottom() {
    if (this.data.hasMore && !this.data.loadingMore) {
      this.loadMore()
    }
  },

  // 刷新数据
  async onRefresh() {
    this.setData({
      refreshing: true,
      page: 1,
      hasMore: true
    })
    
    await Promise.all([
      this.loadIncomeStats(),
      this.loadIncomeDetails(true)
    ])
    
    this.setData({ refreshing: false })
    wx.stopPullDownRefresh()
  },

  // 加载更多
  async loadMore() {
    if (!this.data.hasMore || this.data.loadingMore) return
    
    this.setData({
      loadingMore: true,
      page: this.data.page + 1
    })
    
    await this.loadIncomeDetails(false)
    
    this.setData({ loadingMore: false })
  },

  // 加载收入统计
  async loadIncomeStats() {
    try {
      const result = await request({
        url: API.income.stats,
        method: 'GET'
      })
      
      if ((result.code === 0 || result.code === 200 || result.success === true) && result.data) {
        this.setData({
          totalStats: {
            total_income: result.data.total_income || result.data.balance || '0.00',
            today_income: result.data.today_income || '0.00',
            month_income: result.data.month_income || '0.00'
          }
        })
      }
    } catch (error) {
      console.error('加载收入统计失败:', error)
    }
  },

  // 加载收入明细
  async loadIncomeDetails(reset = false) {
    try {
      if (reset) {
        this.setData({ loading: true })
      }
      
      const params = {
        page: reset ? 1 : this.data.page,
        limit: this.data.limit
      }
      
      // 添加类型筛选
      if (this.data.filterType !== '') {
        params.type = this.data.filterType
      }
      
      const result = await request({
        url: API.income.details,
        method: 'GET',
        data: params
      })
      
      if ((result.code === 0 || result.code === 200 || result.success === true) && result.data) {
        const newList = result.data.list || result.data || []
        
        this.setData({
          incomeList: reset ? newList : [...this.data.incomeList, ...newList],
          total: result.data.total || newList.length,
          hasMore: newList.length >= this.data.limit,
          page: reset ? 1 : this.data.page
        })
      }
    } catch (error) {
      console.error('加载收入明细失败:', error)
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      })
    } finally {
      this.setData({ loading: false })
    }
  },

  // 筛选类型改变
  onFilterChange(e) {
    const typeIndex = parseInt(e.detail.value)
    const selectedOption = this.data.typeOptions[typeIndex]

    this.setData({
      filterType: selectedOption.value,
      filterIndex: typeIndex,
      currentFilterLabel: selectedOption.label,
      page: 1,
      hasMore: true
    })

    this.loadIncomeDetails(true)
  },

  // 查看收入详情
  onViewDetail(e) {
    const item = e.currentTarget.dataset.item
    
    wx.showModal({
      title: '收入详情',
      content: this.formatIncomeDetail(item),
      showCancel: false,
      confirmText: '知道了'
    })
  },

  // 格式化收入详情
  formatIncomeDetail(item) {
    const typeMap = {
      'wifi_share': 'WiFi分润',
      'goods_sale': '商品分润', 
      'advertisement': '广告分润',
      'referral': '推荐奖励',
      'bonus': '等级奖励'
    }
    
    let detail = `收入类型: ${typeMap[item.type] || item.type}\n`
    detail += `收入金额: ¥${item.amount}\n`
    detail += `收入时间: ${this.formatTime(item.created_at || item.createTime)}\n`
    
    if (item.title) {
      detail += `收入标题: ${item.title}\n`
    }
    
    if (item.description) {
      detail += `收入描述: ${item.description}\n`
    }
    
    if (item.source_id) {
      detail += `来源ID: ${item.source_id}\n`
    }
    
    if (item.order_no) {
      detail += `关联订单: ${item.order_no}`
    }
    
    return detail
  },

  // 格式化时间
  formatTime(timeStr) {
    if (!timeStr) return ''

    const date = new Date(timeStr)
    const now = new Date()
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
    const itemDate = new Date(date.getFullYear(), date.getMonth(), date.getDate())

    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const hour = String(date.getHours()).padStart(2, '0')
    const minute = String(date.getMinutes()).padStart(2, '0')

    // 如果是今天，只显示时间
    if (itemDate.getTime() === today.getTime()) {
      return `今天 ${hour}:${minute}`
    }

    // 如果是昨天
    const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000)
    if (itemDate.getTime() === yesterday.getTime()) {
      return `昨天 ${hour}:${minute}`
    }

    // 如果是今年，不显示年份
    if (year === now.getFullYear()) {
      return `${month}-${day} ${hour}:${minute}`
    }

    return `${year}-${month}-${day} ${hour}:${minute}`
  },

  // 格式化金额
  formatAmount(amount) {
    if (!amount && amount !== 0) return '0.00'
    return parseFloat(amount).toFixed(2)
  },

  // 获取收入类型文本
  getIncomeTypeText(type) {
    const typeMap = {
      'wifi_share': 'WiFi分享',
      'goods_sale': '商品销售',
      'advertisement': '广告收益',
      'referral': '推荐奖励',
      'bonus': '等级奖励'
    }

    return typeMap[type] || type || '其他收入'
  },

  // 获取收入类型图标
  getIncomeTypeIcon(type) {
    const iconMap = {
      'wifi_share': '📶',
      'goods_sale': '🛍️',
      'advertisement': '📺',
      'referral': '👥',
      'bonus': '🎁'
    }
    
    return iconMap[type] || '💰'
  },

  // 返回钱包页面
  onBackToWallet() {
    wx.navigateBack()
  }
})
