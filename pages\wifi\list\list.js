// pages/wifi/list/list.js
// WiFi码列表页面

const request = require('../../../utils/request.js')
const util = require('../../../utils/util.js')

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // WiFi码列表
    wifiList: [],
    
    // 分页信息
    pageInfo: {
      page: 1,
      pageSize: 10,
      total: 0,
      hasMore: true
    },
    
    // 搜索关键词
    searchKeyword: '',
    
    // 加载状态
    loading: false,
    loadingMore: false,
    refreshing: false,
    
    // 空状态
    isEmpty: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    console.log('WiFi码列表页面加载')
    this.checkLoginStatus()
    this.loadWifiList()
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    // 从其他页面返回时刷新列表
    if (this.data.wifiList.length > 0) {
      this.refreshWifiList()
    }
  },

  /**
   * 检查登录状态
   */
  checkLoginStatus() {
    const app = getApp()
    if (!app.globalData.isLogin) {
      wx.showModal({
        title: '提示',
        content: '请先登录后查看WiFi码列表',
        showCancel: false,
        success: () => {
          wx.switchTab({
            url: '/pages/index/index'
          })
        }
      })
    }
  },

  /**
   * 加载WiFi码列表
   */
  async loadWifiList(isRefresh = false) {
    if (this.data.loading || this.data.loadingMore) return

    const { pageInfo, searchKeyword } = this.data
    
    if (isRefresh) {
      this.setData({ 
        refreshing: true,
        'pageInfo.page': 1 
      })
    } else {
      this.setData({ 
        loading: pageInfo.page === 1,
        loadingMore: pageInfo.page > 1 
      })
    }

    try {
      // 使用完整的API路径，直接使用wifi/list，不要加client前缀
      console.log('开始请求WiFi列表数据')
      const result = await request.get('/wifi/list', {
        page: isRefresh ? 1 : pageInfo.page,
        pageSize: pageInfo.pageSize,
        keyword: searchKeyword
      })

      console.log('WiFi列表请求结果:', result)
      
      if (result && result.success) {
        // 确保结果中的list是一个数组
        const newList = Array.isArray(result.data.list) ? result.data.list : [];
        const currentList = isRefresh || pageInfo.page === 1 ? [] : this.data.wifiList
        
        // 处理列表数据，转换字段名以匹配前端展示
        const processedList = newList.map(item => {
          return {
            id: item.id,
            title: item.title || '未命名WiFi',
            ssid: item.name || '',  // 后端是name，前端使用ssid
            password: item.password || '',
            merchantName: item.merchant_name || '', // 后端是merchant_name，前端使用merchantName
            status: item.status === 1 ? 'active' : 'inactive',
            scanCount: item.scan_count || item.use_count || 0,
            connectCount: item.connect_count || item.use_count || 0,
            totalEarnings: item.total_earnings || '0.00',
            createTime: item.created_at ? new Date(item.created_at).toLocaleString() : '未知时间'
          }
        })
        
        this.setData({
          wifiList: [...currentList, ...processedList],
          'pageInfo.total': result.data.pagination?.total || 0,
          'pageInfo.hasMore': processedList.length === pageInfo.pageSize,
          isEmpty: (isRefresh || pageInfo.page === 1) && processedList.length === 0
        })
        
        console.log('WiFi列表数据处理完成，条数:', processedList.length)
      } else {
        console.error('加载WiFi列表失败:', result?.message || '未知错误')
        wx.showToast({
          title: result?.message || '加载失败',
          icon: 'none'
        })
      }
    } catch (error) {
      console.error('加载WiFi码列表失败:', error)
      wx.showToast({
        title: '网络错误，请重试',
        icon: 'none'
      })
    } finally {
      this.setData({
        loading: false,
        loadingMore: false,
        refreshing: false
      })
    }
  },

  /**
   * 刷新WiFi码列表
   */
  refreshWifiList() {
    this.loadWifiList(true)
  },

  /**
   * 搜索输入处理
   */
  onSearchInput(e) {
    const keyword = e.detail.value
    this.setData({
      searchKeyword: keyword,
      'pageInfo.page': 1
    })
    
    // 防抖搜索
    if (this.searchTimer) {
      clearTimeout(this.searchTimer)
    }
    this.searchTimer = setTimeout(() => {
      this.loadWifiList(true)
    }, 500)
  },

  /**
   * 清除搜索
   */
  onClearSearch() {
    this.setData({
      searchKeyword: '',
      'pageInfo.page': 1
    })
    this.loadWifiList(true)
  },

  /**
   * WiFi码点击事件
   */
  onWifiItemTap(e) {
    const { id } = e.currentTarget.dataset
    wx.navigateTo({
      url: `/pages/wifi/detail/detail?id=${id}`
    })
  },

  /**
   * 删除WiFi码
   */
  onDeleteWifi(e) {
    const { id, title } = e.currentTarget.dataset
    
    wx.showModal({
      title: '确认删除',
      content: `确定要删除WiFi码"${title}"吗？删除后无法恢复。`,
      success: async (res) => {
        if (res.confirm) {
          try {
            wx.showLoading({
              title: '删除中...'
            })

            const result = await request.delete(`/wifi/${id}`)
            
            wx.hideLoading()

            if (result.success) {
              wx.showToast({
                title: '删除成功',
                icon: 'success'
              })
              
              // 从列表中移除该项
              const wifiList = this.data.wifiList.filter(item => item.id !== id)
              this.setData({
                wifiList: wifiList,
                isEmpty: wifiList.length === 0
              })
            } else {
              wx.showToast({
                title: result.message || '删除失败',
                icon: 'none'
              })
            }
          } catch (error) {
            wx.hideLoading()
            console.error('删除WiFi码失败:', error)
            wx.showToast({
              title: '网络错误，请重试',
              icon: 'none'
            })
          }
        }
      }
    })
  },

  /**
   * 编辑WiFi码
   */
  onEditWifi(e) {
    const { id } = e.currentTarget.dataset
    wx.navigateTo({
      url: `/pages/wifi/create/create?id=${id}&mode=edit`
    })
  },

  /**
   * 分享WiFi码
   */
  onShareWifi(e) {
    const { id, title } = e.currentTarget.dataset
    
    wx.showActionSheet({
      itemList: ['生成分享海报', '复制链接'],
      success: (res) => {
        switch (res.tapIndex) {
          case 0:
            this.generateSharePoster(id, title)
            break
          case 1:
            this.copyShareLink(id, title)
            break
        }
      }
    })
  },

  /**
   * 生成分享海报
   */
  async generateSharePoster(id, title) {
    try {
      wx.showLoading({
        title: '生成中...'
      })

      // 调用海报生成接口
      const result = await request.post(`/wifi/${id}/poster`)
      
      wx.hideLoading()

      if (result.success && result.data.posterUrl) {
        wx.previewImage({
          urls: [result.data.posterUrl],
          current: result.data.posterUrl
        })
      } else {
        wx.showToast({
          title: '生成失败，请重试',
          icon: 'none'
        })
      }
    } catch (error) {
      wx.hideLoading()
      console.error('生成分享海报失败:', error)
      wx.showToast({
        title: '网络错误，请重试',
        icon: 'none'
      })
    }
  },

  /**
   * 复制分享链接
   */
  copyShareLink(id, title) {
    const shareUrl = `pages/wifi/detail/detail?id=${id}`
    
    wx.setClipboardData({
      data: shareUrl,
      success: () => {
        wx.showToast({
          title: '链接已复制',
          icon: 'success'
        })
      }
    })
  },

  /**
   * 创建新WiFi码
   */
  onCreateWifi() {
    wx.navigateTo({
      url: '/pages/wifi/create/create'
    })
  },

  /**
   * 页面下拉刷新
   */
  onPullDownRefresh() {
    this.refreshWifiList()
    setTimeout(() => {
      wx.stopPullDownRefresh()
    }, 1000)
  },

  /**
   * 页面上拉加载更多
   */
  onReachBottom() {
    if (this.data.pageInfo.hasMore && !this.data.loadingMore) {
      this.setData({
        'pageInfo.page': this.data.pageInfo.page + 1
      })
      this.loadWifiList()
    }
  },

  /**
   * 页面分享
   */
  onShareAppMessage() {
    return {
      title: 'WiFi共享商城 - 我的WiFi码',
      path: '/pages/wifi/list/list',
      imageUrl: '/assets/images/share-wifi-list.jpg'
    }
  }
}) 