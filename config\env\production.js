/**
 * 生产环境配置
 */
module.exports = {
  // 数据库配置
  database: {
    host: process.env.DB_HOST,
    port: parseInt(process.env.DB_PORT) || 3306,
    username: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_NAME,
    connectionLimit: 20
  },
  
  // JWT配置
  jwt: {
    secret: process.env.JWT_SECRET, // 生产环境必须设置复杂的密钥
    expiresIn: process.env.JWT_EXPIRES_IN || '7d'
  },
  
  // 腾讯云COS对象存储配置
  cos: {
    secretId: process.env.COS_SECRET_ID,
    secretKey: process.env.COS_SECRET_KEY,
    region: process.env.COS_REGION,
    bucket: process.env.COS_BUCKET
  },
  
  // 微信小程序配置
  wechat: {
    appId: process.env.WECHAT_APPID,
    appSecret: process.env.WECHAT_SECRET
  },
  
  // 跨域配置
  cors: {
    origin: process.env.ALLOWED_ORIGINS ? process.env.ALLOWED_ORIGINS.split(',') : [], // 生产环境限制来源
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization'],
    credentials: true // 支持跨域Cookie
  },
  
  // 是否开启调试模式
  debug: false,
  
  // 上传文件配置
  upload: {
    baseDir: 'uploads',
    maxSize: 10 * 1024 * 1024, // 10MB
    allowTypes: ['image/jpeg', 'image/png', 'image/gif']
  },
  
  // 安全配置
  security: {
    // 启用HTTPS (通过反向代理实现)
    enableHttps: true,
    // XSS保护
    xss: true,
    // CSRF保护
    csrf: true,
    // 内容安全策略
    contentSecurityPolicy: true
  }
}; 