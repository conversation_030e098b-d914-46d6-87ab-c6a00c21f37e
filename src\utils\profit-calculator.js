/**
 * 分润计算工具
 */
const db = require('../../config/database');
const logger = require('./logger');
const PlatformStatsService = require('../services/platform-stats');

class ProfitCalculator {
  /**
   * 获取分润规则
   * @param {string} type 业务类型
   * @returns {Promise<object>} 分润规则
   */
  static async getProfitRule(type) {
    try {
      const sql = 'SELECT * FROM profit_rule WHERE type = ? AND status = 1';
      const result = await db.query(sql, [type]);
      
      if (result.length === 0) {
        throw new Error(`分润规则不存在: ${type}`);
      }
      
      return result[0];
    } catch (error) {
      logger.error(`获取分润规则失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 获取团队信息（包含地区和等级）
   * @param {number} teamId 团队ID
   * @param {string} profitType 分润类型
   * @returns {Promise<object>} 团队信息
   */
  static async getTeamInfoWithRegion(teamId, profitType) {
    try {
      const sql = `
        SELECT
          t.id as team_id,
          t.region_id,
          t.level,
          r.name as region_name,
          r.code as region_code,
          COALESCE(tlp.bonus_rate, 0) as bonus_rate,
          COALESCE(tlp.level_name, '初级团队') as level_name
        FROM team t
        LEFT JOIN region r ON t.region_id = r.id
        LEFT JOIN team_level_profit tlp ON t.level = tlp.level AND tlp.profit_type = ? AND tlp.status = 1
        WHERE t.id = ?
      `;
      const result = await db.query(sql, [profitType, teamId]);

      if (result.length === 0) {
        return {
          teamId: null,
          regionId: null,
          level: 1,
          bonusRate: 0,
          levelName: '初级团队',
          regionName: null,
          regionCode: null
        };
      }

      const team = result[0];
      return {
        teamId: team.team_id,
        regionId: team.region_id,
        level: team.level,
        bonusRate: team.bonus_rate || 0,
        levelName: team.level_name,
        regionName: team.region_name,
        regionCode: team.region_code
      };
    } catch (error) {
      logger.error(`获取团队信息失败: ${error.message}`);
      return {
        teamId: null,
        regionId: null,
        level: 1,
        bonusRate: 0,
        levelName: '初级团队',
        regionName: null,
        regionCode: null
      };
    }
  }

  /**
   * 获取团队等级奖励（保留向后兼容）
   * @param {number} teamId 团队ID
   * @param {string} profitType 分润类型
   * @returns {Promise<object>} 团队等级信息
   */
  static async getTeamLevelBonus(teamId, profitType) {
    const teamInfo = await this.getTeamInfoWithRegion(teamId, profitType);
    return {
      level: teamInfo.level,
      bonusRate: teamInfo.bonusRate,
      levelName: teamInfo.levelName
    };
  }

  /**
   * 计算分润金额（含团队等级奖励和地区隔离）
   * @param {string} type 业务类型
   * @param {number} totalAmount 总金额
   * @param {object} participants 参与者信息
   * @returns {Promise<object>} 分润结果
   */
  static async calculateProfit(type, totalAmount, participants = {}) {
    try {
      const rule = await this.getProfitRule(type);

      // 检查最小分润金额
      if (totalAmount < rule.min_amount) {
        return {
          success: false,
          message: `金额不足最小分润标准: ${rule.min_amount}元`,
          totalAmount,
          minAmount: rule.min_amount
        };
      }

      // 获取团队和地区信息
      let teamInfo = { teamId: null, regionId: null, level: 1, bonusRate: 0, levelName: '初级团队' };
      if (participants.teamId) {
        teamInfo = await this.getTeamInfoWithRegion(participants.teamId, type);
      }

      // 计算基础分润金额
      let platformAmount = totalAmount * rule.platform_rate / 100;
      let leaderAmount = totalAmount * rule.leader_rate / 100;
      let userAmount = totalAmount * rule.user_rate / 100;

      // 应用团队等级奖励（从平台分润中扣除给团长）
      const bonusAmount = leaderAmount * teamInfo.bonusRate / 100;
      platformAmount -= bonusAmount;
      leaderAmount += bonusAmount;

      // 验证分润比例总和
      const totalRate = rule.platform_rate + rule.leader_rate + rule.user_rate;
      if (totalRate !== 100) {
        logger.warn(`分润比例总和不等于100%: ${totalRate}%`);
      }

      const result = {
        success: true,
        type,
        ruleName: rule.name,
        totalAmount: parseFloat(totalAmount),
        teamInfo: {
          teamId: participants.teamId,
          regionId: teamInfo.regionId,
          level: teamInfo.level,
          levelName: teamInfo.levelName,
          bonusRate: teamInfo.bonusRate,
          bonusAmount: parseFloat(bonusAmount.toFixed(2))
        },
        distribution: {
          platform: {
            rate: rule.platform_rate,
            amount: parseFloat(platformAmount.toFixed(2)),
            originalAmount: parseFloat((totalAmount * rule.platform_rate / 100).toFixed(2))
          },
          leader: {
            rate: rule.leader_rate,
            amount: parseFloat(leaderAmount.toFixed(2)),
            originalAmount: parseFloat((totalAmount * rule.leader_rate / 100).toFixed(2)),
            bonusAmount: parseFloat(bonusAmount.toFixed(2)),
            userId: participants.leaderId || null,
            teamId: participants.teamId || null,
            regionId: teamInfo.regionId
          },
          member: {
            rate: rule.user_rate,
            amount: parseFloat(userAmount.toFixed(2)),
            userId: participants.userId || null,
            regionId: teamInfo.regionId
          }
        },
        calculatedAt: new Date()
      };

      return result;
    } catch (error) {
      logger.error(`计算分润失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 执行分润分配
   * @param {object} profitResult 分润计算结果
   * @param {string} sourceId 来源ID
   * @param {string} orderNo 订单号
   * @returns {Promise<object>} 分配结果
   */
  static async distributeProfits(profitResult, sourceId, orderNo = null) {
    try {
      if (!profitResult.success) {
        throw new Error('分润计算失败，无法执行分配');
      }

      const results = [];
      const { distribution, type } = profitResult;

      // 为团长分配分润
      if (distribution.leader.userId && distribution.leader.amount > 0) {
        const leaderProfit = await this.createProfitRecord({
          userId: distribution.leader.userId,
          teamId: distribution.leader.teamId,
          regionId: distribution.leader.regionId,
          amount: distribution.leader.amount,
          sourceType: this.getSourceType(type),
          sourceId,
          orderNo,
          profitType: type,
          role: 'leader',
          rate: distribution.leader.rate,
          totalAmount: profitResult.totalAmount,
          description: `${profitResult.ruleName} - 团长分润${profitResult.teamInfo.bonusAmount > 0 ? ` (含等级奖励${profitResult.teamInfo.bonusAmount}元)` : ''}`,
          status: 0 // 待结算
        });
        results.push({ role: 'leader', ...leaderProfit });
      }

      // 为成员分配分润
      if (distribution.member.userId && distribution.member.amount > 0) {
        const memberProfit = await this.createProfitRecord({
          userId: distribution.member.userId,
          teamId: null,
          regionId: distribution.member.regionId,
          amount: distribution.member.amount,
          sourceType: this.getSourceType(type),
          sourceId,
          orderNo,
          profitType: type,
          role: 'member',
          rate: distribution.member.rate,
          totalAmount: profitResult.totalAmount,
          description: `${profitResult.ruleName} - 成员分润`,
          status: 0 // 待结算
        });
        results.push({ role: 'member', ...memberProfit });
      }

      return {
        success: true,
        totalDistributed: results.reduce((sum, r) => sum + r.amount, 0),
        platformAmount: distribution.platform.amount,
        records: results
      };
    } catch (error) {
      logger.error(`分润分配失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 创建分润记录（支持地区隔离和自动结算）
   * @param {object} data 分润数据
   * @returns {Promise<object>} 分润记录
   */
  static async createProfitRecord(data) {
    try {
      // 检查profit_log表结构，动态构建SQL
      const columns = ['user_id', 'amount', 'source_type', 'source_id', 'remark', 'created_at'];
      const values = [data.userId, data.amount, data.sourceType, data.sourceId, data.description, 'NOW()'];
      const params = [data.userId, data.amount, data.sourceType, data.sourceId, data.description];

      // 添加可选字段
      if (data.teamId !== undefined) {
        columns.splice(1, 0, 'team_id');
        values.splice(1, 0, '?');
        params.splice(1, 0, data.teamId || null);
      }

      if (data.regionId !== undefined) {
        const regionIndex = columns.indexOf('team_id') + 1 || 2;
        columns.splice(regionIndex, 0, 'region_id');
        values.splice(regionIndex, 0, '?');
        params.splice(regionIndex, 0, data.regionId || null);
      }

      // 使用事务确保数据一致性
      return await db.transaction(async (connection) => {
        // 获取用户当前余额
        const userResult = await connection.query(
          'SELECT balance FROM user WHERE id = ?',
          [data.userId]
        );
        const beforeBalance = userResult.length > 0 ? parseFloat(userResult[0].balance) : 0;
        const afterBalance = beforeBalance + parseFloat(data.amount);

        // 创建分润记录
        const sql = `
          INSERT INTO profit_log (${columns.join(', ')})
          VALUES (${values.join(', ')})
        `;

        const result = await connection.query(sql, params);

        // 自动更新用户余额
        await connection.query(
          'UPDATE user SET balance = balance + ? WHERE id = ?',
          [data.amount, data.userId]
        );

        // 记录余额变动日志
        await PlatformStatsService.logBalanceChange({
          userId: data.userId,
          amount: data.amount,
          type: 'profit',
          beforeBalance: beforeBalance,
          afterBalance: afterBalance,
          sourceType: data.sourceType,
          sourceId: data.sourceId,
          profitLogId: result.insertId,
          remark: data.description
        });

        logger.info(`分润记录创建成功，用户${data.userId}余额从¥${beforeBalance}增加到¥${afterBalance}`);

        return {
          id: result.insertId,
          userId: data.userId,
          teamId: data.teamId,
          regionId: data.regionId,
          amount: data.amount,
          role: data.role,
          description: data.description,
          createdAt: new Date()
        };
      });
    } catch (error) {
      logger.error(`创建分润记录失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 获取来源类型编号
   * @param {string} type 业务类型
   * @returns {number} 来源类型编号
   */
  static getSourceType(type) {
    const typeMap = {
      'wifi_share': 1,
      'goods_sale': 2,
      'advertisement': 3
    };
    return typeMap[type] || 1;
  }

  /**
   * 批量计算和分配分润
   * @param {Array} orders 订单列表
   * @returns {Promise<object>} 批量处理结果
   */
  static async batchCalculateAndDistribute(orders) {
    try {
      const results = [];
      let totalProcessed = 0;
      let totalFailed = 0;

      for (const order of orders) {
        try {
          const profitResult = await this.calculateProfit(
            order.type,
            order.amount,
            order.participants
          );

          if (profitResult.success) {
            const distributionResult = await this.distributeProfits(
              profitResult,
              order.sourceId,
              order.orderNo
            );
            results.push({
              orderId: order.id,
              success: true,
              ...distributionResult
            });
            totalProcessed++;
          } else {
            results.push({
              orderId: order.id,
              success: false,
              message: profitResult.message
            });
            totalFailed++;
          }
        } catch (error) {
          results.push({
            orderId: order.id,
            success: false,
            error: error.message
          });
          totalFailed++;
        }
      }

      return {
        totalOrders: orders.length,
        totalProcessed,
        totalFailed,
        results
      };
    } catch (error) {
      logger.error(`批量分润处理失败: ${error.message}`);
      throw error;
    }
  }
}

module.exports = ProfitCalculator;
