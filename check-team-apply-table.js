const db = require('./src/database');
const config = require('./config/index');

async function checkTeamApplyTable() {
  try {
    // 检查team_apply表是否存在
    const [tables] = await db.query(
      "SELECT table_name FROM information_schema.tables WHERE table_schema = ? AND table_name = 'team_apply'",
      [config.database.database]
    );
    
    if (!tables || tables.length === 0) {
      console.log('team_apply表不存在，准备创建...');
      
      // 创建team_apply表
      await db.query(`
        CREATE TABLE team_apply (
          id INT AUTO_INCREMENT PRIMARY KEY,
          user_id INT NOT NULL,
          name VARCHAR(100) NOT NULL COMMENT '团队名称/联系人',
          phone VARCHAR(20) NOT NULL COMMENT '联系电话',
          area VARCHAR(255) NOT NULL COMMENT '所在地区',
          description TEXT COMMENT '申请描述',
          status TINYINT DEFAULT 0 COMMENT '状态: 0-待审核, 1-已通过, 2-已拒绝',
          remark VARCHAR(255) COMMENT '审核备注',
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          INDEX idx_user_id (user_id),
          INDEX idx_status (status)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='联盟申请表'
      `);
      
      console.log('team_apply表创建成功');
    } else {
      console.log('team_apply表已存在');
      
      // 查看表结构
      try {
        const columns = await db.query('SHOW COLUMNS FROM team_apply');
        console.log('表结构：', JSON.stringify(columns, null, 2));
      } catch (columnError) {
        console.error('获取表结构失败:', columnError);
      }
    }
  } catch (error) {
    console.error('检查或创建team_apply表时出错:', error);
  } finally {
    process.exit();
  }
}

checkTeamApplyTable(); 