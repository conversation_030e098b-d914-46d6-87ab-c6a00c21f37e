const GoodsModel = require('../models/goods');
const logger = require('../utils/logger');
const { success, error } = require('../utils/response');

/**
 * 获取商品列表
 */
const getGoodsList = async (req, res) => {
  try {
    logger.info(`获取商品列表请求 - URL: ${req.originalUrl}`);
    console.log('获取商品列表请求参数:', req.query);
    
    const { 
      page = 1, 
      limit = 10, 
      keyword = '', 
      categoryId = '', 
      sort = 'createdAt', 
      order = 'desc' 
    } = req.query;
    
    const result = await GoodsModel.list({ page, limit, keyword, categoryId, sort, order });
    
    console.log(`商品列表获取成功 - 共找到${result.list.length}条记录`);
    logger.info(`商品列表获取成功 - 总记录数: ${result.pagination?.total || 0}`);
    
    return success(res, result, '获取商品列表成功');
  } catch (err) {
    logger.error(`获取商品列表失败: ${err.message}`);
    console.error('获取商品列表错误:', err);
    return error(res, '获取商品列表失败', 500);
  }
};

/**
 * 获取商品详情
 */
const getGoodsDetail = async (req, res) => {
  try {
    const { id } = req.params;
    
    logger.info(`获取商品详情请求 - ID: ${id}, 类型: ${typeof id}`);
    console.log(`获取商品详情 - ID: ${id}, 参数类型: ${typeof id}, URL: ${req.originalUrl}`);
    console.log('请求参数详情:', req.params);
    
    if (!id || isNaN(parseInt(id))) {
      logger.warn(`无效的商品ID: ${id}`);
      return error(res, '无效的商品ID', 400);
    }
    
    const goods = await GoodsModel.getById(parseInt(id));
    
    console.log('获取的商品详情:', goods);
    
    if (!goods) {
      logger.warn(`商品不存在 - ID: ${id}`);
      return error(res, '商品不存在', 404);
    }
    
    // 确保返回字段名一致
    const formattedGoods = {
      ...goods,
      name: goods.title, // 添加name作为title的别名，兼容前端
      goodRate: '100%', // 添加默认好评率
      specifications: [{ id: 0, name: '默认', price: goods.price }], // 添加默认规格
    };
    
    logger.info(`商品详情获取成功 - ID: ${id}`);
    console.log(`返回商品详情: ${JSON.stringify(formattedGoods)}`);
    
    return success(res, formattedGoods, '获取商品详情成功');
  } catch (err) {
    logger.error(`获取商品详情失败: ${err.message}`);
    console.error(`获取商品详情错误: ${err.message}`, err);
    return error(res, '获取商品详情失败', 500);
  }
};

/**
 * 创建商品
 */
const createGoods = async (req, res) => {
  try {
    // 记录请求体，便于调试
    console.log('创建商品请求体:', JSON.stringify(req.body));
    
    const { 
      name, title, cover, images, price, originalPrice, original_price, stock, 
      description, categoryId, category_id, isRecommend, is_recommend, specifications, details 
    } = req.body;
    
    // 兼容前端可能发送的不同字段名
    const goodsTitle = title || name;
    const goodsCategoryId = categoryId || category_id;
    const goodsOriginalPrice = originalPrice || original_price;
    const goodsIsRecommend = isRecommend || is_recommend;
    const goodsDetails = details || '';
    
    // 验证必填字段
    if (!goodsTitle) {
      return error(res, '商品标题不能为空', 400);
    }
    
    if (!cover) {
      return error(res, '商品封面不能为空', 400);
    }
    
    if (!price) {
      return error(res, '商品价格不能为空', 400);
    }
    
    if (stock === undefined || stock === null) {
      return error(res, '商品库存不能为空', 400);
    }
    
    if (!goodsCategoryId) {
      return error(res, '商品分类不能为空', 400);
    }
    
    // 验证价格和库存
    if (price <= 0 || stock < 0) {
      return error(res, '价格或库存值无效', 400);
    }
    
    // 处理images字段，确保是数组格式
    let processedImages = [];
    if (images) {
      if (typeof images === 'string') {
        try {
          processedImages = JSON.parse(images);
        } catch (e) {
          // 如果解析失败，可能是单个URL字符串
          logger.error(`解析商品图片JSON失败: ${e.message}`);
          processedImages = [images]; // 将单个URL作为数组的一个元素
        }
      } else if (Array.isArray(images)) {
        processedImages = images;
      } else if (typeof images === 'object') {
        // 处理其他可能的对象格式
        processedImages = [images.toString()];
      }
    }
    
    const goodsData = {
      title: goodsTitle,
      cover,
      images: processedImages,
      price: parseFloat(price),
      originalPrice: parseFloat(goodsOriginalPrice || price),
      stock: parseInt(stock),
      description: description || '',
      categoryId: parseInt(goodsCategoryId),
      isRecommend: !!goodsIsRecommend,
      details: goodsDetails
    };
    
    console.log('处理后的商品数据:', goodsData);
    
    const goods = await GoodsModel.create(goodsData);
    
    return success(res, goods, '创建商品成功');
  } catch (err) {
    logger.error(`创建商品失败: ${err.message}`);
    return error(res, '创建商品失败: ' + err.message, 500);
  }
};

/**
 * 更新商品
 */
const updateGoods = async (req, res) => {
  try {
    const { id } = req.params;
    const { 
      name, title, cover, images, price, originalPrice, stock, 
      description, categoryId, isRecommend, specifications, details 
    } = req.body;
    
    // 兼容前端可能发送的不同字段名
    const goodsTitle = title || name;
    
    // 检查商品是否存在
    const existingGoods = await GoodsModel.getById(id);
    if (!existingGoods) {
      return error(res, '商品不存在', 404);
    }
    
    // 处理images字段，确保是数组格式
    let processedImages;
    if (images !== undefined) {
      if (typeof images === 'string') {
        try {
          processedImages = JSON.parse(images);
        } catch (e) {
          logger.error(`解析商品图片JSON失败: ${e.message}`);
          processedImages = [];
        }
      } else if (Array.isArray(images)) {
        processedImages = images;
      } else {
        processedImages = [];
      }
    }
    
    // 构建更新数据
    const goodsData = {};
    
    if (goodsTitle !== undefined) goodsData.title = goodsTitle;
    if (cover !== undefined) goodsData.cover = cover;
    if (processedImages !== undefined) goodsData.images = processedImages;
    if (price !== undefined) goodsData.price = parseFloat(price);
    if (originalPrice !== undefined) goodsData.originalPrice = parseFloat(originalPrice);
    if (stock !== undefined) goodsData.stock = parseInt(stock);
    if (description !== undefined) goodsData.description = description;
    if (categoryId !== undefined) goodsData.categoryId = parseInt(categoryId);
    if (isRecommend !== undefined) goodsData.isRecommend = !!isRecommend;
    if (details !== undefined) goodsData.details = details;
    
    const result = await GoodsModel.update(id, goodsData);
    
    if (result) {
      return success(res, null, '更新商品成功');
    } else {
      return error(res, '更新商品失败', 400);
    }
  } catch (err) {
    logger.error(`更新商品失败: ${err.message}`);
    return error(res, '更新商品失败: ' + err.message, 500);
  }
};

/**
 * 删除商品
 */
const deleteGoods = async (req, res) => {
  try {
    const { id } = req.params;
    
    // 添加更多日志
    logger.info(`尝试删除商品，ID: ${id}`);
    console.log(`尝试删除商品，ID: ${id}, 参数类型: ${typeof id}`);
    
    // 确保ID是有效的
    if (!id || isNaN(parseInt(id))) {
      logger.warn(`无效的商品ID: ${id}`);
      return error(res, '无效的商品ID', 400);
    }
    
    // 检查商品是否存在
    const existingGoods = await GoodsModel.getById(id);
    console.log('查询结果:', existingGoods);
    
    if (!existingGoods) {
      logger.warn(`商品不存在，ID: ${id}`);
      return error(res, '商品不存在', 404);
    }
    
    logger.info(`商品存在，准备删除，ID: ${id}`);
    const result = await GoodsModel.delete(id);
    
    if (result) {
      logger.info(`商品删除成功，ID: ${id}`);
      return success(res, null, '删除商品成功');
    } else {
      logger.error(`商品删除失败，ID: ${id}`);
      return error(res, '删除商品失败', 400);
    }
  } catch (err) {
    logger.error(`删除商品失败: ${err.message}`);
    return error(res, '删除商品失败: ' + err.message, 500);
  }
};

/**
 * 更新商品状态（上下架）
 */
const updateGoodsStatus = async (req, res) => {
  try {
    const { id } = req.params;
    const { status } = req.body;
    
    // 检查商品是否存在
    const existingGoods = await GoodsModel.getById(id);
    if (!existingGoods) {
      return error(res, '商品不存在', 404);
    }
    
    // 验证状态值
    if (status !== 0 && status !== 1) {
      return error(res, '无效的状态值', 400);
    }
    
    const result = await GoodsModel.updateStatus(id, status);
    
    if (result) {
      return success(res, null, status === 1 ? '商品上架成功' : '商品下架成功');
    } else {
      return error(res, '更新商品状态失败', 400);
    }
  } catch (err) {
    logger.error(`更新商品状态失败: ${err.message}`);
    return error(res, '更新商品状态失败', 500);
  }
};

/**
 * 获取商品分类
 */
const getCategories = async (req, res) => {
  try {
    logger.info('获取商品分类请求');
    console.log('处理获取商品分类请求');
    
    // 从数据库获取分类
    const categories = await GoodsModel.getCategories();
    
    logger.info(`商品分类获取成功，共${categories.length}个分类`);
    console.log('商品分类:', categories);
    
    return success(res, categories, '获取商品分类成功');
  } catch (err) {
    logger.error(`获取商品分类失败: ${err.message}`);
    console.error('获取商品分类错误:', err);
    
    // 如果获取失败，返回模拟分类数据
    const mockCategories = [
      { id: 1, name: '电子产品', icon: '/public/uploads/icons/electronics.png', count: 100 },
      { id: 2, name: '服装', icon: '/public/uploads/icons/clothing.png', count: 200 },
      { id: 3, name: '食品', icon: '/public/uploads/icons/food.png', count: 150 },
      { id: 4, name: '家居', icon: '/public/uploads/icons/home.png', count: 80 },
      { id: 5, name: '美妆', icon: '/public/uploads/icons/beauty.png', count: 120 }
    ];
    
    logger.info('返回模拟商品分类数据');
    return success(res, mockCategories, '获取商品分类成功');
  }
};

/**
 * 创建商品分类
 */
const createCategory = async (req, res) => {
  try {
    const { name, icon, sortOrder } = req.body;
    
    // 验证必填字段
    if (!name) {
      return error(res, '分类名称不能为空', 400);
    }
    
    const categoryData = {
      name,
      icon: icon || '',
      sortOrder: sortOrder || 0
    };
    
    const category = await GoodsModel.createCategory(categoryData);
    
    return success(res, category, '创建商品分类成功');
  } catch (err) {
    logger.error(`创建商品分类失败: ${err.message}`);
    return error(res, '创建商品分类失败', 500);
  }
};

module.exports = {
  getGoodsList,
  getGoodsDetail,
  createGoods,
  updateGoods,
  deleteGoods,
  updateGoodsStatus,
  getCategories,
  createCategory
}; 