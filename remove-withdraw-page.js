/**
 * 移除多余的提现页面脚本
 * 根据用户需求，移除不需要的提现页面，保留钱包页面的提现功能
 */

const fs = require('fs');
const path = require('path');

// 前端项目路径
const frontendPath = '../wifi-share-miniapp';

// 需要移除的提现页面文件
const withdrawPageFiles = [
  'pages/user/withdraw/withdraw.wxml',
  'pages/user/withdraw/withdraw.js', 
  'pages/user/withdraw/withdraw.wxss',
  'pages/user/withdraw/withdraw.json'
];

// 需要移除的提现记录页面文件
const withdrawRecordsFiles = [
  'pages/user/withdraw-records/withdraw-records.wxml',
  'pages/user/withdraw-records/withdraw-records.js',
  'pages/user/withdraw-records/withdraw-records.wxss', 
  'pages/user/withdraw-records/withdraw-records.json'
];

// 需要移除的添加提现账户页面文件
const addAccountFiles = [
  'pages/user/add-withdraw-account/add-withdraw-account.wxml',
  'pages/user/add-withdraw-account/add-withdraw-account.js',
  'pages/user/add-withdraw-account/add-withdraw-account.wxss',
  'pages/user/add-withdraw-account/add-withdraw-account.json'
];

async function removeWithdrawPages() {
  console.log('🗑️  开始移除多余的提现页面...\n');
  
  // 1. 检查前端项目是否存在
  const frontendExists = fs.existsSync(frontendPath);
  if (!frontendExists) {
    console.log('❌ 前端项目目录不存在:', frontendPath);
    console.log('请确保在正确的目录下运行此脚本');
    return;
  }
  
  console.log('✅ 找到前端项目目录:', frontendPath);
  
  // 2. 移除提现页面文件
  console.log('\n1️⃣ 移除提现申请页面文件...');
  await removePageFiles(withdrawPageFiles, '提现申请页面');
  
  // 3. 移除提现记录页面文件
  console.log('\n2️⃣ 移除提现记录页面文件...');
  await removePageFiles(withdrawRecordsFiles, '提现记录页面');
  
  // 4. 移除添加提现账户页面文件
  console.log('\n3️⃣ 移除添加提现账户页面文件...');
  await removePageFiles(addAccountFiles, '添加提现账户页面');
  
  // 5. 更新app.json，移除页面注册
  console.log('\n4️⃣ 更新app.json，移除页面注册...');
  await updateAppJson();
  
  // 6. 修改钱包页面，移除提现跳转
  console.log('\n5️⃣ 修改钱包页面，移除提现跳转...');
  await updateWalletPage();
  
  console.log('\n🎉 提现页面移除完成！');
  console.log('\n📋 移除内容总结:');
  console.log('- ✅ 移除了提现申请页面');
  console.log('- ✅ 移除了提现记录页面'); 
  console.log('- ✅ 移除了添加提现账户页面');
  console.log('- ✅ 更新了app.json页面注册');
  console.log('- ✅ 修改了钱包页面提现按钮');
  console.log('\n💡 现在钱包页面的提现按钮将显示"功能开发中"提示');
}

// 移除页面文件
async function removePageFiles(files, pageName) {
  let removedCount = 0;
  let notFoundCount = 0;
  
  for (const file of files) {
    const filePath = path.join(frontendPath, file);
    
    try {
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
        console.log(`  ✅ 已移除: ${file}`);
        removedCount++;
      } else {
        console.log(`  ⚠️  文件不存在: ${file}`);
        notFoundCount++;
      }
    } catch (error) {
      console.log(`  ❌ 移除失败: ${file} - ${error.message}`);
    }
  }
  
  // 尝试移除空目录
  const pageDir = path.dirname(path.join(frontendPath, files[0]));
  try {
    if (fs.existsSync(pageDir)) {
      const dirContents = fs.readdirSync(pageDir);
      if (dirContents.length === 0) {
        fs.rmdirSync(pageDir);
        console.log(`  ✅ 已移除空目录: ${path.relative(frontendPath, pageDir)}`);
      }
    }
  } catch (error) {
    console.log(`  ⚠️  无法移除目录: ${error.message}`);
  }
  
  console.log(`  📊 ${pageName}: 移除${removedCount}个文件，${notFoundCount}个文件不存在`);
}

// 更新app.json
async function updateAppJson() {
  const appJsonPath = path.join(frontendPath, 'app.json');
  
  try {
    if (!fs.existsSync(appJsonPath)) {
      console.log('  ❌ app.json文件不存在');
      return;
    }
    
    const appJsonContent = fs.readFileSync(appJsonPath, 'utf8');
    const appJson = JSON.parse(appJsonContent);
    
    // 需要移除的页面路径
    const pagesToRemove = [
      'pages/user/withdraw/withdraw',
      'pages/user/withdraw-records/withdraw-records',
      'pages/user/add-withdraw-account/add-withdraw-account'
    ];
    
    let removedPages = 0;
    
    if (appJson.pages) {
      const originalLength = appJson.pages.length;
      appJson.pages = appJson.pages.filter(page => {
        const shouldRemove = pagesToRemove.includes(page);
        if (shouldRemove) {
          console.log(`  ✅ 从app.json移除页面: ${page}`);
          removedPages++;
        }
        return !shouldRemove;
      });
      
      if (removedPages > 0) {
        // 写回文件
        fs.writeFileSync(appJsonPath, JSON.stringify(appJson, null, 2), 'utf8');
        console.log(`  📊 已从app.json移除${removedPages}个页面注册`);
      } else {
        console.log('  ⚠️  app.json中未找到需要移除的页面');
      }
    }
    
  } catch (error) {
    console.log(`  ❌ 更新app.json失败: ${error.message}`);
  }
}

// 更新钱包页面
async function updateWalletPage() {
  const walletJsPath = path.join(frontendPath, 'pages/user/wallet/wallet.js');
  
  try {
    if (!fs.existsSync(walletJsPath)) {
      console.log('  ❌ 钱包页面JS文件不存在');
      return;
    }
    
    let walletContent = fs.readFileSync(walletJsPath, 'utf8');
    
    // 替换提现方法，改为显示开发中提示
    const newWithdrawMethod = `  /**
   * 提现操作
   */
  onWithdraw: function () {
    console.log('🔧 提现按钮被点击')
    wx.showToast({
      title: '提现功能开发中',
      icon: 'none'
    })
  },`;
    
    // 查找并替换提现方法
    const withdrawMethodRegex = /\/\*\*\s*\n\s*\*\s*提现操作\s*\n\s*\*\/\s*\n\s*onWithdraw:\s*function\s*\([^)]*\)\s*\{[^}]*\}/s;
    
    if (withdrawMethodRegex.test(walletContent)) {
      walletContent = walletContent.replace(withdrawMethodRegex, newWithdrawMethod);
      fs.writeFileSync(walletJsPath, walletContent, 'utf8');
      console.log('  ✅ 已更新钱包页面提现方法');
    } else {
      console.log('  ⚠️  未找到钱包页面提现方法，可能已经是正确的');
    }
    
  } catch (error) {
    console.log(`  ❌ 更新钱包页面失败: ${error.message}`);
  }
}

// 运行移除脚本
console.log('🚀 开始执行移除脚本...');
removeWithdrawPages().then(() => {
  console.log('✅ 脚本执行完成');
}).catch(error => {
  console.error('❌ 脚本执行失败:', error);
});
