const db = require('./src/database');

async function checkAllianceTeam() {
  try {
    console.log('🔍 检查联盟申请和团队创建情况...\n');

    // 1. 检查联盟申请表
    console.log('📋 联盟申请记录:');
    const applications = await db.query(`
      SELECT ta.*, u.nickname, u.phone, u.is_leader, u.team_id
      FROM team_apply ta
      LEFT JOIN user u ON ta.user_id = u.id
      ORDER BY ta.created_at DESC
      LIMIT 10
    `);

    if (applications && applications.length > 0) {
      console.table(applications.map(app => ({
        ID: app.id,
        用户ID: app.user_id,
        用户昵称: app.nickname,
        团队名称: app.name,
        联系人: app.contact,
        状态: app.status === 0 ? '待审核' : app.status === 1 ? '已通过' : '已拒绝',
        用户是否团长: app.is_leader === 1 ? '是' : '否',
        用户团队ID: app.team_id,
        申请时间: app.created_at,
        更新时间: app.updated_at
      })));
    } else {
      console.log('❌ 没有找到联盟申请记录');
    }
    
    // 2. 检查团队表
    console.log('\n👥 团队记录:');
    const teams = await db.query(`
      SELECT t.*, u.nickname as leader_name, u.phone as leader_phone
      FROM team t
      LEFT JOIN user u ON t.leader_id = u.id
      ORDER BY t.created_at DESC
      LIMIT 10
    `);
    
    if (teams && teams.length > 0) {
      console.table(teams.map(team => ({
        ID: team.id,
        团队名称: team.name,
        团长ID: team.leader_id,
        团长昵称: team.leader_name,
        团长电话: team.leader_phone,
        成员数量: team.member_count,
        状态: team.status === 1 ? '正常' : '禁用',
        创建时间: team.created_at
      })));
    } else {
      console.log('❌ 没有找到团队记录');
    }
    
    // 3. 检查团队成员表
    console.log('\n👤 团队成员记录:');
    const members = await db.query(`
      SELECT tm.*, t.name as team_name, u.nickname as user_name
      FROM team_member tm
      LEFT JOIN team t ON tm.team_id = t.id
      LEFT JOIN user u ON tm.user_id = u.id
      ORDER BY tm.joined_at DESC
      LIMIT 10
    `);
    
    if (members && members.length > 0) {
      console.table(members.map(member => ({
        ID: member.id,
        团队ID: member.team_id,
        团队名称: member.team_name,
        用户ID: member.user_id,
        用户昵称: member.user_name,
        角色: member.role,
        加入时间: member.joined_at
      })));
    } else {
      console.log('❌ 没有找到团队成员记录');
    }
    
    // 4. 检查用户的团长状态
    console.log('\n👑 用户团长状态:');
    const leaders = await db.query(`
      SELECT id, nickname, phone, is_leader, team_id
      FROM user
      WHERE is_leader = 1 OR team_id IS NOT NULL
      ORDER BY id
    `);
    
    if (leaders && leaders.length > 0) {
      console.table(leaders.map(leader => ({
        用户ID: leader.id,
        昵称: leader.nickname,
        电话: leader.phone,
        是否团长: leader.is_leader === 1 ? '是' : '否',
        团队ID: leader.team_id
      })));
    } else {
      console.log('❌ 没有找到团长用户');
    }
    
    // 5. 特别检查ID为25的申请
    console.log('\n🔍 检查ID为25的申请详情:');
    const app25 = await db.query(`
      SELECT ta.*, u.nickname, u.phone, u.is_leader, u.team_id
      FROM team_apply ta
      LEFT JOIN user u ON ta.user_id = u.id
      WHERE ta.id = 25
    `);
    
    if (app25 && app25.length > 0) {
      const app = app25[0];
      console.log('申请详情:', {
        申请ID: app.id,
        用户ID: app.user_id,
        用户昵称: app.nickname,
        申请状态: app.status === 0 ? '待审核' : app.status === 1 ? '已通过' : '已拒绝',
        用户是否团长: app.is_leader === 1 ? '是' : '否',
        用户团队ID: app.team_id,
        申请时间: app.created_at,
        更新时间: app.updated_at
      });
      
      // 检查是否有对应的团队
      if (app.status === 1) {
        const relatedTeam = await db.query(`
          SELECT * FROM team WHERE leader_id = ?
        `, [app.user_id]);
        
        if (relatedTeam && relatedTeam.length > 0) {
          console.log('✅ 找到对应的团队:', relatedTeam[0]);
        } else {
          console.log('❌ 没有找到对应的团队，可能创建失败');
        }
      }
    } else {
      console.log('❌ 没有找到ID为25的申请');
    }
    
    process.exit(0);
  } catch (error) {
    console.error('❌ 检查失败:', error);
    process.exit(1);
  }
}

checkAllianceTeam();
