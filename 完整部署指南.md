# 🚀 WiFi共享商城完整部署指南

## 📋 部署概览

您的项目包含三个部分：
1. **wifi-share-server** - 后端API服务器 (端口4000)
2. **wifi-share-miniapp** - 微信小程序前端
3. **wifi-share-admin** - 管理后台

## 🛠️ 宝塔面板部署后端服务器

### 第一步：环境准备

1. **安装Node.js**
   - 进入宝塔面板 → 软件商店
   - 搜索并安装 "Node.js" (推荐版本16+)

2. **安装MySQL**
   - 安装 "MySQL 8.0"
   - 设置root密码并记住

3. **安装PM2**
   ```bash
   npm install -g pm2
   ```

### 第二步：上传代码

将 `wifi-share-server` 文件夹上传到：
```
/www/wwwroot/您的域名/wifi-share-server/
```

### 第三步：配置数据库

1. **创建数据库**
   - 数据库名：`mall`
   - 字符集：`utf8mb4`
   - 排序规则：`utf8mb4_unicode_ci`

2. **修改配置文件**
   编辑 `wifi-share-server/.env`：
   ```env
   # 服务器配置
   PORT=4000
   NODE_ENV=production
   
   # 数据库配置
   DB_HOST=localhost
   DB_USER=root
   DB_PASSWORD=您的MySQL密码
   DB_NAME=mall
   DB_PORT=3306
   
   # JWT配置
   JWT_SECRET=wifi_share_secret_key_production
   JWT_EXPIRES_IN=7d
   ```

### 第四步：安装依赖和初始化

```bash
# 进入项目目录
cd /www/wwwroot/您的域名/wifi-share-server

# 安装依赖
npm install

# 初始化数据库
node init-database.js
```

### 第五步：启动服务

```bash
# 使用PM2启动（推荐）
pm2 start server.js --name wifi-share-server

# 查看状态
pm2 status

# 查看日志
pm2 logs wifi-share-server
```

### 第六步：配置防火墙

在宝塔面板 → 安全 → 添加端口规则：
- 端口：4000
- 备注：WiFi共享API服务

### 第七步：验证部署

访问以下地址验证：
```
http://您的服务器IP:4000/api/v1/health
```

如果返回成功响应，说明后端部署成功！

## 📱 配置小程序前端

### 修改API地址

编辑 `wifi-share-miniapp/config/config.js`：

```javascript
module.exports = {
  api: {
    baseUrl: 'http://您的服务器IP:4000',  // 改为您的服务器IP
    clientPath: '/api/v1/client',
    timeout: 10000
  },
  
  apiBaseUrl: 'http://您的服务器IP:4000',  // 改为您的服务器IP
  
  imageServer: {
    baseUrl: 'http://您的服务器IP:4000'    // 改为您的服务器IP
  }
}
```

### 配置域名白名单

在微信公众平台后台配置：
1. 登录微信公众平台
2. 进入小程序管理后台
3. 开发 → 开发管理 → 开发设置
4. 服务器域名配置：
   - request合法域名：`https://您的域名.com`
   - uploadFile合法域名：`https://您的域名.com`
   - downloadFile合法域名：`https://您的域名.com`

## 🔧 常用管理命令

### PM2进程管理
```bash
# 查看所有进程
pm2 list

# 查看特定进程状态
pm2 status wifi-share-server

# 查看实时日志
pm2 logs wifi-share-server

# 重启服务
pm2 restart wifi-share-server

# 停止服务
pm2 stop wifi-share-server

# 删除进程
pm2 delete wifi-share-server

# 开机自启
pm2 startup
pm2 save
```

### 数据库管理
```bash
# 连接数据库
mysql -u root -p

# 查看数据库
SHOW DATABASES;

# 使用mall数据库
USE mall;

# 查看表结构
SHOW TABLES;
```

## 🔍 故障排查

### 1. 服务无法启动
```bash
# 查看详细错误日志
pm2 logs wifi-share-server --lines 50

# 检查端口占用
netstat -tlnp | grep 4000

# 检查数据库连接
node check-db-connection.js
```

### 2. 数据库连接失败
- 检查MySQL服务是否启动
- 验证数据库密码是否正确
- 确认数据库名称是否为`mall`

### 3. 小程序无法连接API
- 检查服务器IP是否正确
- 确认端口4000是否开放
- 验证API地址配置是否正确

## 📊 监控和维护

### 1. 日志监控
```bash
# 查看应用日志
tail -f /www/wwwroot/您的域名/wifi-share-server/logs/combined.log

# 查看错误日志
tail -f /www/wwwroot/您的域名/wifi-share-server/logs/error.log
```

### 2. 性能监控
```bash
# PM2监控面板
pm2 monit

# 系统资源监控
htop
```

### 3. 定期备份
- 定期备份数据库
- 备份代码文件
- 备份配置文件

## ✅ 部署检查清单

- [ ] Node.js已安装
- [ ] MySQL已安装并运行
- [ ] PM2已安装
- [ ] 代码已上传
- [ ] 数据库已创建
- [ ] 配置文件已修改
- [ ] 依赖已安装
- [ ] 数据库已初始化
- [ ] 服务已启动
- [ ] 端口已开放
- [ ] API接口可访问
- [ ] 小程序配置已更新

## 🎉 部署完成

恭喜！您的WiFi共享商城后端服务器已成功部署到宝塔面板。

**访问地址：**
- API服务：`http://您的服务器IP:4000`
- 健康检查：`http://您的服务器IP:4000/api/v1/health`
- 客户端API：`http://您的服务器IP:4000/api/v1/client`

现在您可以在微信开发者工具中测试小程序，确保前后端连接正常！
