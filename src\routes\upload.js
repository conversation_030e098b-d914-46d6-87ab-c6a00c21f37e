const express = require('express');
const router = express.Router();
const uploadController = require('../controllers/upload');
const authMiddleware = require('../middlewares/auth');

// 管理员单文件上传
router.post('/', 
  authMiddleware.adminAuth, 
  uploadController.uploadMiddleware, 
  uploadController.uploadSingle
);

// 管理员多文件上传
router.post('/multiple', 
  authMiddleware.adminAuth, 
  uploadController.uploadMultipleMiddleware, 
  uploadController.uploadMultiple
);

// 删除文件
router.delete('/:filename', 
  authMiddleware.adminAuth, 
  uploadController.deleteFile
);

module.exports = router; 