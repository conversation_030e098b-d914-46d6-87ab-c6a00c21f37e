// pages/mall/order/payment/payment.js
const app = getApp()
const API = require('../../../../config/api')
const { request } = require('../../../../utils/request')
const { showToast, showModal } = require('../../../../utils/util')

Page({
  /**
   * 页面的初始数据
   */
  data: {
    orderId: null,
    orderInfo: null,
    paymentAmount: 0,
    paymentMethod: 'wechat',
    loading: true,
    paying: false,
    isLoggedIn: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    console.log('💰 支付页面接收到的参数:', options)

    // 支持两种参数格式：id 和 orderId
    const orderId = options.id || options.orderId

    if (orderId) {
      this.setData({
        orderId: orderId,
        paymentAmount: options.amount || 0,
        isLoggedIn: app.globalData.isLoggedIn
      })

      console.log('💰 设置订单ID:', orderId)
      this.fetchOrderInfo(orderId)
    } else {
      console.error('💰 订单ID不存在，接收到的参数:', options)
      showToast('订单ID不存在')
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
    }
  },

  /**
   * 获取订单信息
   */
  fetchOrderInfo: function (orderId) {
    console.log('💰 开始获取订单信息，订单ID:', orderId)
    this.setData({ loading: true })

    const url = `${API.order.detail}/${orderId}`
    console.log('💰 请求订单详情URL:', url)

    request({
      url: url,
      method: 'GET'
    }).then(res => {
      console.log('💰 支付页面订单信息响应:', res)
      // 兼容不同的响应格式
      if ((res.code === 0 || res.code === 200 || res.success === true) && res.data) {
        const orderData = res.data
        this.setData({
          orderInfo: orderData,
          paymentAmount: parseFloat(orderData.total_amount || orderData.totalAmount || 0),
          loading: false
        })
        console.log('✅ 订单信息获取成功:', orderData)
        console.log('💰 设置支付金额:', parseFloat(orderData.total_amount || orderData.totalAmount || 0))
      } else {
        console.error('❌ 订单信息响应格式错误:', res)
        showToast('获取订单信息失败')
        this.setData({ loading: false })
      }
    }).catch(err => {
      console.error('❌ 获取订单信息失败:', err)
      showToast('获取订单信息失败')
      this.setData({ loading: false })
    })
  },

  /**
   * 选择支付方式
   */
  onSelectPayment: function (e) {
    this.setData({
      paymentMethod: e.currentTarget.dataset.method
    })
  },

  /**
   * 确认支付
   */
  onConfirmPayment: function () {
    if (this.data.paying) return
    
    this.setData({ paying: true })
    
    request({
      url: API.payment.create,
      method: 'POST',
      data: {
        orderId: this.data.orderId,
        paymentMethod: this.data.paymentMethod
      }
    }).then(res => {
      console.log('💰 支付创建响应:', res)
      if ((res.code === 0 || res.code === 200 || res.success === true) && res.data) {
        // 微信支付
        if (this.data.paymentMethod === 'wechat') {
          const payParams = res.data
          console.log('💰 支付参数:', payParams)

          // 检查是否为真实支付
          if (payParams.isReal === false || payParams.paySign.startsWith('mock_')) {
            // 模拟支付流程
            console.log('💰 使用模拟支付')
            wx.showModal({
              title: '模拟支付',
              content: `订单金额：¥${payParams.amount}\n\n注意：这是模拟支付，实际不会扣费\n\n是否确认支付？`,
              confirmText: '确认支付',
              cancelText: '取消',
              success: (modalRes) => {
                if (modalRes.confirm) {
                  // 模拟支付成功
                  console.log('💰 模拟支付确认')
                  this.handlePaymentSuccess()
                } else {
                  console.log('💰 模拟支付取消')
                  showToast('支付已取消')
                }
                this.setData({ paying: false })
              }
            })
          } else {
            // 真实微信支付
            console.log('💰 使用真实微信支付')
            wx.requestPayment({
              timeStamp: payParams.timeStamp,
              nonceStr: payParams.nonceStr,
              package: payParams.package,
              signType: payParams.signType || 'RSA',
              paySign: payParams.paySign,
              success: () => {
                console.log('💰 微信支付成功')
                this.handlePaymentSuccess()
              },
              fail: (err) => {
                console.error('💰 微信支付失败:', err)
                if (err.errMsg && err.errMsg.includes('cancel')) {
                  showToast('支付已取消')
                } else {
                  showToast('支付失败: ' + (err.errMsg || '未知错误'))
                }
              },
              complete: () => {
                this.setData({ paying: false })
              }
            })
          }
        } else {
          // 其他支付方式
          showToast('暂不支持此支付方式')
          this.setData({ paying: false })
        }
      } else {
        showToast('创建支付失败')
        this.setData({ paying: false })
      }
    }).catch(err => {
      console.error('创建支付失败', err)
      showToast('创建支付失败')
      this.setData({ paying: false })
    })
  },

  /**
   * 处理支付成功
   */
  handlePaymentSuccess: function () {
    showToast('支付成功')

    // 调用支付成功回调API，更新订单状态
    this.updateOrderStatus()
  },

  /**
   * 更新订单状态为已支付
   */
  updateOrderStatus: function () {
    const { request } = require('../../../../utils/request')
    const API = require('../../../../config/api')

    console.log('💰 开始更新订单状态，订单ID:', this.data.orderId)

    const url = `${API.order.paymentSuccess}/${this.data.orderId}`
    console.log('💰 支付成功回调URL:', url)

    request({
      url: url,
      method: 'POST',
      data: {}
    }).then(res => {
      console.log('💰 支付成功回调响应:', res)
      if ((res.code === 0 || res.code === 200 || res.success === true)) {
        console.log('💰 订单状态更新成功')
        showToast('支付成功')
        // 跳转到订单列表的待发货页面
        this.navigateToOrderList('pending')
      } else {
        console.error('💰 支付回调失败:', res)
        showToast('支付成功，但状态更新失败')
        this.navigateToOrderList('pending')
      }
    }).catch(err => {
      console.error('💰 支付回调请求失败:', err)

      // 即使回调失败，支付也是成功的，只是状态更新失败
      showToast('支付成功，请手动刷新订单列表')
      this.navigateToOrderList('pending')
    })
  },

  /**
   * 跳转到订单列表
   * @param {string} status - 订单状态 ('all', 'pending', 'shipped', 'completed', 'cancelled')
   */
  navigateToOrderList: function (status = 'all') {
    let url = '/pages/mall/order/list/list'

    // 根据状态名称映射到对应的状态码
    const statusMap = {
      'all': -1,
      'pending': 1,    // 待发货
      'shipped': 2,    // 待收货
      'completed': 3,  // 已完成
      'cancelled': 4   // 已取消
    }

    if (status && status !== 'all' && statusMap[status] !== undefined) {
      url += `?status=${statusMap[status]}`
    }

    console.log('💰 跳转到订单列表:', url)
    wx.redirectTo({
      url: url
    })
  },

  /**
   * 取消支付
   */
  onCancelPayment: function () {
    showModal({
      title: '提示',
      content: '确定取消支付吗？',
      success: (res) => {
        if (res.confirm) {
          this.navigateToOrderList()
        }
      }
    })
  },

  /**
   * 广告加载成功
   */
  onAdLoad: function () {
    console.log('广告加载成功')
  },

  /**
   * 广告加载失败
   */
  onAdError: function (err) {
    console.error('广告加载失败', err)
  }
}) 