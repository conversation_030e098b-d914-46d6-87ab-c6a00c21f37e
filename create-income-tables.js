const mysql = require('mysql2/promise');

async function createIncomeTables() {
  let connection;
  
  try {
    // 连接数据库
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: 'wo587129955',
      database: 'mall'
    });
    
    console.log('🔧 开始创建收入管理表...\n');
    
    // 1. 创建收入记录表
    console.log('1️⃣ 创建收入记录表...');
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS income_record (
        id int(11) NOT NULL AUTO_INCREMENT COMMENT '收入记录ID',
        user_id int(11) NOT NULL COMMENT '用户ID',
        team_id int(11) DEFAULT NULL COMMENT '团队ID',
        amount decimal(10,2) NOT NULL COMMENT '收入金额',
        type varchar(20) NOT NULL COMMENT '收入类型',
        source_type tinyint(1) NOT NULL COMMENT '来源类型',
        source_id int(11) DEFAULT NULL COMMENT '来源ID',
        order_no varchar(50) DEFAULT NULL COMMENT '关联订单号',
        title varchar(100) NOT NULL COMMENT '收入标题',
        description varchar(255) DEFAULT NULL COMMENT '收入描述',
        status tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态',
        settle_status tinyint(1) NOT NULL DEFAULT '0' COMMENT '结算状态',
        settle_time datetime DEFAULT NULL COMMENT '结算时间',
        created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        updated_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
        PRIMARY KEY (id),
        KEY user_id (user_id),
        KEY team_id (team_id),
        KEY type (type),
        KEY source_type (source_type),
        KEY status (status),
        KEY created_at (created_at)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='收入记录表'
    `);
    console.log('✅ 收入记录表创建成功');
    
    // 2. 创建提现记录表
    console.log('\n2️⃣ 创建提现记录表...');
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS withdraw_record (
        id int(11) NOT NULL AUTO_INCREMENT COMMENT '提现记录ID',
        user_id int(11) NOT NULL COMMENT '用户ID',
        amount decimal(10,2) NOT NULL COMMENT '提现金额',
        fee decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '手续费',
        actual_amount decimal(10,2) NOT NULL COMMENT '实际到账金额',
        withdraw_type varchar(20) NOT NULL DEFAULT 'wechat' COMMENT '提现方式',
        account_info text DEFAULT NULL COMMENT '账户信息',
        status tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态',
        audit_user_id int(11) DEFAULT NULL COMMENT '审核人ID',
        audit_time datetime DEFAULT NULL COMMENT '审核时间',
        audit_remark varchar(255) DEFAULT NULL COMMENT '审核备注',
        process_time datetime DEFAULT NULL COMMENT '处理时间',
        complete_time datetime DEFAULT NULL COMMENT '完成时间',
        transaction_no varchar(50) DEFAULT NULL COMMENT '交易流水号',
        remark varchar(255) DEFAULT NULL COMMENT '备注',
        created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        updated_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
        PRIMARY KEY (id),
        KEY user_id (user_id),
        KEY status (status),
        KEY created_at (created_at)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='提现记录表'
    `);
    console.log('✅ 提现记录表创建成功');
    
    // 3. 创建钱包交易记录表
    console.log('\n3️⃣ 创建钱包交易记录表...');
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS wallet_transaction (
        id int(11) NOT NULL AUTO_INCREMENT COMMENT '交易记录ID',
        user_id int(11) NOT NULL COMMENT '用户ID',
        type varchar(20) NOT NULL COMMENT '交易类型',
        amount decimal(10,2) NOT NULL COMMENT '交易金额',
        balance_before decimal(10,2) NOT NULL COMMENT '交易前余额',
        balance_after decimal(10,2) NOT NULL COMMENT '交易后余额',
        related_type varchar(20) DEFAULT NULL COMMENT '关联类型',
        related_id int(11) DEFAULT NULL COMMENT '关联记录ID',
        title varchar(100) NOT NULL COMMENT '交易标题',
        description varchar(255) DEFAULT NULL COMMENT '交易描述',
        transaction_no varchar(50) DEFAULT NULL COMMENT '交易流水号',
        status tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态',
        created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        updated_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
        PRIMARY KEY (id),
        KEY user_id (user_id),
        KEY type (type),
        KEY related_type_id (related_type, related_id),
        KEY created_at (created_at)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='钱包交易记录表'
    `);
    console.log('✅ 钱包交易记录表创建成功');
    
    // 4. 创建收入统计表
    console.log('\n4️⃣ 创建收入统计表...');
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS income_statistics (
        id int(11) NOT NULL AUTO_INCREMENT COMMENT '统计ID',
        user_id int(11) NOT NULL COMMENT '用户ID',
        team_id int(11) DEFAULT NULL COMMENT '团队ID',
        stat_date date NOT NULL COMMENT '统计日期',
        stat_type varchar(10) NOT NULL COMMENT '统计类型',
        total_income decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '总收入',
        wifi_income decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT 'WiFi收入',
        goods_income decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '商品收入',
        ad_income decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '广告收入',
        referral_income decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '推荐收入',
        bonus_income decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '奖励收入',
        withdraw_amount decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '提现金额',
        transaction_count int(11) NOT NULL DEFAULT '0' COMMENT '交易笔数',
        created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        updated_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
        PRIMARY KEY (id),
        UNIQUE KEY user_date_type (user_id, stat_date, stat_type),
        KEY team_id (team_id),
        KEY stat_date (stat_date),
        KEY stat_type (stat_type)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='收入统计表'
    `);
    console.log('✅ 收入统计表创建成功');
    
    // 5. 更新用户表，添加收入字段
    console.log('\n5️⃣ 更新用户表收入字段...');
    
    // 检查字段是否存在，如果不存在则添加
    const fieldsToAdd = [
      { name: 'total_income', type: 'decimal(10,2)', default: '0.00', comment: '总收入' },
      { name: 'today_income', type: 'decimal(10,2)', default: '0.00', comment: '今日收入' },
      { name: 'month_income', type: 'decimal(10,2)', default: '0.00', comment: '本月收入' },
      { name: 'total_withdraw', type: 'decimal(10,2)', default: '0.00', comment: '总提现金额' }
    ];
    
    for (const field of fieldsToAdd) {
      try {
        const [columns] = await connection.execute(`
          SELECT COUNT(*) as count 
          FROM information_schema.columns 
          WHERE table_schema = 'mall' 
          AND table_name = 'user' 
          AND column_name = ?
        `, [field.name]);
        
        if (columns[0].count === 0) {
          await connection.execute(`
            ALTER TABLE user 
            ADD COLUMN ${field.name} ${field.type} NOT NULL DEFAULT '${field.default}' COMMENT '${field.comment}' AFTER balance
          `);
          console.log(`✅ 添加字段 ${field.name}`);
        } else {
          console.log(`✅ 字段 ${field.name} 已存在`);
        }
      } catch (error) {
        console.log(`⚠️  字段 ${field.name} 添加失败: ${error.message}`);
      }
    }
    
    // 6. 插入测试数据
    console.log('\n6️⃣ 插入测试数据...');
    
    // 插入收入记录
    await connection.execute(`
      INSERT IGNORE INTO income_record (user_id, team_id, amount, type, source_type, title, description, status, settle_status) VALUES
      (1, 1, 15.50, 'wifi_share', 1, 'WiFi分享收入', '用户使用WiFi产生的分润收入', 1, 1),
      (1, 1, 8.30, 'advertisement', 3, '广告点击收入', '用户点击广告产生的收入', 1, 1),
      (1, 1, 25.00, 'referral', 4, '推荐奖励', '成功推荐新用户获得的奖励', 1, 1),
      (1, 1, 12.80, 'wifi_share', 1, 'WiFi分享收入', '用户使用WiFi产生的分润收入', 1, 1),
      (1, 1, 5.20, 'advertisement', 3, '广告点击收入', '用户点击广告产生的收入', 1, 1)
    `);
    
    // 插入钱包交易记录
    await connection.execute(`
      INSERT IGNORE INTO wallet_transaction (user_id, type, amount, balance_before, balance_after, related_type, related_id, title, description) VALUES
      (1, 'income', 15.50, 0.00, 15.50, 'income_record', 1, 'WiFi分享收入', '用户使用WiFi产生的分润收入'),
      (1, 'income', 8.30, 15.50, 23.80, 'income_record', 2, '广告点击收入', '用户点击广告产生的收入'),
      (1, 'income', 25.00, 23.80, 48.80, 'income_record', 3, '推荐奖励', '成功推荐新用户获得的奖励'),
      (1, 'income', 12.80, 48.80, 61.60, 'income_record', 4, 'WiFi分享收入', '用户使用WiFi产生的分润收入'),
      (1, 'income', 5.20, 61.60, 66.80, 'income_record', 5, '广告点击收入', '用户点击广告产生的收入')
    `);
    
    // 更新用户余额
    await connection.execute(`
      UPDATE user SET 
        balance = 66.80,
        total_income = 66.80,
        today_income = 18.00,
        month_income = 66.80
      WHERE id = 1
    `);
    
    // 插入统计数据
    await connection.execute(`
      INSERT INTO income_statistics (user_id, team_id, stat_date, stat_type, total_income, wifi_income, ad_income, referral_income, transaction_count) VALUES
      (1, 1, CURDATE(), 'daily', 18.00, 12.80, 5.20, 0.00, 2),
      (1, 1, DATE_FORMAT(CURDATE(), '%Y-%m-01'), 'monthly', 66.80, 28.30, 13.50, 25.00, 5)
      ON DUPLICATE KEY UPDATE 
        total_income = VALUES(total_income),
        wifi_income = VALUES(wifi_income),
        ad_income = VALUES(ad_income),
        referral_income = VALUES(referral_income),
        transaction_count = VALUES(transaction_count),
        updated_at = CURRENT_TIMESTAMP
    `);
    
    console.log('✅ 测试数据插入成功');
    
    // 7. 验证创建结果
    console.log('\n7️⃣ 验证创建结果...');
    const [tables] = await connection.execute(`
      SELECT 
        table_name as '表名',
        table_comment as '表说明'
      FROM information_schema.tables 
      WHERE table_schema = 'mall' 
      AND table_name IN ('income_record', 'withdraw_record', 'wallet_transaction', 'income_statistics')
      ORDER BY table_name
    `);
    
    console.table(tables);
    
    // 检查数据
    const [incomeCount] = await connection.execute('SELECT COUNT(*) as count FROM income_record');
    const [transactionCount] = await connection.execute('SELECT COUNT(*) as count FROM wallet_transaction');
    const [statsCount] = await connection.execute('SELECT COUNT(*) as count FROM income_statistics');
    
    console.log('\n📊 数据统计:');
    console.log(`- 收入记录: ${incomeCount[0].count} 条`);
    console.log(`- 钱包交易: ${transactionCount[0].count} 条`);
    console.log(`- 收入统计: ${statsCount[0].count} 条`);
    
    console.log('\n🎉 收入管理表创建完成！');
    
  } catch (error) {
    console.error('❌ 创建失败:', error.message);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// 运行创建
createIncomeTables();
