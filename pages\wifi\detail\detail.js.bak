// pages/wifi/detail/detail.js
// WiFi码详情页面

const request = require('../../../utils/request.js')
const util = require('../../../utils/util.js')
const API = require('../../../config/api.js')
const config = require('../../../config/config.js') // 引入配置文件
const QR = require('../../../utils/weapp-qrcode.js') // 引入二维码生成库

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // WiFi码ID
    wifiId: '',
    
    // WiFi码详细信息
    wifiInfo: {
      id: '',
      title: '',
      ssid: '',
      password: '',
      merchantName: '',
      status: 'active',
      qrcodeUrl: '',
      createTime: '',
      updateTime: ''
    },
    
    // 统计数据
    statsData: {
      scanCount: 0,         // 扫码次数
      connectCount: 0,      // 连接次数
      todayCount: 0,        // 今日连接
      totalEarnings: 0,     // 总收益
      todayEarnings: 0,     // 今日收益
      weeklyStats: [],      // 最近7天统计数据
      monthlyStats: []      // 最近30天统计数据
    },
    
    // 推荐商品
    recommendGoods: [],
    
    // 加载状态
    loading: true,
    showPassword: false,
    showDetailedStats: false,  // 是否显示详细统计
    
    // 广告数据
    adData: {
      title: '推广位招租',
      subtitle: '点击了解详情',
      image: '/assets/images/ad-placeholder.jpg',
      link: ''
    },
    
    // 分享设置
    shareSettings: {
      showAdInPoster: true,   // 在分享海报中显示广告
      customText: '',         // 自定义分享文字
      posterBackground: 0     // 海报背景选择，0表示默认
    },
    
    // 错误状态
    hasError: false,
    errorMessage: ''
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    console.log('WiFi详情页面加载，参数:', options)
    
    const { id } = options
    if (!id) {
      this.setData({
        hasError: true,
        errorMessage: 'WiFi码ID不能为空',
        loading: false
      })
      
      wx.showToast({
        title: 'WiFi码ID不能为空',
        icon: 'none'
      })
      
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
      return
    }
    
    this.setData({ wifiId: id })
    console.log('设置wifiId:', id)
    
    // 加载所有数据
    this.loadWifiDetail()
    this.loadDetailedStats()
    this.loadRecommendGoods()
    this.loadAdData()
    this.loadShareSettings()
  },

  /**
   * 加载WiFi码详情
   */
  async loadWifiDetail() {
    try {
      wx.showLoading({
        title: '加载中...'
      })

      // 使用API配置中的路径
      const url = `${API.wifi.detail}/${this.data.wifiId}`
      console.log('请求WiFi详情URL:', url)
      
      const result = await request.get(url, {}, { showError: false })
      
      wx.hideLoading()
      console.log('WiFi详情原始数据:', result)

      if (result && (result.success || result.status === 'success')) {
        let wifiInfo = result.data || {}
        
        // 详细调试每个字段
        console.log('WiFi详情字段检查:')
        console.log('- id:', wifiInfo.id)
        console.log('- title:', wifiInfo.title)
        console.log('- ssid/name:', wifiInfo.ssid || wifiInfo.name)
        console.log('- password:', wifiInfo.password)
        console.log('- merchantName/merchant_name:', wifiInfo.merchantName || wifiInfo.merchant_name)
        console.log('- qrcodeUrl/qrcode_url/qrcode:', wifiInfo.qrcodeUrl || wifiInfo.qrcode_url || wifiInfo.qrcode)
        
        // 检查字段名称并确保正确映射
        // 后端可能使用name而不是ssid，确保兼容
        if (wifiInfo.name && !wifiInfo.ssid) {
          console.log('使用name字段作为ssid')
          wifiInfo.ssid = wifiInfo.name
        }
        
        // 确保商户名称字段正确
        if (wifiInfo.merchant_name && !wifiInfo.merchantName) {
          console.log('使用merchant_name字段作为merchantName')
          wifiInfo.merchantName = wifiInfo.merchant_name
        }
        
        // 确保二维码URL字段正确
        if ((wifiInfo.qrcode_url || wifiInfo.qrcode) && !wifiInfo.qrcodeUrl) {
          console.log('使用qrcode_url或qrcode字段作为qrcodeUrl')
          wifiInfo.qrcodeUrl = wifiInfo.qrcode_url || wifiInfo.qrcode
        }
        
        // 格式化时间
        if (wifiInfo.createTime) {
          wifiInfo.createTime = util.formatTime(new Date(wifiInfo.createTime))
        } else if (wifiInfo.create_time || wifiInfo.created_at) {
          const timeValue = wifiInfo.create_time || wifiInfo.created_at
          console.log('使用create_time或created_at字段作为createTime:', timeValue)
          wifiInfo.createTime = util.formatTime(new Date(timeValue))
        }
        
        if (wifiInfo.updateTime) {
          wifiInfo.updateTime = util.formatTime(new Date(wifiInfo.updateTime))
        } else if (wifiInfo.update_time || wifiInfo.updated_at) {
          const timeValue = wifiInfo.update_time || wifiInfo.updated_at
          console.log('使用update_time或updated_at字段作为updateTime:', timeValue)
          wifiInfo.updateTime = util.formatTime(new Date(timeValue))
        }
        
        // 确保状态字段正确
        if (wifiInfo.status === undefined && wifiInfo.active !== undefined) {
          wifiInfo.status = wifiInfo.active ? 'active' : 'inactive'
        } else if (typeof wifiInfo.status === 'number') {
          wifiInfo.status = wifiInfo.status === 1 ? 'active' : 'inactive'
        }
        
        // 打印WiFi信息以便调试
        console.log('处理后的WiFi详情数据:', wifiInfo)
        
        this.setData({
          wifiInfo: wifiInfo,
          statsData: {
            ...this.data.statsData,
            ...wifiInfo.stats
          },
          loading: false,
          hasError: false
        })
        
        // 如果没有二维码，生成二维码
        if (!wifiInfo.qrcodeUrl) {
          console.log('没有二维码URL，开始生成二维码')
          // 先设置二维码为空，然后延迟一下再生成，确保页面已经渲染完毕
          setTimeout(() => {
            this.directGenerateQRCode()
          }, 300)
        }
      } else {
        console.error('加载WiFi详情失败:', result)
        this.handleDetailError()
      }
    } catch (error) {
      wx.hideLoading()
      console.error('加载WiFi码详情失败:', error)
      this.handleDetailError()
    }
  },
  
  /**
   * 处理详情加载错误
   */
  handleDetailError() {
    console.log('处理详情加载错误，创建默认数据')
    
    // 创建默认数据
    const defaultInfo = {
      id: this.data.wifiId,
      title: `WiFi码 ${this.data.wifiId}`,
      ssid: 'WiFi名称示例',
      password: '12345678',
      merchantName: '示例商户',
      status: 'active',
      createTime: util.formatTime(new Date()),
      updateTime: util.formatTime(new Date())
    }
    
    console.log('默认WiFi数据:', defaultInfo)
    
    this.setData({
      wifiInfo: defaultInfo,
      loading: false,
      hasError: true,
      errorMessage: '无法加载WiFi码详情，显示默认数据'
    })
    
    // 生成本地二维码
    setTimeout(() => {
      this.directGenerateQRCode()
    }, 300)
    
    wx.showToast({
      title: '加载失败，显示默认数据',
      icon: 'none',
      duration: 2000
    })
  },

  /**
   * 直接生成二维码（使用weapp-qrcode库）
   */
  directGenerateQRCode() {
    try {
      console.log('开始生成真实二维码')
      const { ssid, password } = this.data.wifiInfo
      
      if (!ssid || !password) {
        console.error('WiFi名称或密码为空，无法生成二维码')
        return
      }
      
      // 创建WiFi连接URL (标准WiFi二维码格式)
      const wifiUrl = `WIFI:T:WPA;S:${ssid};P:${password};H:false;;`
      console.log('生成WiFi二维码数据:', wifiUrl)

      // 使用二维码库生成
      const qrcode = new QR('qrcodeCanvas', {
        // usingIn: this,
        text: wifiUrl,
        width: 280,
        height: 280,
        colorDark: "#000000",
        colorLight: "#ffffff",
        correctLevel: QR.CorrectLevel.H, // 高容错率
        image: {
          imageResource: '', // 可选的中心Logo
          dx: 70,
          dy: 70,
          dWidth: 60,
          dHeight: 60
        }
      });
      
      console.log('二维码对象创建成功，准备绘制');
      
      // 开始绘制二维码
      qrcode.makeCode(wifiUrl);
      
      // 等待绘制完成，转换成图片
      setTimeout(() => {
        console.log('二维码绘制完成，准备转为图片')
        wx.canvasToTempFilePath({
          canvasId: 'qrcodeCanvas',
          success: (res) => {
            console.log('Canvas转为图片成功:', res.tempFilePath)
            this.setData({
              'wifiInfo.qrcodeUrl': res.tempFilePath
            })
            
            this.uploadQRCodeToServer(res.tempFilePath)
          },
          fail: (err) => {
            console.error('Canvas转为图片失败:', err)
            // 设置默认占位图像
            this.setData({
              'wifiInfo.qrcodeUrl': '/assets/icons/qrcode.png'
            })
          }
        }, this)
      }, 500);
    } catch (error) {
      console.error('二维码生成失败:', error)
      // 设置默认占位图像
      this.setData({
        'wifiInfo.qrcodeUrl': '/assets/icons/qrcode.png'
      })
    }
  },
  
  /**
   * 上传二维码到服务器
   */
  async uploadQRCodeToServer(qrcodeUrl) {
    try {
      console.log('开始上传二维码到服务器:', qrcodeUrl)
      const uploadUrl = API.upload.image
      
      const uploadResult = await new Promise((resolve, reject) => {
        wx.uploadFile({
          url: uploadUrl,
          filePath: qrcodeUrl,
          name: 'file',
          formData: {
            type: 'qrcode',
            wifiId: this.data.wifiId
          },
          success: (res) => {
            console.log('上传成功，原始结果:', res)
            let result = res.data
            
            if (typeof result === 'string') {
              try {
                result = JSON.parse(result)
              } catch (e) {
                console.error('解析上传结果失败:', e)
                reject(e)
                return
              }
            }
            
            resolve(result)
          },
          fail: (err) => {
            console.error('上传失败:', err)
            reject(err)
          }
        })
      })
      
      console.log('上传结果:', uploadResult)
      
      if (uploadResult && (uploadResult.success || uploadResult.code === 200)) {
        const serverUrl = uploadResult.data && uploadResult.data.url
        
        if (serverUrl) {
          console.log('服务器返回的图片URL:', serverUrl)
          
          // 更新WiFi记录的二维码URL
          const updateUrl = `${API.wifi.update}/${this.data.wifiId}`
          await request.put(updateUrl, {
            qrcodeUrl: serverUrl
          })
          
          console.log('更新WiFi记录二维码URL成功')
        }
      }
    } catch (error) {
      console.error('上传二维码到服务器失败:', error)
    }
  },

  /**
   * 加载详细统计数据
   */
  async loadDetailedStats() {
    try {
      // 使用API配置中的路径
      const url = `${API.wifi.statsDetail}/${this.data.wifiId}`
      const result = await request.get(url, {}, { showError: false })
      
      if (result.success) {
        this.setData({
          'statsData.weeklyStats': result.data.weeklyStats || [],
          'statsData.monthlyStats': result.data.monthlyStats || []
        })
      } else {
        console.error('加载详细统计数据失败:', result)
        this.generateMockStatsData()
      }
    } catch (error) {
      console.error('加载详细统计数据失败:', error)
      this.generateMockStatsData()
    }
  },
  
  /**
   * 生成模拟统计数据
   */
  generateMockStatsData() {
    // 生成过去7天的日期
    const weeklyStats = []
    const now = new Date()
    
    for (let i = 6; i >= 0; i--) {
      const date = new Date(now)
      date.setDate(date.getDate() - i)
      
      weeklyStats.push({
        date: util.formatDate(date, 'MM-DD'),
        scanCount: Math.floor(Math.random() * 10),
        connectCount: Math.floor(Math.random() * 8),
        earnings: (Math.random() * 2).toFixed(2)
      })
    }
    
    // 更新统计数据
    this.setData({
      'statsData.weeklyStats': weeklyStats,
      'statsData.scanCount': weeklyStats.reduce((sum, item) => sum + item.scanCount, 0),
      'statsData.connectCount': weeklyStats.reduce((sum, item) => sum + item.connectCount, 0),
      'statsData.todayCount': weeklyStats[6].connectCount,
      'statsData.totalEarnings': weeklyStats.reduce((sum, item) => sum + parseFloat(item.earnings), 0).toFixed(2),
      'statsData.todayEarnings': weeklyStats[6].earnings
    })
  },

  /**
   * 加载广告数据
   */
  async loadAdData() {
    try {
      const result = await request.get(API.ads.wifiDetail, {}, { showError: false })
      
      if (result.success && result.data) {
        this.setData({
          adData: result.data
        })
      } else {
        console.error('加载广告数据失败:', result)
        this.setDefaultAdData()
      }
    } catch (error) {
      console.error('加载广告数据失败:', error)
      this.setDefaultAdData()
    }
  },
  
  /**
   * 设置默认广告数据
   */
  setDefaultAdData() {
    this.setData({
      adData: {
        title: 'WiFi共享商城 - 限时优惠',
        subtitle: '点击查看详情',
        image: '/assets/images/ad-banner.jpg',
        link: '/pages/mall/home/<USER>'
      }
    })
  },
  
  /**
   * 加载分享设置
   */
  async loadShareSettings() {
    try {
      const url = `${API.wifi.shareSettings}/${this.data.wifiId}`
      const result = await request.get(url, {}, { showError: false })
      
      if (result.success && result.data) {
        this.setData({
          shareSettings: {
            ...this.data.shareSettings,
            ...result.data
          }
        })
      }
    } catch (error) {
      console.error('加载分享设置失败:', error)
    }
  },

  /**
   * 生成二维码
   */
  async generateQRCode() {
    try {
      const url = `${API.wifi.qrcode}/${this.data.wifiId}`
      console.log('生成二维码请求URL:', url)
      
      const result = await request.post(url, {}, { showError: false })
      console.log('生成二维码返回结果:', result)
      
      if (result.success || result.status === 'success') {
        // 检查返回字段
        const qrcodeUrl = result.data.qrcodeUrl || result.data.qrcode_url || result.data.qrcode
        
        if (qrcodeUrl) {
          console.log('获取到二维码URL:', qrcodeUrl)
          
          this.setData({
            'wifiInfo.qrcodeUrl': qrcodeUrl
          })
          
          // 保存二维码URL到服务器
          this.saveQrCodeUrlToServer(qrcodeUrl)
        } else {
          console.error('二维码URL字段不存在:', result.data)
          // 如果服务器生成失败，尝试本地生成
          this.generateLocalQRCode()
        }
      } else {
        console.error('生成二维码失败:', result)
        // 如果服务器生成失败，尝试本地生成
        this.generateLocalQRCode()
      }
    } catch (error) {
      console.error('生成二维码失败:', error)
      // 如果服务器生成失败，尝试本地生成
      this.generateLocalQRCode()
    }
  },
  
  /**
   * 保存二维码URL到服务器
   */
  async saveQrCodeUrlToServer(qrcodeUrl) {
    try {
      const url = `${API.wifi.update}/${this.data.wifiId}`
      await request.put(url, {
        qrcodeUrl: qrcodeUrl
      }, { showError: false })
      
      console.log('二维码URL已保存到服务器')
    } catch (error) {
      console.error('保存二维码URL到服务器失败:', error)
    }
  },
  
  /**
   * 本地生成二维码（备用方案）
   */
  generateLocalQRCode() {
    const { ssid, password } = this.data.wifiInfo
    if (!ssid || !password) {
      console.error('WiFi名称或密码为空，无法生成二维码')
      return
    }
    
    console.log('开始本地生成二维码', ssid, password)
    
    // 使用组件生成二维码
    setTimeout(() => {
      const qrcodeComponent = this.selectComponent('#qrcode')
      if (qrcodeComponent) {
        console.log('找到二维码组件，开始生成')
        
        // 监听二维码生成完成事件
        qrcodeComponent.triggerEvent = (eventName, data) => {
          console.log('二维码组件事件:', eventName, data)
          if (eventName === 'generated' && data && data.qrCodeUrl) {
            console.log('二维码生成完成，URL:', data.qrCodeUrl)
            
            // 更新本地显示
            this.setData({
              'wifiInfo.qrcodeUrl': data.qrCodeUrl
            })
            
            // 上传本地生成的二维码到服务器
            this.uploadLocalQrCodeToServer(data.qrCodeUrl)
          }
        }
        
        // 生成二维码
        qrcodeComponent.generateQRCode({
          ssid,
          password,
          merchantName: this.data.wifiInfo.merchantName
        })
        
        // 延迟获取生成的二维码URL
        setTimeout(() => {
          if (qrcodeComponent.getQrCodeUrl) {
            const localQrCodeUrl = qrcodeComponent.getQrCodeUrl()
            console.log('获取到本地生成的二维码URL:', localQrCodeUrl)
            
            if (localQrCodeUrl) {
              // 更新本地显示
              this.setData({
                'wifiInfo.qrcodeUrl': localQrCodeUrl
              })
              
              // 上传本地生成的二维码到服务器
              this.uploadLocalQrCodeToServer(localQrCodeUrl)
            } else {
              console.error('本地生成的二维码URL为空')
            }
          } else {
            console.error('二维码组件没有getQrCodeUrl方法')
          }
        }, 1000) // 等待1秒，确保二维码已生成
      } else {
        console.error('找不到二维码组件')
      }
    }, 500)
  },
  
  /**
   * 上传本地生成的二维码到服务器
   */
  async uploadLocalQrCodeToServer(localQrCodeUrl) {
    try {
      // 先上传图片到服务器
      const uploadResult = await request.upload(API.upload.image, localQrCodeUrl, {
        type: 'qrcode',
        wifiId: this.data.wifiId
      })
      
      if (uploadResult.success && uploadResult.data.url) {
        // 更新WiFi码的二维码URL
        this.saveQrCodeUrlToServer(uploadResult.data.url)
        
        this.setData({
          'wifiInfo.qrcodeUrl': uploadResult.data.url
        })
      }
    } catch (error) {
      console.error('上传本地二维码到服务器失败:', error)
    }
  },
  
  /**
   * 加载推荐商品
   */
  async loadRecommendGoods() {
    try {
      const result = await request.get(API.goods.recommend, {
        limit: 3
      }, { showError: false })
      
      if (result.success) {
        this.setData({
          recommendGoods: result.data.list || []
        })
      } else {
        console.error('加载推荐商品失败:', result)
        this.setDefaultRecommendGoods()
      }
    } catch (error) {
      console.error('加载推荐商品失败:', error)
      this.setDefaultRecommendGoods()
    }
  },
  
  /**
   * 设置默认推荐商品
   */
  setDefaultRecommendGoods() {
    this.setData({
      recommendGoods: [
        {
          id: '1',
          name: '高速WiFi路由器',
          price: '199.00',
          image: '/assets/images/goods1.jpg'
        },
        {
          id: '2',
          name: 'WiFi信号增强器',
          price: '89.00',
          image: '/assets/images/goods2.jpg'
        },
        {
          id: '3',
          name: '便携式WiFi分享器',
          price: '129.00',
          image: '/assets/images/goods3.jpg'
        }
      ]
    })
  },
  
  /**
   * 预览二维码
   */
  onPreviewQRCode() {
    console.log('预览二维码:', this.data.wifiInfo.qrcodeUrl)
    
    if (this.data.wifiInfo.qrcodeUrl) {
      // 处理相对路径
      let imageUrl = this.data.wifiInfo.qrcodeUrl
      if (imageUrl.startsWith('/')) {
        // 如果是相对路径，拼接服务器地址
        imageUrl = config.apiBaseUrl + imageUrl
      }
      
      console.log('预览二维码完整URL:', imageUrl)
      
      wx.previewImage({
        urls: [imageUrl],
        current: imageUrl
      })
    } else {
      wx.showToast({
        title: '二维码生成中，请稍后',
        icon: 'none'
      })
    }
  },

  /**
   * 显示/隐藏密码
   */
  onTogglePassword() {
    this.setData({
      showPassword: !this.data.showPassword
    })
  },
  
  /**
   * 显示/隐藏详细统计
   */
  onToggleDetailedStats() {
    this.setData({
      showDetailedStats: !this.data.showDetailedStats
    })
  },

  /**
   * 复制WiFi信息
   */
  onCopyWifiInfo(e) {
    const { type } = e.currentTarget.dataset
    const { wifiInfo } = this.data
    
    let copyText = ''
    let toastText = ''
    
    switch (type) {
      case 'ssid':
        copyText = wifiInfo.ssid || wifiInfo.name || ''
        toastText = 'WiFi名称已复制'
        break
      case 'password':
        copyText = wifiInfo.password || ''
        toastText = 'WiFi密码已复制'
        break
      case 'all':
        const ssid = wifiInfo.ssid || wifiInfo.name || ''
        const password = wifiInfo.password || ''
        const merchantName = wifiInfo.merchantName || wifiInfo.merchant_name || ''
        copyText = `WiFi名称：${ssid}\nWiFi密码：${password}\n商户：${merchantName}`
        toastText = 'WiFi信息已复制'
        break
    }
    
    if (copyText) {
      wx.setClipboardData({
        data: copyText,
        success: () => {
          wx.showToast({
            title: toastText,
            icon: 'success'
          })
        }
      })
    } else {
      wx.showToast({
        title: '没有可复制的内容',
        icon: 'none'
      })
    }
  },

  /**
   * 编辑WiFi码
   */
  onEditWifi() {
    wx.navigateTo({
      url: `/pages/wifi/create/create?id=${this.data.wifiId}&mode=edit`
    })
  },

  /**
   * 删除WiFi码
   */
  onDeleteWifi() {
    const { wifiInfo } = this.data
    
    wx.showModal({
      title: '确认删除',
      content: `确定要删除WiFi码"${wifiInfo.title}"吗？删除后无法恢复。`,
      success: async (res) => {
        if (res.confirm) {
          try {
            wx.showLoading({
              title: '删除中...'
            })

            const url = `${API.wifi.delete}/${this.data.wifiId}`
            const result = await request.delete(url)
            
            wx.hideLoading()

            if (result.success) {
              wx.showToast({
                title: '删除成功',
                icon: 'success'
              })
              
              setTimeout(() => {
                wx.navigateBack()
              }, 1500)
            } else {
              wx.showToast({
                title: result.message || '删除失败',
                icon: 'none'
              })
            }
          } catch (error) {
            wx.hideLoading()
            console.error('删除WiFi码失败:', error)
            wx.showToast({
              title: '网络错误，请重试',
              icon: 'none'
            })
          }
        }
      }
    })
  },

  /**
   * 打印WiFi码
   */
  onPrintWifi() {
    wx.showActionSheet({
      itemList: ['预览打印', '保存到相册', '微信小程序云打印'],
      success: (res) => {
        switch (res.tapIndex) {
          case 0:
            this.previewPrint()
            break
          case 1:
            this.saveToAlbum()
            break
          case 2:
            this.cloudPrint()
            break
        }
      }
    })
  },

  /**
   * 预览打印
   */
  previewPrint() {
    if (this.data.wifiInfo.qrCodeUrl) {
      wx.previewImage({
        urls: [this.data.wifiInfo.qrCodeUrl],
        current: this.data.wifiInfo.qrCodeUrl
      })
    } else {
      // 尝试从组件获取二维码
      const qrcodeComponent = this.selectComponent('#qrcode')
      if (qrcodeComponent && qrcodeComponent.getQrCodeUrl) {
        const localQrCodeUrl = qrcodeComponent.getQrCodeUrl()
        if (localQrCodeUrl) {
          wx.previewImage({
            urls: [localQrCodeUrl],
            current: localQrCodeUrl
          })
          
          // 上传本地生成的二维码到服务器
          this.uploadLocalQrCodeToServer(localQrCodeUrl)
          return
        }
      }
      
      wx.showToast({
        title: '二维码生成中，请稍后',
        icon: 'none'
      })
    }
  },

  /**
   * 保存到相册
   */
  async saveToAlbum() {
    let qrCodeUrl = this.data.wifiInfo.qrCodeUrl
    
    // 如果没有服务器生成的二维码，尝试获取本地生成的
    if (!qrCodeUrl) {
      const qrcodeComponent = this.selectComponent('#qrcode')
      if (qrcodeComponent && qrcodeComponent.getQrCodeUrl) {
        qrCodeUrl = qrcodeComponent.getQrCodeUrl()
        
        // 上传本地生成的二维码到服务器
        if (qrCodeUrl) {
          this.uploadLocalQrCodeToServer(qrCodeUrl)
        }
      }
    }
    
    if (!qrCodeUrl) {
      wx.showToast({
        title: '二维码生成中，请稍后',
        icon: 'none'
      })
      return
    }

    try {
      wx.showLoading({
        title: '保存中...'
      })

      // 下载图片
      const downloadRes = await new Promise((resolve, reject) => {
        wx.downloadFile({
          url: qrCodeUrl,
          success: resolve,
          fail: reject
        })
      })

      // 保存到相册
      await new Promise((resolve, reject) => {
        wx.saveImageToPhotosAlbum({
          filePath: downloadRes.tempFilePath,
          success: resolve,
          fail: reject
        })
      })

      wx.hideLoading()
      wx.showToast({
        title: '保存成功',
        icon: 'success'
      })
    } catch (error) {
      wx.hideLoading()
      console.error('保存图片失败:', error)
      
      if (error.errMsg && error.errMsg.includes('auth')) {
        wx.showModal({
          title: '提示',
          content: '需要您授权保存图片到相册',
          success: (res) => {
            if (res.confirm) {
              wx.openSetting()
            }
          }
        })
      } else {
        wx.showToast({
          title: '保存失败，请重试',
          icon: 'none'
        })
      }
    }
  },
  
  /**
   * 微信小程序云打印功能
   */
  cloudPrint() {
    let qrCodeUrl = this.data.wifiInfo.qrCodeUrl
    
    // 如果没有服务器生成的二维码，尝试获取本地生成的
    if (!qrCodeUrl) {
      const qrcodeComponent = this.selectComponent('#qrcode')
      if (qrcodeComponent && qrcodeComponent.getQrCodeUrl) {
        qrCodeUrl = qrcodeComponent.getQrCodeUrl()
        
        // 上传本地生成的二维码到服务器
        if (qrCodeUrl) {
          this.uploadLocalQrCodeToServer(qrCodeUrl)
        }
      }
    }
    
    if (!qrCodeUrl) {
      wx.showToast({
        title: '二维码生成中，请稍后',
        icon: 'none'
      })
      return
    }
    
    wx.showModal({
      title: '云打印',
      content: '确定将WiFi码发送到微信小程序云打印服务？您可以选择附近的打印店进行打印。',
      success: (res) => {
        if (res.confirm) {
          // 这里是微信云打印能力，需要调用云打印API
          wx.showLoading({
            title: '准备打印...'
          })
          
          // 模拟云打印过程
          setTimeout(() => {
            wx.hideLoading()
            wx.showModal({
              title: '打印提示',
              content: '暂未接入微信云打印服务，您可以保存图片后通过微信"扫一扫"-"打印"功能，选择附近打印点完成打印。',
              showCancel: false
            })
          }, 1500)
        }
      }
    })
  },

  /**
   * 分享WiFi码
   */
  onShareWifi() {
    wx.showActionSheet({
      itemList: ['生成分享海报', '分享给朋友', '复制链接', '分享设置'],
      success: (res) => {
        switch (res.tapIndex) {
          case 0:
            this.generateSharePoster()
            break
          case 1:
            this.shareToFriend()
            break
          case 2:
            this.copyShareLink()
            break
          case 3:
            this.openShareSettings()
            break
        }
      }
    })
  },
  
  /**
   * 打开分享设置
   */
  openShareSettings() {
    wx.navigateTo({
      url: `/pages/wifi/share-settings/share-settings?id=${this.data.wifiId}`
    })
  },

  /**
   * 生成分享海报
   */
  async generateSharePoster() {
    try {
      wx.showLoading({
        title: '生成中...'
      })

      try {
        const url = `${API.wifi.poster}/${this.data.wifiId}`
        const result = await request.post(url, {
          showAd: this.data.shareSettings.showAdInPoster,
          customText: this.data.shareSettings.customText,
          background: this.data.shareSettings.posterBackground
        }, { showError: false })
        
        wx.hideLoading()

        if (result.success && result.data.posterUrl) {
          wx.previewImage({
            urls: [result.data.posterUrl],
            current: result.data.posterUrl
          })
        } else {
          console.error('生成分享海报失败:', result)
          this.generateLocalSharePoster()
        }
      } catch (error) {
        wx.hideLoading()
        console.error('生成分享海报失败:', error)
        this.generateLocalSharePoster()
      }
    } catch (error) {
      wx.hideLoading()
      console.error('生成分享海报失败:', error)
      wx.showToast({
        title: '网络错误，请重试',
        icon: 'none'
      })
    }
  },
  
  /**
   * 生成本地分享海报
   */
  generateLocalSharePoster() {
    wx.showToast({
      title: '正在生成本地海报',
      icon: 'none'
    })
    
    // 获取二维码URL
    let qrCodeUrl = this.data.wifiInfo.qrCodeUrl
    if (!qrCodeUrl) {
      const qrcodeComponent = this.selectComponent('#qrcode')
      if (qrcodeComponent && qrcodeComponent.getQrCodeUrl) {
        qrCodeUrl = qrcodeComponent.getQrCodeUrl()
        
        // 上传本地生成的二维码到服务器
        if (qrCodeUrl) {
          this.uploadLocalQrCodeToServer(qrCodeUrl)
        }
      }
    }
    
    if (qrCodeUrl) {
      wx.previewImage({
        urls: [qrCodeUrl],
        current: qrCodeUrl
      })
    } else {
      wx.showToast({
        title: '无法生成海报，请稍后重试',
        icon: 'none'
      })
    }
  },

  /**
   * 分享给朋友
   */
  shareToFriend() {
    // 触发页面分享
    wx.showShareMenu({
      withShareTicket: true
    })
  },

  /**
   * 复制分享链接
   */
  copyShareLink() {
    const shareUrl = `pages/wifi/detail/detail?id=${this.data.wifiId}`
    
    wx.setClipboardData({
      data: shareUrl,
      success: () => {
        wx.showToast({
          title: '链接已复制',
          icon: 'success'
        })
      }
    })
  },

  /**
   * 商品点击事件
   */
  onGoodsTap(e) {
    const { id } = e.currentTarget.dataset
    wx.navigateTo({
      url: `/pages/mall/goods/goods?id=${id}`
    })
  },

  /**
   * 广告点击事件
   */
  onAdTap() {
    const { adData } = this.data
    if (adData.link) {
      wx.navigateTo({
        url: adData.link
      })
    } else {
      wx.showToast({
        title: '广告详情页面开发中',
        icon: 'none'
      })
    }
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh() {
    this.loadWifiDetail()
    this.loadDetailedStats()
    this.loadAdData()
    this.loadShareSettings()
    setTimeout(() => {
      wx.stopPullDownRefresh()
    }, 1000)
  },

  /**
   * 页面分享
   */
  onShareAppMessage() {
    const { wifiInfo } = this.data
    return {
      title: `${wifiInfo.title} - WiFi免费连接`,
      path: `/pages/wifi/detail/detail?id=${this.data.wifiId}`,
      imageUrl: wifiInfo.qrCodeUrl || '/assets/images/share-wifi-detail.jpg'
    }
  }
}) 