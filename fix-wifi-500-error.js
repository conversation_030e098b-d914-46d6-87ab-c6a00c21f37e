/**
 * WiFi API 500错误修复脚本
 * 修复WiFi列表API，移除status过滤条件
 */

const fs = require('fs');
const path = require('path');

// 源文件路径
const wifiRouterPath = path.join(__dirname, 'src', 'routes', 'wifi.js');

// 备份文件路径
const backupPath = path.join(__dirname, 'src', 'routes', 'wifi.js.bak');

// 新的client/list路由处理函数代码
const newClientListFunction = `
/**
 * @route   GET /api/client/wifi/list
 * @desc    获取WiFi码列表（客户端，无需认证）
 * @access  Public
 */
router.get('/client/list', async (req, res) => {
  try {
    console.log('处理客户端WiFi列表请求，参数：', req.query);
    const page = parseInt(req.query.page) || 1;
    const pageSize = parseInt(req.query.pageSize) || parseInt(req.query.limit) || 10;
    const keyword = req.query.keyword || '';
    
    // 完全移除status过滤，返回所有WiFi记录
    const conditions = [];
    const params = [];
    
    if (keyword) {
      conditions.push('(title LIKE ? OR name LIKE ? OR merchant_name LIKE ?)');
      params.push(\`%\${keyword}%\`, \`%\${keyword}%\`, \`%\${keyword}%\`);
    }
    
    const whereClause = conditions.length > 0 ? 'WHERE ' + conditions.join(' AND ') : '';
    
    // 获取总数
    const countSql = \`SELECT COUNT(*) as total FROM wifi \${whereClause}\`;
    console.log('执行SQL查询', { sql: countSql, params });
    
    const countResult = await db.query(countSql, params);
    
    // 确保安全地获取total值
    let total = 0;
    if (Array.isArray(countResult) && countResult.length > 0) {
      if (Array.isArray(countResult[0]) && countResult[0].length > 0) {
        total = countResult[0][0]?.total || 0;
      } else if (countResult[0]?.total !== undefined) {
        total = countResult[0].total;
      }
    }
    
    console.log('查询到客户端WiFi总数：', total);
    
    // 获取分页数据
    const offset = (page - 1) * pageSize;
    const listSql = \`SELECT id, title, name, password, merchant_name, qrcode, use_count, status, created_at 
       FROM wifi \${whereClause} 
       ORDER BY created_at DESC 
       LIMIT \${parseInt(pageSize)} OFFSET \${parseInt(offset)}\`;
       
    console.log('执行SQL查询', { sql: listSql, params });
    
    const listResult = await db.query(listSql, params);
    
    // 处理MySQL2返回的[rows, fields]格式
    let rows = [];
    if (Array.isArray(listResult) && listResult.length > 0) {
      if (Array.isArray(listResult[0])) {
        rows = listResult[0];
      } else {
        rows = listResult;
      }
    }
    
    // 确保rows是一个数组
    const list = Array.isArray(rows) ? rows : [];
    
    console.log(\`返回客户端WiFi列表: 找到\${list.length}条记录\`);
    console.log('WiFi列表数据:', list);
    
    return success(res, {
      list: list, // 确保是一个数组
      pagination: {
        total,
        page,
        pageSize,
        pages: Math.ceil(total / pageSize)
      }
    }, \`获取WiFi码列表成功，找到\${list.length}条记录\`);
  } catch (err) {
    console.error('获取客户端WiFi列表失败:', err);
    console.error('错误堆栈:', err.stack);
    
    // 出错时返回空列表，而不是返回错误
    return success(res, {
      list: [],
      pagination: {
        total: 0,
        page: parseInt(req.query.page) || 1,
        pageSize: parseInt(req.query.pageSize) || parseInt(req.query.limit) || 10,
        pages: 0
      }
    }, '获取WiFi列表成功，但未找到记录');
  }
});`;

// 要替换的旧代码的正则表达式
const oldClientListRegex = /router\.get\('\/client\/list'[\s\S]*?}\);/;

// 读取源文件
fs.readFile(wifiRouterPath, 'utf8', (err, data) => {
  if (err) {
    console.error('读取文件失败:', err);
    process.exit(1);
  }
  
  // 备份原文件
  fs.writeFile(backupPath, data, 'utf8', (err) => {
    if (err) {
      console.error('备份文件失败:', err);
      process.exit(1);
    }
    console.log(`原文件已备份至 ${backupPath}`);
    
    // 替换代码
    const newContent = data.replace(oldClientListRegex, newClientListFunction);
    
    // 写入新文件
    fs.writeFile(wifiRouterPath, newContent, 'utf8', (err) => {
      if (err) {
        console.error('写入文件失败:', err);
        process.exit(1);
      }
      console.log(`WiFi列表API已成功修复，文件路径: ${wifiRouterPath}`);
      console.log('请重启服务器以应用更改');
    });
  });
}); 