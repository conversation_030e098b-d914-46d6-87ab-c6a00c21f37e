/* pages/alliance/index.wxss */
.alliance-container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 页面标题 */
.page-header {
  text-align: center;
  margin-bottom: 30rpx;
}

.header-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.header-desc {
  font-size: 26rpx;
  color: #999;
}

/* 标签页导航 */
.tab-nav {
  display: flex;
  background-color: #fff;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.tab-item {
  flex: 1;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #666;
  position: relative;
}

.tab-item.active {
  color: #07c160;
  font-weight: 500;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background-color: #07c160;
  border-radius: 2rpx;
}

/* 标签页内容 */
.tab-content {
  margin-bottom: 20rpx;
}

/* 团队数据卡片 */
.team-stats {
  display: flex;
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.stat-item {
  flex: 1;
  text-align: center;
  position: relative;
}

.stat-item:not(:last-child)::after {
  content: '';
  position: absolute;
  right: 0;
  top: 20%;
  height: 60%;
  width: 1rpx;
  background-color: #eee;
}

.stat-value {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #999;
}

/* 团队成员列表 */
.team-members {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  border-bottom: 1rpx solid #eee;
  padding-bottom: 15rpx;
}

.member-list {
  
}

.member-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}

.member-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 20rpx;
  background-color: #f5f5f5;
}

.member-info {
  flex: 1;
}

.member-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 6rpx;
}

.member-date {
  font-size: 24rpx;
  color: #999;
}

.member-contribution {
  text-align: center;
  margin-left: 20rpx;
}

.contribution-value {
  font-size: 32rpx;
  font-weight: bold;
  color: #07c160;
}

.contribution-label {
  font-size: 22rpx;
  color: #999;
}

.load-more {
  text-align: center;
  padding: 20rpx 0;
  font-size: 26rpx;
  color: #07c160;
}

.empty-tip {
  text-align: center;
  padding: 40rpx 0;
  font-size: 26rpx;
  color: #999;
}

/* 收益管理 */
.profit-card {
  background-color: #07c160;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  color: #fff;
  box-shadow: 0 4rpx 12rpx rgba(7, 193, 96, 0.2);
}

.profit-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
  font-size: 28rpx;
}

.profit-amount {
  font-size: 48rpx;
  font-weight: bold;
}

.profit-actions {
  display: flex;
  justify-content: flex-end;
}

.action-button {
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 30rpx;
  padding: 10rpx 30rpx;
  font-size: 24rpx;
}

.profit-rules {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.rule-item {
  display: flex;
  justify-content: space-between;
  padding: 15rpx 0;
  font-size: 28rpx;
  color: #333;
  border-bottom: 1rpx solid #f5f5f5;
}

.rule-value {
  font-weight: bold;
  color: #07c160;
}

.rule-tips {
  margin-top: 20rpx;
  font-size: 24rpx;
  color: #999;
}

.profit-tips {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.tip-title {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.tip-icon {
  margin-right: 10rpx;
}

.tip-content {
  display: flex;
  flex-direction: column;
}

.tip-content text {
  font-size: 26rpx;
  color: #666;
  line-height: 1.8;
}

/* 已申请状态 */
.status-section {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 40rpx;
  margin-bottom: 30rpx;
  text-align: center;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.status-icon {
  font-size: 60rpx;
  margin-bottom: 20rpx;
}

.status-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 15rpx;
}

.status-desc {
  font-size: 28rpx;
  color: #07c160;
  margin-bottom: 20rpx;
}

.status-tip {
  font-size: 26rpx;
  color: #999;
  margin-bottom: 30rpx;
}

.status-button {
  width: 300rpx;
  height: 80rpx;
  line-height: 80rpx;
  background-color: #07c160;
  color: #fff;
  border-radius: 40rpx;
  margin: 0 auto;
  font-size: 28rpx;
}

/* 申请表单 */
.form-section {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.form-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  text-align: center;
}

.form-item {
  margin-bottom: 25rpx;
}

.form-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.required {
  color: #ff4d4f;
}

.form-input {
  height: 80rpx;
  background-color: #f5f5f5;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #333;
}

.form-picker {
  height: 80rpx;
  background-color: #f5f5f5;
  border-radius: 8rpx;
  padding: 0 20rpx;
  display: flex;
  align-items: center;
}

.picker-content {
  width: 100%;
  font-size: 28rpx;
  color: #333;
}

.placeholder {
  color: #999;
}

.form-textarea {
  width: 100%;
  height: 200rpx;
  background-color: #f5f5f5;
  border-radius: 8rpx;
  padding: 20rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
}

.form-tips {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 30rpx;
}

.submit-button {
  height: 90rpx;
  line-height: 90rpx;
  text-align: center;
  background-color: #07c160;
  color: #fff;
  border-radius: 45rpx;
  font-size: 32rpx;
  font-weight: 500;
  box-shadow: 0 4rpx 12rpx rgba(7, 193, 96, 0.2);
}

.submit-button.disabled {
  background-color: #ccc;
}

/* 联盟优势 */
.benefits-section {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.benefits-list {
  
}

.benefit-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #eee;
}

.benefit-item:last-child {
  border-bottom: none;
}

.benefit-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  margin-right: 20rpx;
}

.team-icon {
  background-color: #ffe7ba;
}

.profit-icon {
  background-color: #d4f7d4;
}

.level-icon {
  background-color: #d6e4ff;
}

.benefit-content {
  flex: 1;
}

.benefit-title {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 6rpx;
}

.benefit-desc {
  font-size: 24rpx;
  color: #999;
}

/* 广告容器 */
.ad-container {
  border-radius: 16rpx;
  overflow: hidden;
  margin-bottom: 20rpx;
} 