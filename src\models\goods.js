const db = require('../database');
const logger = require('../utils/logger');

/**
 * 商品模型
 */
class GoodsModel {
  /**
   * 创建商品
   * @param {object} goodsData 商品数据
   * @returns {Promise<object>} 创建的商品信息
   */
  static async create(goodsData) {
    try {
      const { 
        title, cover, images, price, originalPrice, stock, 
        description, categoryId, isRecommend, specifications, details 
      } = goodsData;
      
      // 确保images是有效的数组，并转换为JSON字符串
      let imagesStr;
      try {
        if (Array.isArray(images)) {
          imagesStr = JSON.stringify(images);
        } else if (typeof images === 'string') {
          // 尝试解析字符串，如果是有效的JSON则使用，否则作为单个URL处理
          try {
            const parsed = JSON.parse(images);
            imagesStr = Array.isArray(parsed) ? images : JSON.stringify([images]);
          } catch (e) {
            imagesStr = JSON.stringify([images]);
          }
        } else {
          imagesStr = JSON.stringify([]);
        }
      } catch (e) {
        logger.error(`处理商品图片数据失败: ${e.message}`);
        imagesStr = JSON.stringify([]);
      }
      
      // 将规格信息存储到details字段中
      const detailsWithSpecs = details || '';
      
      const sql = `
        INSERT INTO goods (
          title, cover, images, price, original_price, stock, sales, 
          description, category_id, is_recommend, details, 
          created_at, updated_at
        )
        VALUES (?, ?, ?, ?, ?, ?, 0, ?, ?, ?, ?, NOW(), NOW())
      `;
      
      const result = await db.query(sql, [
        title, cover, imagesStr, price, originalPrice, stock,
        description, categoryId, isRecommend ? 1 : 0, detailsWithSpecs
      ]);
      
      if (result.insertId) {
        return { 
          id: result.insertId, 
          ...goodsData,
          sales: 0
        };
      } else {
        throw new Error('创建商品失败');
      }
    } catch (error) {
      logger.error(`创建商品失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 获取商品列表
   * @param {object} params 查询参数
   * @param {number} params.page 页码
   * @param {number} params.limit 每页数量
   * @param {string} params.keyword 搜索关键词
   * @param {number} params.categoryId 分类ID
   * @param {string} params.sort 排序字段
   * @param {string} params.order 排序方式
   * @returns {Promise<object>} 商品列表和分页信息
   */
  static async list({ page = 1, limit = 10, keyword = '', categoryId = '', sort = 'createdAt', order = 'desc' }) {
    try {
      const whereConditions = [];
      const params = [];
      
      // 关键词搜索
      if (keyword) {
        whereConditions.push('title LIKE ?');
        params.push(`%${keyword}%`);
      }
      
      // 分类筛选
      if (categoryId) {
        whereConditions.push('category_id = ?');
        params.push(categoryId);
      }
      
      // 构建WHERE子句
      const whereClause = whereConditions.length > 0 ? 'WHERE ' + whereConditions.join(' AND ') : '';
      
      // 处理排序
      let orderByClause = 'ORDER BY created_at DESC';
      const allowedSortFields = ['price', 'sales', 'createdAt'];
      if (allowedSortFields.includes(sort)) {
        const dbField = sort === 'createdAt' ? 'created_at' : sort;
        const orderDirection = order.toUpperCase() === 'ASC' ? 'ASC' : 'DESC';
        orderByClause = `ORDER BY ${dbField} ${orderDirection}`;
      }
      
      const offset = (page - 1) * limit;
      
      const countSql = `SELECT COUNT(*) as total FROM goods ${whereClause}`;
      // 使用直接插入数值的方式处理LIMIT子句
      const listSql = `
        SELECT 
          id, title, cover, price, original_price, stock, sales, 
          category_id, is_recommend, is_hot, is_new, status, created_at
        FROM goods
        ${whereClause}
        ${orderByClause}
        LIMIT ${parseInt(offset)}, ${parseInt(limit)}
      `;
      
      console.log('执行SQL查询:', listSql, params);
      
      const [countResult, listResult] = await Promise.all([
        db.query(countSql, params),
        db.query(listSql, params)
      ]);
      
      // 处理结果，将is_recommend转换为布尔值，格式化日期等
      const goodsList = listResult.map(item => ({
        id: item.id,
        title: item.title,
        name: item.title, // 添加name作为title的别名，兼容前端
        cover: item.cover,
        price: item.price,
        originalPrice: item.original_price,
        stock: item.stock,
        sales: item.sales,
        categoryId: item.category_id,
        category_id: item.category_id, // 添加category_id作为categoryId的别名
        isRecommend: Boolean(item.is_recommend),
        is_recommend: item.is_recommend, // 添加is_recommend作为isRecommend的别名
        isHot: Boolean(item.is_hot),
        isNew: Boolean(item.is_new),
        status: item.status, // 添加status字段
        createdAt: item.created_at,
        categoryName: '家居用品' // 添加默认分类名称
      }));
      
      const total = countResult[0].total;
      
      return {
        list: goodsList,
        pagination: {
          total,
          page: parseInt(page),
          limit: parseInt(limit),
          pages: Math.ceil(total / limit)
        }
      };
    } catch (error) {
      logger.error(`获取商品列表失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 根据ID获取商品详情
   * @param {number} id 商品ID
   * @returns {Promise<object|null>} 商品详情
   */
  static async getById(id) {
    try {
      console.log(`GoodsModel.getById - 查询商品，ID: ${id}, 类型: ${typeof id}`);
      
      const sql = `
        SELECT g.*, c.name as category_name
        FROM goods g
        LEFT JOIN category c ON g.category_id = c.id
        WHERE g.id = ?
        LIMIT 1
      `;
      
      console.log(`执行SQL查询: ${sql}, 参数: [${id}]`);
      const result = await db.query(sql, [id]);
      console.log(`查询结果: ${JSON.stringify(result)}`);
      
      if (result.length === 0) {
        console.log(`未找到ID为 ${id} 的商品`);
        return null;
      }
      
      const goods = result[0];
      console.log(`找到商品: ${JSON.stringify(goods)}`);
      
      // 解析JSON字段
      let images = [];
      let details = [];
      
      try {
        images = JSON.parse(goods.images || '[]');
      } catch (e) {
        logger.error(`解析商品图片JSON失败: ${e.message}`);
      }
      
      try {
        details = JSON.parse(goods.details || '[]');
      } catch (e) {
        logger.error(`解析商品规格JSON失败: ${e.message}`);
      }
      
      return {
        id: goods.id,
        title: goods.title,
        cover: goods.cover,
        images,
        price: goods.price,
        originalPrice: goods.original_price,
        stock: goods.stock,
        sales: goods.sales,
        description: goods.description,
        categoryId: goods.category_id,
        categoryName: goods.category_name,
        isRecommend: Boolean(goods.is_recommend),
        details,
        createdAt: goods.created_at,
        updatedAt: goods.updated_at
      };
    } catch (error) {
      logger.error(`获取商品详情失败: ${error.message}`);
      console.error(`获取商品详情失败: ${error.message}, 堆栈: ${error.stack}`);
      throw error;
    }
  }

  /**
   * 更新商品
   * @param {number} id 商品ID
   * @param {object} goodsData 商品更新数据
   * @returns {Promise<boolean>} 更新是否成功
   */
  static async update(id, goodsData) {
    try {
      const { 
        title, cover, images, price, originalPrice, stock, 
        description, categoryId, isRecommend, specifications, details 
      } = goodsData;
      
      const imagesStr = images ? JSON.stringify(images) : undefined;
      const detailsWithSpecs = details ? JSON.stringify(details) : undefined;
      
      const updates = [];
      const params = [];
      
      // 构建动态更新字段
      if (title !== undefined) { updates.push('title = ?'); params.push(title); }
      if (cover !== undefined) { updates.push('cover = ?'); params.push(cover); }
      if (imagesStr !== undefined) { updates.push('images = ?'); params.push(imagesStr); }
      if (price !== undefined) { updates.push('price = ?'); params.push(price); }
      if (originalPrice !== undefined) { updates.push('original_price = ?'); params.push(originalPrice); }
      if (stock !== undefined) { updates.push('stock = ?'); params.push(stock); }
      if (description !== undefined) { updates.push('description = ?'); params.push(description); }
      if (categoryId !== undefined) { updates.push('category_id = ?'); params.push(categoryId); }
      if (isRecommend !== undefined) { updates.push('is_recommend = ?'); params.push(isRecommend ? 1 : 0); }
      if (detailsWithSpecs !== undefined) { updates.push('details = ?'); params.push(detailsWithSpecs); }
      
      updates.push('updated_at = NOW()');
      
      // 如果没有更新字段，则直接返回成功
      if (updates.length === 1) {
        return true;
      }
      
      params.push(id);
      
      const sql = `
        UPDATE goods
        SET ${updates.join(', ')}
        WHERE id = ?
      `;
      
      const result = await db.query(sql, params);
      return result.affectedRows > 0;
    } catch (error) {
      logger.error(`更新商品失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 删除商品
   * @param {number} id 商品ID
   * @returns {Promise<boolean>} 删除是否成功
   */
  static async delete(id) {
    try {
      console.log(`GoodsModel.delete - 删除商品，ID: ${id}, 类型: ${typeof id}`);
      
      const sql = 'DELETE FROM goods WHERE id = ?';
      console.log(`执行SQL查询: ${sql}, 参数: [${id}]`);
      
      const result = await db.query(sql, [id]);
      console.log(`删除结果: ${JSON.stringify(result)}`);
      
      return result.affectedRows > 0;
    } catch (error) {
      logger.error(`删除商品失败: ${error.message}`);
      console.error(`删除商品失败: ${error.message}, 堆栈: ${error.stack}`);
      throw error;
    }
  }

  /**
   * 更新商品状态（上下架）
   * @param {number} id 商品ID
   * @param {number} status 状态值 (0:下架, 1:上架)
   * @returns {Promise<boolean>} 更新是否成功
   */
  static async updateStatus(id, status) {
    try {
      const sql = 'UPDATE goods SET status = ?, updated_at = NOW() WHERE id = ?';
      const result = await db.query(sql, [status, id]);
      return result.affectedRows > 0;
    } catch (error) {
      logger.error(`更新商品状态失败: ${error.message}`);
      throw error;
    }
  }
  
  /**
   * 获取商品分类列表
   * @returns {Promise<Array>} 分类列表
   */
  static async getCategories() {
    try {
      console.log('GoodsModel.getCategories - 开始获取商品分类');
      
      const sql = `
        SELECT c.id, c.name, c.icon, COUNT(g.id) as count
        FROM category c
        LEFT JOIN goods g ON c.id = g.category_id
        GROUP BY c.id
        ORDER BY c.sort_order ASC, c.id ASC
      `;
      
      console.log('执行SQL查询:', sql);
      
      const result = await db.query(sql);
      console.log('分类查询结果:', JSON.stringify(result));
      
      if (!result || result.length === 0) {
        console.log('未找到任何商品分类，返回默认分类');
        return [
          { id: 1, name: '电子产品', icon: '/uploads/icons/electronics.png', count: 0 },
          { id: 2, name: '服装', icon: '/uploads/icons/clothing.png', count: 0 },
          { id: 3, name: '食品', icon: '/uploads/icons/food.png', count: 0 },
          { id: 4, name: '家居', icon: '/uploads/icons/home.png', count: 0 },
          { id: 5, name: '美妆', icon: '/uploads/icons/beauty.png', count: 0 }
        ];
      }
      
      // 确保字段格式正确
      const formattedResult = result.map(item => ({
        id: item.id,
        name: item.name || '未命名分类',
        icon: item.icon || '',
        count: item.count || 0
      }));
      
      console.log('返回格式化后的分类:', JSON.stringify(formattedResult));
      return formattedResult;
    } catch (error) {
      console.error(`获取商品分类列表失败: ${error.message}`, error);
      logger.error(`获取商品分类列表失败: ${error.message}`);
      
      // 发生错误时返回默认分类
      return [
        { id: 1, name: '电子产品', icon: '/uploads/icons/electronics.png', count: 0 },
        { id: 2, name: '服装', icon: '/uploads/icons/clothing.png', count: 0 },
        { id: 3, name: '食品', icon: '/uploads/icons/food.png', count: 0 },
        { id: 4, name: '家居', icon: '/uploads/icons/home.png', count: 0 },
        { id: 5, name: '美妆', icon: '/uploads/icons/beauty.png', count: 0 }
      ];
    }
  }
  
  /**
   * 创建商品分类
   * @param {object} categoryData 分类数据
   * @returns {Promise<object>} 创建的分类信息
   */
  static async createCategory(categoryData) {
    try {
      const { name, icon, sortOrder = 0 } = categoryData;
      
      const sql = `
        INSERT INTO category (name, icon, sort_order, created_at, updated_at)
        VALUES (?, ?, ?, NOW(), NOW())
      `;
      
      const result = await db.query(sql, [name, icon, sortOrder]);
      
      if (result.insertId) {
        return { 
          id: result.insertId, 
          name,
          icon,
          sortOrder,
          count: 0
        };
      } else {
        throw new Error('创建商品分类失败');
      }
    } catch (error) {
      logger.error(`创建商品分类失败: ${error.message}`);
      throw error;
    }
  }
}

module.exports = GoodsModel; 