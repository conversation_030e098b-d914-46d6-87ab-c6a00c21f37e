const axios = require('axios');
const jwt = require('jsonwebtoken');

async function testRealAllianceRequest() {
  try {
    console.log('🧪 模拟真实的联盟申请请求...\n');
    
    // 1. 生成一个有效的JWT token（模拟用户登录）
    const testUser = {
      id: 1,
      openid: 'oW20C7mVlW8e3W2AgUGtDTJeAbQU'
    };
    
    // 使用与后端相同的密钥生成token
    const token = jwt.sign(
      testUser,
      'wifi_share_secret_key', // 确保与后端配置一致
      { expiresIn: '24h' }
    );
    
    console.log('1️⃣ 生成的测试token:', token.substring(0, 50) + '...\n');
    
    // 2. 模拟前端发送的数据（与您提供的数据一致）
    const testData = {
      name: "huahong ",
      contact: "tianyang",
      phone: "187200759701", // 注意：这里有11位数字，可能是问题
      email: "<EMAIL>",
      area: "华东地区",
      description: "测试联盟申请"
    };
    
    console.log('2️⃣ 发送的申请数据:');
    console.log(JSON.stringify(testData, null, 2));
    
    try {
      console.log('\n3️⃣ 发送联盟申请请求...');
      const response = await axios.post('http://localhost:4000/api/v1/client/alliance/apply', testData, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        timeout: 10000
      });
      
      console.log('✅ 联盟申请成功:');
      console.log('状态码:', response.status);
      console.log('响应数据:', JSON.stringify(response.data, null, 2));
      
    } catch (apiError) {
      if (apiError.response) {
        console.log('❌ 联盟申请失败:');
        console.log('状态码:', apiError.response.status);
        console.log('响应头:', JSON.stringify(apiError.response.headers, null, 2));
        console.log('错误信息:', JSON.stringify(apiError.response.data, null, 2));
        
        if (apiError.response.status === 500) {
          console.log('\n🔍 500错误分析:');
          console.log('- 可能原因1: 数据库连接问题');
          console.log('- 可能原因2: SQL语句执行错误');
          console.log('- 可能原因3: 数据验证失败');
          console.log('- 可能原因4: 字段长度超限');
          console.log('- 建议: 检查后端控制台的详细错误日志');
        }
      } else if (apiError.code === 'ECONNREFUSED') {
        console.log('❌ 无法连接到服务器');
        console.log('请确保后端服务正在运行在 http://localhost:4000');
      } else {
        console.log('❌ 请求失败:', apiError.message);
      }
    }
    
    // 4. 测试数据验证
    console.log('\n4️⃣ 数据验证检查:');
    console.log('姓名长度:', testData.name.length, '字符');
    console.log('联系人长度:', testData.contact.length, '字符');
    console.log('手机号长度:', testData.phone.length, '字符');
    console.log('邮箱长度:', testData.email.length, '字符');
    console.log('区域长度:', testData.area.length, '字符');
    console.log('描述长度:', testData.description ? testData.description.length : 0, '字符');
    
    // 检查手机号格式
    if (testData.phone.length !== 11) {
      console.log('⚠️  警告: 手机号长度不是11位，可能导致数据库插入失败');
    }
    
  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error.message);
  }
}

// 运行测试
testRealAllianceRequest();
