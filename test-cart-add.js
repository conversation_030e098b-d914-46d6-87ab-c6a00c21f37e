const axios = require('axios');

// 测试购物车添加API
async function testCartAdd() {
  try {
    console.log('开始测试购物车添加API...');
    
    // 首先登录获取token
    const loginResponse = await axios.post('http://localhost:4000/api/v1/client/auth/login', {
      phone: '13800138000',
      password: '123456'
    });
    
    if (loginResponse.data.status !== 'success') {
      console.error('登录失败:', loginResponse.data);
      return;
    }
    
    const token = loginResponse.data.data.token;
    console.log('登录成功，获取到token:', token.substring(0, 20) + '...');
    
    // 测试添加商品到购物车
    const cartAddResponse = await axios.post('http://localhost:4000/api/v1/client/cart/add', {
      goodsId: 4,
      quantity: 2,
      specificationId: 0
    }, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    console.log('购物车添加API响应:', cartAddResponse.data);
    
    if (cartAddResponse.data.status === 'success') {
      console.log('✅ 购物车添加API测试成功！');
    } else {
      console.log('❌ 购物车添加API测试失败:', cartAddResponse.data.message);
    }
    
  } catch (error) {
    console.error('测试过程中出现错误:');
    if (error.response) {
      console.error('响应状态:', error.response.status);
      console.error('响应数据:', error.response.data);
    } else {
      console.error('错误信息:', error.message);
    }
  }
}

// 执行测试
testCartAdd();
