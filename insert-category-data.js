const db = require('./src/database');

async function insertCategoryData() {
  try {
    console.log('开始插入商品分类测试数据...');
    
    // 首先检查是否已有数据
    const existingData = await db.query('SELECT COUNT(*) as count FROM category');
    const count = existingData[0].count;
    
    if (count > 0) {
      console.log(`商品分类表已有 ${count} 条数据，跳过插入操作`);
      return;
    }
    
    // 插入测试数据
    const categories = [
      { name: '电子产品', icon: '/uploads/icons/electronics.png', sort_order: 1 },
      { name: '服装', icon: '/uploads/icons/clothing.png', sort_order: 2 },
      { name: '食品', icon: '/uploads/icons/food.png', sort_order: 3 },
      { name: '家居', icon: '/uploads/icons/home.png', sort_order: 4 },
      { name: '美妆', icon: '/uploads/icons/beauty.png', sort_order: 5 }
    ];
    
    for (const category of categories) {
      await db.query(
        'INSERT INTO category (name, icon, sort_order) VALUES (?, ?, ?)',
        [category.name, category.icon, category.sort_order]
      );
      console.log(`已插入分类: ${category.name}`);
    }
    
    console.log('商品分类测试数据插入完成');
  } catch (err) {
    console.error('插入商品分类数据时出错:', err);
  } finally {
    process.exit();
  }
}

insertCategoryData(); 