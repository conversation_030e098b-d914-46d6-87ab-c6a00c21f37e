// config/api.js
// 引入全局配置文件
const config = require('./config')

// API基础URL（所有接口的前缀）
const ApiBaseUrl = config.apiBaseUrl

// API接口配置对象，统一管理所有前端需要调用的后端接口地址
const API = {
  // 用户相关接口
  user: {
    login: `${ApiBaseUrl}/api/v1/client/user/login`,           // 用户登录
    register: `${ApiBaseUrl}/api/v1/client/user/register`,     // 用户注册
    info: `${ApiBaseUrl}/api/v1/client/user/info`,             // 获取用户信息
    update: `${ApiBaseUrl}/api/v1/client/user/update`,         // 更新用户信息
    wallet: `${ApiBaseUrl}/api/v1/client/user/wallet`,         // 用户钱包信息
    team: `${ApiBaseUrl}/api/v1/client/user/team`,             // 用户团队信息
    address: { // 用户收货地址相关
      list: `${ApiBaseUrl}/api/v1/client/address/list`,        // 地址列表
      default: `${ApiBaseUrl}/api/v1/client/address/default`,  // 默认地址
      add: `${ApiBaseUrl}/api/v1/client/address/add`,          // 新增地址
      update: `${ApiBaseUrl}/api/v1/client/address/update`,    // 更新地址
      delete: `${ApiBaseUrl}/api/v1/client/address/delete`,    // 删除地址
      setDefault: `${ApiBaseUrl}/api/v1/client/address/set-default` // 设置默认地址
    }
  },
  
  // WiFi相关接口
  wifi: {
    list: `${ApiBaseUrl}/api/v1/client/wifi/list`,
    detail: `${ApiBaseUrl}/api/v1/client/wifi/detail`,
    create: `${ApiBaseUrl}/api/v1/client/wifi/create`,
    update: `${ApiBaseUrl}/api/v1/client/wifi/update`,
    delete: `${ApiBaseUrl}/api/v1/client/wifi/delete`,
    stats: `${ApiBaseUrl}/api/v1/client/wifi/stats`,           // WiFi码统计信息
    qrcode: `${ApiBaseUrl}/api/v1/client/wifi-qrcode`,         // 获取WiFi二维码
    poster: `${ApiBaseUrl}/api/v1/client/wifi-poster`          // WiFi海报生成
  },
  
  // 商品相关接口
  goods: {
    list: `${ApiBaseUrl}/api/v1/client/goods/list`,            // 商品列表
    detail: `${ApiBaseUrl}/api/v1/client/goods/detail`,        // 商品详情（需拼接商品ID）
    categories: `${ApiBaseUrl}/api/v1/client/goods/categories`, // 商品分类
    recommend: `${ApiBaseUrl}/api/v1/client/goods/recommend`   // 推荐商品
  },
  
  // 购物车相关接口
  cart: {
    list: `${ApiBaseUrl}/api/v1/client/cart/list`,             // 购物车列表
    add: `${ApiBaseUrl}/api/v1/client/cart/add`,               // 添加到购物车
    update: `${ApiBaseUrl}/api/v1/client/cart/update`,         // 更新购物车
    delete: `${ApiBaseUrl}/api/v1/client/cart/remove`,         // 删除购物车商品
    clear: `${ApiBaseUrl}/api/v1/client/cart/clear`,           // 清空购物车
    select: `${ApiBaseUrl}/api/v1/client/cart/select`          // 选择购物车商品
  },
  
  // 订单相关接口
  order: {
    create: `${ApiBaseUrl}/api/v1/client/order/create`,        // 创建订单
    list: `${ApiBaseUrl}/api/v1/client/order/list`,            // 订单列表
    detail: `${ApiBaseUrl}/api/v1/client/order/client/detail`,  // 订单详情
    cancel: `${ApiBaseUrl}/api/v1/client/order/cancel`,        // 取消订单
    delete: `${ApiBaseUrl}/api/v1/client/order/delete`,        // 删除订单
    confirm: `${ApiBaseUrl}/api/v1/client/order/confirm`,      // 确认收货
    paymentSuccess: `${ApiBaseUrl}/api/v1/client/order/payment-success`, // 支付成功回调
    stats: `${ApiBaseUrl}/api/v1/client/order/stats`           // 订单统计
  },

  // 物流相关接口
  logistics: {
    companies: `${ApiBaseUrl}/api/v1/logistics/companies`,     // 物流公司列表
    track: `${ApiBaseUrl}/api/v1/logistics/track`,             // 物流查询
    orderTrack: `${ApiBaseUrl}/api/v1/client/logistics/order`  // 订单物流查询
  },

  // 配送相关接口
  delivery: {
    methods: `${ApiBaseUrl}/api/v1/client/delivery/methods`,   // 获取配送方式列表
    calculate: `${ApiBaseUrl}/api/v1/client/delivery/calculate`, // 计算配送费用
    areas: `${ApiBaseUrl}/api/v1/client/delivery/areas`       // 获取配送区域
  },
  
  // 支付相关接口
  payment: {
    create: `${ApiBaseUrl}/api/v1/client/payment/create`,      // 创建支付订单
    check: `${ApiBaseUrl}/api/v1/client/payment/check`,        // 检查支付状态
    list: `${ApiBaseUrl}/api/v1/client/payment/list`           // 支付记录列表
  },
  
  // 广告相关接口
  ads: {
    list: `${ApiBaseUrl}/api/v1/client/ads/list`,              // 广告列表
    detail: `${ApiBaseUrl}/api/v1/client/ads/detail`,          // 广告详情
    stats: `${ApiBaseUrl}/api/v1/client/ads/stats`,            // 广告统计
    spaces: `${ApiBaseUrl}/api/v1/client/ads/spaces`           // 广告位信息
  },
  
  // 联盟相关接口
  alliance: {
    apply: `${ApiBaseUrl}/api/v1/client/alliance/apply`,       // 申请加入联盟
    status: `${ApiBaseUrl}/api/v1/client/alliance/status`      // 联盟状态查询
  },
  
  // 团队相关接口
  team: {
    info: `${ApiBaseUrl}/api/v1/client/team/info`,             // 团队信息
    members: `${ApiBaseUrl}/api/v1/client/team/members`,       // 团队成员
    stats: `${ApiBaseUrl}/api/v1/client/team/stats`,           // 团队统计
    create: `${ApiBaseUrl}/api/v1/client/team/create`,         // 创建团队
    join: `${ApiBaseUrl}/api/v1/client/team/join`,             // 加入团队
    leave: `${ApiBaseUrl}/api/v1/client/team/leave`,           // 离开团队
    getByInviteCode: `${ApiBaseUrl}/api/v1/team/invite`        // 根据邀请码获取团队信息
  },
  
  // 收益相关接口
  profit: {
    rules: `${ApiBaseUrl}/api/v1/client/profit/rules`,         // 收益规则
    stats: `${ApiBaseUrl}/api/v1/client/income/stats`,         // 收益统计
    details: `${ApiBaseUrl}/api/v1/client/income/details`,     // 收益明细
    withdraw: `${ApiBaseUrl}/api/v1/client/withdraw/apply`     // 提现申请
  },

  // 收入相关接口
  income: {
    stats: `${ApiBaseUrl}/api/v1/client/income/stats`,         // 收入统计
    details: `${ApiBaseUrl}/api/v1/client/income/details`,     // 收入明细
    bill: {
      list: `${ApiBaseUrl}/api/v1/client/income/bill/list`,    // 收入账单列表
      detail: `${ApiBaseUrl}/api/v1/client/income/bill/detail` // 收入账单详情
    }
  },
  
  // 上传相关接口
  upload: {
    image: `${ApiBaseUrl}/api/v1/client/upload/image`,         // 图片上传
    file: `${ApiBaseUrl}/api/v1/client/upload`,                // 文件上传（包括头像）
    avatar: `${ApiBaseUrl}/api/v1/client/upload`               // 头像上传（使用通用文件上传接口）
  }
}

// 导出API对象，供全局调用
module.exports = API 