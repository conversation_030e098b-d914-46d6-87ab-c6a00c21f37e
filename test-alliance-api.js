const axios = require('axios');

async function testAllianceAPI() {
  try {
    console.log('🧪 测试联盟申请API...\n');
    
    // 测试数据
    const testData = {
      name: "hua<PERSON>",
      contact: "t<PERSON><PERSON>", 
      phone: "18720759701",
      email: "<EMAIL>",
      area: "华东地区",
      description: "测试联盟申请"
    };
    
    console.log('1️⃣ 发送的申请数据:');
    console.log(JSON.stringify(testData, null, 2));
    
    // 这里需要一个有效的token
    const testToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6MSwib3BlbmlkIjoib1cyMEM3bVZsVzhlM1cyQWdVR3REVEplQWJRVSIsImlhdCI6MTc1MzgxMzE2NywiZXhwIjoxNzU0NDE3OTY3fQ.your-signature-here';
    
    try {
      console.log('\n2️⃣ 测试联盟申请接口...');
      const response = await axios.post('http://localhost:4000/api/v1/client/alliance/apply', testData, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${testToken}`
        },
        timeout: 10000
      });
      
      console.log('✅ 联盟申请成功:');
      console.log('状态码:', response.status);
      console.log('响应数据:', JSON.stringify(response.data, null, 2));
      
    } catch (apiError) {
      if (apiError.response) {
        console.log('❌ 联盟申请失败:');
        console.log('状态码:', apiError.response.status);
        console.log('错误信息:', JSON.stringify(apiError.response.data, null, 2));
        
        if (apiError.response.status === 404) {
          console.log('\n🔍 404错误分析:');
          console.log('- 可能原因1: 路由未正确注册');
          console.log('- 可能原因2: 后端服务未重启');
          console.log('- 可能原因3: 路径拼写错误');
        } else if (apiError.response.status === 401) {
          console.log('\n🔍 401错误分析:');
          console.log('- 可能原因: token无效或已过期');
          console.log('- 建议: 使用有效的用户token');
        }
      } else if (apiError.code === 'ECONNREFUSED') {
        console.log('❌ 无法连接到服务器');
        console.log('请确保后端服务正在运行在 http://localhost:4000');
      } else {
        console.log('❌ 请求失败:', apiError.message);
      }
    }
    
    // 测试获取申请状态接口
    console.log('\n3️⃣ 测试获取申请状态接口...');
    try {
      const statusResponse = await axios.get('http://localhost:4000/api/v1/client/alliance/status', {
        headers: {
          'Authorization': `Bearer ${testToken}`
        },
        timeout: 10000
      });
      
      console.log('✅ 获取申请状态成功:');
      console.log('状态码:', statusResponse.status);
      console.log('响应数据:', JSON.stringify(statusResponse.data, null, 2));
      
    } catch (statusError) {
      if (statusError.response) {
        console.log('❌ 获取申请状态失败:');
        console.log('状态码:', statusError.response.status);
        console.log('错误信息:', JSON.stringify(statusError.response.data, null, 2));
      } else {
        console.log('❌ 获取申请状态请求失败:', statusError.message);
      }
    }
    
  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error.message);
  }
}

// 运行测试
testAllianceAPI();
