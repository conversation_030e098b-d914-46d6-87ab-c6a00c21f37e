<template>
  <div class="app-container">
    <div class="detail-header">
      <el-page-header @back="goBack" :content="regionDetail.name || '地区详情'" />
    </div>
    
    <el-card class="box-card" v-loading="loading">
      <div slot="header" class="clearfix">
        <span>基本信息</span>
        <el-button
          style="float: right; padding: 3px 0"
          type="text"
          icon="el-icon-edit"
          @click="handleEdit"
        >
          编辑
        </el-button>
      </div>
      
      <el-descriptions :column="2" border>
        <el-descriptions-item label="地区ID">{{ regionDetail.id }}</el-descriptions-item>
        <el-descriptions-item label="地区名称">{{ regionDetail.name }}</el-descriptions-item>
        <el-descriptions-item label="地区编码">{{ regionDetail.code }}</el-descriptions-item>
        <el-descriptions-item label="上级地区">{{ parentName }}</el-descriptions-item>
        <el-descriptions-item label="层级">
          <el-tag v-if="regionDetail.level === 1" type="success">省级</el-tag>
          <el-tag v-else-if="regionDetail.level === 2" type="warning">市级</el-tag>
          <el-tag v-else-if="regionDetail.level === 3" type="info">区/县</el-tag>
          <el-tag v-else type="danger">其他</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag v-if="regionDetail.status === 1" type="success">启用</el-tag>
          <el-tag v-else type="danger">禁用</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ regionDetail.created_at }}</el-descriptions-item>
        <el-descriptions-item label="更新时间">{{ regionDetail.updated_at }}</el-descriptions-item>
      </el-descriptions>
    </el-card>
    
    <el-card class="box-card" style="margin-top: 20px;">
      <div slot="header" class="clearfix">
        <span>子地区列表</span>
        <el-button
          style="float: right; padding: 3px 0"
          type="text"
          icon="el-icon-plus"
          @click="handleAddChild"
        >
          添加子地区
        </el-button>
      </div>
      
      <el-table
        v-loading="childrenLoading"
        :data="regionDetail.children || []"
        border
        style="width: 100%"
      >
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="name" label="地区名称" min-width="150" />
        <el-table-column prop="code" label="地区编码" width="120" />
        <el-table-column prop="level" label="层级" width="80">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.level === 1" type="success">省级</el-tag>
            <el-tag v-else-if="scope.row.level === 2" type="warning">市级</el-tag>
            <el-tag v-else-if="scope.row.level === 3" type="info">区/县</el-tag>
            <el-tag v-else type="danger">其他</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.status === 1" type="success">启用</el-tag>
            <el-tag v-else type="danger">禁用</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-view"
              @click="goToDetail(scope.row.id)"
            >
              详情
            </el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleEditChild(scope.row)"
            >
              编辑
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <div class="empty-block" v-if="!regionDetail.children || regionDetail.children.length === 0">
        <span class="empty-text">暂无子地区</span>
      </div>
    </el-card>
    
    <el-card class="box-card" style="margin-top: 20px;">
      <div slot="header" class="clearfix">
        <span>关联团队</span>
      </div>
      
      <el-table
        v-loading="teamsLoading"
        :data="regionDetail.teams || []"
        border
        style="width: 100%"
      >
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="name" label="团队名称" min-width="150" />
        <el-table-column prop="leader_id" label="团长ID" width="100" />
        <el-table-column prop="member_count" label="成员数量" width="100" />
        <el-table-column prop="status" label="状态" width="100">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.status === 1" type="success">启用</el-tag>
            <el-tag v-else type="danger">禁用</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150" fixed="right">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-view"
              @click="goToTeamDetail(scope.row.id)"
            >
              查看团队
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <div class="empty-block" v-if="!regionDetail.teams || regionDetail.teams.length === 0">
        <span class="empty-text">暂无关联团队</span>
      </div>
    </el-card>
    
    <!-- 地区表单对话框 -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="500px" @close="resetForm">
      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        label-width="100px"
        size="small"
      >
        <el-form-item label="地区名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入地区名称" />
        </el-form-item>
        <el-form-item label="地区编码" prop="code">
          <el-input v-model="form.code" placeholder="请输入地区编码" />
        </el-form-item>
        <el-form-item label="上级地区" prop="parent_id" v-if="!isEditingChild">
          <el-cascader
            v-model="form.parent_id"
            :options="regionTree"
            :props="{ 
              checkStrictly: true,
              value: 'id',
              label: 'name',
              emitPath: false
            }"
            clearable
            placeholder="请选择上级地区"
          />
        </el-form-item>
        <el-form-item label="层级" prop="level">
          <el-select v-model="form.level" placeholder="请选择层级">
            <el-option label="省级" :value="1" />
            <el-option label="市级" :value="2" />
            <el-option label="区/县" :value="3" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitForm">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getRegionDetail, getRegionTree, createRegion, updateRegion } from '@/api/region'

export default {
  name: 'RegionDetail',
  data() {
    return {
      regionId: null,
      regionDetail: {},
      parentName: '顶级地区',
      loading: false,
      childrenLoading: false,
      teamsLoading: false,
      regionTree: [],
      
      // 对话框状态
      dialogVisible: false,
      dialogTitle: '',
      isEditingChild: false,
      
      // 表单数据
      form: {
        id: undefined,
        name: '',
        code: '',
        parent_id: 0,
        level: 1,
        status: 1
      },
      
      // 表单校验规则
      rules: {
        name: [
          { required: true, message: '请输入地区名称', trigger: 'blur' },
          { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
        ],
        code: [
          { required: true, message: '请输入地区编码', trigger: 'blur' },
          { pattern: /^[A-Za-z0-9]+$/, message: '地区编码只能包含字母和数字', trigger: 'blur' }
        ],
        level: [
          { required: true, message: '请选择层级', trigger: 'change' }
        ]
      }
    }
  },
  created() {
    this.regionId = this.$route.params.id
    this.fetchData()
    this.getRegionTree()
  },
  methods: {
    async fetchData() {
      if (!this.regionId) return
      
      this.loading = true
      this.childrenLoading = true
      this.teamsLoading = true
      
      try {
        const { data } = await getRegionDetail(this.regionId)
        this.regionDetail = data
        
        // 查找父地区名称
        if (data.parent_id > 0) {
          const parentRegion = await this.findParentRegion(data.parent_id)
          this.parentName = parentRegion ? parentRegion.name : '未知地区'
        }
      } catch (error) {
        console.error('获取地区详情失败:', error)
        this.$message.error('获取地区详情失败')
      } finally {
        this.loading = false
        this.childrenLoading = false
        this.teamsLoading = false
      }
    },
    async getRegionTree() {
      try {
        const { data } = await getRegionTree()
        this.regionTree = [{ id: 0, name: '顶级地区', children: data }]
      } catch (error) {
        console.error('获取地区树形结构失败:', error)
      }
    },
    async findParentRegion(parentId) {
      try {
        const { data } = await getRegionDetail(parentId)
        return data
      } catch (error) {
        return null
      }
    },
    goBack() {
      this.$router.push('/region/list')
    },
    goToDetail(id) {
      this.$router.push(`/region/detail/${id}`)
    },
    goToTeamDetail(id) {
      this.$router.push(`/team/detail/${id}`)
    },
    handleEdit() {
      this.dialogTitle = '编辑地区'
      this.dialogVisible = true
      this.isEditingChild = false
      this.form = {
        id: this.regionDetail.id,
        name: this.regionDetail.name,
        code: this.regionDetail.code,
        parent_id: this.regionDetail.parent_id,
        level: this.regionDetail.level,
        status: this.regionDetail.status
      }
    },
    handleAddChild() {
      this.dialogTitle = '添加子地区'
      this.dialogVisible = true
      this.isEditingChild = true
      this.form = {
        id: undefined,
        name: '',
        code: '',
        parent_id: this.regionId,
        level: this.regionDetail.level + 1,
        status: 1
      }
    },
    handleEditChild(row) {
      this.dialogTitle = '编辑子地区'
      this.dialogVisible = true
      this.isEditingChild = true
      this.form = {
        id: row.id,
        name: row.name,
        code: row.code,
        parent_id: row.parent_id,
        level: row.level,
        status: row.status
      }
    },
    resetForm() {
      this.$refs.form && this.$refs.form.resetFields()
    },
    submitForm() {
      this.$refs.form.validate(async valid => {
        if (!valid) return
        
        try {
          if (this.form.id) {
            // 更新
            await updateRegion(this.form.id, this.form)
            this.$message.success('更新成功')
          } else {
            // 新增
            await createRegion(this.form)
            this.$message.success('新增成功')
          }
          this.dialogVisible = false
          this.fetchData()
          this.getRegionTree() // 更新树形结构
        } catch (error) {
          this.$message.error('操作失败')
        }
      })
    }
  }
}
</script>

<style scoped>
.detail-header {
  margin-bottom: 20px;
}

.empty-block {
  min-height: 60px;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #909399;
}
</style>
