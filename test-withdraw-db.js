console.log('🚀 开始创建提现系统数据库...');

const mysql = require('mysql2/promise');

async function createWithdrawDB() {
  try {
    const connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: 'wo587129955',
      database: 'mall'
    });
    
    console.log('✅ 数据库连接成功');
    
    // 创建提现方式表
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS withdraw_methods (
        id int(11) NOT NULL AUTO_INCREMENT,
        type_code varchar(20) NOT NULL,
        type_name varchar(50) NOT NULL,
        icon varchar(100) DEFAULT NULL,
        min_amount decimal(10,2) NOT NULL DEFAULT '1.00',
        max_amount decimal(10,2) NOT NULL DEFAULT '50000.00',
        fee_rate decimal(5,4) NOT NULL DEFAULT '0.0100',
        process_time varchar(50) DEFAULT '实时到账',
        is_enabled tinyint(1) NOT NULL DEFAULT '1',
        sort_order int(11) NOT NULL DEFAULT '0',
        PRIMARY KEY (id),
        UNIQUE KEY type_code (type_code)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
    `);
    console.log('✅ 提现方式表创建成功');
    
    // 插入提现方式数据
    await connection.execute(`
      INSERT INTO withdraw_methods (type_code, type_name, icon, min_amount, max_amount, fee_rate, process_time, sort_order) VALUES
      ('wechat', '微信零钱', '/assets/icons/wechat.png', 1.00, 20000.00, 0.0060, '实时到账', 1),
      ('alipay', '支付宝', '/assets/icons/alipay.png', 1.00, 20000.00, 0.0060, '实时到账', 2),
      ('bank_card', '银行卡', '/assets/icons/bank.png', 50.00, 50000.00, 0.0100, '1-3个工作日', 3)
      ON DUPLICATE KEY UPDATE
        type_name = VALUES(type_name),
        icon = VALUES(icon),
        min_amount = VALUES(min_amount),
        max_amount = VALUES(max_amount),
        fee_rate = VALUES(fee_rate),
        process_time = VALUES(process_time)
    `);
    console.log('✅ 提现方式数据插入成功');
    
    // 创建提现申请表
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS withdraw_requests (
        id int(11) NOT NULL AUTO_INCREMENT,
        user_id int(11) NOT NULL,
        amount decimal(10,2) NOT NULL,
        fee decimal(10,2) NOT NULL DEFAULT '0.00',
        actual_amount decimal(10,2) NOT NULL,
        withdraw_type varchar(20) NOT NULL,
        status tinyint(1) NOT NULL DEFAULT '0',
        apply_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
        transaction_no varchar(50) DEFAULT NULL,
        PRIMARY KEY (id),
        KEY user_id (user_id)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
    `);
    console.log('✅ 提现申请表创建成功');
    
    // 查询验证
    const [methods] = await connection.execute('SELECT * FROM withdraw_methods ORDER BY sort_order');
    console.log('\n📋 提现方式配置:');
    console.table(methods);
    
    await connection.end();
    console.log('\n🎉 提现系统数据库创建完成！');
    
  } catch (error) {
    console.error('❌ 错误:', error.message);
  }
}

createWithdrawDB();
