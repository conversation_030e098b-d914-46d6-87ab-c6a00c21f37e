const express = require('express');
const router = express.Router();
const { verifyToken, checkRole } = require('../middlewares/auth');
const logger = require('../utils/logger');
const db = require('../database');

/**
 * 初始化默认广告位
 */
async function initDefaultAdSpaces() {
  try {
    logger.info('检查广告位是否存在...');
    const spaces = await db.query('SELECT * FROM ad_space');
    
    if (spaces.length === 0) {
      logger.info('创建默认广告位...');
      const defaultSpaces = [
        { 
          name: '首页轮播图', 
          code: 'home_banner', 
          width: 750, 
          height: 350, 
          description: '首页顶部轮播广告位', 
          price: 200, 
          status: 1 
        },
        { 
          name: '商城顶部广告', 
          code: 'mall_top_banner', 
          width: 750, 
          height: 200, 
          description: '商城顶部广告位', 
          price: 150, 
          status: 1 
        },
        { 
          name: '商品详情页推荐', 
          code: 'product_recommend', 
          width: 690, 
          height: 200, 
          description: '商品详情页底部推荐广告', 
          price: 100, 
          status: 1 
        },
        { 
          name: '个人中心广告', 
          code: 'profile_banner', 
          width: 750, 
          height: 200, 
          description: '个人中心顶部广告', 
          price: 120, 
          status: 1 
        }
      ];
      
      for (const space of defaultSpaces) {
        await db.query(
          'INSERT INTO ad_space (name, code, width, height, description, price, status, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), NOW())',
          [space.name, space.code, space.width, space.height, space.description, space.price, space.status]
        );
      }
      
      logger.info('默认广告位创建完成');
    } else {
      logger.info(`已存在${spaces.length}个广告位，无需初始化`);
    }
  } catch (err) {
    logger.error('初始化广告位失败:', err);
  }
}

// 启动时初始化默认广告位
initDefaultAdSpaces();

/**
 * 获取广告位分类列表
 * GET /api/advertisement/categories
 */
router.get('/categories', verifyToken, async (req, res, next) => {
  try {
    // 从数据库中获取分类
    const categories = [
      { code: 'home', name: '首页广告位' },
      { code: 'mall', name: '商城广告位' },
      { code: 'product', name: '商品详情页广告位' },
      { code: 'wifi', name: 'WiFi连接页广告位' },
      { code: 'profile', name: '个人中心广告位' },
      { code: 'popup', name: '弹窗广告位' }
    ];

    res.json({
      status: 'success',
      data: categories
    });
  } catch (error) {
    next(error);
  }
});

/**
 * 获取广告位列表
 * GET /api/advertisement/spaces
 */
router.get('/spaces', verifyToken, async (req, res, next) => {
  try {
    const { name, status, page = 1, limit = 10 } = req.query;
    
    // 确保page和limit是数字类型
    const pageNum = parseInt(page, 10);
    const limitNum = parseInt(limit, 10);
    
    // 构建查询条件
    let whereClause = '1=1';
    const params = [];
    
    if (name) {
      whereClause += ' AND name LIKE ?';
      params.push(`%${name}%`);
    }
    
    if (status !== undefined) {
      whereClause += ' AND status = ?';
      params.push(parseInt(status, 10));
    }
    
    // 计算总数
    const countSql = `SELECT COUNT(*) as total FROM ad_space WHERE ${whereClause}`;
    const countResult = await db.query(countSql, params);
    const total = countResult[0].total;
    
    // 计算偏移量
    const offset = (pageNum - 1) * limitNum;
    
    // 使用普通查询而不是参数化查询来处理LIMIT
    const sql = `
      SELECT * FROM ad_space 
      WHERE ${whereClause}
      ORDER BY id DESC
      LIMIT ${offset}, ${limitNum}
    `;
    
    const spaces = await db.query(sql, params);
    
    // 调试信息
    console.log(`广告位列表查询结果: ${spaces.length}条记录`);
    console.log('广告位列表数据:', JSON.stringify(spaces, null, 2));
    
    // 确保价格字段是数字类型
    const processedSpaces = spaces.map(space => ({
      ...space,
      price: parseFloat(space.price) || 0
    }));
    
    res.json({
      status: 'success',
      data: {
        list: processedSpaces,
        total: total,
        page: pageNum,
        limit: limitNum
      }
    });
  } catch (error) {
    logger.error('获取广告位列表失败:', error);
    next(error);
  }
});

/**
 * 获取单个广告位
 * GET /api/advertisement/spaces/:id
 */
router.get('/spaces/:id', verifyToken, async (req, res, next) => {
  try {
    const { id } = req.params;
    
    const sql = 'SELECT * FROM ad_space WHERE id = ?';
    const spaces = await db.query(sql, [id]);
    
    if (spaces.length === 0) {
      return res.status(404).json({
        status: 'error',
        message: '广告位不存在'
      });
    }
    
    res.json({
      status: 'success',
      data: spaces[0]
    });
  } catch (error) {
    next(error);
  }
});

/**
 * 创建广告位
 * POST /api/advertisement/spaces
 */
router.post('/spaces', verifyToken, checkRole(['admin']), async (req, res, next) => {
  try {
    const { name, code, width, height, description, price, status } = req.body;
    
    // 验证必填字段
    if (!name || !code) {
      return res.status(400).json({
        status: 'error',
        message: '广告位名称和编码不能为空'
      });
    }
    
    // 检查编码是否已存在
    const checkSql = 'SELECT COUNT(*) as count FROM ad_space WHERE code = ?';
    const checkResult = await db.query(checkSql, [code]);
    
    if (checkResult[0].count > 0) {
      return res.status(400).json({
        status: 'error',
        message: '广告位编码已存在'
      });
    }
    
    // 插入数据
    const sql = `
      INSERT INTO ad_space (name, code, width, height, description, price, status, created_at, updated_at)
      VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
    `;
    
    const result = await db.query(sql, [
      name, 
      code, 
      width || 0, 
      height || 0, 
      description || '', 
      price || 0, 
      status === undefined ? 1 : status
    ]);
    
    // 获取插入的数据
    const insertedId = result.insertId;
    const insertedSpace = await db.query('SELECT * FROM ad_space WHERE id = ?', [insertedId]);
    
    res.status(201).json({
      status: 'success',
      message: '广告位创建成功',
      data: insertedSpace[0]
    });
  } catch (error) {
    logger.error('创建广告位失败:', error);
    next(error);
  }
});

/**
 * 更新广告位
 * PUT /api/advertisement/spaces/:id
 */
router.put('/spaces/:id', verifyToken, checkRole(['admin']), async (req, res, next) => {
  try {
    const { id } = req.params;
    const { name, code, width, height, description, price, status } = req.body;
    
    // 检查广告位是否存在
    const checkSql = 'SELECT COUNT(*) as count FROM ad_space WHERE id = ?';
    const checkResult = await db.query(checkSql, [id]);
    
    if (checkResult[0].count === 0) {
      return res.status(404).json({
        status: 'error',
        message: '广告位不存在'
      });
    }
    
    // 如果更新code，检查是否与其他广告位冲突
    if (code) {
      const codeCheckSql = 'SELECT COUNT(*) as count FROM ad_space WHERE code = ? AND id != ?';
      const codeCheckResult = await db.query(codeCheckSql, [code, id]);
      
      if (codeCheckResult[0].count > 0) {
        return res.status(400).json({
          status: 'error',
          message: '广告位编码已存在'
        });
      }
    }
    
    // 构建更新字段
    const updateFields = [];
    const updateParams = [];
    
    if (name !== undefined) {
      updateFields.push('name = ?');
      updateParams.push(name);
    }
    
    if (code !== undefined) {
      updateFields.push('code = ?');
      updateParams.push(code);
    }
    
    if (width !== undefined) {
      updateFields.push('width = ?');
      updateParams.push(width);
    }
    
    if (height !== undefined) {
      updateFields.push('height = ?');
      updateParams.push(height);
    }
    
    if (description !== undefined) {
      updateFields.push('description = ?');
      updateParams.push(description);
    }
    
    if (price !== undefined) {
      updateFields.push('price = ?');
      updateParams.push(price);
    }
    
    if (status !== undefined) {
      updateFields.push('status = ?');
      updateParams.push(status);
    }
    
    // 如果没有要更新的字段，直接返回成功
    if (updateFields.length === 0) {
      return res.json({
        status: 'success',
        message: '无字段更新'
      });
    }
    
    // 更新数据
    const sql = `UPDATE ad_space SET ${updateFields.join(', ')} WHERE id = ?`;
    await db.query(sql, [...updateParams, id]);
    
    // 获取更新后的数据
    const updatedSpace = await db.query('SELECT * FROM ad_space WHERE id = ?', [id]);
    
    res.json({
      status: 'success',
      message: '广告位更新成功',
      data: updatedSpace[0]
    });
  } catch (error) {
    logger.error('更新广告位失败:', error);
    next(error);
  }
});

/**
 * 删除广告位
 * DELETE /api/advertisement/spaces/:id
 */
router.delete('/spaces/:id', verifyToken, checkRole(['admin']), async (req, res, next) => {
  try {
    const { id } = req.params;
    
    // 检查广告位是否存在
    const checkSql = 'SELECT COUNT(*) as count FROM ad_space WHERE id = ?';
    const checkResult = await db.query(checkSql, [id]);
    
    if (checkResult[0].count === 0) {
      return res.status(404).json({
        status: 'error',
        message: '广告位不存在'
      });
    }
    
    // 删除广告位
    await db.query('DELETE FROM ad_space WHERE id = ?', [id]);
    
    res.json({
      status: 'success',
      message: '广告位删除成功'
    });
  } catch (error) {
    logger.error('删除广告位失败:', error);
    next(error);
  }
});

/**
 * 获取广告位编码说明
 * GET /api/advertisement/code-guide
 */
router.get('/code-guide', verifyToken, async (req, res, next) => {
  try {
    const codeGuide = {
      naming_rules: {
        title: '广告位编码命名规则',
        rules: [
          '格式：{页面}_{位置}_{类型}',
          '页面：home(首页)、mall(商城)、product(商品)、wifi(WiFi)、profile(个人中心)',
          '位置：top(顶部)、middle(中部)、bottom(底部)、sidebar(侧边)、popup(弹窗)',
          '类型：banner(横幅)、recommend(推荐)、interstitial(插屏)'
        ]
      },
      examples: {
        title: '编码示例',
        examples: [
          { code: 'home_banner', description: '首页轮播图' },
          { code: 'mall_top_banner', description: '商城顶部横幅' },
          { code: 'product_detail_bottom', description: '商品详情底部' },
          { code: 'wifi_success_banner', description: 'WiFi连接成功页' },
          { code: 'app_startup_popup', description: '启动页弹窗' }
        ]
      },
      categories: [
        { code: 'home', name: '首页广告位' },
        { code: 'mall', name: '商城广告位' },
        { code: 'product', name: '商品详情页广告位' },
        { code: 'wifi', name: 'WiFi连接页广告位' },
        { code: 'profile', name: '个人中心广告位' },
        { code: 'popup', name: '弹窗广告位' }
      ].map(item => ({
        code: item.code,
        name: item.name,
        description: `${item.name}相关的广告位`
      }))
    };

    res.json({
      status: 'success',
      data: codeGuide
    });
  } catch (error) {
    next(error);
  }
});

/**
 * 获取广告内容列表
 * GET /api/advertisement/contents
 */
router.get('/contents', verifyToken, async (req, res, next) => {
  try {
    const { title, spaceId, status, isRecommend, page = 1, limit = 10 } = req.query;
    
    // 确保page和limit是数字类型
    const pageNum = parseInt(page, 10);
    const limitNum = parseInt(limit, 10);
    
    // 构建查询条件
    let whereClause = '1=1';
    const params = [];
    
    if (title) {
      whereClause += ' AND a.title LIKE ?';
      params.push(`%${title}%`);
    }
    
    if (spaceId) {
      whereClause += ' AND a.space_id = ?';
      params.push(parseInt(spaceId, 10));
    }
    
    if (status !== undefined) {
      whereClause += ' AND a.status = ?';
      params.push(parseInt(status, 10));
    }
    
    if (isRecommend !== undefined) {
      whereClause += ' AND a.is_recommend = ?';
      params.push(parseInt(isRecommend, 10));
    }
    
    // 计算总数
    const countSql = `SELECT COUNT(*) as total FROM advertisement WHERE ${whereClause.replace(/a\./g, '')}`;
    const countResult = await db.query(countSql, params);
    const total = countResult[0].total;
    
    // 计算偏移量
    const offset = (pageNum - 1) * limitNum;
    
    // 使用普通查询而不是参数化查询来处理LIMIT
    const sql = `
      SELECT a.*, s.name as space_name 
      FROM advertisement a
      LEFT JOIN ad_space s ON a.space_id = s.id
      WHERE ${whereClause}
      ORDER BY a.id DESC
      LIMIT ${offset}, ${limitNum}
    `;
    
    logger.info('广告内容查询SQL:', { sql, params });
    const contents = await db.query(sql, params);
    
    // 处理图片URL
    const processedContents = contents.map(content => {
      if (content.image && !content.image.startsWith('http')) {
        // 确保图片URL有正确的前缀
        if (content.image.startsWith('/uploads/uploads/')) {
          content.image = content.image.replace('/uploads/uploads/', '/uploads/');
        } else if (!content.image.startsWith('/uploads/')) {
          content.image = `/uploads/${content.image}`;
        }
      }
      return content;
    });
    
    res.json({
      status: 'success',
      data: {
        list: processedContents,
        total: total,
        page: pageNum,
        limit: limitNum
      }
    });
  } catch (error) {
    logger.error('获取广告内容列表失败:', error);
    next(error);
  }
});

/**
 * 获取单个广告内容
 * GET /api/advertisement/contents/:id
 */
router.get('/contents/:id', verifyToken, async (req, res, next) => {
  try {
    const { id } = req.params;
    
    const sql = `
      SELECT a.*, s.name as space_name 
      FROM advertisement a
      LEFT JOIN ad_space s ON a.space_id = s.id
      WHERE a.id = ?
    `;
    const contents = await db.query(sql, [id]);
    
    if (contents.length === 0) {
      return res.status(404).json({
        status: 'error',
        message: '广告内容不存在'
      });
    }
    
    // 处理图片URL
    const content = contents[0];
    if (content.image && !content.image.startsWith('http')) {
      // 确保图片URL有正确的前缀
      if (content.image.startsWith('/uploads/uploads/')) {
        content.image = content.image.replace('/uploads/uploads/', '/uploads/');
      } else if (!content.image.startsWith('/uploads/')) {
        content.image = `/uploads/${content.image}`;
      }
    }
    
    res.json({
      status: 'success',
      data: content
    });
  } catch (error) {
    logger.error('获取广告内容详情失败:', error);
    next(error);
  }
});

/**
 * 创建广告内容
 * POST /api/advertisement/contents
 */
router.post('/contents', verifyToken, checkRole(['admin']), async (req, res, next) => {
  try {
    const { 
      title, 
      space_id, 
      image, 
      url, 
      start_time, 
      end_time, 
      status = 1, 
      is_recommend = 0 
    } = req.body;
    
    // 验证必填字段
    if (!title || !space_id || !image) {
      return res.status(400).json({
        status: 'error',
        message: '广告标题、广告位和图片不能为空'
      });
    }
    
    // 检查广告位是否存在
    const checkSpaceSql = 'SELECT COUNT(*) as count FROM ad_space WHERE id = ?';
    const checkSpaceResult = await db.query(checkSpaceSql, [space_id]);
    
    if (checkSpaceResult[0].count === 0) {
      return res.status(400).json({
        status: 'error',
        message: '选择的广告位不存在'
      });
    }
    
    // 处理图片路径
    let processedImage = image;
    
    // 如果图片路径不是以/uploads开头且不是外部URL，则添加/uploads前缀
    if (image && !image.startsWith('/uploads/') && !image.startsWith('http')) {
      processedImage = `/uploads/${image}`;
    }
    
    // 如果没有提供图片，使用默认图片
    if (!image || image === '') {
      processedImage = '/uploads/default-ad.jpg';
    }
    
    console.log('处理后的图片路径:', processedImage);
    
    // 插入数据
    const sql = `
      INSERT INTO advertisement (
        title, space_id, image, url,
        start_time, end_time, status,
        view_count, click_count, created_at, updated_at
      )
      VALUES (?, ?, ?, ?, ?, ?, ?, 0, 0, NOW(), NOW())
    `;
    
    const result = await db.query(sql, [
      title,
      space_id,
      processedImage,
      url || '',
      start_time || new Date().toISOString().slice(0, 19).replace('T', ' '),
      end_time || new Date(Date.now() + 30*24*60*60*1000).toISOString().slice(0, 19).replace('T', ' '),
      status
    ]);
    
    // 获取插入的数据
    const insertedId = result.insertId;
    const insertedContent = await db.query('SELECT * FROM advertisement WHERE id = ?', [insertedId]);
    
    res.status(201).json({
      status: 'success',
      message: '广告内容创建成功',
      data: insertedContent[0]
    });
  } catch (error) {
    logger.error('创建广告内容失败:', error);
    next(error);
  }
});

/**
 * 更新广告内容
 * PUT /api/advertisement/contents/:id
 */
router.put('/contents/:id', verifyToken, checkRole(['admin']), async (req, res, next) => {
  try {
    const { id } = req.params;
    const { 
      title, 
      space_id, 
      image, 
      url, 
      start_time, 
      end_time, 
      status
    } = req.body;
    
    logger.info('更新广告内容参数:', { id, body: req.body });
    
    // 检查广告内容是否存在
    const checkSql = 'SELECT COUNT(*) as count FROM advertisement WHERE id = ?';
    const checkResult = await db.query(checkSql, [id]);
    
    if (checkResult[0].count === 0) {
      return res.status(404).json({
        status: 'error',
        message: '广告内容不存在'
      });
    }
    
    // 如果更新了广告位，检查广告位是否存在
    if (space_id) {
      const checkSpaceSql = 'SELECT COUNT(*) as count FROM ad_space WHERE id = ?';
      const checkSpaceResult = await db.query(checkSpaceSql, [space_id]);
      
      if (checkSpaceResult[0].count === 0) {
        return res.status(400).json({
          status: 'error',
          message: '选择的广告位不存在'
        });
      }
    }
    
    // 构建更新字段
    const updateFields = [];
    const updateParams = [];
    
    if (title !== undefined) {
      updateFields.push('title = ?');
      updateParams.push(title);
    }
    
    if (space_id !== undefined) {
      updateFields.push('space_id = ?');
      updateParams.push(space_id);
    }
    
    if (image !== undefined) {
      // 处理图片路径
      let processedImage = image;
      
      // 如果图片路径不是以/uploads开头且不是外部URL，则添加/uploads前缀
      if (image && !image.startsWith('/uploads/') && !image.startsWith('http')) {
        processedImage = `/uploads/${image}`;
      }
      
      // 如果没有提供图片，使用默认图片
      if (!image || image === '') {
        processedImage = '/uploads/default-ad.jpg';
      }
      
      console.log('更新广告 - 处理后的图片路径:', processedImage);
      
      updateFields.push('image = ?');
      updateParams.push(processedImage);
    }
    
    if (url !== undefined) {
      updateFields.push('url = ?');
      updateParams.push(url);
    }
    
    if (start_time !== undefined) {
      updateFields.push('start_time = ?');
      // 转换ISO日期格式为MySQL可接受的格式
      let formattedStartTime = start_time;
      if (start_time && start_time.includes('T')) {
        formattedStartTime = start_time.replace('T', ' ').replace(/\.\d+Z$/, '');
      }
      updateParams.push(formattedStartTime);
    }
    
    if (end_time !== undefined) {
      updateFields.push('end_time = ?');
      // 转换ISO日期格式为MySQL可接受的格式
      let formattedEndTime = end_time;
      if (end_time && end_time.includes('T')) {
        formattedEndTime = end_time.replace('T', ' ').replace(/\.\d+Z$/, '');
      }
      updateParams.push(formattedEndTime);
    }
    
    if (status !== undefined) {
      updateFields.push('status = ?');
      // 确保status是数字
      updateParams.push(parseInt(status, 10));
    }
    
    // 如果没有要更新的字段，直接返回成功
    if (updateFields.length === 0) {
      return res.json({
        status: 'success',
        message: '无字段更新'
      });
    }
    
    // 更新数据
    const sql = `UPDATE advertisement SET ${updateFields.join(', ')} WHERE id = ?`;
    await db.query(sql, [...updateParams, id]);
    
    // 获取更新后的数据
    const updatedContent = await db.query('SELECT * FROM advertisement WHERE id = ?', [id]);
    
    res.json({
      status: 'success',
      message: '广告内容更新成功',
      data: updatedContent[0]
    });
  } catch (error) {
    logger.error('更新广告内容失败:', error);
    next(error);
  }
});

/**
 * 删除广告内容
 * DELETE /api/advertisement/contents/:id
 */
router.delete('/contents/:id', verifyToken, checkRole(['admin']), async (req, res, next) => {
  try {
    const { id } = req.params;
    
    // 检查广告内容是否存在
    const checkSql = 'SELECT COUNT(*) as count FROM advertisement WHERE id = ?';
    const checkResult = await db.query(checkSql, [id]);
    
    if (checkResult[0].count === 0) {
      return res.status(404).json({
        status: 'error',
        message: '广告内容不存在'
      });
    }
    
    // 删除广告内容
    await db.query('DELETE FROM advertisement WHERE id = ?', [id]);
    
    res.json({
      status: 'success',
      message: '广告内容删除成功'
    });
  } catch (error) {
    logger.error('删除广告内容失败:', error);
    next(error);
  }
});

/**
 * 获取广告统计概览
 * GET /api/advertisement/stats
 */
router.get('/stats', verifyToken, async (req, res, next) => {
  try {
    // 模拟广告统计数据
    const stats = {
      total_ads: 25,
      active_ads: 18,
      total_clicks: 15600,
      total_revenue: 8500.50,
      today_clicks: 320,
      today_revenue: 180.30,
      click_rate: 0.045,
      conversion_rate: 0.032
    };

    res.json({
      status: 'success',
      message: '获取广告统计成功',
      data: stats
    });
  } catch (error) {
    logger.error('获取广告统计失败:', error);
    next(error);
  }
});

/**
 * 获取广告趋势数据
 * GET /api/advertisement/trend
 */
router.get('/trend', verifyToken, async (req, res, next) => {
  try {
    const { period = '7d' } = req.query;

    // 模拟趋势数据
    const trendData = [];
    const days = period === '7d' ? 7 : period === '30d' ? 30 : 90;

    for (let i = days - 1; i >= 0; i--) {
      const date = new Date();
      date.setDate(date.getDate() - i);
      trendData.push({
        date: date.toISOString().split('T')[0],
        clicks: Math.floor(Math.random() * 500) + 200,
        revenue: Math.random() * 200 + 50,
        impressions: Math.floor(Math.random() * 2000) + 1000
      });
    }

    res.json({
      status: 'success',
      message: '获取广告趋势成功',
      data: trendData
    });
  } catch (error) {
    logger.error('获取广告趋势失败:', error);
    next(error);
  }
});

/**
 * 获取广告位排行
 * GET /api/advertisement/space-rank
 */
router.get('/space-rank', verifyToken, async (req, res, next) => {
  try {
    const { period = '7d' } = req.query;

    // 模拟广告位排行数据
    const spaceRank = [
      {
        space_id: 12,
        space_name: '首页顶部轮播图',
        clicks: 3200,
        revenue: 850.50,
        click_rate: 0.065
      },
      {
        space_id: 13,
        space_name: '首页中部推荐位',
        clicks: 2800,
        revenue: 720.30,
        click_rate: 0.058
      },
      {
        space_id: 14,
        space_name: '首页底部广告栏',
        clicks: 2100,
        revenue: 560.20,
        click_rate: 0.042
      },
      {
        space_id: 15,
        space_name: '分类页顶部',
        clicks: 1800,
        revenue: 480.10,
        click_rate: 0.038
      },
      {
        space_id: 16,
        space_name: '分类页内嵌',
        clicks: 1500,
        revenue: 390.80,
        click_rate: 0.035
      }
    ];

    res.json({
      status: 'success',
      message: '获取广告位排行成功',
      data: spaceRank
    });
  } catch (error) {
    logger.error('获取广告位排行失败:', error);
    next(error);
  }
});

/**
 * 获取广告内容排行
 * GET /api/advertisement/content-rank
 */
router.get('/content-rank', verifyToken, async (req, res, next) => {
  try {
    const { period = '7d' } = req.query;

    // 模拟广告内容排行数据
    const contentRank = [
      {
        content_id: 1,
        title: '夏季新品促销',
        space_name: '首页轮播图',
        clicks: 1200,
        revenue: 320.50,
        click_rate: 0.078
      },
      {
        content_id: 2,
        title: '限时特价商品',
        space_name: '首页推荐位',
        clicks: 980,
        revenue: 260.30,
        click_rate: 0.065
      },
      {
        content_id: 3,
        title: '品牌联合活动',
        space_name: '分类页顶部',
        clicks: 850,
        revenue: 220.80,
        click_rate: 0.058
      },
      {
        content_id: 4,
        title: '会员专享优惠',
        space_name: '用户中心',
        clicks: 720,
        revenue: 180.40,
        click_rate: 0.052
      },
      {
        content_id: 5,
        title: '新用户注册礼',
        space_name: '首页底部',
        clicks: 650,
        revenue: 150.20,
        click_rate: 0.048
      }
    ];

    res.json({
      status: 'success',
      message: '获取广告内容排行成功',
      data: contentRank
    });
  } catch (error) {
    logger.error('获取广告内容排行失败:', error);
    next(error);
  }
});

/**
 * 获取广告位统计
 * GET /api/advertisement/space-stats
 */
router.get('/space-stats', verifyToken, async (req, res, next) => {
  try {
    // 模拟广告位统计数据
    const spaceStats = [
      {
        space_id: 12,
        space_name: '首页顶部轮播图',
        space_code: 'home_banner',
        total_clicks: 3200,
        today_clicks: 120,
        total_revenue: 850.50,
        today_revenue: 32.50,
        click_rate: 0.065,
        avg_revenue: 0.266,
        status: 1
      },
      {
        space_id: 13,
        space_name: '首页中部推荐位',
        space_code: 'home_middle',
        total_clicks: 2800,
        today_clicks: 95,
        total_revenue: 720.30,
        today_revenue: 28.30,
        click_rate: 0.058,
        avg_revenue: 0.257,
        status: 1
      },
      {
        space_id: 14,
        space_name: '首页底部广告栏',
        space_code: 'home_bottom',
        total_clicks: 2100,
        today_clicks: 78,
        total_revenue: 560.20,
        today_revenue: 22.10,
        click_rate: 0.042,
        avg_revenue: 0.267,
        status: 1
      },
      {
        space_id: 15,
        space_name: '分类页顶部',
        space_code: 'category_top',
        total_clicks: 1800,
        today_clicks: 65,
        total_revenue: 480.10,
        today_revenue: 18.50,
        click_rate: 0.038,
        avg_revenue: 0.267,
        status: 1
      },
      {
        space_id: 16,
        space_name: '分类页内嵌',
        space_code: 'category_inline',
        total_clicks: 1500,
        today_clicks: 52,
        total_revenue: 390.80,
        today_revenue: 15.20,
        click_rate: 0.035,
        avg_revenue: 0.260,
        status: 0
      }
    ];

    res.json({
      status: 'success',
      message: '获取广告位统计成功',
      data: spaceStats
    });
  } catch (error) {
    logger.error('获取广告位统计失败:', error);
    next(error);
  }
});

module.exports = router;