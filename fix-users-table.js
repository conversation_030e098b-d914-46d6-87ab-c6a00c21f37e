const mysql = require('mysql2/promise');

async function fixUsersTable() {
  const connection = await mysql.createConnection({
    host: 'localhost',
    user: 'root',
    password: 'wo587129955',
    database: 'mall'
  });
  
  try {
    console.log('🔧 修复users表结构...\n');
    
    // 检查users表结构
    console.log('1️⃣ 检查当前users表结构...');
    const [columns] = await connection.execute("SHOW COLUMNS FROM users");
    console.log('当前字段:');
    columns.forEach(col => {
      console.log(`  - ${col.Field}: ${col.Type} ${col.Null} ${col.Key} ${col.Default}`);
    });
    
    // 检查是否有total_income字段
    const hasTotal = columns.some(col => col.Field === 'total_income');
    const hasBalance = columns.some(col => col.Field === 'balance');
    
    if (!hasTotal) {
      console.log('\n2️⃣ 添加total_income字段...');
      await connection.execute(`
        ALTER TABLE users ADD COLUMN total_income decimal(10,2) DEFAULT 0.00 COMMENT '总收入'
      `);
      console.log('✅ total_income字段添加成功');
    } else {
      console.log('✅ total_income字段已存在');
    }
    
    if (!hasBalance) {
      console.log('\n3️⃣ 添加balance字段...');
      await connection.execute(`
        ALTER TABLE users ADD COLUMN balance decimal(10,2) DEFAULT 0.00 COMMENT '余额'
      `);
      console.log('✅ balance字段添加成功');
    } else {
      console.log('✅ balance字段已存在');
    }
    
    // 检查是否有role字段
    const hasRole = columns.some(col => col.Field === 'role');
    if (!hasRole) {
      console.log('\n4️⃣ 添加role字段...');
      await connection.execute(`
        ALTER TABLE users ADD COLUMN role enum('user','admin','team_leader') DEFAULT 'user' COMMENT '角色'
      `);
      console.log('✅ role字段添加成功');
    } else {
      console.log('✅ role字段已存在');
    }
    
    // 检查是否有status字段
    const hasStatus = columns.some(col => col.Field === 'status');
    if (!hasStatus) {
      console.log('\n5️⃣ 添加status字段...');
      await connection.execute(`
        ALTER TABLE users ADD COLUMN status tinyint(1) DEFAULT 1 COMMENT '状态：0禁用，1启用'
      `);
      console.log('✅ status字段添加成功');
    } else {
      console.log('✅ status字段已存在');
    }
    
    // 检查是否有nickname字段
    const hasNickname = columns.some(col => col.Field === 'nickname');
    if (!hasNickname) {
      console.log('\n6️⃣ 添加nickname字段...');
      await connection.execute(`
        ALTER TABLE users ADD COLUMN nickname varchar(50) NOT NULL DEFAULT '用户' COMMENT '昵称'
      `);
      console.log('✅ nickname字段添加成功');
    } else {
      console.log('✅ nickname字段已存在');
    }
    
    // 检查是否有phone字段
    const hasPhone = columns.some(col => col.Field === 'phone');
    if (!hasPhone) {
      console.log('\n7️⃣ 添加phone字段...');
      await connection.execute(`
        ALTER TABLE users ADD COLUMN phone varchar(20) DEFAULT NULL COMMENT '手机号'
      `);
      console.log('✅ phone字段添加成功');
    } else {
      console.log('✅ phone字段已存在');
    }
    
    // 检查是否有avatar字段
    const hasAvatar = columns.some(col => col.Field === 'avatar');
    if (!hasAvatar) {
      console.log('\n8️⃣ 添加avatar字段...');
      await connection.execute(`
        ALTER TABLE users ADD COLUMN avatar varchar(255) DEFAULT NULL COMMENT '头像'
      `);
      console.log('✅ avatar字段添加成功');
    } else {
      console.log('✅ avatar字段已存在');
    }
    
    // 显示最终表结构
    console.log('\n9️⃣ 最终表结构:');
    const [finalColumns] = await connection.execute("SHOW COLUMNS FROM users");
    finalColumns.forEach(col => {
      console.log(`  - ${col.Field}: ${col.Type} ${col.Null} ${col.Key} ${col.Default}`);
    });
    
    console.log('\n✅ users表结构修复完成！');
    
  } catch (error) {
    console.error('❌ 错误:', error.message);
  } finally {
    await connection.end();
  }
}

fixUsersTable();
