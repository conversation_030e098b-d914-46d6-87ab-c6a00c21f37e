// 测试数据库连接和团队功能
const mysql = require('mysql2');

console.log('🔍 开始测试数据库连接和团队功能...');

// 数据库连接配置
const dbConfig = {
  host: 'localhost',
  user: 'root',
  password: 'wo587129955',
  database: 'mall',
  port: 3306
};

console.log('📋 连接配置:', {
  host: dbConfig.host,
  user: dbConfig.user,
  database: dbConfig.database,
  port: dbConfig.port
});

// 创建连接
const connection = mysql.createConnection(dbConfig);

// 测试连接
connection.connect((err) => {
  if (err) {
    console.error('❌ 数据库连接失败:', err.message);
    console.log('\n💡 解决建议:');
    
    switch (err.code) {
      case 'ECONNREFUSED':
        console.log('1. 检查MySQL服务是否启动: net start mysql');
        console.log('2. 确认端口3306是否正确');
        break;
      case 'ER_ACCESS_DENIED_ERROR':
        console.log('1. 检查用户名和密码是否正确');
        console.log('2. 当前密码: wo587129955');
        break;
      case 'ER_BAD_DB_ERROR':
        console.log('1. 数据库mall不存在，需要先创建');
        console.log('2. 执行: CREATE DATABASE mall;');
        break;
      default:
        console.log('1. 检查MySQL服务状态');
        console.log('2. 确认网络连接');
    }
    return;
  }

  console.log('✅ 数据库连接成功!');
  
  // 测试基本查询
  testBasicQuery();
});

function testBasicQuery() {
  console.log('\n🔍 测试基本查询...');
  
  connection.query('SELECT 1 as test', (err, results) => {
    if (err) {
      console.error('❌ 基本查询失败:', err.message);
      return;
    }
    
    console.log('✅ 基本查询成功');
    
    // 检查团队表
    checkTeamTable();
  });
}

function checkTeamTable() {
  console.log('\n📋 检查团队表结构...');
  
  connection.query('DESCRIBE team', (err, results) => {
    if (err) {
      console.error('❌ 团队表不存在:', err.message);
      console.log('💡 需要执行建表语句创建team表');
      connection.end();
      return;
    }
    
    console.log('✅ 团队表存在');
    console.log('📊 表结构:');
    
    const fields = {};
    results.forEach(field => {
      fields[field.Field] = field.Type;
      console.log(`  - ${field.Field}: ${field.Type} ${field.Null === 'YES' ? '(可空)' : '(非空)'} ${field.Key ? `[${field.Key}]` : ''}`);
    });
    
    // 检查关键字段
    const hasInviteCode = 'invite_code' in fields;
    const hasLevel = 'level' in fields;
    
    console.log('\n🔍 关键字段检查:');
    console.log(`  invite_code: ${hasInviteCode ? '✅ 存在' : '❌ 缺失'}`);
    console.log(`  level: ${hasLevel ? '✅ 存在' : '❌ 缺失'}`);
    
    if (!hasInviteCode || !hasLevel) {
      console.log('\n⚠️ 需要添加缺失字段:');
      if (!hasInviteCode) {
        console.log('ALTER TABLE team ADD COLUMN invite_code VARCHAR(20) UNIQUE COMMENT "团队邀请码";');
      }
      if (!hasLevel) {
        console.log('ALTER TABLE team ADD COLUMN level INT DEFAULT 1 COMMENT "团队等级";');
      }
    }
    
    // 查看团队数据
    checkTeamData();
  });
}

function checkTeamData() {
  console.log('\n📊 检查团队数据...');
  
  connection.query('SELECT * FROM team LIMIT 5', (err, results) => {
    if (err) {
      console.error('❌ 查询团队数据失败:', err.message);
      connection.end();
      return;
    }
    
    console.log(`✅ 找到 ${results.length} 条团队记录`);
    
    if (results.length === 0) {
      console.log('💡 团队表为空，可以插入测试数据:');
      console.log(`INSERT INTO team (name, leader_id, member_count) VALUES ('测试团队', 1, 1);`);
    } else {
      console.log('📋 团队数据:');
      results.forEach((team, index) => {
        console.log(`  ${index + 1}. ID: ${team.id}, 名称: ${team.name || '未命名'}, 邀请码: ${team.invite_code || '无'}, 等级: ${team.level || '无'}`);
      });
      
      // 检查邀请码
      const teamsWithoutCode = results.filter(team => !team.invite_code);
      if (teamsWithoutCode.length > 0) {
        console.log(`\n⚠️ 有 ${teamsWithoutCode.length} 个团队没有邀请码，需要生成:`);
        console.log(`UPDATE team SET invite_code = CONCAT('TEAM', LPAD(id, 6, '0')) WHERE invite_code IS NULL;`);
      }
    }
    
    // 测试团队API查询
    testTeamAPI();
  });
}

function testTeamAPI() {
  console.log('\n🔍 测试团队API查询...');
  
  const teamQuery = `
    SELECT 
      t.id, 
      t.name as teamName, 
      t.member_count as memberCount,
      IFNULL(t.total_profit, 0) as totalIncome,
      1 as teamLevel,
      CONCAT('TEAM', LPAD(t.id, 6, '0')) as inviteCode,
      t.created_at as createTime
    FROM team t
    LIMIT 1
  `;
  
  connection.query(teamQuery, (err, results) => {
    if (err) {
      console.error('❌ 团队API查询失败:', err.message);
    } else if (results.length === 0) {
      console.log('⚠️ 没有团队数据，API将返回默认数据');
    } else {
      console.log('✅ 团队API查询成功:');
      const team = results[0];
      console.log(`  团队名称: ${team.teamName || '未命名'}`);
      console.log(`  成员数量: ${team.memberCount || 0}`);
      console.log(`  总收益: ¥${team.totalIncome || '0.00'}`);
      console.log(`  邀请码: ${team.inviteCode}`);
      console.log(`  等级: LV.${team.teamLevel}`);
    }
    
    // 关闭连接
    connection.end();
    console.log('\n🔌 数据库连接已关闭');
    console.log('\n🎉 测试完成!');
  });
}

// 错误处理
connection.on('error', (err) => {
  console.error('💥 连接错误:', err.message);
});
