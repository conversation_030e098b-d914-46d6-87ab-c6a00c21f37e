const mysql = require('mysql2/promise');

async function testAvatarUpdate() {
  let connection;
  
  try {
    // 连接数据库
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: 'wo587129955',
      database: 'mall'
    });
    
    console.log('🔍 测试头像更新功能...\n');
    
    // 1. 查看当前用户数据
    console.log('1️⃣ 当前用户数据:');
    const [currentUser] = await connection.execute('SELECT id, nickname, avatar FROM user WHERE id = 1');
    console.table(currentUser);
    
    // 2. 模拟前端发送的头像更新数据
    const testAvatarUrl = 'http://localhost:4000/uploads/avatars/test-avatar-' + Date.now() + '.jpg';
    console.log('\n2️⃣ 测试头像URL:', testAvatarUrl);
    
    // 3. 直接更新数据库
    console.log('\n3️⃣ 直接更新数据库...');
    const [updateResult] = await connection.execute(
      'UPDATE user SET avatar = ?, updated_at = NOW() WHERE id = 1',
      [testAvatarUrl]
    );
    
    console.log('更新结果:', {
      affectedRows: updateResult.affectedRows,
      changedRows: updateResult.changedRows
    });
    
    // 4. 查看更新后的数据
    console.log('\n4️⃣ 更新后的用户数据:');
    const [updatedUser] = await connection.execute('SELECT id, nickname, avatar FROM user WHERE id = 1');
    console.table(updatedUser);
    
    // 5. 测试恢复默认头像
    console.log('\n5️⃣ 恢复默认头像...');
    await connection.execute(
      'UPDATE user SET avatar = ?, updated_at = NOW() WHERE id = 1',
      ['/assets/images/default-avatar.png']
    );
    
    const [restoredUser] = await connection.execute('SELECT id, nickname, avatar FROM user WHERE id = 1');
    console.log('恢复后的用户数据:');
    console.table(restoredUser);
    
    console.log('\n✅ 数据库头像更新测试完成');
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// 运行测试
testAvatarUpdate();
