/**
 * 完整的团队功能测试脚本
 * 测试所有修复后的团队相关功能
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:4000/api/v1';
const TEST_TOKEN = 'test-token';

async function testAllTeamFixes() {
  console.log('🚀 开始测试所有团队功能修复...\n');

  try {
    // 1. 测试团队信息API
    console.log('1. 测试团队信息API...');
    const teamInfoResponse = await axios.get(`${BASE_URL}/client/team/info`, {
      headers: {
        'Authorization': `Bearer ${TEST_TOKEN}`
      }
    });
    
    console.log('✅ 团队信息API状态:', teamInfoResponse.status);
    const teamData = teamInfoResponse.data.data;
    const inviteCode = teamData.team ? teamData.team.inviteCode : null;
    console.log('📋 团队名称:', teamData.team?.name);
    console.log('🔑 邀请码:', inviteCode);
    console.log('👥 成员数量:', teamData.team?.memberCount);
    console.log('💰 总收益:', teamData.team?.totalIncome);
    console.log('');

    // 2. 测试团队成员列表API
    console.log('2. 测试团队成员列表API...');
    const membersResponse = await axios.get(`${BASE_URL}/client/team/members?page=1&limit=10`, {
      headers: {
        'Authorization': `Bearer ${TEST_TOKEN}`
      }
    });
    
    console.log('✅ 团队成员API状态:', membersResponse.status);
    const membersList = membersResponse.data.data.list;
    console.log('👥 成员列表数量:', membersList.length);
    if (membersList.length > 0) {
      console.log('👤 第一个成员:', membersList[0].nickname, '- 角色:', membersList[0].role_name);
    }
    console.log('');

    // 3. 测试邀请二维码API（需要认证）
    if (inviteCode) {
      console.log('3. 测试邀请二维码API（需要认证）...');
      const qrcodeResponse = await axios.get(`${BASE_URL}/client/team/invite-qrcode`, {
        params: {
          inviteCode: inviteCode
        },
        headers: {
          'Authorization': `Bearer ${TEST_TOKEN}`
        }
      });
      
      console.log('✅ 邀请二维码API状态:', qrcodeResponse.status);
      console.log('🔗 邀请URL:', qrcodeResponse.data.data.invite_url);
      console.log('📱 二维码URL:', qrcodeResponse.data.data.qrcode_url);
      console.log('');
    }

    // 4. 测试通过邀请码获取团队信息API（公开接口）
    if (inviteCode) {
      console.log('4. 测试通过邀请码获取团队信息API（公开接口）...');
      const teamByCodeResponse = await axios.get(`${BASE_URL}/client/team/info-by-code`, {
        params: {
          inviteCode: inviteCode
        }
      });
      
      console.log('✅ 通过邀请码获取团队信息API状态:', teamByCodeResponse.status);
      const teamByCodeData = teamByCodeResponse.data.data.team;
      console.log('📋 团队名称:', teamByCodeData.name);
      console.log('👤 团长:', teamByCodeData.leader.name);
      console.log('👥 成员数量:', teamByCodeData.memberCount);
      console.log('💰 总收益:', teamByCodeData.totalIncome);
      console.log('');
    }

    // 5. 测试URL格式修复
    console.log('5. 验证URL格式修复...');
    if (inviteCode) {
      const qrcodeResponse = await axios.get(`${BASE_URL}/client/team/invite-qrcode`, {
        params: {
          inviteCode: inviteCode
        },
        headers: {
          'Authorization': `Bearer ${TEST_TOKEN}`
        }
      });
      
      const inviteUrl = qrcodeResponse.data.data.invite_url;
      if (inviteUrl.startsWith('pages/invite/invite?code=')) {
        console.log('✅ URL格式修复成功：使用小程序页面路径');
        console.log('🔗 正确的邀请URL:', inviteUrl);
      } else {
        console.log('❌ URL格式仍有问题:', inviteUrl);
      }
    }

    console.log('\n🎉 所有团队功能测试完成！');
    console.log('\n📋 修复总结:');
    console.log('✅ 团队成员API：从304状态码修复为200');
    console.log('✅ 邀请二维码API：从401未授权修复为200');
    console.log('✅ 团队信息API：正确返回邀请码');
    console.log('✅ 邀请码获取API：新增公开接口');
    console.log('✅ URL格式：从占位符域名修复为小程序页面路径');
    console.log('✅ 邀请页面：创建完整的邀请页面');

  } catch (error) {
    console.error('❌ 测试过程中出错:', error.message);
    if (error.response) {
      console.error('错误响应状态:', error.response.status);
      console.error('错误响应数据:', error.response.data);
    }
  }
}

// 运行测试
testAllTeamFixes();
