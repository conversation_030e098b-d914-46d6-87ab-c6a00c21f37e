const express = require('express');
const router = express.Router();
const { verifyToken } = require('../middlewares/auth');
const { success, error } = require('../utils/response');
const db = require('../../config/database');
const config = require('../../config');

// 管理端团队列表（包含联盟申请转换的团队）
router.get('/list', verifyToken, async (req, res) => {
  console.log('获取团队列表');
  
  // 验证管理员权限
  if (req.user.role !== 'admin') {
    return error(res, '无权限访问', 403);
  }
  
  const { page = 1, limit = 10, name = '', leader = '', status = '' } = req.query;
  const pageNum = parseInt(page);
  const limitNum = parseInt(limit);
  const offset = (pageNum - 1) * limitNum;
  
  try {
    // 首先检查team表是否存在，如果不存在则创建
    const tables = await db.query(
      "SELECT table_name FROM information_schema.tables WHERE table_schema = ? AND table_name = 'team'",
      [config.database.database]
    );
    
    if (!tables || tables.length === 0) {
      // 创建team表
      await db.query(`
        CREATE TABLE IF NOT EXISTS team (
          id INT PRIMARY KEY AUTO_INCREMENT,
          name VARCHAR(100) NOT NULL COMMENT '团队名称',
          leader_id INT NOT NULL COMMENT '团长用户ID',
          member_count INT DEFAULT 1 COMMENT '团队成员数量',
          status TINYINT DEFAULT 1 COMMENT '状态：0-禁用，1-正常',
          level INT DEFAULT 1 COMMENT '团队等级',
          total_income DECIMAL(10, 2) DEFAULT 0.00 COMMENT '团队总收益',
          month_income DECIMAL(10, 2) DEFAULT 0.00 COMMENT '本月收益',
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          INDEX idx_leader_id (leader_id),
          INDEX idx_status (status)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='团队表'
      `);
      
      // 创建团队成员表
      await db.query(`
        CREATE TABLE IF NOT EXISTS team_member (
          id INT PRIMARY KEY AUTO_INCREMENT,
          team_id INT NOT NULL COMMENT '团队ID',
          user_id INT NOT NULL COMMENT '用户ID',
          role VARCHAR(20) DEFAULT 'member' COMMENT '角色：leader-团长，member-成员',
          joined_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          INDEX idx_team_id (team_id),
          INDEX idx_user_id (user_id),
          UNIQUE KEY uk_team_user (team_id, user_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='团队成员表'
      `);
    }
    
    // 构建查询条件
    let whereConditions = [];
    let params = [];
    
    if (name) {
      whereConditions.push('t.name LIKE ?');
      params.push(`%${name}%`);
    }
    
    if (leader) {
      whereConditions.push('u.nickname LIKE ?');
      params.push(`%${leader}%`);
    }
    
    if (status !== '') {
      whereConditions.push('t.status = ?');
      params.push(parseInt(status));
    }
    
    const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';
    
    // 查询总数
    const countResult = await db.query(
      `SELECT COUNT(*) as total FROM team t 
       LEFT JOIN user u ON t.leader_id = u.id 
       ${whereClause}`,
      params
    );
    
    const total = countResult && countResult.length > 0 ? countResult[0].total : 0;
    
    // 查询列表（包含联盟申请来源信息）
    const teams = await db.query(
      `SELECT
        t.*,
        u.nickname as leader_name,
        u.phone as leader_phone,
        u.avatar as leader_avatar,
        (SELECT COUNT(*) FROM team_member WHERE team_id = t.id) as actual_member_count,
        IFNULL(t.total_income, 0) as total_income,
        IFNULL(t.month_income, 0) as month_income,
        IFNULL(t.today_income, 0) as today_income,
        ta.id as alliance_apply_id,
        ta.area as alliance_area,
        ta.description as alliance_description,
        ta.created_at as alliance_apply_time,
        CASE
          WHEN ta.id IS NOT NULL THEN '联盟申请'
          ELSE '手动创建'
        END as team_source,
        CASE
          WHEN ta.id IS NOT NULL THEN 'alliance'
          ELSE 'manual'
        END as source_type
      FROM team t
      LEFT JOIN user u ON t.leader_id = u.id
      LEFT JOIN team_apply ta ON ta.user_id = t.leader_id AND ta.status = 1
      ${whereClause}
      ORDER BY t.created_at DESC
      LIMIT ${limitNum} OFFSET ${offset}`,
      params
    );
    
    return success(res, {
      list: teams || [],
      pagination: {
        total,
        page: pageNum,
        limit: limitNum,
        pages: Math.ceil(total / limitNum) || 1
      }
    }, '获取团队列表成功');
  } catch (err) {
    console.error('获取团队列表失败:', err);
    return error(res, '获取团队列表失败，请稍后重试');
  }
});

// 获取团队详情
router.get('/detail/:id', verifyToken, async (req, res) => {
  console.log('获取团队详情');
  
  if (req.user.role !== 'admin') {
    return error(res, '无权限访问', 403);
  }
  
  const { id } = req.params;
  
  try {
    // 获取团队详情（包含联盟申请信息）
    const teams = await db.query(
      `SELECT
        t.*,
        u.nickname as leader_name,
        u.phone as leader_phone,
        u.avatar as leader_avatar,
        u.created_at as leader_joined_at,
        ta.id as alliance_apply_id,
        ta.name as alliance_apply_name,
        ta.contact as alliance_contact,
        ta.phone as alliance_phone,
        ta.email as alliance_email,
        ta.area as alliance_area,
        ta.description as alliance_description,
        ta.created_at as alliance_apply_time,
        ta.remark as alliance_remark,
        CASE
          WHEN ta.id IS NOT NULL THEN '联盟申请'
          ELSE '手动创建'
        END as team_source,
        CASE
          WHEN ta.id IS NOT NULL THEN 'alliance'
          ELSE 'manual'
        END as source_type
      FROM team t
      LEFT JOIN user u ON t.leader_id = u.id
      LEFT JOIN team_apply ta ON ta.user_id = t.leader_id AND ta.status = 1
      WHERE t.id = ?`,
      [id]
    );

    if (!teams || teams.length === 0) {
      return error(res, '团队不存在');
    }

    const team = teams[0];

    // 获取团队成员统计
    const memberStats = await db.query(
      `SELECT COUNT(*) as total FROM team_member WHERE team_id = ?`,
      [id]
    );

    team.member_stats = {
      total: memberStats[0].total || 1
    };
    
    return success(res, team, '获取团队详情成功');
  } catch (err) {
    console.error('获取团队详情失败:', err);
    return error(res, '获取团队详情失败，请稍后重试');
  }
});

// 创建团队
router.post('/create', verifyToken, async (req, res) => {
  console.log('创建团队');
  
  if (req.user.role !== 'admin') {
    return error(res, '无权限访问', 403);
  }
  
  const { name, leader_id, status = 1 } = req.body;
  
  if (!name || !leader_id) {
    return error(res, '团队名称和团长不能为空');
  }
  
  try {
    // 检查用户是否已经是其他团队的团长
    const existingTeams = await db.query(
      'SELECT * FROM team WHERE leader_id = ?',
      [leader_id]
    );
    
    if (existingTeams && existingTeams.length > 0) {
      return error(res, '该用户已经是其他团队的团长');
    }
    
    // 创建团队
    const result = await db.query(
      'INSERT INTO team (name, leader_id, status, member_count) VALUES (?, ?, ?, 1)',
      [name, leader_id, status]
    );
    
    if (result && result.insertId) {
      // 将团长添加到团队成员表
      await db.query(
        'INSERT INTO team_member (team_id, user_id, role) VALUES (?, ?, ?)',
        [result.insertId, leader_id, 'leader']
      );
      
      // 更新用户为团长
      await db.query(
        'UPDATE user SET is_leader = 1 WHERE id = ?',
        [leader_id]
      );
      
      return success(res, { id: result.insertId }, '创建团队成功');
    }
    
    return error(res, '创建团队失败');
  } catch (err) {
    console.error('创建团队失败:', err);
    return error(res, '创建团队失败，请稍后重试');
  }
});

// 更新团队
router.put('/update/:id', verifyToken, async (req, res) => {
  console.log('更新团队');
  
  if (req.user.role !== 'admin') {
    return error(res, '无权限访问', 403);
  }
  
  const { id } = req.params;
  const { name, status, level } = req.body;
  
  try {
    const updates = [];
    const params = [];
    
    if (name !== undefined) {
      updates.push('name = ?');
      params.push(name);
    }
    
    if (status !== undefined) {
      updates.push('status = ?');
      params.push(status);
    }
    
    if (level !== undefined) {
      updates.push('level = ?');
      params.push(level);
    }
    
    if (updates.length === 0) {
      return error(res, '没有需要更新的内容');
    }
    
    params.push(id);
    
    await db.query(
      `UPDATE team SET ${updates.join(', ')} WHERE id = ?`,
      params
    );
    
    return success(res, { id }, '更新团队成功');
  } catch (err) {
    console.error('更新团队失败:', err);
    return error(res, '更新团队失败，请稍后重试');
  }
});

// 删除团队
router.delete('/delete/:id', verifyToken, async (req, res) => {
  console.log('删除团队');
  
  if (req.user.role !== 'admin') {
    return error(res, '无权限访问', 403);
  }
  
  const { id } = req.params;
  
  try {
    // 获取团队信息
    const teams = await db.query('SELECT * FROM team WHERE id = ?', [id]);
    
    if (!teams || teams.length === 0) {
      return error(res, '团队不存在');
    }
    
    const team = teams[0];
    
    // 删除团队成员关系
    await db.query('DELETE FROM team_member WHERE team_id = ?', [id]);
    
    // 更新团长状态
    await db.query('UPDATE user SET is_leader = 0 WHERE id = ?', [team.leader_id]);
    
    // 删除团队
    await db.query('DELETE FROM team WHERE id = ?', [id]);
    
    return success(res, { id }, '删除团队成功');
  } catch (err) {
    console.error('删除团队失败:', err);
    return error(res, '删除团队失败，请稍后重试');
  }
});

// 获取团队成员列表
router.get('/:id/members', verifyToken, async (req, res) => {
  console.log('获取团队成员列表');
  
  if (req.user.role !== 'admin') {
    return error(res, '无权限访问', 403);
  }
  
  const { id } = req.params;
  const { page = 1, limit = 10 } = req.query;
  const pageNum = parseInt(page);
  const limitNum = parseInt(limit);
  const offset = (pageNum - 1) * limitNum;
  
  try {
    // 查询总数
    const countResult = await db.query(
      'SELECT COUNT(*) as total FROM team_member WHERE team_id = ?',
      [id]
    );
    
    const total = countResult && countResult.length > 0 ? countResult[0].total : 0;
    
    // 查询成员列表
    const members = await db.query(
      `SELECT 
        tm.*,
        u.nickname,
        u.phone,
        u.avatar,
        u.created_at as user_created_at
      FROM team_member tm
      LEFT JOIN user u ON tm.user_id = u.id
      WHERE tm.team_id = ?
      ORDER BY tm.role DESC, tm.joined_at DESC
      LIMIT ${limitNum} OFFSET ${offset}`,
      [id]
    );
    
    return success(res, {
      list: members || [],
      pagination: {
        total,
        page: pageNum,
        limit: limitNum,
        pages: Math.ceil(total / limitNum) || 1
      }
    }, '获取团队成员列表成功');
  } catch (err) {
    console.error('获取团队成员列表失败:', err);
    return error(res, '获取团队成员列表失败，请稍后重试');
  }
});

// 添加团队成员
router.post('/:id/members', verifyToken, async (req, res) => {
  console.log('添加团队成员');
  
  if (req.user.role !== 'admin') {
    return error(res, '无权限访问', 403);
  }
  
  const { id } = req.params;
  const { user_id } = req.body;
  
  if (!user_id) {
    return error(res, '用户ID不能为空');
  }
  
  try {
    // 检查用户是否已经在团队中
    const existing = await db.query(
      'SELECT * FROM team_member WHERE team_id = ? AND user_id = ?',
      [id, user_id]
    );
    
    if (existing && existing.length > 0) {
      return error(res, '该用户已经在团队中');
    }
    
    // 添加成员
    await db.query(
      'INSERT INTO team_member (team_id, user_id, role) VALUES (?, ?, ?)',
      [id, user_id, 'member']
    );
    
    // 更新团队成员数量
    await db.query(
      'UPDATE team SET member_count = member_count + 1 WHERE id = ?',
      [id]
    );
    
    return success(res, { team_id: id, user_id }, '添加团队成员成功');
  } catch (err) {
    console.error('添加团队成员失败:', err);
    return error(res, '添加团队成员失败，请稍后重试');
  }
});

// 移除团队成员
router.delete('/:id/members/:userId', verifyToken, async (req, res) => {
  console.log('移除团队成员');
  
  if (req.user.role !== 'admin') {
    return error(res, '无权限访问', 403);
  }
  
  const { id, userId } = req.params;
  
  try {
    // 检查是否是团长
    const member = await db.query(
      'SELECT * FROM team_member WHERE team_id = ? AND user_id = ?',
      [id, userId]
    );
    
    if (!member || member.length === 0) {
      return error(res, '成员不存在');
    }
    
    if (member[0].role === 'leader') {
      return error(res, '不能移除团长');
    }
    
    // 移除成员
    await db.query(
      'DELETE FROM team_member WHERE team_id = ? AND user_id = ?',
      [id, userId]
    );
    
    // 更新团队成员数量
    await db.query(
      'UPDATE team SET member_count = member_count - 1 WHERE id = ?',
      [id]
    );
    
    return success(res, { team_id: id, user_id: userId }, '移除团队成员成功');
  } catch (err) {
    console.error('移除团队成员失败:', err);
    return error(res, '移除团队成员失败，请稍后重试');
  }
});

// 获取可选团长列表（未加入任何团队的用户）
router.get('/available-leaders', verifyToken, async (req, res) => {
  console.log('获取可选团长列表');

  if (req.user.role !== 'admin') {
    return error(res, '无权限访问', 403);
  }

  try {
    const users = await db.query(
      `SELECT id, nickname, phone, avatar
       FROM user
       WHERE id NOT IN (SELECT user_id FROM team_member)
       AND role = 'user'
       ORDER BY created_at DESC`
    );

    return success(res, users || [], '获取可选团长列表成功');
  } catch (err) {
    console.error('获取可选团长列表失败:', err);
    return error(res, '获取可选团长列表失败，请稍后重试');
  }
});

// 获取团队关联的联盟申请信息
router.get('/alliance-info/:teamId', verifyToken, async (req, res) => {
  console.log('获取团队关联的联盟申请信息');

  if (req.user.role !== 'admin') {
    return error(res, '无权限访问', 403);
  }

  const { teamId } = req.params;

  try {
    const result = await db.query(
      `SELECT
        ta.*,
        t.name as team_name,
        t.created_at as team_created_at,
        u.nickname as user_nickname,
        u.avatar as user_avatar
      FROM team t
      LEFT JOIN team_apply ta ON ta.user_id = t.leader_id AND ta.status = 1
      LEFT JOIN user u ON t.leader_id = u.id
      WHERE t.id = ?`,
      [teamId]
    );

    if (!result || result.length === 0) {
      return error(res, '团队不存在', 404);
    }

    return success(res, result[0], '获取团队联盟申请信息成功');
  } catch (err) {
    console.error('获取团队联盟申请信息失败:', err);
    return error(res, '获取团队联盟申请信息失败，请稍后重试');
  }
});

// 初始化收益相关表（仅供开发使用）
router.post('/init-income-tables', verifyToken, async (req, res) => {
  console.log('初始化收益相关表');
  
  if (req.user.role !== 'admin') {
    return error(res, '无权限访问', 403);
  }
  
  try {
    // 创建收益账单表
    await db.query(`
      CREATE TABLE IF NOT EXISTS income_bill (
        id INT PRIMARY KEY AUTO_INCREMENT,
        bill_no VARCHAR(50) NOT NULL UNIQUE COMMENT '账单编号',
        user_id INT NOT NULL COMMENT '产生收益的用户ID',
        team_id INT COMMENT '所属团队ID',
        leader_id INT COMMENT '团长ID',
        type VARCHAR(20) NOT NULL COMMENT '收益类型：wifi_share, goods_sale, advertisement',
        source_id INT COMMENT '来源ID（WiFi ID、订单ID、广告ID）',
        source_type VARCHAR(20) COMMENT '来源类型：wifi, order, ad',
        amount DECIMAL(10, 2) NOT NULL COMMENT '收益金额',
        user_share DECIMAL(10, 2) DEFAULT 0.00 COMMENT '用户分润',
        leader_share DECIMAL(10, 2) DEFAULT 0.00 COMMENT '团长分润',
        platform_share DECIMAL(10, 2) DEFAULT 0.00 COMMENT '平台分润',
        status TINYINT DEFAULT 1 COMMENT '状态：1待结算，2已结算，3已取消',
        settle_time DATETIME COMMENT '结算时间',
        remark TEXT COMMENT '备注',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_user_id (user_id),
        INDEX idx_team_id (team_id),
        INDEX idx_leader_id (leader_id),
        INDEX idx_type (type),
        INDEX idx_status (status),
        INDEX idx_created_at (created_at)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='收益账单表'
    `);
    
    // 创建分润规则表
    await db.query(`
      CREATE TABLE IF NOT EXISTS profit_rules (
        id INT PRIMARY KEY AUTO_INCREMENT,
        type VARCHAR(20) NOT NULL UNIQUE COMMENT '业务类型',
        name VARCHAR(50) NOT NULL COMMENT '规则名称',
        platform_rate INT NOT NULL DEFAULT 0 COMMENT '平台分润比例（百分比）',
        leader_rate INT NOT NULL DEFAULT 0 COMMENT '团长分润比例（百分比）',
        user_rate INT NOT NULL DEFAULT 0 COMMENT '用户分润比例（百分比）',
        min_amount DECIMAL(10, 2) DEFAULT 0.00 COMMENT '最小分润金额',
        status TINYINT DEFAULT 1 COMMENT '状态：0禁用，1启用',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分润规则表'
    `);
    
    // 插入默认分润规则
    const existingRules = await db.query('SELECT * FROM profit_rules');
    if (!existingRules || existingRules.length === 0) {
      await db.query(`
        INSERT INTO profit_rules (type, name, platform_rate, leader_rate, user_rate, status) VALUES
        ('wifi_share', 'WiFi分享', 40, 40, 20, 1),
        ('goods_sale', '商品销售', 50, 30, 20, 1),
        ('advertisement', '广告点击', 60, 30, 10, 1)
      `);
    }
    
    // 更新team表，确保有收益字段
    const columns = await db.query(`
      SELECT COLUMN_NAME 
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = DATABASE() 
      AND TABLE_NAME = 'team' 
      AND COLUMN_NAME IN ('total_income', 'month_income', 'today_income')
    `);
    
    const existingColumns = columns.map(col => col.COLUMN_NAME);
    
    if (!existingColumns.includes('total_income')) {
      await db.query(`ALTER TABLE team ADD COLUMN total_income DECIMAL(10, 2) DEFAULT 0.00 COMMENT '团队总收益'`);
    }
    
    if (!existingColumns.includes('month_income')) {
      await db.query(`ALTER TABLE team ADD COLUMN month_income DECIMAL(10, 2) DEFAULT 0.00 COMMENT '本月收益'`);
    }
    
    if (!existingColumns.includes('today_income')) {
      await db.query(`ALTER TABLE team ADD COLUMN today_income DECIMAL(10, 2) DEFAULT 0.00 COMMENT '今日收益'`);
    }
    
    return success(res, {}, '收益相关表初始化成功');
  } catch (err) {
    console.error('初始化收益表失败:', err);
    return error(res, '初始化收益表失败: ' + err.message);
  }
});

// 客户端获取团队信息（无需管理员权限）
router.get('/info', verifyToken, async (req, res) => {
  console.log('客户端获取团队信息');

  const userId = req.user.id;

  try {
    // 查询用户所在的团队信息
    const teamInfo = await db.query(
      `SELECT
        t.*,
        u.nickname as leader_name,
        u.phone as leader_phone,
        u.avatar as leader_avatar,
        tm.role as user_role,
        tm.joined_at as user_joined_at,
        (SELECT COUNT(*) FROM team_member WHERE team_id = t.id) as member_count
      FROM team_member tm
      LEFT JOIN team t ON tm.team_id = t.id
      LEFT JOIN user u ON t.leader_id = u.id
      WHERE tm.user_id = ?`,
      [userId]
    );

    if (!teamInfo || teamInfo.length === 0) {
      // 用户不在任何团队中
      return success(res, {
        hasTeam: false,
        team: null,
        message: '您还没有加入任何团队'
      }, '获取团队信息成功');
    }

    const team = teamInfo[0];

    // 获取团队成员列表（简化版）
    const members = await db.query(
      `SELECT
        tm.role,
        u.nickname,
        u.avatar,
        tm.joined_at
      FROM team_member tm
      LEFT JOIN user u ON tm.user_id = u.id
      WHERE tm.team_id = ?
      ORDER BY tm.role DESC, tm.joined_at ASC
      LIMIT 10`,
      [team.id]
    );

    // 生成邀请码（如果没有的话）
    let inviteCode = team.invite_code;
    if (!inviteCode) {
      inviteCode = `TEAM${String(team.id).padStart(6, '0')}`;
      // 更新数据库中的邀请码
      try {
        await db.query('UPDATE team SET invite_code = ? WHERE id = ?', [inviteCode, team.id]);
      } catch (updateErr) {
        console.error('更新邀请码失败:', updateErr);
      }
    }

    // 构建返回数据
    const result = {
      hasTeam: true,
      team: {
        id: team.id,
        name: team.name,
        level: team.level || 1,
        status: team.status,
        memberCount: team.member_count || 0,
        totalIncome: team.total_income || 0,
        monthIncome: team.month_income || 0,
        todayIncome: team.today_income || 0,
        createdAt: team.created_at,
        inviteCode: inviteCode,  // 添加邀请码
        leader: {
          name: team.leader_name,
          phone: team.leader_phone,
          avatar: team.leader_avatar
        },
        userRole: team.user_role,
        userJoinedAt: team.user_joined_at,
        members: members || []
      }
    };

    return success(res, result, '获取团队信息成功');
  } catch (err) {
    console.error('获取团队信息失败:', err);
    return error(res, '获取团队信息失败，请稍后重试');
  }
});

// 客户端获取团队成员列表
router.get('/members', verifyToken, async (req, res) => {
  console.log('客户端获取团队成员列表');

  const userId = req.user.id;
  const { page = 1, limit = 10 } = req.query;
  const pageNum = parseInt(page);
  const limitNum = parseInt(limit);
  const offset = (pageNum - 1) * limitNum;

  try {
    // 首先查询用户所在的团队
    const userTeam = await db.query(
      `SELECT team_id FROM team_member WHERE user_id = ?`,
      [userId]
    );

    if (!userTeam || userTeam.length === 0) {
      return success(res, {
        list: [],
        pagination: {
          total: 0,
          page: pageNum,
          limit: limitNum,
          pages: 0
        }
      }, '您还没有加入任何团队');
    }

    const teamId = userTeam[0].team_id;

    // 查询总数
    const countResult = await db.query(
      'SELECT COUNT(*) as total FROM team_member WHERE team_id = ?',
      [teamId]
    );

    const total = countResult && countResult.length > 0 ? countResult[0].total : 0;

    // 查询成员列表
    const members = await db.query(
      `SELECT
        tm.role,
        tm.joined_at,
        u.id as user_id,
        u.nickname,
        u.phone,
        u.avatar,
        u.created_at as user_created_at,
        CASE
          WHEN tm.role = 'leader' THEN '团长'
          ELSE '成员'
        END as role_name
      FROM team_member tm
      LEFT JOIN user u ON tm.user_id = u.id
      WHERE tm.team_id = ?
      ORDER BY tm.role DESC, tm.joined_at ASC
      LIMIT ${limitNum} OFFSET ${offset}`,
      [teamId]
    );

    // 处理成员头像URL，确保返回正确的URL格式
    const processedMembers = (members || []).map(member => {
      if (member.avatar) {
        // 如果头像不是完整URL，添加服务器地址前缀
        if (!member.avatar.startsWith('http://') && !member.avatar.startsWith('https://')) {
          if (member.avatar.startsWith('/uploads/')) {
            // 已经有/uploads前缀，直接返回相对路径，让前端处理
            // 前端会使用formatImageUrl函数添加完整域名
          } else if (!member.avatar.startsWith('/')) {
            // 没有前导斜杠，添加/uploads/前缀
            member.avatar = `/uploads/${member.avatar}`;
          }
        }
      }
      return member;
    });

    return success(res, {
      list: processedMembers,
      pagination: {
        total,
        page: pageNum,
        limit: limitNum,
        pages: Math.ceil(total / limitNum) || 1
      }
    }, '获取团队成员列表成功');
  } catch (err) {
    console.error('获取团队成员列表失败:', err);
    return error(res, '获取团队成员列表失败，请稍后重试');
  }
});

// 客户端获取团队统计信息
router.get('/stats', verifyToken, async (req, res) => {
  console.log('客户端获取团队统计信息');

  const userId = req.user.id;

  try {
    // 查询用户所在的团队
    const userTeam = await db.query(
      `SELECT
        t.*,
        tm.role as user_role
      FROM team_member tm
      LEFT JOIN team t ON tm.team_id = t.id
      WHERE tm.user_id = ?`,
      [userId]
    );

    if (!userTeam || userTeam.length === 0) {
      return success(res, {
        hasTeam: false,
        stats: null
      }, '您还没有加入任何团队');
    }

    const team = userTeam[0];

    // 获取团队统计数据
    const stats = {
      teamId: team.id,
      teamName: team.name,
      memberCount: team.member_count || 0,
      totalIncome: parseFloat(team.total_income || 0),
      monthIncome: parseFloat(team.month_income || 0),
      todayIncome: parseFloat(team.today_income || 0),
      level: team.level || 1,
      userRole: team.user_role,
      createdAt: team.created_at
    };

    return success(res, {
      hasTeam: true,
      stats: stats
    }, '获取团队统计信息成功');
  } catch (err) {
    console.error('获取团队统计信息失败:', err);
    return error(res, '获取团队统计信息失败，请稍后重试');
  }
});

// 生成团队邀请二维码
router.get('/invite-qrcode', verifyToken, async (req, res) => {
  console.log('生成团队邀请二维码');

  try {
    const { inviteCode, inviteUrl } = req.query;

    if (!inviteCode) {
      return error(res, '邀请码不能为空', 400);
    }

    // 构建邀请URL（如果没有提供）
    // 对于小程序，使用小程序页面路径；对于H5，使用完整URL
    let finalInviteUrl = inviteUrl;
    if (!finalInviteUrl) {
      // 默认使用小程序页面路径，扫码后会在小程序中打开
      finalInviteUrl = `pages/invite/invite?code=${inviteCode}`;

      // 如果需要H5页面，可以使用以下格式（需要配置真实域名）
      // finalInviteUrl = `https://your-real-domain.com/invite?code=${inviteCode}`;
    }

    // 使用第三方服务生成二维码
    const qrServerUrl = `https://api.qrserver.com/v1/create-qr-code/?data=${encodeURIComponent(finalInviteUrl)}&size=300x300&format=png`;

    console.log('生成团队邀请二维码成功，URL:', qrServerUrl);

    return success(res, {
      qrcode_url: qrServerUrl,
      invite_url: finalInviteUrl,
      invite_code: inviteCode
    }, '邀请二维码生成成功');
  } catch (err) {
    console.error('生成团队邀请二维码失败:', err);
    return error(res, '生成邀请二维码失败，请稍后重试');
  }
});

// 通过邀请码获取团队信息（公开接口，不需要登录）
router.get('/info-by-code', async (req, res) => {
  console.log('通过邀请码获取团队信息');

  try {
    const { inviteCode } = req.query;

    if (!inviteCode) {
      return error(res, '邀请码不能为空', 400);
    }

    // 查询团队信息
    const teamQuery = `
      SELECT
        t.id, t.name, t.level, t.member_count,
        t.total_income, t.month_income, t.today_income, t.created_at,
        u.nickname as leader_name, u.phone as leader_phone, u.avatar as leader_avatar
      FROM team t
      LEFT JOIN users u ON t.leader_id = u.id
      WHERE t.invite_code = ?
    `;

    const teamResult = await db.query(teamQuery, [inviteCode]);

    if (!teamResult || teamResult.length === 0) {
      return error(res, '邀请码无效或团队不存在', 404);
    }

    const team = teamResult[0];

    // 构建返回数据
    const result = {
      team: {
        id: team.id,
        name: team.name,
        level: team.level || 1,
        memberCount: team.member_count || 0,
        totalIncome: team.total_income || '0.00',
        monthIncome: team.month_income || '0.00',
        todayIncome: team.today_income || '0.00',
        createdAt: team.created_at,
        inviteCode: inviteCode,
        leader: {
          name: team.leader_name,
          phone: team.leader_phone,
          avatar: team.leader_avatar
        }
      }
    };

    console.log('通过邀请码获取团队信息成功:', result);

    return success(res, result, '获取团队信息成功');
  } catch (err) {
    console.error('通过邀请码获取团队信息失败:', err);
    return error(res, '获取团队信息失败，请稍后重试');
  }
});

module.exports = router;