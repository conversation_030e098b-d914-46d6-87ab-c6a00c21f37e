const WifiModel = require('../models/wifi');
const logger = require('../utils/logger');
const { success, error } = require('../utils/response');
const qrcode = require('qrcode');
const path = require('path');
const fs = require('fs');
const crypto = require('crypto');

/**
 * 创建WiFi码
 */
const createWifi = async (req, res) => {
  try {
    const userId = req.user.id;
    const { title, ssid, password, merchantName } = req.body;
    
    // 生成唯一标识符
    const uuid = crypto.randomUUID();
    
    // 创建WiFi码数据
    const qrcodeDir = path.join(process.cwd(), 'uploads', 'qrcodes');
    const qrcodeFileName = `wifi_${userId}_${Date.now()}.png`;
    const qrcodePath = path.join(qrcodeDir, qrcodeFileName);
    
    // 确保目录存在
    if (!fs.existsSync(qrcodeDir)) {
      fs.mkdirSync(qrcodeDir, { recursive: true });
    }
    
    // 创建WiFi连接URL
    // 格式: WIFI:S:<SSID>;T:<WPA|WEP|>;P:<password>;;
    const wifiUrl = `WIFI:S:${ssid};T:WPA;P:${password};;`;
    
    // 生成二维码
    await qrcode.toFile(qrcodePath, wifiUrl, {
      errorCorrectionLevel: 'H'
    });
    
    // 二维码URL
    const qrcodeUrl = `/uploads/qrcodes/${qrcodeFileName}`;
    
    // 保存到数据库
    const wifiData = {
      title,
      ssid,
      password,
      merchantName,
      userId,
      qrcodeUrl
    };
    
    const wifi = await WifiModel.create(wifiData);
    
    return success(res, wifi, 'WiFi码创建成功');
  } catch (err) {
    logger.error(`创建WiFi码失败: ${err.message}`);
    return error(res, '创建WiFi码失败', 500);
  }
};

/**
 * 获取用户的WiFi码列表
 */
const getWifiList = async (req, res) => {
  try {
    const userId = req.user.id;
    
    const wifiList = await WifiModel.getListByUserId(userId);
    
    return success(res, wifiList, '获取WiFi码列表成功');
  } catch (err) {
    logger.error(`获取WiFi码列表失败: ${err.message}`);
    return error(res, '获取WiFi码列表失败', 500);
  }
};

/**
 * 获取WiFi码详情
 */
const getWifiDetail = async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;
    
    const wifi = await WifiModel.getById(id);
    
    if (!wifi) {
      return error(res, 'WiFi码不存在', 404);
    }
    
    // 检查是否是该用户的WiFi码
    if (wifi.user_id !== userId) {
      return error(res, '无权访问此WiFi码', 403);
    }
    
    return success(res, {
      id: wifi.id,
      title: wifi.title,
      ssid: wifi.ssid,
      password: wifi.password,
      merchantName: wifi.merchant_name,
      qrcodeUrl: wifi.qrcode_url,
      scanCount: wifi.scan_count,
      createdAt: wifi.created_at,
      updatedAt: wifi.updated_at
    }, '获取WiFi码详情成功');
  } catch (err) {
    logger.error(`获取WiFi码详情失败: ${err.message}`);
    return error(res, '获取WiFi码详情失败', 500);
  }
};

/**
 * 修改WiFi码
 */
const updateWifi = async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;
    const { title, ssid, password, merchantName } = req.body;
    
    // 检查WiFi码是否存在
    const wifi = await WifiModel.getById(id);
    
    if (!wifi) {
      return error(res, 'WiFi码不存在', 404);
    }
    
    // 检查是否是该用户的WiFi码
    if (wifi.user_id !== userId) {
      return error(res, '无权修改此WiFi码', 403);
    }
    
    // 如果修改了SSID或密码，需要重新生成二维码
    if (ssid !== wifi.ssid || password !== wifi.password) {
      // 创建WiFi码数据
      const qrcodeDir = path.join(process.cwd(), 'uploads', 'qrcodes');
      const qrcodeFileName = `wifi_${userId}_${Date.now()}.png`;
      const qrcodePath = path.join(qrcodeDir, qrcodeFileName);
      
      // 确保目录存在
      if (!fs.existsSync(qrcodeDir)) {
        fs.mkdirSync(qrcodeDir, { recursive: true });
      }
      
      // 创建WiFi连接URL
      const wifiUrl = `WIFI:S:${ssid};T:WPA;P:${password};;`;
      
      // 生成二维码
      await qrcode.toFile(qrcodePath, wifiUrl, {
        errorCorrectionLevel: 'H'
      });
      
      // 二维码URL
      const qrcodeUrl = `/uploads/qrcodes/${qrcodeFileName}`;
      
      // 更新数据
      const wifiData = {
        title,
        ssid,
        password,
        merchantName,
        qrcodeUrl
      };
      
      await WifiModel.update(id, wifiData);
    } else {
      // 仅更新标题和商户名
      await WifiModel.update(id, { title, merchantName });
    }
    
    return success(res, null, 'WiFi码更新成功');
  } catch (err) {
    logger.error(`修改WiFi码失败: ${err.message}`);
    return error(res, '修改WiFi码失败', 500);
  }
};

/**
 * 删除WiFi码
 */
const deleteWifi = async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;
    
    // 检查WiFi码是否存在
    const wifi = await WifiModel.getById(id);
    
    if (!wifi) {
      return error(res, 'WiFi码不存在', 404);
    }
    
    // 检查是否是该用户的WiFi码
    if (wifi.user_id !== userId) {
      return error(res, '无权删除此WiFi码', 403);
    }
    
    // 删除二维码文件
    if (wifi.qrcode_url) {
      const qrcodePath = path.join(process.cwd(), wifi.qrcode_url);
      if (fs.existsSync(qrcodePath)) {
        fs.unlinkSync(qrcodePath);
      }
    }
    
    // 删除数据库记录
    await WifiModel.delete(id, userId);
    
    return success(res, null, 'WiFi码删除成功');
  } catch (err) {
    logger.error(`删除WiFi码失败: ${err.message}`);
    return error(res, '删除WiFi码失败', 500);
  }
};

/**
 * 获取WiFi码统计数据
 */
const getWifiStats = async (req, res) => {
  try {
    const { id } = req.params;
    const { days = 7 } = req.query;
    const userId = req.user.id;
    
    // 检查WiFi码是否存在
    const wifi = await WifiModel.getById(id);
    
    if (!wifi) {
      return error(res, 'WiFi码不存在', 404);
    }
    
    // 检查是否是该用户的WiFi码
    if (wifi.user_id !== userId) {
      return error(res, '无权访问此WiFi码', 403);
    }
    
    const stats = await WifiModel.getStats(id, parseInt(days));
    
    return success(res, stats, '获取WiFi码统计数据成功');
  } catch (err) {
    logger.error(`获取WiFi码统计数据失败: ${err.message}`);
    return error(res, '获取WiFi码统计数据失败', 500);
  }
};

/**
 * 记录WiFi码扫描（公开接口）
 */
const logWifiScan = async (req, res) => {
  try {
    const { id } = req.params;
    const ip = req.ip;
    const userAgent = req.headers['user-agent'] || '';
    
    // 检查WiFi码是否存在
    const wifi = await WifiModel.getById(id);
    
    if (!wifi) {
      return error(res, 'WiFi码不存在', 404);
    }
    
    // 记录扫描日志
    await WifiModel.logScan(id, ip, userAgent);
    
    // 增加扫描次数
    await WifiModel.incrementScanCount(id);
    
    return success(res, {
      ssid: wifi.ssid,
      password: wifi.password
    }, 'WiFi码扫描成功');
  } catch (err) {
    logger.error(`记录WiFi码扫描失败: ${err.message}`);
    return error(res, '记录WiFi码扫描失败', 500);
  }
};

module.exports = {
  createWifi,
  getWifiList,
  getWifiDetail,
  updateWifi,
  deleteWifi,
  getWifiStats,
  logWifiScan
}; 