const express = require('express');
const mysql = require('mysql2');
const app = express();

app.use(express.json());

// 创建数据库连接
const connection = mysql.createConnection({
  host: 'localhost',
  user: 'root',
  password: 'wo587129955',
  database: 'mall'
});

// 删除测试订单的API
app.delete('/delete-test-order', (req, res) => {
  console.log('收到删除测试订单请求');
  
  // 查找金额为1065.00的订单
  connection.query(
    'SELECT id, order_no, user_id, status, total_amount FROM orders WHERE total_amount = 1065.00',
    (error, results) => {
      if (error) {
        console.error('查询失败:', error);
        return res.status(500).json({ error: '查询失败' });
      }
      
      if (results.length === 0) {
        return res.json({ message: '未找到测试订单' });
      }
      
      const testOrder = results[0];
      console.log(`找到测试订单: ID=${testOrder.id}, 订单号=${testOrder.order_no}`);
      
      // 删除订单商品
      connection.query(
        'DELETE FROM order_goods WHERE order_id = ?',
        [testOrder.id],
        (error) => {
          if (error) {
            console.error('删除订单商品失败:', error);
            return res.status(500).json({ error: '删除订单商品失败' });
          }
          
          // 删除订单
          connection.query(
            'DELETE FROM orders WHERE id = ?',
            [testOrder.id],
            (error) => {
              if (error) {
                console.error('删除订单失败:', error);
                return res.status(500).json({ error: '删除订单失败' });
              }
              
              console.log('测试订单删除成功！');
              res.json({ 
                message: '测试订单删除成功',
                deletedOrder: {
                  id: testOrder.id,
                  order_no: testOrder.order_no,
                  total_amount: testOrder.total_amount
                }
              });
            }
          );
        }
      );
    }
  );
});

// 查看所有订单的API
app.get('/orders', (req, res) => {
  connection.query(
    'SELECT id, order_no, user_id, status, total_amount, created_at FROM orders ORDER BY created_at DESC LIMIT 10',
    (error, results) => {
      if (error) {
        console.error('查询失败:', error);
        return res.status(500).json({ error: '查询失败' });
      }
      
      res.json({ orders: results });
    }
  );
});

const PORT = 3001;
app.listen(PORT, () => {
  console.log(`删除订单API服务器运行在端口 ${PORT}`);
  console.log(`访问 http://localhost:${PORT}/orders 查看订单`);
  console.log(`发送 DELETE 请求到 http://localhost:${PORT}/delete-test-order 删除测试订单`);
});
