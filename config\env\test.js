/**
 * 测试环境配置
 */
module.exports = {
  // 数据库配置
  database: {
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 3306,
    username: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || 'password',
    database: process.env.DB_NAME || 'wifi_share_test',
    connectionLimit: 5
  },
  
  // JWT配置
  jwt: {
    secret: process.env.JWT_SECRET || 'wifi_share_test_secret',
    expiresIn: process.env.JWT_EXPIRES_IN || '1d'
  },
  
  // 腾讯云COS对象存储配置
  cos: {
    secretId: process.env.COS_SECRET_ID || 'test-id',
    secretKey: process.env.COS_SECRET_KEY || 'test-key',
    region: process.env.COS_REGION || 'ap-guangzhou',
    bucket: process.env.COS_BUCKET || 'wifi-share-test'
  },
  
  // 微信小程序配置
  wechat: {
    appId: process.env.WECHAT_APPID || 'test-appid',
    appSecret: process.env.WECHAT_SECRET || 'test-secret'
  },
  
  // 跨域配置
  cors: {
    origin: '*',
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization']
  },
  
  // 是否开启调试模式
  debug: true,
  
  // 上传文件配置
  upload: {
    baseDir: 'test-uploads',
    maxSize: 5 * 1024 * 1024, // 5MB
    allowTypes: ['image/jpeg', 'image/png', 'image/gif']
  }
}; 