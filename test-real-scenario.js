const axios = require('axios');
const jwt = require('jsonwebtoken');
const mysql = require('mysql2/promise');

async function testRealScenario() {
  let connection;
  try {
    console.log('🔍 模拟真实用户场景测试...');
    
    // 连接数据库
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: 'wo587129955',
      database: 'mall'
    });
    
    // 生成JWT token
    const config = require('./config');
    const userId = 2;
    
    const token = jwt.sign(
      { id: userId, openid: 'test_openid_13800138000' },
      config.jwt.secret,
      { expiresIn: config.jwt.expiresIn }
    );
    
    console.log('🔑 生成的token:', token);
    
    // 1. 检查所有商品的价格
    console.log('\n📦 1. 检查所有商品的价格...');
    const [allGoods] = await connection.execute('SELECT id, title, price, original_price, stock FROM goods ORDER BY id');
    console.log('所有商品信息:');
    allGoods.forEach(item => {
      console.log(`- ID: ${item.id}, 标题: ${item.title}, 价格: ${item.price}, 原价: ${item.original_price}, 库存: ${item.stock}`);
    });
    
    // 2. 测试不同价格的商品
    console.log('\n🧪 2. 测试不同价格的商品...');
    
    for (const goods of allGoods.slice(0, 3)) { // 只测试前3个商品
      console.log(`\n--- 测试商品: ${goods.title} (价格: ${goods.price}) ---`);
      
      // 模拟前端计算
      const quantity = 1;
      const goodsPrice = parseFloat(goods.price);
      const totalAmount = goodsPrice * quantity;
      const freight = totalAmount >= 99 ? 0 : 10;
      const finalAmount = totalAmount + freight;
      
      console.log('前端计算:');
      console.log(`- 商品价格: ${goodsPrice}`);
      console.log(`- 数量: ${quantity}`);
      console.log(`- 商品总价: ${totalAmount}`);
      console.log(`- 运费: ${freight}`);
      console.log(`- 最终金额: ${finalAmount}`);
      
      // 创建订单
      const orderData = {
        addressId: 1,
        goods: [
          {
            goodsId: goods.id,
            quantity: quantity
          }
        ],
        remark: `测试商品${goods.id}的价格`,
        paymentMethod: 'wechat',
        couponId: 0,
        fromCart: false
      };
      
      try {
        const createOrderResponse = await axios.post('http://localhost:4000/api/v1/client/order/create', orderData, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          },
          timeout: 10000
        });
        
        console.log('后端订单创建响应:', createOrderResponse.data);
        
        if (createOrderResponse.data.status === 'success') {
          const orderId = createOrderResponse.data.data.orderId;
          
          // 查询数据库中的订单
          const [orders] = await connection.execute('SELECT * FROM orders WHERE id = ?', [orderId]);
          const order = orders[0];
          
          console.log('数据库订单信息:');
          console.log(`- 商品金额: ${order.goods_amount}`);
          console.log(`- 运费: ${order.shipping_fee}`);
          console.log(`- 总金额: ${order.total_amount}`);
          
          // 创建支付订单
          try {
            const paymentResponse = await axios.post('http://localhost:4000/api/v1/client/payment/create', {
              orderId: orderId,
              paymentMethod: 'wechat'
            }, {
              headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
              },
              timeout: 10000
            });
            
            console.log('支付订单创建响应:');
            console.log(`- 支付金额: ${paymentResponse.data.data.amount}`);
            
            // 比较价格
            const dbAmount = parseFloat(order.total_amount);
            const paymentAmount = paymentResponse.data.data.amount;
            const frontendAmount = finalAmount;
            
            console.log('\n价格对比:');
            console.log(`- 前端计算: ${frontendAmount}`);
            console.log(`- 数据库存储: ${dbAmount}`);
            console.log(`- 支付金额: ${paymentAmount}`);
            
            if (Math.abs(frontendAmount - dbAmount) > 0.01) {
              console.log('❌ 前端计算与数据库不匹配!');
            }
            if (Math.abs(dbAmount - paymentAmount) > 0.01) {
              console.log('❌ 数据库与支付金额不匹配!');
            }
            if (Math.abs(frontendAmount - paymentAmount) > 0.01) {
              console.log('❌ 前端计算与支付金额不匹配!');
            }
            
            if (Math.abs(frontendAmount - dbAmount) <= 0.01 && Math.abs(dbAmount - paymentAmount) <= 0.01) {
              console.log('✅ 所有价格匹配正常');
            }
            
          } catch (paymentError) {
            console.log('❌ 支付订单创建失败:', paymentError.response ? paymentError.response.data : paymentError.message);
          }
        }
        
      } catch (orderError) {
        console.log('❌ 订单创建失败:', orderError.response ? orderError.response.data : orderError.message);
      }
      
      console.log(''); // 空行分隔
    }
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    if (error.response) {
      console.error('响应状态:', error.response.status);
      console.error('响应数据:', error.response.data);
    }
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// 运行测试
testRealScenario();
