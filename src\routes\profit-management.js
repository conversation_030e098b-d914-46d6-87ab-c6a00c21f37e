/**
 * 分润管理路由
 */
const express = require('express');
const router = express.Router();
const db = require('../../config/database');
const logger = require('../utils/logger');
const ProfitService = require('../services/profit-service');
const { success, error } = require('../utils/response');
const { verifyToken, adminAuth } = require('../middlewares/auth');

// ==================== 管理端接口 ====================

/**
 * 获取分润规则列表
 * GET /api/v1/admin/profit/rules
 */
router.get('/rules', verifyToken, adminAuth, async (req, res) => {
  try {
    const rules = await db.query('SELECT * FROM profit_rule ORDER BY type');
    const configs = await db.query('SELECT * FROM profit_config WHERE status = 1');
    const teamLevels = await db.query('SELECT * FROM team_level_profit ORDER BY level, profit_type');
    
    return success(res, {
      rules,
      configs,
      teamLevels
    }, '获取分润规则成功');
  } catch (err) {
    logger.error('获取分润规则失败:', err);
    return error(res, '获取分润规则失败');
  }
});

/**
 * 更新分润规则
 * POST /api/v1/admin/profit/rules/update
 */
router.post('/rules/update', verifyToken, adminAuth, async (req, res) => {
  try {
    const { type, platform_rate, leader_rate, user_rate, min_amount } = req.body;
    
    if (!type || platform_rate === undefined || leader_rate === undefined || user_rate === undefined) {
      return error(res, '参数不完整', 400);
    }
    
    // 验证比例总和
    const totalRate = parseInt(platform_rate) + parseInt(leader_rate) + parseInt(user_rate);
    if (totalRate !== 100) {
      return error(res, `分润比例总和必须为100%，当前为${totalRate}%`, 400);
    }
    
    await db.query(
      'UPDATE profit_rule SET platform_rate = ?, leader_rate = ?, user_rate = ?, min_amount = ?, updated_at = NOW() WHERE type = ?',
      [platform_rate, leader_rate, user_rate, min_amount || 0, type]
    );
    
    logger.info(`更新分润规则成功: ${type}`);
    return success(res, null, '更新分润规则成功');
  } catch (err) {
    logger.error('更新分润规则失败:', err);
    return error(res, '更新分润规则失败');
  }
});

/**
 * 更新系统配置
 * POST /api/v1/admin/profit/config/update
 */
router.post('/config/update', verifyToken, adminAuth, async (req, res) => {
  try {
    const { config_key, config_value } = req.body;
    
    if (!config_key || config_value === undefined) {
      return error(res, '参数不完整', 400);
    }
    
    await db.query(
      'UPDATE profit_config SET config_value = ?, updated_at = NOW() WHERE config_key = ?',
      [config_value, config_key]
    );
    
    logger.info(`更新系统配置成功: ${config_key} = ${config_value}`);
    return success(res, null, '更新配置成功');
  } catch (err) {
    logger.error('更新系统配置失败:', err);
    return error(res, '更新系统配置失败');
  }
});

/**
 * 获取分润记录列表
 * GET /api/v1/admin/profit/records
 */
router.get('/records', verifyToken, adminAuth, async (req, res) => {
  try {
    const { 
      page = 1, 
      limit = 20, 
      user_id, 
      team_id, 
      profit_type, 
      status, 
      start_date, 
      end_date 
    } = req.query;
    
    let whereConditions = [];
    let params = [];
    
    if (user_id) {
      whereConditions.push('pl.user_id = ?');
      params.push(user_id);
    }
    
    if (team_id) {
      whereConditions.push('pl.team_id = ?');
      params.push(team_id);
    }
    
    if (profit_type) {
      whereConditions.push('pl.profit_type = ?');
      params.push(profit_type);
    }
    
    if (status !== undefined) {
      whereConditions.push('pl.status = ?');
      params.push(status);
    }
    
    if (start_date) {
      whereConditions.push('pl.created_at >= ?');
      params.push(start_date);
    }
    
    if (end_date) {
      whereConditions.push('pl.created_at <= ?');
      params.push(end_date + ' 23:59:59');
    }
    
    const whereClause = whereConditions.length > 0 ? 'WHERE ' + whereConditions.join(' AND ') : '';
    
    // 获取总数
    const countSql = `SELECT COUNT(*) as total FROM profit_log pl ${whereClause}`;
    const countResult = await db.query(countSql, params);
    const total = countResult[0].total;
    
    // 获取列表
    const offset = (page - 1) * limit;
    const listSql = `
      SELECT 
        pl.*,
        u.nickname as user_nickname,
        t.name as team_name
      FROM profit_log pl
      LEFT JOIN user u ON pl.user_id = u.id
      LEFT JOIN team t ON pl.team_id = t.id
      ${whereClause}
      ORDER BY pl.created_at DESC
      LIMIT ? OFFSET ?
    `;
    
    const list = await db.query(listSql, [...params, parseInt(limit), parseInt(offset)]);
    
    // 获取统计数据
    const statsSql = `
      SELECT 
        SUM(amount) as total_amount,
        SUM(CASE WHEN status = 1 THEN amount ELSE 0 END) as settled_amount,
        SUM(CASE WHEN status = 0 THEN amount ELSE 0 END) as pending_amount,
        COUNT(*) as total_count
      FROM profit_log pl ${whereClause}
    `;
    
    const statsResult = await db.query(statsSql, params);
    const stats = statsResult[0];
    
    return success(res, {
      list,
      total,
      page: parseInt(page),
      limit: parseInt(limit),
      stats: {
        totalAmount: parseFloat(stats.total_amount || 0),
        settledAmount: parseFloat(stats.settled_amount || 0),
        pendingAmount: parseFloat(stats.pending_amount || 0),
        totalCount: parseInt(stats.total_count || 0)
      }
    }, '获取分润记录成功');
  } catch (err) {
    logger.error('获取分润记录失败:', err);
    return error(res, '获取分润记录失败');
  }
});

/**
 * 批量结算分润
 * POST /api/v1/admin/profit/settle
 */
router.post('/settle', verifyToken, adminAuth, async (req, res) => {
  try {
    const { record_ids } = req.body;
    
    if (!record_ids || !Array.isArray(record_ids) || record_ids.length === 0) {
      return error(res, '请选择要结算的记录', 400);
    }
    
    const result = await ProfitService.settleProfitRecords(record_ids);
    
    logger.info(`批量结算分润: ${result.settledRecords}/${result.totalRecords}`);
    return success(res, result, '结算完成');
  } catch (err) {
    logger.error('批量结算分润失败:', err);
    return error(res, '结算失败');
  }
});

/**
 * 自动结算到期分润
 * POST /api/v1/admin/profit/auto-settle
 */
router.post('/auto-settle', verifyToken, adminAuth, async (req, res) => {
  try {
    const result = await ProfitService.autoSettleProfits();
    
    logger.info('手动触发自动结算完成');
    return success(res, result, '自动结算完成');
  } catch (err) {
    logger.error('自动结算失败:', err);
    return error(res, '自动结算失败');
  }
});

/**
 * 获取分润统计报表
 * GET /api/v1/admin/profit/statistics
 */
router.get('/statistics', verifyToken, adminAuth, async (req, res) => {
  try {
    const { time_range = 'month' } = req.query;
    
    let timeCondition = '';
    switch (time_range) {
      case 'today':
        timeCondition = 'AND DATE(created_at) = CURDATE()';
        break;
      case 'week':
        timeCondition = 'AND created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)';
        break;
      case 'month':
        timeCondition = 'AND created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)';
        break;
      case 'year':
        timeCondition = 'AND YEAR(created_at) = YEAR(NOW())';
        break;
    }
    
    // 按分润类型统计
    const typeSql = `
      SELECT 
        profit_type,
        COUNT(*) as count,
        SUM(amount) as total_amount,
        SUM(CASE WHEN status = 1 THEN amount ELSE 0 END) as settled_amount
      FROM profit_log 
      WHERE 1=1 ${timeCondition}
      GROUP BY profit_type
    `;
    
    const typeStats = await db.query(typeSql);
    
    // 按角色统计
    const roleSql = `
      SELECT 
        role,
        COUNT(*) as count,
        SUM(amount) as total_amount,
        SUM(CASE WHEN status = 1 THEN amount ELSE 0 END) as settled_amount
      FROM profit_log 
      WHERE 1=1 ${timeCondition}
      GROUP BY role
    `;
    
    const roleStats = await db.query(roleSql);
    
    // 按日期统计（最近7天）
    const dailySql = `
      SELECT 
        DATE(created_at) as date,
        COUNT(*) as count,
        SUM(amount) as total_amount
      FROM profit_log 
      WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
      GROUP BY DATE(created_at)
      ORDER BY date DESC
    `;
    
    const dailyStats = await db.query(dailySql);
    
    return success(res, {
      timeRange: time_range,
      byType: typeStats,
      byRole: roleStats,
      daily: dailyStats
    }, '获取统计数据成功');
  } catch (err) {
    logger.error('获取分润统计失败:', err);
    return error(res, '获取统计数据失败');
  }
});

// ==================== 客户端接口 ====================

/**
 * 获取用户分润记录
 * GET /api/v1/client/profit/records
 */
router.get('/client/records', verifyToken, async (req, res) => {
  try {
    const userId = req.user.id;
    const { page = 1, limit = 20, profit_type, status } = req.query;
    
    let whereConditions = ['user_id = ?'];
    let params = [userId];
    
    if (profit_type) {
      whereConditions.push('profit_type = ?');
      params.push(profit_type);
    }
    
    if (status !== undefined) {
      whereConditions.push('status = ?');
      params.push(status);
    }
    
    const whereClause = 'WHERE ' + whereConditions.join(' AND ');
    
    // 获取总数
    const countSql = `SELECT COUNT(*) as total FROM profit_log ${whereClause}`;
    const countResult = await db.query(countSql, params);
    const total = countResult[0].total;
    
    // 获取列表
    const offset = (page - 1) * limit;
    const listSql = `
      SELECT * FROM profit_log 
      ${whereClause}
      ORDER BY created_at DESC
      LIMIT ? OFFSET ?
    `;
    
    const list = await db.query(listSql, [...params, parseInt(limit), offset]);
    
    return success(res, {
      list,
      total,
      page: parseInt(page),
      limit: parseInt(limit)
    }, '获取分润记录成功');
  } catch (err) {
    logger.error('获取用户分润记录失败:', err);
    return error(res, '获取分润记录失败');
  }
});

/**
 * 获取用户分润统计
 * GET /api/v1/client/profit/stats
 */
router.get('/client/stats', verifyToken, async (req, res) => {
  try {
    const userId = req.user.id;
    const { time_range = 'month' } = req.query;
    
    const stats = await ProfitService.getUserProfitStats(userId, time_range);
    
    return success(res, stats, '获取分润统计成功');
  } catch (err) {
    logger.error('获取用户分润统计失败:', err);
    return error(res, '获取分润统计失败');
  }
});

module.exports = router;
