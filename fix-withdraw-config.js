const mysql = require('mysql2/promise');

async function fixWithdrawConfig() {
  let connection;
  
  try {
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: 'wo587129955',
      database: 'mall'
    });
    
    console.log('🔧 修复提现配置表...');
    
    // 删除可能存在的错误表
    await connection.execute('DROP TABLE IF EXISTS withdraw_config');
    
    // 重新创建配置表
    await connection.execute(`
      CREATE TABLE withdraw_config (
        id int(11) NOT NULL AUTO_INCREMENT COMMENT '配置ID',
        config_key varchar(50) NOT NULL COMMENT '配置键',
        config_value text NOT NULL COMMENT '配置值',
        config_type varchar(20) NOT NULL DEFAULT 'string' COMMENT '配置类型',
        description varchar(255) DEFAULT NULL COMMENT '配置描述',
        status tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态',
        created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        updated_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
        PRIMARY KEY (id),
        UNIQUE KEY config_key (config_key)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='提现配置表'
    `);
    
    // 插入配置数据
    await connection.execute(`
      INSERT INTO withdraw_config (config_key, config_value, config_type, description) VALUES
      ('min_withdraw_amount', '10', 'number', '最小提现金额（元）'),
      ('max_withdraw_amount', '50000', 'number', '最大提现金额（元）'),
      ('wechat_fee_rate', '0.006', 'number', '微信提现手续费率（0.6%）'),
      ('bank_fee_rate', '0.001', 'number', '银行卡提现手续费率（0.1%）'),
      ('wechat_min_fee', '0.1', 'number', '微信提现最小手续费（元）'),
      ('bank_min_fee', '2', 'number', '银行卡提现最小手续费（元）')
    `);
    
    console.log('✅ 提现配置表修复完成');
    
    // 验证
    const [count] = await connection.execute('SELECT COUNT(*) as count FROM withdraw_config');
    console.log('✅ 配置数量:', count[0].count);
    
    // 插入测试用户微信账户
    console.log('🔧 插入测试用户微信账户...');
    await connection.execute(`
      INSERT INTO wechat_account (user_id, openid, nickname, real_name, is_verified, is_default) VALUES
      (1, 'oW20C7mVlW8e3W2AgUGtDTJeAbQU', '润生', '张润生', 1, 1)
      ON DUPLICATE KEY UPDATE 
        nickname = VALUES(nickname),
        real_name = VALUES(real_name),
        updated_at = CURRENT_TIMESTAMP
    `);
    
    // 插入测试提现记录
    console.log('🔧 插入测试提现记录...');
    await connection.execute(`
      INSERT INTO withdraw (user_id, withdraw_no, amount, fee, actual_amount, withdraw_type, account_id, account_info, status, apply_time, complete_time, remark) VALUES
      (1, 'WD202501290001', 100.00, 0.60, 99.40, 'wechat', 1, '{"type":"wechat","nickname":"润生","real_name":"张润生"}', 4, '2025-01-29 10:00:00', '2025-01-29 11:30:00', '测试微信提现'),
      (1, 'WD202501290002', 200.00, 2.00, 198.00, 'bank_card', 1, '{"type":"bank_card","bank_name":"招商银行","card_mask":"**** **** **** 6789"}', 4, '2025-01-29 14:00:00', '2025-01-29 16:00:00', '测试银行卡提现'),
      (1, 'WD202501290003', 50.00, 0.30, 49.70, 'wechat', 1, '{"type":"wechat","nickname":"润生","real_name":"张润生"}', 0, '2025-01-29 18:00:00', NULL, '待审核提现申请')
      ON DUPLICATE KEY UPDATE updated_at = CURRENT_TIMESTAMP
    `);
    
    console.log('✅ 提现功能数据库完善完成！');
    
    // 最终验证
    console.log('\n📊 最终验证结果:');
    const [configCount] = await connection.execute('SELECT COUNT(*) as count FROM withdraw_config');
    console.log(`✅ 提现配置: ${configCount[0].count} 条`);
    
    const [wechatCount] = await connection.execute('SELECT COUNT(*) as count FROM wechat_account');
    console.log(`✅ 微信账户: ${wechatCount[0].count} 个`);
    
    const [bankCount] = await connection.execute('SELECT COUNT(*) as count FROM bank_card');
    console.log(`✅ 银行卡: ${bankCount[0].count} 张`);
    
    const [withdrawCount] = await connection.execute('SELECT COUNT(*) as count FROM withdraw');
    console.log(`✅ 提现记录: ${withdrawCount[0].count} 条`);
    
    const [userBalance] = await connection.execute('SELECT balance FROM user WHERE id = 1');
    if (userBalance.length > 0) {
      console.log(`✅ 用户余额: ${userBalance[0].balance} 元`);
    }
    
    console.log('\n🎉 钱包提现功能已完全完善！');
    
  } catch (error) {
    console.error('❌ 修复失败:', error.message);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

fixWithdrawConfig();
