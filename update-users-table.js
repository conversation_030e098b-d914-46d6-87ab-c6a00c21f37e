// update-users-table.js
// 更新users表，添加session_key和is_profile_completed字段

const db = require('./src/database');

async function updateUsersTable() {
  try {
    console.log('开始更新users表...');
    
    // 检查users表是否存在
    const tableExists = await db.query(`
      SELECT COUNT(*) as count 
      FROM information_schema.tables 
      WHERE table_schema = DATABASE() 
      AND table_name = 'users'
    `);
    
    if (tableExists[0].count === 0) {
      console.log('users表不存在，创建users表...');
      await db.query(`
        CREATE TABLE users (
          id INT AUTO_INCREMENT PRIMARY KEY,
          openid VARCHAR(100) NOT NULL UNIQUE,
          nickname VARCHAR(100),
          avatar VARCHAR(255),
          gender TINYINT DEFAULT 0,
          phone VARCHAR(20),
          role VARCHAR(20) DEFAULT 'user',
          status TINYINT DEFAULT 1,
          session_key VARCHAR(255),
          is_profile_completed TINYINT(1) DEFAULT 0,
          created_at DATETIME,
          updated_at DATETIME
        )
      `);
      console.log('users表创建成功');
      return;
    }
    
    console.log('检查session_key字段...');
    const sessionKeyExists = await db.query(`
      SELECT COUNT(*) as count 
      FROM information_schema.columns 
      WHERE table_schema = DATABASE() 
      AND table_name = 'users' 
      AND column_name = 'session_key'
    `);
    
    if (sessionKeyExists[0].count === 0) {
      console.log('添加session_key字段...');
      await db.query('ALTER TABLE users ADD COLUMN session_key VARCHAR(255)');
      console.log('session_key字段添加成功');
    } else {
      console.log('session_key字段已存在');
    }
    
    console.log('检查is_profile_completed字段...');
    const profileCompletedExists = await db.query(`
      SELECT COUNT(*) as count 
      FROM information_schema.columns 
      WHERE table_schema = DATABASE() 
      AND table_name = 'users' 
      AND column_name = 'is_profile_completed'
    `);
    
    if (profileCompletedExists[0].count === 0) {
      console.log('添加is_profile_completed字段...');
      await db.query('ALTER TABLE users ADD COLUMN is_profile_completed TINYINT(1) NOT NULL DEFAULT 0');
      console.log('is_profile_completed字段添加成功');
    } else {
      console.log('is_profile_completed字段已存在');
    }
    
    console.log('users表更新完成');
  } catch (error) {
    console.error('更新users表出错:', error);
  } finally {
    process.exit();
  }
}

updateUsersTable(); 