// pages/mall/order/logistics/logistics.js
const { request } = require('../../../../utils/request')
const { showToast } = require('../../../../utils/util')
const API = require('../../../../config/api')

Page({
  data: {
    orderId: '',
    orderInfo: null,
    logisticsInfo: null,
    loading: true,
    noLogistics: false,
    noLogisticsMessage: ''
  },

  onLoad: function (options) {
    console.log('🚚 物流查看页面加载', options)
    
    if (options.orderId) {
      this.setData({ orderId: options.orderId })
      this.fetchLogisticsInfo()
    } else {
      showToast('订单ID不能为空')
      wx.navigateBack()
    }
  },

  /**
   * 获取物流信息
   */
  fetchLogisticsInfo: async function () {
    const { orderId } = this.data

    if (!orderId) {
      showToast('订单ID不能为空')
      return
    }

    this.setData({ loading: true })

    try {
      console.log('🚚 获取物流信息，订单ID:', orderId)

      const res = await request({
        url: `${API.logistics.orderTrack}/${orderId}`,
        method: 'GET'
      })

      console.log('🚚 物流信息响应:', res)

      if ((res.code === 0 || res.success) && res.data) {
        if (res.data.hasLogistics) {
          // 有物流信息
          this.setData({
            orderInfo: res.data.orderInfo,
            logisticsInfo: res.data.traces || [],
            loading: false
          })
          console.log('✅ 物流信息获取成功')
        } else {
          // 无物流信息
          this.setData({
            orderInfo: res.data.orderInfo,
            logisticsInfo: [],
            noLogistics: true,
            noLogisticsMessage: res.data.message || '该订单暂无物流信息',
            loading: false
          })
          console.log('ℹ️ 订单暂无物流信息')
        }
      } else {
        console.error('❌ 物流信息获取失败:', res)
        showToast(res.message || '获取物流信息失败')
        this.setData({ loading: false })
      }
    } catch (err) {
      console.error('❌ 物流信息请求异常:', err)
      showToast('网络异常，请稍后重试')
      this.setData({ loading: false })
    }
  },

  /**
   * 复制物流单号
   */
  onCopyLogisticsNo: function () {
    if (this.data.orderInfo && this.data.orderInfo.logistics_no) {
      wx.setClipboardData({
        data: this.data.orderInfo.logistics_no,
        success: () => {
          showToast('物流单号已复制')
        }
      })
    }
  },

  /**
   * 联系快递员
   */
  onContactCourier: function () {
    // 从物流信息中提取快递员电话
    const deliveryInfo = this.data.logisticsInfo.find(item => 
      item.status === '派送中' && item.desc.includes('电话')
    )
    
    if (deliveryInfo) {
      // 提取电话号码（这里是示例，实际需要根据具体格式解析）
      const phoneMatch = deliveryInfo.desc.match(/电话：(\d{3}\*{4}\d{4})/)
      if (phoneMatch) {
        wx.showModal({
          title: '联系快递员',
          content: `快递员电话：${phoneMatch[1]}`,
          showCancel: false
        })
      }
    } else {
      showToast('暂无快递员联系方式')
    }
  },

  /**
   * 联系客服
   */
  onContactService: function () {
    wx.makePhoneCall({
      phoneNumber: '************'
    })
  }
})
