// pages/mall/cart/cart.js
const app = getApp()
const request = require('../../../utils/request')
const { formatImageUrl } = require('../../../utils/util')

Page({
  /**
   * 页面的初始数据
   */
  data: {
    cartList: [], // 购物车商品列表
    loading: true, // 加载状态
    allSelected: false, // 全选状态
    selectedItems: [], // 选中的商品列表
    totalPrice: 0, // 总价格
    totalQuantity: 0, // 总数量
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    console.log('购物车页面加载')
    this.loadCartData()
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    // 获取全局应用实例
    const app = getApp();
    
    // 检查是否需要强制刷新购物车
    if (app.globalData.needRefreshCart) {
      console.log('检测到购物车需要刷新的标记，强制刷新购物车数据');
      // 重置标记
      app.globalData.needRefreshCart = false;
      // 强制刷新购物车数据
      this.loadCartData();
    } else {
    // 每次显示页面时刷新购物车数据
      this.loadCartData();
    }
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh: function () {
    this.loadCartData().then(() => {
      wx.stopPullDownRefresh()
    })
  },

  /**
   * 加载购物车数据
   */
  loadCartData: function () {
    return new Promise((resolve, reject) => {
      this.setData({ loading: true })
      
      console.log('开始加载购物车数据...');
      
      // 添加随机参数防止缓存
      const timestamp = new Date().getTime();
      
      // 调用后台管理系统购物车API
      request.get(`/api/v1/client/cart/list?_t=${timestamp}`).then(res => {
        console.log('购物车数据:', res.data)
        
        let cartList = [];
        
        if (res && res.data) {
          if (res.data.list && Array.isArray(res.data.list)) {
            cartList = res.data.list;
          } else if (Array.isArray(res.data)) {
            cartList = res.data;
          }
        }
        
        console.log('处理后的购物车数据:', cartList);

        // 处理商品图片URL
        cartList.forEach(item => {
          if (item.cover) {
            item.cover = formatImageUrl(item.cover);
            console.log('处理后的购物车商品图片URL:', item.cover);
          }
        });

        // 计算选中状态
        cartList = cartList.map(item => ({
          ...item,
          selected: true // 默认选中所有商品
        }));
        
        const selectedItems = cartList.filter(item => item.selected)
        const allSelected = cartList.length > 0 && selectedItems.length === cartList.length
        
        // 计算总价和总数量
        const { totalPrice, totalQuantity } = this.calculateTotal(selectedItems)
        
        this.setData({
          cartList,
          selectedItems,
          allSelected,
          totalPrice,
          totalQuantity,
          loading: false
        })
        
        resolve(res)
      }).catch(err => {
        console.error('加载购物车失败:', err)
        
        this.setData({
          cartList: [],
          selectedItems: [],
          allSelected: false,
          totalPrice: 0,
          totalQuantity: 0,
          loading: false
        })
        
        wx.showToast({
          title: '加载购物车失败',
          icon: 'none'
        })
        
        resolve({ data: { list: [] } })
      })
    })
  },

  /**
   * 计算总价和总数量
   */
  calculateTotal: function (items) {
    let totalPrice = 0
    let totalQuantity = 0
    
    items.forEach(item => {
      totalPrice += item.price * item.quantity
      totalQuantity += item.quantity
    })
    
    return {
      totalPrice: totalPrice.toFixed(2),
      totalQuantity
    }
  },

  /**
   * 全选/取消全选
   */
  onSelectAll: function () {
    const { cartList, allSelected } = this.data
    const newAllSelected = !allSelected
    
    // 更新所有商品的选中状态
    const updatedCartList = cartList.map(item => ({
      ...item,
      selected: newAllSelected
    }))
    
    const selectedItems = newAllSelected ? updatedCartList : []
    const { totalPrice, totalQuantity } = this.calculateTotal(selectedItems)
    
    this.setData({
      cartList: updatedCartList,
      allSelected: newAllSelected,
      selectedItems,
      totalPrice,
      totalQuantity
    })
  },

  /**
   * 选择/取消选择单个商品
   */
  onSelectItem: function (e) {
    const { index } = e.currentTarget.dataset
    const { cartList } = this.data
    
    // 更新指定商品的选中状态
    const updatedCartList = [...cartList]
    updatedCartList[index].selected = !updatedCartList[index].selected
    
    // 计算全选状态
    const selectedItems = updatedCartList.filter(item => item.selected)
    const allSelected = selectedItems.length === updatedCartList.length
    
    const { totalPrice, totalQuantity } = this.calculateTotal(selectedItems)
    
    this.setData({
      cartList: updatedCartList,
      selectedItems,
      allSelected,
      totalPrice,
      totalQuantity
    })
  },

  /**
   * 减少商品数量
   */
  onDecreaseQuantity: function (e) {
    const { index } = e.currentTarget.dataset
    const { cartList } = this.data
    const item = cartList[index]
    
    if (item.quantity <= 1) {
      return
    }
    
    this.updateQuantity(index, item.quantity - 1)
  },

  /**
   * 增加商品数量
   */
  onIncreaseQuantity: function (e) {
    const { index } = e.currentTarget.dataset
    const { cartList } = this.data
    const item = cartList[index]

    // 检查库存限制
    if (item.stock !== undefined && item.quantity >= item.stock) {
      showToast('库存不足，无法继续增加')
      return
    }

    this.updateQuantity(index, item.quantity + 1)
  },

  /**
   * 更新商品数量
   */
  updateQuantity: function (index, newQuantity) {
    const { cartList } = this.data
    const item = cartList[index]
    
    // 调用后台管理系统API更新数量
    request.post('/api/v1/client/cart/update', {
      id: item.id,
      quantity: newQuantity,
      selected: item.selected
    }).then(res => {
      console.log('更新数量成功:', res)
      
      // 更新本地数据
      const updatedCartList = [...cartList]
      updatedCartList[index].quantity = newQuantity
      
      // 重新计算总价
      const selectedItems = updatedCartList.filter(item => item.selected)
      const { totalPrice, totalQuantity } = this.calculateTotal(selectedItems)
      
      this.setData({
        cartList: updatedCartList,
        selectedItems,
        totalPrice,
        totalQuantity
      })
      
    }).catch(err => {
      console.error('更新数量失败:', err)
      wx.showToast({
        title: '更新失败，请重试',
        icon: 'none'
      })
    })
  },

  /**
   * 删除商品
   */
  onDeleteItem: function (e) {
    const { index } = e.currentTarget.dataset
    const { cartList } = this.data
    const item = cartList[index]
    
    wx.showModal({
      title: '确认删除',
      content: `确定要删除 "${item.name}" 吗？`,
      success: (res) => {
        if (res.confirm) {
          this.deleteCartItem(item.id, index)
        }
      }
    })
  },

  /**
   * 删除购物车商品
   */
  deleteCartItem: function (itemId, index) {
    // 调用后台管理系统删除API
    request.delete('/api/v1/client/cart/remove', {
      ids: [itemId]
    }).then(res => {
      console.log('删除商品成功:', res)
      
      // 更新本地数据
      const { cartList } = this.data
      const updatedCartList = cartList.filter((_, i) => i !== index)
      
      // 重新计算状态
      const selectedItems = updatedCartList.filter(item => item.selected)
      const allSelected = updatedCartList.length > 0 && selectedItems.length === updatedCartList.length
      const { totalPrice, totalQuantity } = this.calculateTotal(selectedItems)
      
      this.setData({
        cartList: updatedCartList,
        selectedItems,
        allSelected,
        totalPrice,
        totalQuantity
      })
      
      wx.showToast({
        title: '删除成功',
        icon: 'success'
      })
      
    }).catch(err => {
      console.error('删除商品失败:', err)
      wx.showToast({
        title: '删除失败，请重试',
        icon: 'none'
      })
    })
  },

  /**
   * 点击商品跳转详情页
   */
  onGoodsDetail: function (e) {
    const { id } = e.currentTarget.dataset
    wx.navigateTo({
      url: `/pages/mall/goods/goods?id=${id}`
    })
  },

  /**
   * 去购物（跳转商城）
   */
  onGoShopping: function () {
    wx.switchTab({
      url: '/pages/mall/home/<USER>'
    })
  },

  /**
   * 点击广告
   */
  onAdClick: function () {
    console.log('点击购物车广告')
    // 可以跳转到广告页面或活动页面
    wx.showToast({
      title: '查看优惠活动',
      icon: 'none'
    })
  },

  /**
   * 去结算
   */
  onCheckout: function () {
    const { selectedItems } = this.data

    if (selectedItems.length === 0) {
      wx.showToast({
        title: '请选择要结算的商品',
        icon: 'none'
      })
      return
    }

    // 检查选中商品的库存
    const outOfStockItems = selectedItems.filter(item =>
      item.stock !== undefined && (item.stock === 0 || item.quantity > item.stock)
    )

    if (outOfStockItems.length > 0) {
      const itemNames = outOfStockItems.map(item => item.name).join('、')
      wx.showToast({
        title: `${itemNames} 库存不足，请调整数量或移除`,
        icon: 'none',
        duration: 3000
      })
      return
    }

    // 跳转到订单确认页面，传递选中的商品数据
    const selectedItemsStr = JSON.stringify(selectedItems)
    wx.navigateTo({
      url: `/pages/mall/order/confirm/confirm?items=${encodeURIComponent(selectedItemsStr)}`
    })
  },

  /**
   * 分享功能
   */
  onShareAppMessage: function () {
    return {
      title: 'WiFi共享商城 - 我的购物车',
      path: '/pages/mall/cart/cart',
      imageUrl: '/assets/images/share-mall.jpg'
    }
  },

  /**
   * 分享到朋友圈
   */
  onShareTimeline: function () {
    return {
      title: 'WiFi共享商城 - 精选好物',
      imageUrl: '/assets/images/share-mall.jpg'
    }
  }
}) 