const db = require('./config/database');

async function testDatabase() {
  try {
    console.log('测试数据库连接...');
    
    // 测试查询商品
    console.log('1. 查询商品...');
    const goods = await db.query('SELECT id, title, price, stock FROM goods WHERE id = ? AND status = 1', [1]);
    console.log('商品查询结果:', goods);
    
    // 测试查询用户
    console.log('2. 查询用户...');
    const user = await db.query('SELECT id, openid, nickname FROM user WHERE id = ?', [2]);
    console.log('用户查询结果:', user);
    
    // 测试查询购物车
    console.log('3. 查询购物车...');
    const cart = await db.query('SELECT * FROM cart WHERE user_id = ?', [2]);
    console.log('购物车查询结果:', cart);
    
    // 测试插入购物车
    console.log('4. 测试插入购物车...');
    const insertResult = await db.query(
      'INSERT INTO cart (user_id, goods_id, quantity, specs_id, selected) VALUES (?, ?, ?, ?, 1)',
      [2, 1, 1, 0]
    );
    console.log('插入结果:', insertResult);
    
    // 再次查询购物车
    console.log('5. 再次查询购物车...');
    const cartAfter = await db.query('SELECT * FROM cart WHERE user_id = ?', [2]);
    console.log('插入后购物车查询结果:', cartAfter);
    
  } catch (error) {
    console.error('数据库测试失败:', error);
    console.error('错误堆栈:', error.stack);
  }
}

testDatabase();
