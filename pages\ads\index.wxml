<!--pages/ads/index.wxml-->
<view class="ads-container">
  <!-- 收益统计 -->
  <view class="stats-section">
    <view class="section-title">收益统计</view>
    <view class="stats-content">
      <view class="stats-item">
        <view class="stats-value">¥{{stats.todayIncome}}</view>
        <view class="stats-label">今日收益</view>
      </view>
      <view class="stats-item">
        <view class="stats-value">¥{{stats.monthIncome}}</view>
        <view class="stats-label">本月收益</view>
      </view>
      <view class="stats-item">
        <view class="stats-value">¥{{stats.totalIncome}}</view>
        <view class="stats-label">累计收益</view>
      </view>
    </view>
    <view class="stats-chart">
      <view class="chart-placeholder">
        [简版统计图表]
      </view>
    </view>
  </view>

  <!-- 广告位列表 -->
  <view class="ad-spaces-section">
    <view class="section-title">广告位列表</view>
    <view class="ad-spaces-list">
      <block wx:if="{{adSpaces.length > 0}}">
        <view class="ad-space-item" wx:for="{{adSpaces}}" wx:key="id">
          <view class="ad-space-name">{{item.name}}</view>
          <view class="ad-space-stats">
            <text>点击量: {{item.clicks || 0}}次</text>
            <text>收益: ¥{{item.income || '0.00'}}</text>
          </view>
        </view>
        <view class="view-more" bindtap="onViewMoreAdSpaces">
          查看更多 >
        </view>
      </block>
      <view class="empty-ad-spaces" wx:else>
        <text>暂无广告位数据</text>
      </view>
    </view>
  </view>

  <!-- 功能按钮 -->
  <view class="function-buttons">
    <view class="function-button" bindtap="onCreateAd">投放我的广告</view>
    <view class="function-button" bindtap="onWithdraw">收益提现</view>
  </view>

  <!-- 广告位 -->
  <view class="ad-container">
    <ad unit-id="adunit-id" ad-type="banner" ad-theme="white" bindload="onAdLoad" binderror="onAdError"></ad>
  </view>

  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{loading}}">
    <text class="loading-text">加载中...</text>
  </view>
</view> 