-- 地区隔离分润系统数据库迁移脚本
-- 创建时间: 2025-07-15

-- 1. 创建地区管理表
CREATE TABLE IF NOT EXISTS `region` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL COMMENT '地区名称',
  `code` varchar(20) NOT NULL COMMENT '地区代码',
  `parent_id` int DEFAULT NULL COMMENT '父级地区ID',
  `level` tinyint NOT NULL DEFAULT '1' COMMENT '地区级别：1省份，2城市，3区县',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态：0禁用，1启用',
  `sort_order` int NOT NULL DEFAULT '0' COMMENT '排序',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_region_code` (`code`),
  KEY `idx_region_parent` (`parent_id`),
  KEY `idx_region_level` (`level`),
  KEY `idx_region_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='地区管理表';

-- 2. 为team表添加地区字段
ALTER TABLE `team` 
ADD COLUMN `region_id` int DEFAULT NULL COMMENT '所属地区ID' AFTER `leader_id`,
ADD KEY `idx_team_region` (`region_id`);

-- 3. 为user表添加地区字段
ALTER TABLE `user` 
ADD COLUMN `region_id` int DEFAULT NULL COMMENT '所属地区ID' AFTER `team_id`,
ADD KEY `idx_user_region` (`region_id`);

-- 4. 为profit_log表添加地区字段（用于分润隔离）
ALTER TABLE `profit_log` 
ADD COLUMN `region_id` int DEFAULT NULL COMMENT '分润所属地区ID' AFTER `team_id`,
ADD KEY `idx_profit_region` (`region_id`);

-- 5. 创建地区分润配置表
CREATE TABLE IF NOT EXISTS `region_profit_config` (
  `id` int NOT NULL AUTO_INCREMENT,
  `region_id` int NOT NULL COMMENT '地区ID',
  `profit_type` varchar(20) NOT NULL COMMENT '分润类型：wifi_share,goods_sale,advertisement',
  `platform_rate` decimal(5,2) NOT NULL DEFAULT '30.00' COMMENT '平台分润比例',
  `leader_rate` decimal(5,2) NOT NULL DEFAULT '40.00' COMMENT '团长分润比例',
  `member_rate` decimal(5,2) NOT NULL DEFAULT '30.00' COMMENT '成员分润比例',
  `min_amount` decimal(10,2) NOT NULL DEFAULT '0.01' COMMENT '最小分润金额',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态：0禁用，1启用',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_region_profit_type` (`region_id`, `profit_type`),
  KEY `idx_region_profit_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='地区分润配置表';

-- 6. 创建团队业绩统计表（按地区隔离）
CREATE TABLE IF NOT EXISTS `team_performance` (
  `id` int NOT NULL AUTO_INCREMENT,
  `team_id` int NOT NULL COMMENT '团队ID',
  `region_id` int NOT NULL COMMENT '地区ID',
  `date` date NOT NULL COMMENT '统计日期',
  `wifi_share_count` int NOT NULL DEFAULT '0' COMMENT 'WiFi分享次数',
  `wifi_share_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT 'WiFi分享收益',
  `goods_sale_count` int NOT NULL DEFAULT '0' COMMENT '商品销售次数',
  `goods_sale_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '商品销售收益',
  `ad_click_count` int NOT NULL DEFAULT '0' COMMENT '广告点击次数',
  `ad_click_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '广告点击收益',
  `total_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '总收益',
  `member_count` int NOT NULL DEFAULT '0' COMMENT '团队成员数',
  `active_member_count` int NOT NULL DEFAULT '0' COMMENT '活跃成员数',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_team_performance_date` (`team_id`, `region_id`, `date`),
  KEY `idx_team_performance_region` (`region_id`),
  KEY `idx_team_performance_date` (`date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='团队业绩统计表';

-- 7. 插入基础地区数据
INSERT INTO `region` (`name`, `code`, `parent_id`, `level`, `status`, `sort_order`) VALUES
('北京市', 'BJ', NULL, 1, 1, 1),
('上海市', 'SH', NULL, 1, 1, 2),
('广东省', 'GD', NULL, 1, 1, 3),
('浙江省', 'ZJ', NULL, 1, 1, 4),
('江苏省', 'JS', NULL, 1, 1, 5),
('山东省', 'SD', NULL, 1, 1, 6),
('河南省', 'HN', NULL, 1, 1, 7),
('四川省', 'SC', NULL, 1, 1, 8),
('湖北省', 'HB', NULL, 1, 1, 9),
('湖南省', 'HN2', NULL, 1, 1, 10);

-- 8. 插入基础分润规则数据
INSERT INTO `profit_rule` (`name`, `type`, `platform_rate`, `leader_rate`, `user_rate`, `status`) VALUES
('WiFi分享分润', 1, 30.00, 40.00, 30.00, 1),
('商品销售分润', 2, 20.00, 50.00, 30.00, 1),
('广告点击分润', 3, 40.00, 35.00, 25.00, 1);

-- 9. 为现有团队分配默认地区（北京市）
UPDATE `team` SET `region_id` = 1 WHERE `region_id` IS NULL;

-- 10. 为现有用户分配默认地区（北京市）
UPDATE `user` SET `region_id` = 1 WHERE `region_id` IS NULL;
