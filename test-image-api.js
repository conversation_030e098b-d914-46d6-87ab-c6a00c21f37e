// 测试图片API访问
const http = require('http');

function testImageAccess() {
  console.log('🔍 测试图片API访问...\n');
  
  // 测试图片路径
  const imagePath = '/uploads/images/1752135040816_65d32f63.jpeg';
  const testUrl = `http://localhost:4000${imagePath}`;
  
  console.log(`📸 测试图片URL: ${testUrl}`);
  
  const options = {
    hostname: 'localhost',
    port: 4000,
    path: imagePath,
    method: 'GET',
    headers: {
      'User-Agent': 'Test-Client/1.0'
    }
  };
  
  const req = http.request(options, (res) => {
    console.log(`\n📊 响应状态码: ${res.statusCode}`);
    console.log(`📋 响应头:`, res.headers);
    
    if (res.statusCode === 200) {
      console.log('✅ 图片访问成功！');
      console.log(`📏 Content-Type: ${res.headers['content-type']}`);
      console.log(`📐 Content-Length: ${res.headers['content-length']}`);
      
      let dataLength = 0;
      res.on('data', (chunk) => {
        dataLength += chunk.length;
      });
      
      res.on('end', () => {
        console.log(`📦 实际接收数据长度: ${dataLength} bytes`);
        console.log('🎉 图片服务测试完成！');
      });
    } else {
      console.log('❌ 图片访问失败！');
      
      let errorData = '';
      res.on('data', (chunk) => {
        errorData += chunk.toString();
      });
      
      res.on('end', () => {
        console.log('📄 错误响应内容:', errorData);
      });
    }
  });
  
  req.on('error', (err) => {
    console.error('❌ 请求失败:', err.message);
  });
  
  req.end();
}

// 等待服务器启动后测试
setTimeout(() => {
  testImageAccess();
}, 3000);
