const db = require('../database');
const { success, error } = require('../utils/response');

// 获取用户的团队信息
exports.getTeamInfo = async (req, res) => {
  console.log('获取用户团队信息');

  try {
    const userId = req.user ? req.user.id : null;

    // 如果没有用户信息，返回默认数据
    if (!userId) {
      return success(res, {
        id: 0,
        teamName: '我的团队',
        memberCount: 0,
        wifiCount: 0,
        monthPerformance: '0.00',
        totalIncome: '0.00',
        todayIncome: '0.00',
        weekIncome: '0.00',
        activeMembers: 0,
        newMembersThisMonth: 0,
        teamLevel: 1,
        createTime: '',
        isLeader: false,
        inviteCode: ''
      }, '获取团队信息成功');
    }

    // 检查用户是否是团队成员或团长
    let memberQuery = [];
    try {
      memberQuery = await db.query(
        `SELECT tm.*, t.*
         FROM team_member tm
         JOIN team t ON tm.team_id = t.id
         WHERE tm.user_id = ?`,
        [userId]
      );
    } catch (err) {
      console.log('team_member表不存在，返回默认团队信息');
      return success(res, {
        id: 0,
        teamName: '我的团队',
        memberCount: 1,
        wifiCount: 0,
        monthPerformance: '0.00',
        totalIncome: '0.00',
        todayIncome: '0.00',
        weekIncome: '0.00',
        activeMembers: 1,
        newMembersThisMonth: 0,
        teamLevel: 1,
        createTime: new Date().toISOString(),
        isLeader: true,
        inviteCode: 'TEAM000001'
      }, '获取团队信息成功');
    }

    // 如果用户不是任何团队的成员，返回空数据
    if (!memberQuery || memberQuery.length === 0) {
      return success(res, {
        id: 0,
        teamName: '我的团队',
        memberCount: 0,
        wifiCount: 0,
        monthPerformance: '0.00',
        totalIncome: '0.00',
        todayIncome: '0.00',
        weekIncome: '0.00',
        activeMembers: 0,
        newMembersThisMonth: 0,
        teamLevel: 1,
        createTime: '',
        isLeader: false,
        inviteCode: ''
      }, '获取团队信息成功');
    }
    
    const teamMember = memberQuery[0];
    const teamId = teamMember.team_id;
    
    // 获取团队基本信息
    const teamQuery = await db.query(
      `SELECT
        t.id,
        t.name as teamName,
        t.member_count as memberCount,
        1 as teamLevel,
        CONCAT('TEAM', LPAD(t.id, 6, '0')) as inviteCode,
        t.created_at as createTime,
        t.leader_id
       FROM team t
       WHERE t.id = ?`,
      [teamId]
    );

    if (!teamQuery || teamQuery.length === 0) {
      return error(res, '团队不存在', 404);
    }

    const team = teamQuery[0];
    team.isLeader = team.leader_id === userId;

    // 获取团队月收益 - 优先从缓存获取，缓存不存在则实时计算
    let monthPerformance = 0;
    try {
      // 先尝试从缓存获取
      const cacheQuery = await db.query(
        `SELECT month_income FROM team_performance_cache
         WHERE team_id = ? AND date = CURDATE()`,
        [teamId]
      );

      if (cacheQuery.length > 0) {
        monthPerformance = cacheQuery[0].month_income || 0;
      } else {
        // 缓存不存在，实时计算
        const monthIncomeQuery = await db.query(
          `SELECT IFNULL(SUM(pl.amount), 0) as monthIncome
           FROM profit_log pl
           WHERE pl.user_id IN (
             SELECT user_id FROM team_member WHERE team_id = ?
           ) AND YEAR(pl.created_at) = YEAR(NOW()) AND MONTH(pl.created_at) = MONTH(NOW())`,
          [teamId]
        );
        monthPerformance = monthIncomeQuery[0]?.monthIncome || 0;

        // 异步刷新缓存
        setImmediate(() => {
          const PlatformStatsService = require('../services/platform-stats');
          PlatformStatsService.refreshTeamCache(teamId).catch(err => {
            console.log('异步刷新团队缓存失败:', err.message);
          });
        });
      }
    } catch (err) {
      console.log('获取月收益失败，使用默认值:', err.message);
      monthPerformance = 0;
    }
    team.monthPerformance = parseFloat(monthPerformance).toFixed(2);

    // 获取团队总收益 - 优先从缓存获取
    let totalIncome = 0;
    try {
      // 先尝试从缓存获取
      const cacheQuery = await db.query(
        `SELECT total_income FROM team_performance_cache
         WHERE team_id = ? AND date = CURDATE()`,
        [teamId]
      );

      if (cacheQuery.length > 0) {
        totalIncome = cacheQuery[0].total_income || 0;
      } else {
        // 缓存不存在，实时计算
        const totalIncomeQuery = await db.query(
          `SELECT IFNULL(SUM(pl.amount), 0) as totalIncome
           FROM profit_log pl
           WHERE pl.user_id IN (
             SELECT user_id FROM team_member WHERE team_id = ?
           )`,
          [teamId]
        );
        totalIncome = totalIncomeQuery[0]?.totalIncome || 0;
      }
    } catch (err) {
      console.log('获取总收益失败，使用默认值:', err.message);
      totalIncome = 0;
    }
    team.totalIncome = parseFloat(totalIncome).toFixed(2);

    // 获取团队WiFi数量
    const wifiCountQuery = await db.query(
      `SELECT COUNT(*) as wifiCount
       FROM wifi
       WHERE user_id IN (
         SELECT user_id FROM team_member WHERE team_id = ?
       )`,
      [teamId]
    );

    const wifiCount = wifiCountQuery[0].wifiCount || 0;
    team.wifiCount = wifiCount;

    // 获取今日收益 - 从分润记录实时统计
    let todayIncome = 0;
    try {
      const todayIncomeQuery = await db.query(
        `SELECT IFNULL(SUM(pl.amount), 0) as todayIncome
         FROM profit_log pl
         WHERE pl.user_id IN (
           SELECT user_id FROM team_member WHERE team_id = ?
         ) AND DATE(pl.created_at) = CURDATE()`,
        [teamId]
      );
      todayIncome = todayIncomeQuery[0]?.todayIncome || 0;
    } catch (err) {
      console.log('获取今日收益失败，使用默认值:', err.message);
      todayIncome = 0;
    }
    team.todayIncome = parseFloat(todayIncome).toFixed(2);

    // 获取本周收益 - 从分润记录实时统计
    let weekIncome = 0;
    try {
      const weekIncomeQuery = await db.query(
        `SELECT IFNULL(SUM(pl.amount), 0) as weekIncome
         FROM profit_log pl
         WHERE pl.user_id IN (
           SELECT user_id FROM team_member WHERE team_id = ?
         ) AND YEARWEEK(pl.created_at) = YEARWEEK(NOW())`,
        [teamId]
      );
      weekIncome = weekIncomeQuery[0]?.weekIncome || 0;
    } catch (err) {
      console.log('获取本周收益失败，使用默认值:', err.message);
      weekIncome = 0;
    }
    team.weekIncome = parseFloat(weekIncome).toFixed(2);

    // 获取活跃成员数（本月有收益的成员）
    let activeMembers = 0;
    try {
      const activeMembersQuery = await db.query(
        `SELECT COUNT(DISTINCT user_id) as activeMembers
         FROM income_bill
         WHERE user_id IN (
           SELECT user_id FROM team_member WHERE team_id = ?
         ) AND YEAR(created_at) = YEAR(NOW()) AND MONTH(created_at) = MONTH(NOW())`,
        [teamId]
      );
      activeMembers = activeMembersQuery[0]?.activeMembers || 0;
    } catch (err) {
      console.log('income_bill表不存在，使用默认值');
      activeMembers = 0;
    }
    team.activeMembers = activeMembers;

    // 获取本月新增成员数（只使用joined_at字段）
    let newMembersThisMonth = 0;
    try {
      const newMembersQuery = await db.query(
        `SELECT COUNT(*) as newMembersThisMonth
         FROM team_member
         WHERE team_id = ?
         AND YEAR(joined_at) = YEAR(NOW())
         AND MONTH(joined_at) = MONTH(NOW())`,
        [teamId]
      );
      newMembersThisMonth = newMembersQuery[0]?.newMembersThisMonth || 0;
    } catch (err) {
      console.log('获取新增成员数失败，使用默认值:', err.message);
      newMembersThisMonth = 0;
    }
    team.newMembersThisMonth = newMembersThisMonth;

    // 格式化创建时间
    if (team.createTime) {
      team.createTime = new Date(team.createTime).toLocaleDateString('zh-CN');
    }

    // 确保数值格式正确
    team.monthPerformance = parseFloat(team.monthPerformance || 0).toFixed(2);
    team.totalIncome = parseFloat(team.totalIncome || 0).toFixed(2);

    return success(res, team, '获取团队信息成功');
  } catch (err) {
    console.error('获取团队信息失败:', err);
    return error(res, '获取团队信息失败，请稍后重试');
  }
};

// 获取团队成员列表
exports.getTeamMembers = async (req, res) => {
  console.log('获取团队成员列表');
  
  try {
    const userId = req.user.id;
    const { page = 1, limit = 10 } = req.query;
    const pageNum = parseInt(page);
    const limitNum = parseInt(limit);
    const offset = (pageNum - 1) * limitNum;
    
    // 检查用户是否是团队成员或团长
    const memberQuery = await db.query(
      `SELECT tm.*, t.* 
       FROM team_member tm
       JOIN team t ON tm.team_id = t.id
       WHERE tm.user_id = ?`,
      [userId]
    );
    
    // 如果用户不是任何团队的成员，返回空列表
    if (!memberQuery || memberQuery.length === 0) {
      return success(res, {
        list: [],
        pagination: {
          total: 0,
          page: pageNum,
          limit: limitNum,
          pages: 0
        }
      }, '获取团队成员列表成功');
    }
    
    const teamId = memberQuery[0].team_id;
    
    // 查询总数
    const countResult = await db.query(
      `SELECT COUNT(*) as total FROM team_member WHERE team_id = ?`,
      [teamId]
    );
    
    const total = countResult[0].total || 0;
    
    // 查询成员列表 - 使用字符串拼接避免参数类型问题
    const members = await db.query(
      `SELECT
        tm.id,
        tm.user_id as userId,
        tm.role,
        tm.joined_at as joinedAt,
        u.nickname,
        u.avatar,
        u.phone
       FROM team_member tm
       LEFT JOIN user u ON tm.user_id = u.id
       WHERE tm.team_id = ?
       ORDER BY tm.role DESC, tm.joined_at DESC
       LIMIT ${limitNum} OFFSET ${offset}`,
      [teamId]
    );
    
    return success(res, {
      list: members || [],
      pagination: {
        total,
        page: pageNum,
        limit: limitNum,
        pages: Math.ceil(total / limitNum) || 1
      }
    }, '获取团队成员列表成功');
  } catch (err) {
    console.error('获取团队成员列表失败:', err);
    return error(res, '获取团队成员列表失败，请稍后重试');
  }
}; 