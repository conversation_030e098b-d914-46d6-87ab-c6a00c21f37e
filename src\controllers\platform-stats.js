const PlatformStatsService = require('../services/platform-stats');
const { success, error } = require('../utils/response');
const logger = require('../utils/logger');

/**
 * 平台收益统计控制器
 */

/**
 * 获取平台收益统计
 * GET /api/v1/admin/platform/stats
 */
const getPlatformStats = async (req, res) => {
  try {
    const { timeRange = 'all', regionId } = req.query;
    
    const stats = await PlatformStatsService.getPlatformStats(
      timeRange, 
      regionId ? parseInt(regionId) : null
    );
    
    return success(res, stats, '获取平台统计成功');
  } catch (err) {
    logger.error(`获取平台统计失败: ${err.message}`);
    return error(res, '获取平台统计失败', 500);
  }
};

/**
 * 获取收益趋势
 * GET /api/v1/admin/platform/trend
 */
const getRevenueTrend = async (req, res) => {
  try {
    const { days = 7, regionId } = req.query;
    
    const trend = await PlatformStatsService.getRevenueTrend(
      parseInt(days),
      regionId ? parseInt(regionId) : null
    );
    
    return success(res, trend, '获取收益趋势成功');
  } catch (err) {
    logger.error(`获取收益趋势失败: ${err.message}`);
    return error(res, '获取收益趋势失败', 500);
  }
};

/**
 * 刷新团队业绩缓存
 * POST /api/v1/admin/platform/refresh-team-cache
 */
const refreshTeamCache = async (req, res) => {
  try {
    const { teamId } = req.body;
    
    if (!teamId) {
      return error(res, '团队ID不能为空', 400);
    }
    
    await PlatformStatsService.refreshTeamCache(parseInt(teamId));
    
    return success(res, null, '团队业绩缓存刷新成功');
  } catch (err) {
    logger.error(`刷新团队业绩缓存失败: ${err.message}`);
    return error(res, '刷新团队业绩缓存失败', 500);
  }
};

/**
 * 获取用户余额变动记录
 * GET /api/v1/admin/platform/balance-logs
 */
const getBalanceLogs = async (req, res) => {
  try {
    const { userId, type, startDate, endDate, page = 1, limit = 20 } = req.query;
    
    let whereConditions = [];
    const params = [];
    
    if (userId) {
      whereConditions.push('user_id = ?');
      params.push(parseInt(userId));
    }
    
    if (type) {
      whereConditions.push('type = ?');
      params.push(type);
    }
    
    if (startDate) {
      whereConditions.push('created_at >= ?');
      params.push(startDate);
    }
    
    if (endDate) {
      whereConditions.push('created_at <= ?');
      params.push(endDate);
    }
    
    const whereClause = whereConditions.length > 0 ? 
      `WHERE ${whereConditions.join(' AND ')}` : '';
    
    const offset = (parseInt(page) - 1) * parseInt(limit);
    
    // 获取总数
    const db = require('../database');
    const countSql = `SELECT COUNT(*) as total FROM balance_log ${whereClause}`;
    const countResult = await db.query(countSql, params);
    const total = countResult[0].total;
    
    // 获取列表
    const listSql = `
      SELECT 
        bl.*,
        u.nickname as user_nickname,
        u.phone as user_phone
      FROM balance_log bl
      LEFT JOIN user u ON bl.user_id = u.id
      ${whereClause}
      ORDER BY bl.created_at DESC
      LIMIT ?, ?
    `;
    
    const list = await db.query(listSql, [...params, offset, parseInt(limit)]);
    
    return success(res, {
      list: list.map(item => ({
        ...item,
        amount: parseFloat(item.amount).toFixed(2),
        beforeBalance: parseFloat(item.before_balance).toFixed(2),
        afterBalance: parseFloat(item.after_balance).toFixed(2)
      })),
      pagination: {
        total,
        page: parseInt(page),
        limit: parseInt(limit),
        pages: Math.ceil(total / parseInt(limit))
      }
    }, '获取余额变动记录成功');
  } catch (err) {
    logger.error(`获取余额变动记录失败: ${err.message}`);
    return error(res, '获取余额变动记录失败', 500);
  }
};

/**
 * 获取实时统计概览
 * GET /api/v1/admin/platform/overview
 */
const getPlatformOverview = async (req, res) => {
  try {
    const db = require('../database');
    
    // 获取今日统计
    const todayStats = await db.query(`
      SELECT 
        COUNT(*) as today_transactions,
        SUM(amount) as today_profit,
        COUNT(DISTINCT user_id) as today_active_users
      FROM profit_log 
      WHERE DATE(created_at) = CURDATE()
    `);
    
    // 获取本月统计
    const monthStats = await db.query(`
      SELECT 
        COUNT(*) as month_transactions,
        SUM(amount) as month_profit,
        COUNT(DISTINCT user_id) as month_active_users
      FROM profit_log 
      WHERE YEAR(created_at) = YEAR(NOW()) AND MONTH(created_at) = MONTH(NOW())
    `);
    
    // 获取用户统计
    const userStats = await db.query(`
      SELECT
        COUNT(*) as total_users,
        SUM(balance) as total_balance,
        COUNT(CASE WHEN balance > 0 THEN 1 END) as users_with_balance,
        COUNT(CASE WHEN DATE(created_at) = CURDATE() THEN 1 END) as today_new_users
      FROM user
    `);

    // 获取WiFi统计
    const wifiStats = await db.query(`
      SELECT
        COUNT(*) as total_wifi_codes,
        COUNT(CASE WHEN DATE(created_at) = CURDATE() THEN 1 END) as today_new_codes
      FROM wifi
    `);

    // 获取订单统计
    const orderStats = await db.query(`
      SELECT
        COUNT(*) as total_orders,
        COUNT(CASE WHEN DATE(created_at) = CURDATE() THEN 1 END) as today_orders,
        SUM(total_amount) as total_revenue,
        SUM(CASE WHEN DATE(created_at) = CURDATE() THEN total_amount ELSE 0 END) as today_revenue
      FROM orders
      WHERE status != 'cancelled'
    `);
    
    // 获取团队统计
    const teamStats = await db.query(`
      SELECT 
        COUNT(*) as total_teams,
        SUM(member_count) as total_members,
        AVG(member_count) as avg_team_size
      FROM team
    `);
    
    // 获取地区统计 - 暂时返回空数组，因为region表不存在
    const regionStats = [];
    
    const overview = {
      // 前端期望的数据结构
      users: {
        total_users: userStats[0]?.total_users || 0,
        today_new_users: userStats[0]?.today_new_users || 0,
        total_balance: parseFloat(userStats[0]?.total_balance || 0).toFixed(2),
        users_with_balance: userStats[0]?.users_with_balance || 0
      },
      wifi: {
        total_wifi_codes: wifiStats[0]?.total_wifi_codes || 0,
        today_new_codes: wifiStats[0]?.today_new_codes || 0
      },
      orders: {
        total_orders: orderStats[0]?.total_orders || 0,
        today_orders: orderStats[0]?.today_orders || 0
      },
      revenue: {
        total_revenue: parseFloat(orderStats[0]?.total_revenue || 0).toFixed(2),
        today_revenue: parseFloat(orderStats[0]?.today_revenue || 0).toFixed(2)
      },
      // 保留原有的详细统计
      today: {
        transactions: todayStats[0]?.today_transactions || 0,
        profit: parseFloat(todayStats[0]?.today_profit || 0).toFixed(2),
        activeUsers: todayStats[0]?.today_active_users || 0
      },
      month: {
        transactions: monthStats[0]?.month_transactions || 0,
        profit: parseFloat(monthStats[0]?.month_profit || 0).toFixed(2),
        activeUsers: monthStats[0]?.month_active_users || 0
      },
      teams: {
        total: teamStats[0]?.total_teams || 0,
        totalMembers: teamStats[0]?.total_members || 0,
        avgSize: parseFloat(teamStats[0]?.avg_team_size || 0).toFixed(1)
      },
      regions: regionStats.map(region => ({
        name: region.region_name,
        teamCount: region.team_count,
        userCount: region.user_count,
        totalProfit: parseFloat(region.total_profit).toFixed(2)
      })),
      generatedAt: new Date()
    };
    
    return success(res, overview, '获取平台概览成功');
  } catch (err) {
    logger.error(`获取平台概览失败: ${err.message}`);
    return error(res, '获取平台概览失败', 500);
  }
};

/**
 * 获取用户增长趋势
 * GET /api/v1/admin/platform/user-growth
 */
const getUserGrowth = async (req, res) => {
  try {
    const { period = '7d' } = req.query;
    const db = require('../database');

    let days = 7;
    if (period === '30d') days = 30;
    else if (period === '90d') days = 90;

    const userGrowth = await db.query(`
      SELECT
        DATE(created_at) as date,
        COUNT(*) as new_users
      FROM user
      WHERE created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
      GROUP BY DATE(created_at)
      ORDER BY date ASC
    `, [days]);

    return success(res, userGrowth, '获取用户增长趋势成功');
  } catch (err) {
    logger.error(`获取用户增长趋势失败: ${err.message}`);
    return error(res, '获取用户增长趋势失败', 500);
  }
};

/**
 * 获取业务类型统计
 * GET /api/v1/admin/platform/business-type
 */
const getBusinessTypeStats = async (req, res) => {
  try {
    const db = require('../database');

    const businessStats = await db.query(`
      SELECT
        'WiFi分享' as type,
        COUNT(*) as count,
        SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as active_count
      FROM wifi
      UNION ALL
      SELECT
        '商品销售' as type,
        COUNT(*) as count,
        SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as active_count
      FROM orders
      UNION ALL
      SELECT
        '团队管理' as type,
        COUNT(*) as count,
        COUNT(*) as active_count
      FROM team
    `);

    return success(res, businessStats, '获取业务类型统计成功');
  } catch (err) {
    logger.error(`获取业务类型统计失败: ${err.message}`);
    return error(res, '获取业务类型统计失败', 500);
  }
};

/**
 * 获取地区统计
 * GET /api/v1/admin/platform/region
 */
const getRegionStats = async (req, res) => {
  try {
    // 暂时返回空数组，因为region表不存在
    const regionStats = [];

    return success(res, regionStats, '获取地区统计成功');
  } catch (err) {
    logger.error(`获取地区统计失败: ${err.message}`);
    return error(res, '获取地区统计失败', 500);
  }
};

module.exports = {
  getPlatformStats,
  getRevenueTrend,
  refreshTeamCache,
  getBalanceLogs,
  getPlatformOverview,
  getUserGrowth,
  getBusinessTypeStats,
  getRegionStats
};
