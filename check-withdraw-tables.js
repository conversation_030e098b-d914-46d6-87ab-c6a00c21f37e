const mysql = require('mysql2/promise');

async function checkWithdrawTables() {
  let connection;
  
  try {
    // 连接数据库
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: 'wo587129955',
      database: 'mall'
    });
    
    console.log('🔍 检查提现相关表结构...\n');
    
    // 1. 检查所有提现相关表
    console.log('1️⃣ 提现相关表:');
    const [tables] = await connection.execute(`
      SELECT table_name, table_comment 
      FROM information_schema.tables 
      WHERE table_schema = 'mall' 
      AND (table_name LIKE '%withdraw%' OR table_comment LIKE '%提现%')
      ORDER BY table_name
    `);
    console.table(tables);
    
    // 2. 检查user_withdraw_account表结构
    console.log('\n2️⃣ user_withdraw_account表结构:');
    try {
      const [columns] = await connection.execute(`
        SELECT column_name, column_type, is_nullable, column_default, column_comment
        FROM information_schema.columns 
        WHERE table_schema = 'mall' 
        AND table_name = 'user_withdraw_account'
        ORDER BY ordinal_position
      `);
      if (columns.length > 0) {
        console.table(columns);
      } else {
        console.log('❌ user_withdraw_account表不存在');
      }
    } catch (error) {
      console.log('❌ 检查user_withdraw_account表失败:', error.message);
    }
    
    // 3. 检查withdraw_application表结构
    console.log('\n3️⃣ withdraw_application表结构:');
    try {
      const [columns2] = await connection.execute(`
        SELECT column_name, column_type, is_nullable, column_default, column_comment
        FROM information_schema.columns 
        WHERE table_schema = 'mall' 
        AND table_name = 'withdraw_application'
        ORDER BY ordinal_position
      `);
      if (columns2.length > 0) {
        console.table(columns2);
      } else {
        console.log('❌ withdraw_application表不存在');
      }
    } catch (error) {
      console.log('❌ 检查withdraw_application表失败:', error.message);
    }
    
    // 4. 检查withdraw_config表结构
    console.log('\n4️⃣ withdraw_config表结构:');
    try {
      const [columns3] = await connection.execute(`
        SELECT column_name, column_type, is_nullable, column_default, column_comment
        FROM information_schema.columns 
        WHERE table_schema = 'mall' 
        AND table_name = 'withdraw_config'
        ORDER BY ordinal_position
      `);
      if (columns3.length > 0) {
        console.table(columns3);
      } else {
        console.log('❌ withdraw_config表不存在');
      }
    } catch (error) {
      console.log('❌ 检查withdraw_config表失败:', error.message);
    }
    
    // 5. 检查原有的withdraw表
    console.log('\n5️⃣ 原有withdraw表结构:');
    try {
      const [columns4] = await connection.execute(`
        SELECT column_name, column_type, is_nullable, column_default, column_comment
        FROM information_schema.columns 
        WHERE table_schema = 'mall' 
        AND table_name = 'withdraw'
        ORDER BY ordinal_position
      `);
      if (columns4.length > 0) {
        console.table(columns4);
      } else {
        console.log('❌ withdraw表不存在');
      }
    } catch (error) {
      console.log('❌ 检查withdraw表失败:', error.message);
    }
    
    // 6. 检查bank_card表结构
    console.log('\n6️⃣ bank_card表结构:');
    try {
      const [columns5] = await connection.execute(`
        SELECT column_name, column_type, is_nullable, column_default, column_comment
        FROM information_schema.columns 
        WHERE table_schema = 'mall' 
        AND table_name = 'bank_card'
        ORDER BY ordinal_position
      `);
      if (columns5.length > 0) {
        console.table(columns5);
      } else {
        console.log('❌ bank_card表不存在');
      }
    } catch (error) {
      console.log('❌ 检查bank_card表失败:', error.message);
    }
    
    console.log('\n📊 检查完成');
    
  } catch (error) {
    console.error('❌ 检查失败:', error.message);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// 运行检查
checkWithdrawTables();
