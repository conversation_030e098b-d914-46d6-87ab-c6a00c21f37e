const path = require('path');
const dotenv = require('dotenv');
const fs = require('fs');

// 加载.env文件
const dotenvPath = path.resolve(process.cwd(), '.env');
if (fs.existsSync(dotenvPath)) {
  dotenv.config({ path: dotenvPath });
}

// 确定环境
const NODE_ENV = process.env.NODE_ENV || 'development';

// 加载环境配置
const envConfig = require(path.resolve(__dirname, 'env', `${NODE_ENV}.js`));

// 基础配置
const baseConfig = {
  // 服务器配置
  server: {
    port: process.env.PORT || 8083,
    nodeEnv: NODE_ENV
  },
  
  // 日志配置
  logger: {
    level: process.env.LOG_LEVEL || 'info',
    logDir: path.resolve(process.cwd(), 'logs')
  }
};

// 合并配置
const config = Object.assign({}, baseConfig, envConfig);

// 暴露配置
module.exports = config; 