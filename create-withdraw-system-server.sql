-- 创建完整的提现系统数据表
-- 执行时间：2025年1月29日

USE mall;

-- 1. 完善银行卡表
DROP TABLE IF EXISTS `bank_card`;
CREATE TABLE `bank_card` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '银行卡ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `bank_name` varchar(50) NOT NULL COMMENT '银行名称',
  `bank_code` varchar(20) DEFAULT NULL COMMENT '银行代码',
  `card_number` varchar(50) NOT NULL COMMENT '卡号（加密存储）',
  `card_number_mask` varchar(50) NOT NULL COMMENT '卡号掩码显示',
  `card_holder` varchar(50) NOT NULL COMMENT '持卡人姓名',
  `card_type` tinyint(1) NOT NULL DEFAULT '1' COMMENT '卡类型：1储蓄卡，2信用卡',
  `phone` varchar(20) DEFAULT NULL COMMENT '预留手机号',
  `id_card` varchar(50) DEFAULT NULL COMMENT '身份证号（加密存储）',
  `is_default` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否默认：0否，1是',
  `is_verified` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已验证：0否，1是',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0禁用，1启用',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='银行卡表';

-- 2. 微信支付账户表
CREATE TABLE IF NOT EXISTS `wechat_account` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '微信账户ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `openid` varchar(100) NOT NULL COMMENT '微信openid',
  `nickname` varchar(100) DEFAULT NULL COMMENT '微信昵称',
  `real_name` varchar(50) DEFAULT NULL COMMENT '真实姓名',
  `is_verified` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否实名认证：0否，1是',
  `is_default` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否默认提现方式：0否，1是',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0禁用，1启用',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_openid` (`user_id`, `openid`),
  KEY `user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='微信支付账户表';

-- 3. 完善提现申请表
DROP TABLE IF EXISTS `withdraw`;
CREATE TABLE `withdraw` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '提现ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `withdraw_no` varchar(50) NOT NULL COMMENT '提现单号',
  `amount` decimal(10,2) NOT NULL COMMENT '提现金额',
  `fee` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '手续费',
  `actual_amount` decimal(10,2) NOT NULL COMMENT '实际到账金额',
  `withdraw_type` varchar(20) NOT NULL COMMENT '提现方式：wechat,bank_card',
  `account_id` int(11) NOT NULL COMMENT '账户ID（银行卡ID或微信账户ID）',
  `account_info` text DEFAULT NULL COMMENT '账户信息快照（JSON格式）',
  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态：0待审核，1审核通过，2审核拒绝，3处理中，4已完成，5已取消',
  `apply_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '申请时间',
  `audit_user_id` int(11) DEFAULT NULL COMMENT '审核人ID',
  `audit_time` datetime DEFAULT NULL COMMENT '审核时间',
  `audit_remark` varchar(255) DEFAULT NULL COMMENT '审核备注',
  `process_time` datetime DEFAULT NULL COMMENT '处理时间',
  `complete_time` datetime DEFAULT NULL COMMENT '完成时间',
  `transaction_id` varchar(100) DEFAULT NULL COMMENT '微信支付交易ID',
  `bank_transaction_id` varchar(100) DEFAULT NULL COMMENT '银行交易流水号',
  `failure_reason` varchar(255) DEFAULT NULL COMMENT '失败原因',
  `remark` varchar(255) DEFAULT NULL COMMENT '用户备注',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `withdraw_no` (`withdraw_no`),
  KEY `user_id` (`user_id`),
  KEY `status` (`status`),
  KEY `withdraw_type` (`withdraw_type`),
  KEY `apply_time` (`apply_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='提现申请表';

-- 4. 提现配置表
CREATE TABLE IF NOT EXISTS `withdraw_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `config_key` varchar(50) NOT NULL COMMENT '配置键',
  `config_value` text NOT NULL COMMENT '配置值',
  `config_type` varchar(20) NOT NULL DEFAULT 'string' COMMENT '配置类型',
  `description` varchar(255) DEFAULT NULL COMMENT '配置描述',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0禁用，1启用',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `config_key` (`config_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='提现配置表';

-- 5. 银行信息表
CREATE TABLE IF NOT EXISTS `bank_info` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '银行ID',
  `bank_code` varchar(20) NOT NULL COMMENT '银行代码',
  `bank_name` varchar(50) NOT NULL COMMENT '银行名称',
  `bank_logo` varchar(255) DEFAULT NULL COMMENT '银行logo',
  `is_support` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否支持：0否，1是',
  `min_amount` decimal(10,2) NOT NULL DEFAULT '1.00' COMMENT '最小提现金额',
  `max_amount` decimal(10,2) NOT NULL DEFAULT '50000.00' COMMENT '最大提现金额',
  `fee_rate` decimal(5,4) NOT NULL DEFAULT '0.0000' COMMENT '手续费率',
  `sort_order` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0禁用，1启用',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `bank_code` (`bank_code`),
  KEY `status` (`status`),
  KEY `sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='银行信息表';

-- 插入提现配置数据
INSERT INTO `withdraw_config` (`config_key`, `config_value`, `config_type`, `description`) VALUES
('min_withdraw_amount', '10', 'number', '最小提现金额（元）'),
('max_withdraw_amount', '50000', 'number', '最大提现金额（元）'),
('daily_withdraw_limit', '100000', 'number', '每日提现限额（元）'),
('wechat_fee_rate', '0.006', 'number', '微信提现手续费率'),
('bank_fee_rate', '0.001', 'number', '银行卡提现手续费率'),
('wechat_min_fee', '0.1', 'number', '微信提现最小手续费（元）'),
('bank_min_fee', '2', 'number', '银行卡提现最小手续费（元）'),
('auto_audit_enabled', 'true', 'boolean', '是否启用自动审核'),
('auto_audit_limit', '1000', 'number', '自动审核金额限制（元）'),
('working_hours', '{"start": "09:00", "end": "18:00"}', 'json', '工作时间配置'),
('weekend_process', 'false', 'boolean', '是否周末处理提现'),
('notification_enabled', 'true', 'boolean', '是否启用提现通知')
ON DUPLICATE KEY UPDATE 
  `config_value` = VALUES(`config_value`),
  `description` = VALUES(`description`),
  `updated_at` = CURRENT_TIMESTAMP;

-- 插入银行信息数据
INSERT INTO `bank_info` (`bank_code`, `bank_name`, `min_amount`, `max_amount`, `fee_rate`, `sort_order`) VALUES
('ICBC', '中国工商银行', 10.00, 50000.00, 0.001, 1),
('ABC', '中国农业银行', 10.00, 50000.00, 0.001, 2),
('BOC', '中国银行', 10.00, 50000.00, 0.001, 3),
('CCB', '中国建设银行', 10.00, 50000.00, 0.001, 4),
('COMM', '交通银行', 10.00, 50000.00, 0.001, 5),
('CMB', '招商银行', 10.00, 50000.00, 0.001, 6),
('CITIC', '中信银行', 10.00, 50000.00, 0.001, 7),
('CEB', '光大银行', 10.00, 50000.00, 0.001, 8),
('CMBC', '中国民生银行', 10.00, 50000.00, 0.001, 9),
('PAB', '平安银行', 10.00, 50000.00, 0.001, 10)
ON DUPLICATE KEY UPDATE 
  `bank_name` = VALUES(`bank_name`),
  `min_amount` = VALUES(`min_amount`),
  `max_amount` = VALUES(`max_amount`),
  `updated_at` = CURRENT_TIMESTAMP;

-- 为测试用户插入微信账户
INSERT INTO `wechat_account` (`user_id`, `openid`, `nickname`, `real_name`, `is_verified`, `is_default`) VALUES
(1, 'oW20C7mVlW8e3W2AgUGtDTJeAbQU', '润生', '张润生', 1, 1)
ON DUPLICATE KEY UPDATE 
  `nickname` = VALUES(`nickname`),
  `updated_at` = CURRENT_TIMESTAMP;

-- 为测试用户插入银行卡
INSERT INTO `bank_card` (`user_id`, `bank_name`, `bank_code`, `card_number`, `card_number_mask`, `card_holder`, `phone`, `is_verified`) VALUES
(1, '招商银行', 'CMB', 'encrypted_6217000123456789', '**** **** **** 6789', '张润生', '***********', 1)
ON DUPLICATE KEY UPDATE 
  `updated_at` = CURRENT_TIMESTAMP;

-- 显示创建结果
SELECT '✅ 提现系统表创建完成' as message;

-- 验证表结构
SELECT 
  table_name as '表名',
  table_comment as '表说明'
FROM information_schema.tables 
WHERE table_schema = 'mall' 
AND table_name IN ('bank_card', 'wechat_account', 'withdraw', 'withdraw_config', 'bank_info')
ORDER BY table_name;
