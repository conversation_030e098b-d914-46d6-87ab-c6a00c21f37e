// pages/user/address/edit.js
const app = getApp();
const { STORAGE_KEYS } = require('../../../utils/constants');

Page({
  /**
   * 页面的初始数据
   */
  data: {
    type: 'add', // add 或 edit
    addressId: null,
    address: {
      name: '',
      phone: '',
      province: '',
      city: '',
      district: '',
      address: '',
      is_default: false
    },
    region: ['', '', ''],
    loading: false,
    isLoggedIn: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    // 检查登录状态
    this.checkLoginStatus();
    
    const { type, id } = options;
    
    this.setData({
      type: type || 'add',
      addressId: id || null
    });
    
    // 如果是编辑模式，加载地址详情
    if (type === 'edit' && id) {
      this.loadAddressDetail(id);
    }
  },

  /**
   * 检查登录状态
   */
  checkLoginStatus: function() {
    const app = getApp();
    
    if (!app) {
      console.error('获取app实例失败');
      return;
    }
    
    // 获取全局登录状态
    const isLoggedIn = app.globalData.isLogin;
    
    this.setData({
      isLoggedIn
    });
    
    if (!isLoggedIn) {
      wx.showToast({
        title: '请先登录',
        icon: 'none',
        duration: 2000
      });
      
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  /**
   * 加载地址详情
   */
  loadAddressDetail: function(id) {
    this.setData({
      loading: true
    });
    
    // 添加时间戳和随机数防止缓存
    const timestamp = new Date().getTime();
    const random = Math.floor(Math.random() * 10000);
    
    // 调用获取地址详情接口
    wx.request({
      url: `${app.globalData.apiBase}/user/address/detail?_t=${timestamp}&_r=${random}`,
      method: 'GET',
      header: {
        'Authorization': `Bearer ${wx.getStorageSync(STORAGE_KEYS.TOKEN)}`,
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache'
      },
      data: {
        id: id
      },
      success: (res) => {
        console.log('获取地址详情结果:', res);
        if (res.data && res.data.status === 'success' && res.data.data) {
          const addressData = res.data.data;
          
          // 更新页面数据
          this.setData({
            address: addressData,
            region: [
              addressData.province || '',
              addressData.city || '',
              addressData.district || ''
            ],
            loading: false
          });
        } else {
          this.setData({
            loading: false
          });
          
          wx.showToast({
            title: '获取地址详情失败',
            icon: 'none'
          });
          
          setTimeout(() => {
            wx.navigateBack();
          }, 1500);
        }
      },
      fail: (err) => {
        console.error('获取地址详情失败:', err);
        this.setData({
          loading: false
        });
        
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
        
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      }
    });
  },

  /**
   * 输入框内容变化处理
   */
  onInputChange: function(e) {
    const { field } = e.currentTarget.dataset;
    const { value } = e.detail;
    
    this.setData({
      [`address.${field}`]: value
    });
  },

  /**
   * 地区选择器变化处理
   */
  onRegionChange: function(e) {
    const region = e.detail.value;
    
    this.setData({
      region,
      'address.province': region[0] || '',
      'address.city': region[1] || '',
      'address.district': region[2] || ''
    });
  },

  /**
   * 默认地址开关变化处理
   */
  onDefaultChange: function(e) {
    this.setData({
      'address.is_default': e.detail.value
    });
  },

  /**
   * 保存地址
   */
  onSaveAddress: function() {
    // 表单验证
    const { address } = this.data;
    
    if (!address.name) {
      wx.showToast({
        title: '请输入收货人姓名',
        icon: 'none'
      });
      return;
    }
    
    if (!address.phone) {
      wx.showToast({
        title: '请输入手机号码',
        icon: 'none'
      });
      return;
    }
    
    // 简单的手机号验证
    if (!/^1\d{10}$/.test(address.phone)) {
      wx.showToast({
        title: '手机号格式不正确',
        icon: 'none'
      });
      return;
    }
    
    if (!address.province || !address.city || !address.district) {
      wx.showToast({
        title: '请选择所在地区',
        icon: 'none'
      });
      return;
    }
    
    if (!address.address) {
      wx.showToast({
        title: '请输入详细地址',
        icon: 'none'
      });
      return;
    }
    
    // 显示加载提示
    this.setData({
      loading: true
    });
    
    // 构建请求数据
    const requestData = {
      name: address.name,
      phone: address.phone,
      province: address.province,
      city: address.city,
      district: address.district,
      address: address.address,
      is_default: address.is_default ? 1 : 0
    };
    
    // 如果是编辑模式，添加地址ID
    if (this.data.type === 'edit' && this.data.addressId) {
      requestData.id = this.data.addressId;
    }
    
    console.log('保存地址请求数据:', requestData);
    console.log('API基础URL:', app.globalData.apiBase);
    
    // 调用保存地址接口
    wx.request({
      url: `${app.globalData.apiBase}/user/address/${this.data.type === 'add' ? 'add' : 'update'}`,
      method: 'POST',
      header: {
        'Authorization': `Bearer ${wx.getStorageSync(STORAGE_KEYS.TOKEN)}`,
        'Content-Type': 'application/json'
      },
      data: requestData,
      success: (res) => {
        console.log('保存地址结果:', res);
        this.setData({
          loading: false
        });
        
        if (res.data && res.data.status === 'success') {
          wx.showToast({
            title: this.data.type === 'add' ? '添加成功' : '更新成功',
            icon: 'success'
          });
          
          // 返回上一页并刷新列表
          const pages = getCurrentPages();
          const prevPage = pages[pages.length - 2];
          
          // 如果上一页是地址列表页，通知其刷新
          if (prevPage && prevPage.loadAddressList) {
            prevPage.loadAddressList();
          }
          
          setTimeout(() => {
            wx.navigateBack();
          }, 1000);
        } else {
          wx.showToast({
            title: res.data?.message || '操作失败',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        console.error('保存地址失败:', err);
        this.setData({
          loading: false
        });
        
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 使用微信收货地址
   */
  onUseWechatAddress: function() {
    wx.chooseAddress({
      success: (res) => {
        // 更新表单数据
        this.setData({
          address: {
            name: res.userName,
            phone: res.telNumber,
            province: res.provinceName,
            city: res.cityName,
            district: res.countyName,
            address: res.detailInfo,
            is_default: this.data.address.is_default
          },
          region: [res.provinceName, res.cityName, res.countyName]
        });

        wx.showToast({
          title: '已导入微信地址',
          icon: 'success'
        });
      },
      fail: (err) => {
        // 用户取消操作不提示错误
        if (err.errMsg !== 'chooseAddress:fail cancel') {
          wx.showToast({
            title: '获取微信地址失败',
            icon: 'none'
          });
        }
      }
    });
  }
}); 