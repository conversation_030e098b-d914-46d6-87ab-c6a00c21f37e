# 微信支付配置指南

## 概述

系统已经集成了真实的微信支付功能，支持模拟支付和真实支付两种模式。默认情况下使用模拟支付，配置完成后可切换到真实支付。

## 配置步骤

### 1. 获取微信支付相关信息

#### 1.1 微信小程序信息
- 登录 [微信公众平台](https://mp.weixin.qq.com/)
- 进入小程序管理后台
- 在"开发" -> "开发管理" -> "开发设置"中获取：
  - AppID (小程序ID)
  - AppSecret (小程序密钥)

#### 1.2 微信支付商户信息
- 登录 [微信支付商户平台](https://pay.weixin.qq.com/)
- 在"账户中心" -> "商户信息"中获取：
  - 商户号 (mchid)

#### 1.3 API证书和密钥
- 在微信支付商户平台的"账户中心" -> "API安全"中：
  - 下载API证书（包含私钥文件）
  - 设置API v3密钥（32位字符串）
  - 获取证书序列号

### 2. 配置文件设置

#### 2.1 创建配置文件
复制 `src/config/wechat.example.js` 为 `src/config/wechat.js`：

```bash
cd src/config
cp wechat.example.js wechat.js
```

#### 2.2 编辑配置文件
编辑 `src/config/wechat.js`，填入真实的配置信息：

```javascript
module.exports = {
  miniProgram: {
    appId: 'wx1234567890abcdef',  // 你的小程序AppID
    appSecret: 'your_app_secret'   // 你的小程序AppSecret
  },

  payment: {
    mchId: '1234567890',           // 你的商户号
    appId: 'wx1234567890abcdef',   // 你的小程序AppID
    serialNo: 'ABC123DEF456',      // 你的证书序列号
    apiV3Key: 'your32characterapiv3key123456',  // 你的API v3密钥
    privateKeyPath: './src/config/wechat_private_key.pem',  // 私钥文件路径
    notifyUrl: 'https://your-domain.com/api/v1/client/payment/wechat-notify',  // 回调地址
    enabled: true  // 启用真实支付
  }
};
```

#### 2.3 放置私钥文件
将从微信支付商户平台下载的私钥文件重命名为 `wechat_private_key.pem`，放置在 `src/config/` 目录下。

### 3. 环境变量配置（可选）

也可以通过环境变量配置，在 `.env` 文件中添加：

```env
WECHAT_APPID=wx1234567890abcdef
WECHAT_MCHID=1234567890
WECHAT_SERIAL_NO=ABC123DEF456
WECHAT_APIV3_KEY=your32characterapiv3key123456
WECHAT_PRIVATE_KEY_PATH=./src/config/wechat_private_key.pem
WECHAT_NOTIFY_URL=https://your-domain.com/api/v1/client/payment/wechat-notify
WECHAT_PAY_ENABLED=true
```

### 4. 配置支付回调地址

#### 4.1 设置公网访问
支付回调地址必须是公网可访问的HTTPS地址。开发环境可以使用：
- ngrok
- 花生壳
- 或其他内网穿透工具

#### 4.2 在微信商户平台配置回调地址
- 登录微信支付商户平台
- 进入"产品中心" -> "开发配置"
- 设置支付回调URL为：`https://your-domain.com/api/v1/client/payment/wechat-notify`

### 5. 测试配置

#### 5.1 重启服务
配置完成后重启后端服务：

```bash
npm run dev
```

#### 5.2 检查日志
查看服务启动日志，确认微信支付初始化成功：

```
微信支付初始化成功
配置信息: {
  appid: 'wx1234567890abcdef',
  mchid: '1234567890',
  notify_url: 'https://your-domain.com/api/v1/client/payment/wechat-notify'
}
```

#### 5.3 测试支付
- 在小程序中创建订单
- 进入支付页面
- 如果配置正确，会调用真实的微信支付
- 如果配置不完整，会自动降级到模拟支付

## 支付模式说明

### 模拟支付模式
- 默认模式，无需真实配置
- 显示模拟支付对话框
- 不会产生真实扣费
- 适用于开发和测试

### 真实支付模式
- 需要完整的微信支付配置
- 调用真实的微信支付接口
- 会产生真实的资金流动
- 适用于生产环境

## 故障排除

### 常见问题

1. **微信支付初始化失败**
   - 检查配置文件是否正确
   - 确认私钥文件是否存在
   - 验证证书序列号是否正确

2. **支付时显示模拟支付**
   - 检查 `enabled` 配置是否为 `true`
   - 查看服务器日志确认初始化状态
   - 验证所有必要参数是否配置

3. **支付回调失败**
   - 确认回调地址是公网可访问的HTTPS地址
   - 检查微信商户平台的回调URL配置
   - 查看服务器日志确认是否收到回调

### 调试建议

1. 开发阶段建议使用模拟支付
2. 生产环境部署前先在测试环境验证真实支付
3. 定期检查微信支付证书有效期
4. 监控支付回调成功率

## 安全注意事项

1. **私钥文件安全**
   - 不要将私钥文件提交到版本控制系统
   - 确保私钥文件权限设置正确

2. **API密钥安全**
   - 定期更换API v3密钥
   - 不要在代码中硬编码密钥

3. **回调验证**
   - 系统会自动验证微信支付回调签名
   - 不要跳过签名验证步骤

## 技术支持

如果遇到配置问题，请：
1. 查看服务器日志
2. 确认微信支付商户平台配置
3. 验证网络连接和HTTPS证书
4. 联系技术支持团队
