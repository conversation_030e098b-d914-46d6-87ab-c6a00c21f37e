// pages/user/profile/profile.js
const userService = require('../../../services/user')
const { STORAGE_KEYS, PAGES } = require('../../../utils/constants')
const config = require('../../../config/config') // Added missing import
const util = require('../../../utils/util') // Added util import for formatImageUrl

Page({
  data: {
    // 用户信息
    userInfo: {},
    isLoggedIn: false,
    isProfileCompleted: false,
    isDemote: false, // 是否为降级用户（拒绝授权）
    
    // 订单统计
    orderStats: {
      pending: 0,     // 待付款
      shipped: 0,     // 待发货
      delivering: 0,  // 待收货
      completed: 0    // 已完成
    },
    
    // 消息数量
    messageCount: 0,
    
    // 加载状态
    loading: false,

    // 临时用户信息（用于编辑）
    tempUserInfo: {
      avatar: '',
      nickname: ''
    },

    // 头像编辑弹窗显示状态
    showAvatarEdit: false,
    
    // 广告配置
    adUnitId: 'adunit-xxxxxxxxxxxxxxxx', // 替换为实际的广告单元ID

    // 新版头像昵称相关
    tempAvatar: '', // 临时头像
    tempNickname: '', // 临时昵称

    // 防抖相关
    isChoosingAvatar: false // 防止重复选择头像
  },

  onLoad: function (options) {
    console.log('我的页面加载')
    this.checkLoginStatus()
  },

  onShow: function () {
    // 每次显示页面时刷新数据
    this.checkLoginStatus()
    if (this.data.isLoggedIn) {
      this.loadUserData()
    }
    
    // 微信小程序使用的是原生TabBar，不需要设置active状态
    // 如果将来使用自定义TabBar，可以取消下面的注释
    /*
    try {
      if (typeof this.getTabBar === 'function') {
        const tabBar = this.getTabBar();
        if (tabBar && typeof tabBar.setData === 'function') {
          tabBar.setData({
            active: 3 // "我的"是第4个tab，索引为3（首页0，商城1，购物车2，我的3）
          });
        }
      }
    } catch (error) {
      console.error('设置TabBar失败:', error);
    }
    */
  },

  /**
   * 检查登录状态
   */
  checkLoginStatus() {
    const app = getApp();
    
    if (!app) {
      console.error('获取app实例失败');
      return;
    }
    
    // 获取全局登录状态
    const isLoggedIn = app.globalData.isLogin;
    const userInfo = app.globalData.userInfo;
    const isProfileCompleted = app.globalData.isProfileCompleted;
    const isDemote = app.globalData.isDemote;
    
    console.log('检查登录状态:', 
      '已登录:', isLoggedIn, 
      '资料完整:', isProfileCompleted, 
      '降级用户:', isDemote, 
      '用户信息:', userInfo);
    
    // 设置页面状态
      this.setData({
      isLoggedIn,
      isProfileCompleted,
      isDemote,
      userInfo: userInfo || {
        nickname: '微信用户',
        avatar: '/assets/images/default-avatar.png'
      }
    });
    
    // 如果已登录，设置临时用户信息（用于编辑）
    if (isLoggedIn && userInfo) {
      this.setData({
        tempUserInfo: {
          avatar: userInfo.avatar || '',
          nickname: userInfo.nickname || ''
        }
      });
      
      // 如果已登录且资料完整，加载用户数据
      if (isProfileCompleted) {
        this.loadUserData();
      }
    }
    
    // 注册登录状态变化回调
    app.loginStateCallback = (isLogin) => {
      console.log('登录状态变化:', isLogin);
      if (isLogin) {
        this.checkLoginStatus();
      }
    };
  },

  // 加载用户数据
  async loadUserData() {
    try {
      this.setData({ loading: true });
      console.log('开始加载用户数据...');
      
      // 并行加载用户信息、订单统计、消息数量
      const [userInfoRes, orderStatsRes, messageRes] = await Promise.allSettled([
        this.getUserInfo(),
        this.getOrderStats(),
        this.getMessageCount()
      ]);
      
      console.log('所有数据加载完成，开始处理结果');
      
      // 处理用户信息
      if (userInfoRes.status === 'fulfilled' && userInfoRes.value.success) {
        // 确保用户数据有效
        const userData = userInfoRes.value.data;
        console.log('获取到的用户信息:', JSON.stringify(userData));
        
        // 检查是否为降级用户（拒绝授权）
        const isDemote = userData.is_demote === true;
        this.setData({ isDemote: isDemote });
        
        // 确保头像URL是完整的 - 使用统一的formatImageUrl函数
        if (userData && userData.avatar) {
          userData.avatar = util.formatImageUrl(userData.avatar);
          console.log('处理后的头像URL:', userData.avatar);

          // 检查头像是否有效
          this.validateAndSetAvatar(userData);
        } else {
          // 设置默认头像
          userData.avatar = this.getDefaultAvatar();
          console.log('使用默认头像');
        }
        
        // 设置临时用户信息，用于编辑
        const tempUserInfo = {
          avatar: userData.avatar || '',
          nickname: userData.nickname || ''
        };
        
        this.setData({
          userInfo: userData,
          tempUserInfo: tempUserInfo
        });
        
        // 更新本地存储
        wx.setStorageSync(STORAGE_KEYS.USER_INFO, userData);
        
        // 更新全局状态
        const app = getApp();
        if (app && app.globalData) {
          app.globalData.userInfo = userData;
          app.globalData.isDemote = isDemote;
        }
      } else if (userInfoRes.status === 'rejected') {
        console.error('获取用户信息失败:', userInfoRes.reason);
      }
      
      // 处理订单统计
      if (orderStatsRes.status === 'fulfilled' && orderStatsRes.value.success) {
        this.setData({
          orderStats: orderStatsRes.value.data
        });
      }
      
      // 处理消息数量
      if (messageRes.status === 'fulfilled' && messageRes.value.success) {
        this.setData({
          messageCount: messageRes.value.data.unread_count || 0
        });
      }
      
    } catch (error) {
      console.error('加载用户数据失败:', error);
      wx.showToast({
        title: '加载数据失败',
        icon: 'none'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  // 获取用户信息
  async getUserInfo() {
    try {
      // 检查token是否存在
      const token = wx.getStorageSync('token');
      if (!token) {
        console.warn('获取用户信息失败: 未找到token，尝试重新登录');
        // 如果没有token，尝试重新登录
        const app = getApp();
        if (app && app.checkLoginStatus) {
          await app.checkLoginStatus();
        }
        throw new Error('未找到登录凭证');
      }

      const result = await userService.getUserInfo();
      console.log('获取用户信息成功:', result);
      return result;
    } catch (error) {
      console.error('获取用户信息失败:', error);
      throw error;
    }
  },

  // 获取订单统计
  getOrderStats() {
    return new Promise((resolve) => {
      // 暂时返回模拟数据
      setTimeout(() => {
        resolve({
          success: true,
          data: {
            pending: 2,     // 待付款
            shipped: 1,     // 待发货  
            delivering: 3,  // 待收货
            completed: 15   // 已完成
          }
        })
      }, 500)
    })
  },

  // 获取消息数量
  getMessageCount() {
    return new Promise((resolve) => {
      // 暂时返回模拟数据
      setTimeout(() => {
        resolve({
          success: true,
          data: {
            unread_count: 5
          }
        })
      }, 300)
    })
  },

  // 登录
  onLogin() {
    console.log('点击登录按钮，开始登录');

    // 显示选择提示
    wx.showModal({
      title: '获取用户信息',
      content: '为了提供更好的服务，需要获取您的头像和昵称信息',
      confirmText: '授权获取',
      cancelText: '稍后再说',
      success: (res) => {
        if (res.confirm) {
          // 用户同意授权，获取用户信息
          this.getUserProfileAndLogin();
        } else {
          // 用户拒绝授权，进行降级登录
          this.performDemoteLogin();
        }
      }
    });
  },

  // 获取用户信息并登录
  getUserProfileAndLogin() {
    console.log('开始获取用户信息...');

    wx.showLoading({
      title: '获取用户信息中...',
      mask: true
    });

    // 使用 getUserProfile 获取用户信息
    wx.getUserProfile({
      desc: '完善会员资料',  // 使用7个字符，确保符合5-30字符要求
      success: (res) => {
        console.log('获取用户信息成功:', res.userInfo);

        // 执行完整登录
        this.performFullLogin(res.userInfo);
      },
      fail: (error) => {
        console.error('获取用户信息失败:', error);
        wx.hideLoading();

        // 提供更详细的错误信息
        let errorMsg = '获取用户信息失败';
        if (error.errMsg && error.errMsg.includes('desc length')) {
          errorMsg = 'desc参数长度不符合要求，请联系开发者';
        } else if (error.errMsg && error.errMsg.includes('cancel')) {
          errorMsg = '您拒绝了授权';
        }

        // 如果用户拒绝授权，进行降级登录
        wx.showModal({
          title: '提示',
          content: `${errorMsg}，将使用基础功能。您可以稍后在个人资料中完善信息。`,
          showCancel: false,
          confirmText: '我知道了',
          success: () => {
            this.performDemoteLogin();
          }
        });
      }
    });
  },

  // 执行完整登录（包含用户信息）
  async performFullLogin(userInfo) {
    try {
      console.log('执行完整登录，用户信息:', userInfo);

      const app = getApp();
      const res = await app.doLoginWithUserInfo(userInfo);

      if (res && res.status === 'success') {
        // 登录成功
        const userData = res.data.user || {
          nickname: userInfo.nickName || '微信用户',
          avatar: userInfo.avatarUrl || '/assets/images/default-avatar.png'
        };

        console.log('完整登录成功，用户数据:', userData);

        // 设置页面状态
        this.setData({
          isLoggedIn: true,
          isProfileCompleted: true,
          isDemote: false,
          userInfo: userData,
          tempUserInfo: {
            avatar: userData.avatar || '',
            nickname: userData.nickname || ''
          }
        });

        // 更新全局状态
        app.globalData.isLogin = true;
        app.globalData.isLoggedIn = true;
        app.globalData.userInfo = userData;
        app.globalData.isProfileCompleted = true;
        app.globalData.isDemote = false;

        // 加载用户数据
        this.loadUserData();

        wx.showToast({
          title: '登录成功',
          icon: 'success'
        });
      } else {
        console.error('完整登录失败:', res);
        wx.showToast({
          title: '登录失败，请重试',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('完整登录异常:', error);
      wx.showToast({
        title: '登录失败，请重试',
        icon: 'none'
      });
    } finally {
      wx.hideLoading();
    }
  },

  // 执行降级登录（不获取用户信息）
  async performDemoteLogin() {
    try {
      console.log('执行降级登录...');

      wx.showLoading({
        title: '登录中...',
        mask: true
      });

      const app = getApp();
      const res = await app.silentLogin(true); // 传入 true 表示降级登录

      if (res && res.status === 'success') {
        // 降级登录成功
        const userData = res.data.user || {
          nickname: '微信用户',
          avatar: '/assets/images/default-avatar.png'
        };

        console.log('降级登录成功，用户数据:', userData);

        // 设置页面状态
        this.setData({
          isLoggedIn: true,
          isProfileCompleted: false,
          isDemote: true,
          userInfo: userData,
          tempUserInfo: {
            avatar: userData.avatar || '',
            nickname: userData.nickname || ''
          }
        });

        // 更新全局状态
        app.globalData.isLogin = true;
        app.globalData.isLoggedIn = true;
        app.globalData.userInfo = userData;
        app.globalData.isProfileCompleted = false;
        app.globalData.isDemote = true;

        console.log('降级登录成功，使用默认用户信息');

        wx.showToast({
          title: '登录成功',
          icon: 'success'
        });
      } else {
        console.error('降级登录失败:', res);
        wx.showToast({
          title: '登录失败，请重试',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('降级登录异常:', error);
      wx.showToast({
        title: '登录失败，请重试',
        icon: 'none'
      });
    } finally {
      wx.hideLoading();
    }
  },

  // 获取默认头像
  getDefaultAvatar() {
    // 使用本地默认头像或者一个稳定的网络头像
    return '/assets/images/default-avatar.png';
  },

  // 验证并设置头像
  validateAndSetAvatar(userData) {
    // 使用用户服务中的头像处理逻辑
    userData.avatar = userService.processAvatarUrl(userData.avatar);

    this.setData({ userInfo: userData });
    wx.setStorageSync(STORAGE_KEYS.USER_INFO, userData);
  },

  // 头像加载错误处理
  onAvatarError(e) {
    console.log('头像加载失败:', e.detail);
    // 使用默认头像
    const userInfo = this.data.userInfo;
    userInfo.avatar = '/assets/images/default-avatar.png';
    this.setData({ userInfo });
    wx.setStorageSync(STORAGE_KEYS.USER_INFO, userInfo);
  },

  // 选择头像
  onChooseAvatar(e) {
    console.log('onChooseAvatar被调用:', e);

    try {
      const { avatarUrl } = e.detail || {};
      console.log('用户选择了新头像:', avatarUrl);

      if (!avatarUrl) {
        console.log('头像URL为空，选择失败');
        wx.showToast({
          title: '头像选择失败',
          icon: 'none'
        });
        return;
      }

      // 更新临时数据
      this.setData({
        'tempUserInfo.avatar': avatarUrl
      });

      wx.showToast({
        title: '头像已选择',
        icon: 'success'
      });

      console.log('头像已更新到临时数据:', avatarUrl);
    } catch (error) {
      console.error('选择头像时发生错误:', error);
      wx.showToast({
        title: '头像选择出错',
        icon: 'none'
      });
    }
  },

  // 获取真实用户信息
  getRealUserInfo() {
    console.log('用户点击获取真实信息按钮');

    wx.showModal({
      title: '获取真实用户信息',
      content: '是否获取您的微信头像和昵称？这将替换当前的默认信息。',
      confirmText: '获取',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          this.getUserProfileAndUpdate();
        }
      }
    });
  },

  // 获取用户信息并更新
  getUserProfileAndUpdate() {
    console.log('开始获取真实用户信息...');

    wx.showLoading({
      title: '获取用户信息中...',
      mask: true
    });

    // 使用 getUserProfile 获取用户信息
    wx.getUserProfile({
      desc: '完善会员资料',  // 使用7个字符，确保符合5-30字符要求
      success: (res) => {
        console.log('获取真实用户信息成功:', res.userInfo);

        const userInfo = res.userInfo;

        // 更新临时数据
        this.setData({
          'tempUserInfo.avatar': userInfo.avatarUrl,
          'tempUserInfo.nickname': userInfo.nickName
        });

        wx.hideLoading();

        wx.showModal({
          title: '信息获取成功',
          content: `获取到您的昵称：${userInfo.nickName}，是否保存这些信息？`,
          confirmText: '保存',
          cancelText: '取消',
          success: (modalRes) => {
            if (modalRes.confirm) {
              // 用户确认保存，执行保存操作
              this.saveUserInfo();
            }
          }
        });
      },
      fail: (error) => {
        console.error('获取真实用户信息失败:', error);
        wx.hideLoading();

        // 提供更详细的错误信息
        let errorMsg = '获取用户信息失败';
        if (error.errMsg && error.errMsg.includes('desc length')) {
          errorMsg = 'desc参数长度不符合要求';
        } else if (error.errMsg && error.errMsg.includes('cancel')) {
          errorMsg = '您拒绝了授权';
        }

        wx.showModal({
          title: '获取失败',
          content: `${errorMsg}，您可以稍后重试。如果问题持续存在，请联系客服。`,
          showCancel: false,
          confirmText: '我知道了'
        });
      }
    });
  },



  // 昵称输入处理
  onNicknameInput(e) {
    console.log('用户输入昵称:', e.detail.value);

    this.setData({
      tempNickname: e.detail.value
    });
  },

  // 保存新的用户信息
  saveNewUserInfo() {
    console.log('保存新用户信息被调用');

    const { tempAvatar, tempNickname } = this.data;

    if (!tempAvatar && !tempNickname) {
      wx.showToast({
        title: '请先选择头像或输入昵称',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    wx.showLoading({
      title: '保存中...',
      mask: true
    });

    // 准备更新数据
    const updateData = {};
    if (tempAvatar) {
      updateData.avatar = tempAvatar;
    }
    if (tempNickname) {
      updateData.nickname = tempNickname;
    }

    console.log('准备更新的数据:', updateData);

    // 调用服务更新用户信息
    userService.updateUserInfo(updateData).then(res => {
      console.log('用户信息更新成功:', res);
      wx.hideLoading();

      // 更新页面显示的用户信息
      const updatedUserInfo = { ...this.data.userInfo };
      if (tempAvatar) {
        updatedUserInfo.avatarUrl = tempAvatar;
        // 同时更新tempUserInfo
        this.setData({
          'tempUserInfo.avatar': tempAvatar
        });
      }
      if (tempNickname) {
        updatedUserInfo.nickName = tempNickname;
        // 同时更新tempUserInfo
        this.setData({
          'tempUserInfo.nickname': tempNickname
        });
      }

      // 更新用户信息和清空临时数据
      this.setData({
        userInfo: updatedUserInfo,
        tempAvatar: '',
        tempNickname: '',
        isDemote: false
      });

      // 更新本地存储
      wx.setStorageSync('userInfo', updatedUserInfo);

      wx.showToast({
        title: '保存成功',
        icon: 'success',
        duration: 2000
      });

      // 重新加载用户数据
      this.loadUserData();

    }).catch(error => {
      console.error('更新用户信息失败:', error);
      wx.hideLoading();
      wx.showToast({
        title: '保存失败，请重试',
        icon: 'none',
        duration: 2000
      });
    });
  },



  // 输入昵称
  onInputNickname(e) {
    const nickname = e.detail.value;
    console.log('用户输入了新昵称:', nickname);
    
    // 更新临时数据
    this.setData({
      'tempUserInfo.nickname': nickname
    });
  },

  // 保存用户信息
  async onSaveUserInfo() {
    const { tempUserInfo } = this.data;
    
    // 验证数据
    if (!tempUserInfo.avatar) {
      wx.showToast({
        title: '请选择头像',
        icon: 'none'
      });
      return;
    }
    
    if (!tempUserInfo.nickname || tempUserInfo.nickname === '微信用户') {
      wx.showToast({
        title: '请输入昵称',
        icon: 'none'
      });
      return;
    }
    
    // 显示加载提示
    wx.showLoading({
      title: '保存中...',
      mask: true
    });
    
    try {
      // 上传头像（如果是本地临时文件）
      let avatarUrl = tempUserInfo.avatar;
      console.log('准备处理头像URL:', avatarUrl);

      // 检查是否是需要上传的临时文件
      // 微信头像选择返回的URL通常包含这些特征
      const isTemporaryFile = avatarUrl && (
        avatarUrl.startsWith('wxfile://') ||
        avatarUrl.startsWith('http://tmp') ||
        avatarUrl.includes('temp') ||
        avatarUrl.includes('tmp') ||
        avatarUrl.startsWith('blob:') ||
        avatarUrl.includes('WeappFileSystem') ||
        avatarUrl.includes('wxd57d522936cb95a1') ||
        (avatarUrl.includes('.jpeg') && !avatarUrl.startsWith('http://localhost')) ||
        (avatarUrl.includes('.jpg') && !avatarUrl.startsWith('http://localhost')) ||
        (avatarUrl.includes('.png') && !avatarUrl.startsWith('http://localhost') && !avatarUrl.startsWith('/assets/'))
      );

      console.log('临时文件检测结果:', isTemporaryFile, '头像URL:', avatarUrl);

      if (isTemporaryFile) {
        try {
          console.log('检测到临时文件，开始上传头像...', avatarUrl);
          // 上传头像到服务器
          const uploadedUrl = await this.uploadAvatar(avatarUrl);
          if (uploadedUrl) {
            avatarUrl = uploadedUrl;
            console.log('头像上传成功，新URL:', avatarUrl);
          } else {
            console.error('头像上传失败，使用默认头像');
            avatarUrl = '/assets/images/default-avatar.png';
          }
        } catch (uploadError) {
          console.error('头像上传异常:', uploadError);
          avatarUrl = '/assets/images/default-avatar.png';
        }
      } else if (!avatarUrl || avatarUrl === '') {
        // 如果没有头像，使用默认头像
        avatarUrl = '/assets/images/default-avatar.png';
        console.log('使用默认头像');
      } else {
        // 检查是否是服务器URL或默认头像
        const isServerUrl = avatarUrl.startsWith('http://localhost:4000');
        const isDefaultAvatar = avatarUrl === '/assets/images/default-avatar.png' || avatarUrl.includes('/assets/');

        if (!isServerUrl && !isDefaultAvatar) {
          // 强制上传策略：如果不是服务器URL且不是默认头像，就尝试上传
          console.log('强制上传策略：尝试上传头像', avatarUrl);
          try {
            const uploadedUrl = await this.uploadAvatar(avatarUrl);
            if (uploadedUrl) {
              avatarUrl = uploadedUrl;
              console.log('强制上传成功，新URL:', avatarUrl);
            } else {
              console.log('强制上传失败，保持原URL');
            }
          } catch (error) {
            console.error('强制上传异常:', error);
          }
        } else {
          console.log('使用现有头像URL:', avatarUrl);
        }
      }
      
      // 更新用户信息
      const updateData = {
        avatar: avatarUrl,
        nickname: tempUserInfo.nickname
      };

      console.log('准备更新用户信息:', JSON.stringify(updateData));
      console.log('最终头像URL:', avatarUrl);

      // 调用更新用户信息接口
      const updateRes = await userService.updateUserInfo(updateData);
      console.log('服务器更新响应:', JSON.stringify(updateRes));
      
      // 隐藏加载提示
      wx.hideLoading();
      
      if (updateRes && updateRes.success) {
        // 使用服务器返回的用户信息，确保数据一致性
        const serverUserInfo = updateRes.data || {};
        console.log('服务器返回的用户信息:', JSON.stringify(serverUserInfo));

        // 如果服务器返回的头像还是默认头像，但我们上传了新头像，使用我们的头像URL
        let finalUserInfo = { ...this.data.userInfo, ...updateData };
        if (serverUserInfo.avatar && serverUserInfo.avatar !== '/assets/images/default-avatar.png') {
          finalUserInfo.avatar = serverUserInfo.avatar;
        } else if (avatarUrl && avatarUrl !== '/assets/images/default-avatar.png') {
          // 强制使用我们上传的头像URL
          finalUserInfo.avatar = avatarUrl;
          console.log('强制使用上传的头像URL:', avatarUrl);
        }

        // 更新其他字段
        if (serverUserInfo.nickname) {
          finalUserInfo.nickname = serverUserInfo.nickname;
        }

        console.log('最终用户信息:', JSON.stringify(finalUserInfo));

        this.setData({
          userInfo: finalUserInfo,
          isProfileCompleted: true,
          isDemote: false,
          showAvatarEdit: false // 关闭编辑弹窗
        });

        // 更新本地存储
        wx.setStorageSync(STORAGE_KEYS.USER_INFO, finalUserInfo);

        // 更新全局状态
        const app = getApp();
        if (app && app.globalData) {
          app.globalData.userInfo = finalUserInfo;
          app.globalData.isProfileCompleted = true;
          app.globalData.isDemote = false;
        }

        wx.showToast({
          title: '保存成功',
          icon: 'success'
        });

        // 重新加载用户数据以确保同步
        setTimeout(() => {
          this.loadUserData();
        }, 1000);
      } else {
        console.error('更新用户信息失败:', updateRes);
        wx.showToast({
          title: '保存失败，请重试',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('保存用户信息失败:', error);
      
      // 隐藏加载提示
      wx.hideLoading();
      
      wx.showToast({
        title: '保存失败，请重试',
        icon: 'none'
      });
    }
  },

  // 上传头像
  uploadAvatar(filePath) {
    return new Promise((resolve, reject) => {
      // 显示上传中提示
      wx.showLoading({
        title: '上传中...',
        mask: true
      });
      
      wx.uploadFile({
        // 修改为正确的上传路径
        url: `${config.api.baseUrl}/api/v1/client/upload`,
        filePath,
        name: 'file',
        header: {
          'Authorization': `Bearer ${wx.getStorageSync(STORAGE_KEYS.TOKEN)}`
        },
      success: (res) => {
          // 隐藏上传中提示
          wx.hideLoading();
          
          try {
            // 尝试解析返回结果
            const data = JSON.parse(res.data);
            
            // 检查返回结果是否正确（兼容code和status两种格式）
            if (data && (data.status === 'success' || data.code === 200) && data.data && data.data.url) {
              resolve({
                success: true,
                data: data.data
              });
            } else {
              console.error('上传头像失败，服务器返回:', data);
              reject(new Error('上传头像失败，服务器返回错误'));
            }
          } catch (e) {
            console.error('解析上传结果失败:', e, '原始数据:', res.data);
            reject(new Error('解析上传结果失败'));
          }
        },
        fail: (err) => {
          // 隐藏上传中提示
          wx.hideLoading();
          console.error('上传头像请求失败:', err);
          reject(err);
        }
      });
    });
  },

  // 执行降级登录（用户拒绝授权时调用）
  async performDemoteLogin() {
    let loadingShown = false;
    
    try {
      // 显示加载提示
      wx.showLoading({ title: '登录中...', mask: true });
      loadingShown = true;
      
      console.log('开始执行降级登录（无用户信息）');
      
      // 获取app实例
      const app = getApp();
      if (!app) {
        throw new Error('获取app实例失败');
      }
      
      // 调用app中的降级登录方法
      const res = await app.silentLogin(true); // 传入true表示这是一个降级登录
      
      // 确保隐藏loading
      if (loadingShown) {
        wx.hideLoading();
        loadingShown = false;
      }
      
      if (res && res.status === 'success') {
        // 更新页面状态
        this.setData({
          isLoggedIn: true,
          isProfileCompleted: false,
          isDemote: true,
          userInfo: res.data.user || {
            nickname: '微信用户',
            avatar: '/assets/images/default-avatar.png'
          }
        });
        
        console.log('降级登录成功，使用默认用户信息');
        
        wx.showToast({
          title: '登录成功',
          icon: 'success'
        });
        
        // 加载用户数据
        this.loadUserData();
      } else {
        wx.showToast({
          title: '登录失败，请重试',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('降级登录失败:', error);
      
      // 确保隐藏loading
      if (loadingShown) {
        wx.hideLoading();
        loadingShown = false;
      }
      
      wx.showToast({
        title: '登录失败，请重试',
        icon: 'none',
        duration: 2000
      });
    }
  },

  // 执行登录
  async performLogin(userInfo) {
    let loadingShown = false;
    
    try {
      // 显示加载提示
      wx.showLoading({ title: '登录中...', mask: true });
      loadingShown = true;
      
      console.log('开始执行登录，用户信息:', JSON.stringify(userInfo));
      
      // 获取app实例
      const app = getApp();
      if (!app) {
        throw new Error('获取app实例失败');
      }
      
      // 调用app中的完整登录方法
      const res = await app.doLoginWithUserInfo(userInfo);
      
      // 确保隐藏loading
      if (loadingShown) {
        wx.hideLoading();
        loadingShown = false;
      }
      
      if (res && res.status === 'success') {
        // 更新页面状态
        this.setData({
          isLoggedIn: true,
          isProfileCompleted: true,
          isDemote: false,
          userInfo: res.data.user
        });
        
        console.log('登录成功，获取到的用户信息:', JSON.stringify(res.data.user));
        
        wx.showToast({
          title: '登录成功',
          icon: 'success'
        });
        
        // 加载用户数据
        this.loadUserData();
      } else {
        wx.showToast({
          title: '登录失败，请重试',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('登录失败:', error);
      
      // 确保隐藏loading
      if (loadingShown) {
        wx.hideLoading();
        loadingShown = false;
      }
      
      wx.showToast({
        title: '登录失败，请重试',
        icon: 'none',
        duration: 2000
      });
    }
  },

  // 退出登录
  onLogout() {
    wx.showModal({
      title: '确认退出',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          // 清除本地存储
          wx.removeStorageSync(STORAGE_KEYS.TOKEN)
          wx.removeStorageSync(STORAGE_KEYS.USER_INFO)
          
          // 更新全局状态
          const app = getApp();
          if (app && app.globalData) {
            app.globalData.token = null
            app.globalData.userInfo = null
            app.globalData.isLogin = false
            app.globalData.isProfileCompleted = false
            app.globalData.isDemote = false
          }
          
          this.setData({
            isLoggedIn: false,
            isProfileCompleted: false,
            isDemote: false,
            userInfo: {},
            tempUserInfo: {
              avatar: '',
              nickname: ''
            },
            orderStats: {
              pending: 0,
              shipped: 0,
              delivering: 0,
              completed: 0
            },
            messageCount: 0
          })
          
          wx.showToast({
            title: '已退出登录',
            icon: 'success'
          })
        }
      }
    })
  },

  // 点击广告
  onAdClick() {
    if (!this.data.isLoggedIn) {
      this.showLoginTip()
      return
    }
    wx.navigateTo({ url: PAGES.ADS })
  },

  // 查看全部订单
  onViewAllOrders() {
    if (!this.data.isLoggedIn) {
      this.showLoginTip()
      return
    }
    wx.navigateTo({ url: '/pages/mall/order/list/list' })
  },

  // 按状态查看订单
  onViewOrdersByStatus(e) {
    if (!this.data.isLoggedIn) {
      this.showLoginTip()
      return
    }
    const status = e.currentTarget.dataset.status
    wx.navigateTo({ url: `/pages/mall/order/list/list?status=${status}` })
  },

  // 跳转到钱包页面
  onNavigateToWallet() {
    if (!this.data.isLoggedIn) {
      this.showLoginTip()
      return
    }
    wx.navigateTo({ url: '/pages/user/wallet/wallet' })
  },

  // 跳转到团队页面
  onNavigateToTeam() {
    if (!this.data.isLoggedIn) {
      this.showLoginTip()
      return
    }
    wx.navigateTo({ url: '/pages/user/team/team' })
  },

  // 联系客服
  onContactService() {
    // 使用微信客服功能，不需要登录验证
    // 使用微信开放能力，打开客服会话
    wx.showToast({
      title: '正在连接客服...',
      icon: 'loading',
      duration: 1000,
      success: () => {
        setTimeout(() => {
          // 提示用户通过按钮联系客服
    wx.showModal({
      title: '联系客服',
            content: '请点击"确定"按钮，然后通过客服按钮联系我们',
            confirmText: '确定',
      showCancel: false,
            success: (res) => {
              if (res.confirm) {
                console.log('用户确认联系客服');
              }
            }
          });
        }, 1000);
      }
    });
  },

  /**
   * 导航到帮助中心
   */
  onNavigateToHelp: function() {
    if (!this.data.isLoggedIn) {
      this.showLoginTip();
      return;
    }
    
    // TODO: 实现帮助中心页面
    wx.showToast({
      title: '帮助中心开发中',
      icon: 'none'
    });
  },

  /**
   * 导航到收货地址管理
   */
  onNavigateToAddress: function() {
    if (!this.data.isLoggedIn) {
      this.showLoginTip();
      return;
    }
    
    wx.navigateTo({
      url: '/pages/user/address/list'
    });
  },

  /**
   * 导航到消息中心
   */
  onNavigateToMessages: function() {
    if (!this.data.isLoggedIn) {
      this.showLoginTip();
      return;
    }
    
    // TODO: 实现消息中心页面
    wx.showToast({
      title: '消息中心开发中',
      icon: 'none'
    });
  },

  // 邀请好友
  onInviteFriends() {
    if (!this.data.isLoggedIn) {
      this.showLoginTip()
      return
    }
    
    // 直接触发分享操作
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    })
    
    // 显示分享提示
    wx.showModal({
      title: '邀请好友',
      content: '点击右上角"..."按钮，选择"转发"即可邀请好友',
      confirmText: '我知道了',
      showCancel: false
    })
  },

  // 分享
  onShareAppMessage() {
    return {
      title: 'WiFi共享商业系统 - 邀请您加入',
      path: '/pages/index/index?inviter=' + (this.data.userInfo.id || ''),
      imageUrl: '/assets/images/share-home.jpg'
    }
  },

  // 广告加载成功
  onAdLoad() {
    console.log('广告加载成功');
  },

  // 广告加载失败
  onAdError(e) {
    console.error('广告加载失败', e.detail);
  },

  // 广告被点击
  onAdClick() {
    console.log('广告被点击');
  },

  // 显示登录提示
  showLoginTip() {
    wx.showModal({
      title: '提示',
      content: '请先登录',
      confirmText: '去登录',
      success: (res) => {
        if (res.confirm) {
          this.onLogin()
        }
      }
    })
  },

  // 下拉刷新
  onPullDownRefresh() {
    if (this.data.isLoggedIn) {
      this.loadUserData().then(() => {
        wx.stopPullDownRefresh()
      })
    } else {
      wx.stopPullDownRefresh()
    }
  },

  // 头像加载错误处理
  onAvatarError(e) {
    console.error('头像加载失败，使用默认头像', e);
    const userInfo = this.data.userInfo;
    const oldAvatar = userInfo.avatar;
    
    // 使用本地默认头像
    userInfo.avatar = '/assets/images/default-avatar.png';
    
    this.setData({
      userInfo: userInfo
    });
    
    // 更新本地存储
    wx.setStorageSync(STORAGE_KEYS.USER_INFO, userInfo);
    
    // 更新全局状态
    const app = getApp();
    if (app && app.globalData) {
      app.globalData.userInfo = userInfo;
    }
    
    // 记录错误信息
    console.warn(`头像加载失败，原头像URL: ${oldAvatar}，已替换为默认头像`);
  },

  // 强制获取微信头像和用户信息（兼容新版微信小程序）
  forceGetWechatInfo() {
    console.log('强制获取微信头像被调用');

    // 检查微信版本，决定使用哪种方式获取头像
    const systemInfo = wx.getSystemInfoSync();
    console.log('系统信息:', systemInfo);

    wx.showModal({
      title: '完善个人资料',
      content: '请选择头像和昵称来完善您的个人资料。注意：由于微信政策调整，可能无法获取真实头像。',
      confirmText: '开始设置',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          // 先尝试使用新版头像昵称填写能力
          this.tryNewAvatarMethod();
        }
      }
    });
  },

  // 尝试使用新版头像昵称填写能力
  tryNewAvatarMethod() {
    console.log('尝试使用新版头像昵称填写能力');

    wx.showModal({
      title: '设置头像昵称',
      content: '请在下方选择头像并输入昵称，然后点击保存按钮。',
      confirmText: '我知道了',
      showCancel: false,
      success: () => {
        // 显示头像昵称编辑区域
        this.setData({
          showAvatarEdit: true
        });
      }
    });
  },

  // 头像选择处理（微信官方API）
  onChooseAvatar(e) {
    console.log('选择头像事件:', e);

    // 防抖检查
    if (this.data.isChoosingAvatar) {
      console.log('头像选择正在进行中，忽略重复调用');
      return;
    }

    // 设置选择状态
    this.setData({ isChoosingAvatar: true });

    // 2秒后重置状态
    setTimeout(() => {
      this.setData({ isChoosingAvatar: false });
    }, 2000);

    // 检查是否有错误
    if (e.detail.errMsg && !e.detail.errMsg.includes('ok')) {
      console.error('头像选择失败:', e.detail.errMsg);

      // 重置选择状态
      this.setData({ isChoosingAvatar: false });

      // 处理不同类型的错误
      if (e.detail.errMsg.includes('another chooseAvatar is in progress')) {
        wx.showToast({
          title: '操作太频繁，请稍后再试',
          icon: 'none',
          duration: 2000
        });
        return;
      } else if (e.detail.errMsg.includes('cancel')) {
        console.log('用户取消选择头像');
        return;
      } else if (e.detail.errMsg.includes('ENOENT') || e.detail.errMsg.includes('no such file')) {
        console.log('开发者工具文件访问问题，自动切换到相册选择');
        wx.showToast({
          title: '自动切换到相册选择',
          icon: 'none',
          duration: 1500
        });
        // 延迟调用相册选择，避免冲突
        setTimeout(() => {
          this.chooseFromAlbum();
        }, 500);
        return;
      } else {
        // 其他未知错误，提供备用方案
        wx.showModal({
          title: '头像选择失败',
          content: '微信头像选择遇到问题，是否使用相册选择？',
          confirmText: '选择相册',
          cancelText: '取消',
          success: (res) => {
            if (res.confirm) {
              this.chooseFromAlbum();
            }
          }
        });
        return;
      }
    }

    const avatarUrl = e.detail.avatarUrl;
    console.log('获取到的头像URL:', avatarUrl);

    // 重置选择状态
    this.setData({ isChoosingAvatar: false });

    if (avatarUrl) {
      console.log('准备更新临时头像URL:', avatarUrl);
      // 更新临时头像
      this.setData({
        'tempUserInfo.avatar': avatarUrl
      });
      console.log('临时头像已更新到data中');

      wx.showToast({
        title: '头像选择成功',
        icon: 'success',
        duration: 1500
      });

      console.log('头像已更新到临时数据:', avatarUrl);
    } else {
      console.error('未获取到有效的头像URL');
      this.showAvatarSelectFallback();
    }
  },

  // 显示头像选择备用方案
  showAvatarSelectFallback() {
    wx.showModal({
      title: '头像选择',
      content: '微信头像选择暂时不可用，请使用其他方式选择头像。',
      confirmText: '从相册选择',
      cancelText: '使用默认头像',
      success: (res) => {
        if (res.confirm) {
          this.chooseFromAlbum();
        } else {
          this.useDefaultAvatar();
        }
      }
    });
  },

  // 头像选择功能
  chooseAvatarAlternative() {
    wx.showActionSheet({
      itemList: ['从相册选择', '使用默认头像'],
      success: (res) => {
        if (res.tapIndex === 0) {
          // 从相册选择
          this.chooseImageFromAlbum();
        } else if (res.tapIndex === 1) {
          // 使用默认头像
          this.useDefaultAvatar();
        }
      },
      fail: (err) => {
        console.log('用户取消选择头像');
      }
    });
  },

  // 从相册选择图片（主要方案）
  chooseFromAlbum() {
    // 防抖检查
    if (this.data.isChoosingAvatar) {
      console.log('头像选择正在进行中，忽略重复调用');
      return;
    }

    // 设置选择状态
    this.setData({ isChoosingAvatar: true });

    wx.chooseMedia({
      count: 1,
      mediaType: ['image'],
      sourceType: ['album', 'camera'],
      maxDuration: 30,
      camera: 'back',
      success: (res) => {
        console.log('选择图片成功:', res);
        const tempFilePath = res.tempFiles[0].tempFilePath;
        console.log('选择的图片路径:', tempFilePath);

        // 更新临时头像
        this.setData({
          'tempUserInfo.avatar': tempFilePath
        });

        wx.showToast({
          title: '头像选择成功',
          icon: 'success'
        });

        // 重置选择状态
        this.setData({ isChoosingAvatar: false });
      },
      fail: (err) => {
        console.error('选择图片失败:', err);
        wx.showToast({
          title: '选择图片失败',
          icon: 'none'
        });

        // 重置选择状态
        this.setData({ isChoosingAvatar: false });
      }
    });
  },

  // 尝试微信头像选择（带错误处理）
  tryWechatAvatar() {
    // 防抖检查
    if (this.data.isChoosingAvatar) {
      console.log('头像选择正在进行中，忽略重复调用');
      return;
    }

    // 设置选择状态
    this.setData({ isChoosingAvatar: true });

    // 2秒后重置状态（防止卡死）
    setTimeout(() => {
      this.setData({ isChoosingAvatar: false });
    }, 2000);

    // 使用微信头像选择API
    wx.getUserProfile({
      desc: '完善会员资料',
      success: (res) => {
        console.log('微信头像选择成功:', res);
        console.log('用户信息详情:', JSON.stringify(res.userInfo));

        // 检查用户信息是否存在
        if (res.userInfo) {
          const userInfo = res.userInfo;
          console.log('头像URL:', userInfo.avatarUrl);
          console.log('昵称:', userInfo.nickName);

          // 更新临时头像和昵称
          const updateData = {};

          if (userInfo.avatarUrl) {
            updateData['tempUserInfo.avatar'] = userInfo.avatarUrl;
            console.log('设置头像:', userInfo.avatarUrl);
          }

          if (userInfo.nickName && userInfo.nickName !== '微信用户') {
            updateData['tempUserInfo.nickname'] = userInfo.nickName;
            console.log('设置昵称:', userInfo.nickName);
          }

          if (Object.keys(updateData).length > 0) {
            this.setData(updateData);
            console.log('更新临时用户信息:', updateData);

            wx.showToast({
              title: '头像选择成功',
              icon: 'success'
            });
          } else {
            console.warn('没有获取到有效的用户信息');
            wx.showToast({
              title: '未获取到头像信息',
              icon: 'none'
            });
          }
        } else {
          console.error('用户信息为空');
          wx.showToast({
            title: '获取用户信息失败',
            icon: 'none'
          });
        }

        // 重置选择状态
        this.setData({ isChoosingAvatar: false });
      },
      fail: (error) => {
        console.error('微信头像选择失败:', error);

        // 重置选择状态
        this.setData({ isChoosingAvatar: false });

        // 提供备用方案
        wx.showModal({
          title: '头像选择失败',
          content: '微信头像选择不可用，是否使用相册选择头像？',
          confirmText: '选择相册',
          cancelText: '取消',
          success: (res) => {
            if (res.confirm) {
              this.chooseFromAlbum();
            }
          }
        });
      }
    });
  },



  // 使用默认头像
  useDefaultAvatar() {
    // 使用项目中已有的默认头像
    const defaultAvatar = '/assets/images/default-avatar.png';

    this.setData({
      'tempUserInfo.avatar': defaultAvatar
    });

    wx.showToast({
      title: '已设置默认头像',
      icon: 'success'
    });
  },

  // 上传头像到服务器
  uploadAvatar(filePath) {
    return new Promise((resolve, reject) => {
      console.log('开始上传头像:', filePath);

      const app = getApp();
      const token = app.globalData.token;

      if (!token) {
        reject(new Error('未登录'));
        return;
      }

      wx.uploadFile({
        url: 'http://localhost:4000/api/v1/client/upload',
        filePath: filePath,
        name: 'file',  // 后端期望的字段名是'file'
        header: {
          'Authorization': `Bearer ${token}`
        },
        success: (res) => {
          console.log('头像上传响应:', res);
          console.log('响应状态码:', res.statusCode);
          console.log('响应数据:', res.data);

          if (res.statusCode === 200) {
            try {
              const data = JSON.parse(res.data);
              console.log('解析后的响应数据:', JSON.stringify(data));

              // 适配后端返回的数据结构
              if (data.success && data.data && data.data.url) {
                // 确保URL是完整的
                let avatarUrl = data.data.url;
                console.log('上传成功，原始URL:', avatarUrl);

                // 如果是相对路径，转换为完整URL
                if (avatarUrl.startsWith('/uploads/')) {
                  avatarUrl = `http://localhost:4000${avatarUrl}`;
                } else if (!avatarUrl.startsWith('http')) {
                  // 如果不是http开头，也不是/uploads/开头，可能是其他相对路径
                  avatarUrl = `http://localhost:4000/uploads/images/${avatarUrl}`;
                }

                console.log('上传成功，完整URL:', avatarUrl);
                resolve(avatarUrl);
              } else if (data.status === 'success' && data.data && data.data.url) {
                // 兼容其他可能的响应格式
                let avatarUrl = data.data.url;
                console.log('上传成功（兼容格式），原始URL:', avatarUrl);

                // 如果是相对路径，转换为完整URL
                if (avatarUrl.startsWith('/uploads/')) {
                  avatarUrl = `http://localhost:4000${avatarUrl}`;
                } else if (!avatarUrl.startsWith('http')) {
                  avatarUrl = `http://localhost:4000/uploads/images/${avatarUrl}`;
                }

                console.log('上传成功（兼容格式），完整URL:', avatarUrl);
                resolve(avatarUrl);
              } else {
                console.error('上传失败，响应格式不正确:', data);
                reject(new Error(data.message || '上传失败'));
              }
            } catch (e) {
              console.error('解析响应失败:', e, '原始数据:', res.data);
              reject(new Error('解析响应失败'));
            }
          } else {
            console.error('上传失败，HTTP状态码:', res.statusCode);
            reject(new Error(`上传失败，状态码: ${res.statusCode}`));
          }
        },
        fail: (error) => {
          console.error('头像上传失败:', error);
          reject(error);
        }
      });
    });
  },

  // 昵称输入处理
  onNicknameInput(e) {
    console.log('输入昵称:', e.detail.value);
    this.setData({
      'tempUserInfo.nickname': e.detail.value
    });
  },

  // 昵称失焦处理（微信安全监测）
  onNicknameBlur(e) {
    const nickname = e.detail.value;
    console.log('昵称失焦:', nickname);

    // 微信会在失焦时进行安全监测，如果不通过会清空内容
    // 这里我们保存当前值，以防被清空后可以恢复
    this.setData({
      'tempUserInfo.nickname': nickname,
      lastValidNickname: nickname
    });
  },

  // 昵称表单提交处理
  onNicknameSubmit(e) {
    const nickname = e.detail.value.nickname;
    console.log('昵称表单提交:', nickname);

    if (nickname) {
      this.setData({
        'tempUserInfo.nickname': nickname
      });
    }
  },

  // 保存新的用户信息
  saveNewUserInfo() {
    const { tempUserInfo } = this.data;

    if (!tempUserInfo.nickname || tempUserInfo.nickname.trim() === '') {
      wx.showToast({
        title: '请输入昵称',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({
      title: '保存中...',
      mask: true
    });

    // 处理头像上传和保存
    const avatarUrl = tempUserInfo.avatar || '/assets/images/default-avatar.png';
    const nickname = tempUserInfo.nickname.trim();

    // 检查是否是需要上传的临时文件
    if (avatarUrl.includes('wxfile://') || avatarUrl.includes('tmp') || avatarUrl.includes('temp')) {
      console.log('检测到临时文件，开始上传头像:', avatarUrl);
      this.uploadAndSaveUserInfo(nickname, avatarUrl);
    } else {
      console.log('使用现有头像URL:', avatarUrl);
      this.saveUserInfoToServer(nickname, avatarUrl);
    }
  },

  // 上传头像并保存用户信息
  async uploadAndSaveUserInfo(nickname, avatarPath) {
    try {
      console.log('开始上传头像:', avatarPath);

      // 上传头像
      const uploadedUrl = await this.uploadAvatar(avatarPath);

      if (uploadedUrl) {
        console.log('头像上传成功，URL:', uploadedUrl);
        this.saveUserInfoToServer(nickname, uploadedUrl);
      } else {
        console.error('头像上传失败，使用默认头像');
        this.saveUserInfoToServer(nickname, '/assets/images/default-avatar.png');
      }
    } catch (error) {
      console.error('上传头像过程中出错:', error);
      wx.showToast({
        title: '头像上传失败',
        icon: 'none'
      });
      // 使用默认头像继续保存
      this.saveUserInfoToServer(nickname, '/assets/images/default-avatar.png');
    }
  },

  // 保存用户信息到服务器
  saveUserInfoToServer(nickname, avatar) {
    const updateData = {
      nickname: nickname,
      avatar: avatar
    };

    userService.updateUserInfo(updateData).then(serverRes => {
      console.log('服务器更新成功:', serverRes);

      // 更新页面数据
      this.setData({
        userInfo: {
          ...this.data.userInfo,
          nickname: updateData.nickname,
          avatar: updateData.avatar
        },
        isDemote: false,
        isProfileCompleted: true,
        showAvatarEdit: false
      });

      // 更新本地存储
      wx.setStorageSync('userInfo', this.data.userInfo);
      wx.setStorageSync('isDemote', false);

      // 更新全局状态
      const app = getApp();
      if (app && app.globalData) {
        app.globalData.userInfo = this.data.userInfo;
        app.globalData.isProfileCompleted = true;
        app.globalData.isDemote = false;
      }

      wx.hideLoading();
      wx.showToast({
        title: '保存成功',
        icon: 'success'
      });

      // 重新加载用户数据
      this.loadUserData();

    }).catch(error => {
      console.error('服务器更新失败:', error);
      wx.hideLoading();
      wx.showToast({
        title: '保存失败，请重试',
        icon: 'none'
      });
    });
  },

  // 取消编辑
  cancelAvatarEdit() {
    this.setData({
      showAvatarEdit: false
    });
  }
})