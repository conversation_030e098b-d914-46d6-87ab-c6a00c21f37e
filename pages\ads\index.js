// pages/ads/index.js
const app = getApp()
const API = require('../../config/api')
const { request } = require('../../utils/request')
const { showToast, showModal } = require('../../utils/util')

Page({
  /**
   * 页面的初始数据
   */
  data: {
    stats: {
      todayIncome: '0.00',
      monthIncome: '0.00',
      totalIncome: '0.00'
    },
    adSpaces: [],
    loading: true,
    isLoggedIn: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    this.setData({
      isLoggedIn: app.globalData.isLoggedIn
    })
    
    if (this.data.isLoggedIn) {
      this.fetchAdStats()
      this.fetchAdSpaces()
    }
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    if (!this.data.isLoggedIn) {
      showModal({
        title: '提示',
        content: '请先登录',
        confirmText: '去登录',
        success: (res) => {
          if (res.confirm) {
            wx.switchTab({
              url: '/pages/user/profile/profile'
            })
          }
        }
      })
    }
  },

  /**
   * 获取广告统计数据
   */
  fetchAdStats: function () {
    this.setData({ loading: true })
    
    request({
      url: '/api/v1/client/ads/stats',
      method: 'GET'
    }).then(res => {
      if (res.code === 0 && res.data) {
        this.setData({
          stats: {
            todayIncome: res.data.todayIncome || '0.00',
            monthIncome: res.data.monthIncome || '0.00',
            totalIncome: res.data.totalIncome || '0.00'
          },
          loading: false
        })
      } else {
        showToast('获取广告统计失败')
        this.setData({ loading: false })
      }
    }).catch(err => {
      console.error('获取广告统计失败', err)
      showToast('获取广告统计失败')
      this.setData({ loading: false })
    })
  },

  /**
   * 获取广告位列表
   */
  fetchAdSpaces: function () {
    request({
      url: '/api/v1/client/ads/spaces',
      method: 'GET'
    }).then(res => {
      if (res.code === 0 && res.data) {
        this.setData({
          adSpaces: res.data.list || []
        })
      } else {
        showToast('获取广告位失败')
      }
    }).catch(err => {
      console.error('获取广告位失败', err)
      showToast('获取广告位失败')
    })
  },

  /**
   * 投放我的广告
   */
  onCreateAd: function () {
    showToast('广告投放功能开发中')
  },

  /**
   * 收益提现
   */
  onWithdraw: function () {
    wx.navigateTo({
      url: '/pages/user/wallet/wallet'
    })
  },

  /**
   * 查看更多广告位
   */
  onViewMoreAdSpaces: function () {
    showToast('更多广告位功能开发中')
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {
    if (this.data.isLoggedIn) {
      Promise.all([
        this.fetchAdStats(),
        this.fetchAdSpaces()
      ]).finally(() => {
        wx.stopPullDownRefresh()
      })
    } else {
      wx.stopPullDownRefresh()
    }
  },

  /**
   * 广告加载成功
   */
  onAdLoad: function () {
    console.log('广告加载成功')
  },

  /**
   * 广告加载失败
   */
  onAdError: function (err) {
    console.error('广告加载失败', err)
  }
}) 