/**
 * 分润自动结算定时任务
 */
const cron = require('node-cron');
const logger = require('../utils/logger');
const ProfitService = require('../services/profit-service');
const db = require('../../config/database');

class ProfitSettlementTask {
  /**
   * 启动定时任务
   */
  static start() {
    // 每小时执行一次自动结算
    cron.schedule('0 * * * *', async () => {
      try {
        logger.info('开始执行分润自动结算任务');
        
        // 检查是否启用自动结算
        const configResult = await db.query(
          'SELECT config_value FROM profit_config WHERE config_key = "auto_settle_enabled"'
        );
        
        const autoSettleEnabled = configResult.length > 0 ? 
          configResult[0].config_value === 'true' : false;
        
        if (!autoSettleEnabled) {
          logger.info('自动结算功能已禁用，跳过执行');
          return;
        }
        
        // 执行自动结算
        const result = await ProfitService.autoSettleProfits();
        
        if (result.success) {
          logger.info(`分润自动结算完成: 结算${result.settledRecords}条记录`);
        } else {
          logger.warn(`分润自动结算完成: ${result.message}`);
        }
        
      } catch (error) {
        logger.error(`分润自动结算任务执行失败: ${error.message}`);
      }
    });
    
    // 每天凌晨2点执行团队等级更新
    cron.schedule('0 2 * * *', async () => {
      try {
        logger.info('开始执行团队等级更新任务');
        await this.updateTeamLevels();
        logger.info('团队等级更新任务完成');
      } catch (error) {
        logger.error(`团队等级更新任务执行失败: ${error.message}`);
      }
    });
    
    // 每周一凌晨3点执行分润统计报告
    cron.schedule('0 3 * * 1', async () => {
      try {
        logger.info('开始生成周度分润统计报告');
        await this.generateWeeklyReport();
        logger.info('周度分润统计报告生成完成');
      } catch (error) {
        logger.error(`周度分润统计报告生成失败: ${error.message}`);
      }
    });
    
    logger.info('分润自动结算定时任务已启动');
  }
  
  /**
   * 更新团队等级
   */
  static async updateTeamLevels() {
    try {
      // 获取所有团队
      const teams = await db.query('SELECT * FROM team WHERE status = 1');
      
      for (const team of teams) {
        // 计算团队月度业绩
        const monthlyStats = await db.query(`
          SELECT 
            SUM(amount) as monthly_amount,
            COUNT(*) as monthly_count
          FROM profit_log 
          WHERE team_id = ? 
          AND created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
          AND status = 1
        `, [team.id]);
        
        const monthlyAmount = parseFloat(monthlyStats[0]?.monthly_amount || 0);
        const memberCount = team.member_count || 0;
        
        // 根据业绩和成员数确定等级
        let newLevel = 1;
        
        if (monthlyAmount >= 20000 && memberCount >= 50) {
          newLevel = 5; // 王者团队
        } else if (monthlyAmount >= 8000 && memberCount >= 20) {
          newLevel = 4; // 精英团队
        } else if (monthlyAmount >= 3000 && memberCount >= 10) {
          newLevel = 3; // 高级团队
        } else if (monthlyAmount >= 1000 && memberCount >= 5) {
          newLevel = 2; // 中级团队
        }
        
        // 更新团队等级
        if (newLevel !== team.level) {
          await db.query(
            'UPDATE team SET level = ?, updated_at = NOW() WHERE id = ?',
            [newLevel, team.id]
          );
          
          logger.info(`团队${team.name}等级更新: ${team.level} -> ${newLevel}`);
        }
      }
      
    } catch (error) {
      logger.error(`更新团队等级失败: ${error.message}`);
      throw error;
    }
  }
  
  /**
   * 生成周度分润统计报告
   */
  static async generateWeeklyReport() {
    try {
      const weekStart = new Date();
      weekStart.setDate(weekStart.getDate() - 7);
      
      // 获取周度统计数据
      const weeklyStats = await db.query(`
        SELECT 
          profit_type,
          role,
          COUNT(*) as count,
          SUM(amount) as total_amount,
          SUM(CASE WHEN status = 1 THEN amount ELSE 0 END) as settled_amount
        FROM profit_log 
        WHERE created_at >= ?
        GROUP BY profit_type, role
      `, [weekStart]);
      
      // 获取团队排行
      const teamRanking = await db.query(`
        SELECT 
          t.id,
          t.name,
          t.level,
          SUM(pl.amount) as weekly_profit,
          COUNT(pl.id) as profit_count
        FROM team t
        LEFT JOIN profit_log pl ON t.id = pl.team_id 
        WHERE pl.created_at >= ?
        GROUP BY t.id, t.name, t.level
        ORDER BY weekly_profit DESC
        LIMIT 10
      `, [weekStart]);
      
      // 生成报告内容
      const report = {
        period: {
          start: weekStart.toISOString().split('T')[0],
          end: new Date().toISOString().split('T')[0]
        },
        summary: {
          totalRecords: weeklyStats.reduce((sum, s) => sum + parseInt(s.count), 0),
          totalAmount: weeklyStats.reduce((sum, s) => sum + parseFloat(s.total_amount), 0),
          settledAmount: weeklyStats.reduce((sum, s) => sum + parseFloat(s.settled_amount), 0)
        },
        byType: weeklyStats,
        teamRanking: teamRanking,
        generatedAt: new Date()
      };
      
      // 保存报告到数据库或文件
      logger.info('周度分润统计报告:', JSON.stringify(report, null, 2));
      
      // 这里可以添加发送邮件或推送通知的逻辑
      
    } catch (error) {
      logger.error(`生成周度分润统计报告失败: ${error.message}`);
      throw error;
    }
  }
  
  /**
   * 停止定时任务
   */
  static stop() {
    cron.getTasks().forEach(task => {
      task.stop();
    });
    logger.info('分润自动结算定时任务已停止');
  }
  
  /**
   * 手动执行结算任务（用于测试）
   */
  static async manualSettle() {
    try {
      logger.info('手动执行分润结算任务');
      const result = await ProfitService.autoSettleProfits();
      logger.info('手动结算完成:', result);
      return result;
    } catch (error) {
      logger.error(`手动结算失败: ${error.message}`);
      throw error;
    }
  }
  
  /**
   * 手动执行团队等级更新（用于测试）
   */
  static async manualUpdateLevels() {
    try {
      logger.info('手动执行团队等级更新');
      await this.updateTeamLevels();
      logger.info('手动团队等级更新完成');
      return { success: true, message: '团队等级更新完成' };
    } catch (error) {
      logger.error(`手动团队等级更新失败: ${error.message}`);
      throw error;
    }
  }
}

module.exports = ProfitSettlementTask;
