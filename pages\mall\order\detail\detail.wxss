/* pages/mall/order/detail/detail.wxss */
.container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

/* 订单状态 */
.status-section {
  background-color: #07c160;
  padding: 60rpx 40rpx;
  display: flex;
  align-items: center;
  color: #fff;
}

.status-icon {
  margin-right: 30rpx;
}

.status-emoji {
  font-size: 60rpx;
  line-height: 1;
}

.status-info {
  flex: 1;
}

.status-text {
  font-size: 32rpx;
  font-weight: 500;
  display: block;
  margin-bottom: 10rpx;
}

.status-desc {
  font-size: 26rpx;
  opacity: 0.8;
}

/* 通用区块样式 */
.address-section,
.goods-section,
.order-section,
.amount-section {
  background-color: #fff;
  margin: 20rpx;
  border-radius: 16rpx;
  overflow: hidden;
}

.section-title {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}

.title-icon {
  font-size: 32rpx;
  margin-right: 16rpx;
  line-height: 1;
}

/* 收货地址 */
.address-info {
  padding: 30rpx;
}

.receiver-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.receiver-name {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.receiver-phone {
  font-size: 28rpx;
  color: #666;
}

.receiver-address {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
}

/* 商品信息 */
.goods-list {
  padding: 0 30rpx 30rpx;
}

.goods-item {
  display: flex;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.goods-item:last-child {
  border-bottom: none;
}

.goods-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 12rpx;
  margin-right: 24rpx;
  background-color: #f5f5f5;
}

.goods-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.goods-name {
  font-size: 28rpx;
  color: #333;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.goods-spec {
  font-size: 24rpx;
  color: #999;
  margin: 8rpx 0;
}

.goods-price-qty {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.goods-price {
  font-size: 28rpx;
  color: #ff6b35;
  font-weight: 500;
}

.goods-qty {
  font-size: 24rpx;
  color: #999;
}

/* 订单信息 */
.order-info {
  padding: 0 30rpx 30rpx;
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f8f8f8;
}

.info-row:last-child {
  border-bottom: none;
}

.info-label {
  font-size: 26rpx;
  color: #666;
}

.info-value {
  font-size: 26rpx;
  color: #333;
  text-align: right;
  flex: 1;
  margin-left: 40rpx;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.copy-btn {
  color: #07c160;
  font-size: 24rpx;
  margin-left: 20rpx;
  padding: 8rpx 16rpx;
  border: 1rpx solid #07c160;
  border-radius: 20rpx;
}

/* 费用明细 */
.amount-info {
  padding: 0 30rpx 30rpx;
}

.amount-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
}

.amount-label {
  font-size: 26rpx;
  color: #666;
}

.amount-value {
  font-size: 26rpx;
  color: #333;
}

.amount-value.discount {
  color: #07c160;
}

.amount-row.total {
  border-top: 1rpx solid #f0f0f0;
  margin-top: 10rpx;
  padding-top: 30rpx;
}

.amount-row.total .amount-label {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.amount-row.total .amount-value {
  font-size: 32rpx;
  color: #ff6b35;
  font-weight: 500;
}

/* 操作按钮 */
.action-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  padding: 20rpx;
  border-top: 1rpx solid #eee;
  display: flex;
  gap: 20rpx;
  z-index: 100;
}

.action-btn {
  flex: 1;
  padding: 24rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  border: none;
}

.action-btn.primary {
  background-color: #07c160;
  color: #fff;
}

.action-btn.secondary {
  background-color: #fff;
  color: #666;
  border: 1rpx solid #ddd;
}

/* 加载和错误状态 */
.loading-container,
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  font-size: 28rpx;
  color: #999;
}

.error-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
  opacity: 0.6;
  line-height: 1;
}

.error-text {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 40rpx;
}

.error-btn {
  background-color: #07c160;
  color: #fff;
  border-radius: 40rpx;
  padding: 20rpx 60rpx;
  border: none;
  font-size: 28rpx;
}
