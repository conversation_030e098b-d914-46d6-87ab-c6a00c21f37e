const axios = require('axios');
const fs = require('fs');
const path = require('path');
const FormData = require('form-data');

// 测试服务器配置
const baseURL = 'http://localhost:4000/api/v1';
let token = '';

// 登录获取token
async function login() {
  try {
    const loginResponse = await axios.post(`${baseURL}/admin/auth/login`, {
      username: 'admin',
      password: 'wo587129955' // 使用用户提供的密码
    });

    console.log('登录响应:', loginResponse.data);
    if (loginResponse.data && loginResponse.data.data && loginResponse.data.data.token) {
      token = loginResponse.data.data.token;
      console.log('登录成功，获取到token:', token);
      return true;
    } else {
      console.error('登录失败，未获取到token');
      return false;
    }
  } catch (error) {
    console.error('登录请求失败:', error.response ? error.response.data : error.message);
    return false;
  }
}

// 测试上传功能
async function testUpload() {
  try {
    // 创建测试图片
    const testImagePath = path.join(__dirname, 'test-image.png');
    
    // 检查测试图片是否存在，不存在则创建一个简单的测试图片
    if (!fs.existsSync(testImagePath)) {
      // 这里应该创建一个图片，但为了简单起见，我们使用一个已有的图片
      // 如果没有图片，测试会失败
      console.log('请确保test-image.png文件存在于服务器根目录');
    }
    
    if (fs.existsSync(testImagePath)) {
      const formData = new FormData();
      formData.append('file', fs.createReadStream(testImagePath));
      
      const uploadResponse = await axios.post(`${baseURL}/admin/upload`, formData, {
        headers: {
          ...formData.getHeaders(),
          'Authorization': `Bearer ${token}`
        }
      });
      
      console.log('上传响应:', uploadResponse.data);
      if (uploadResponse.data && uploadResponse.data.code === 200) {
        console.log('文件上传成功，文件URL:', uploadResponse.data.data.url);
        return true;
      } else {
        console.error('文件上传失败');
        return false;
      }
    } else {
      console.error('测试图片不存在');
      return false;
    }
  } catch (error) {
    console.error('上传请求失败:', error.response ? error.response.data : error.message);
    return false;
  }
}

// 运行测试
async function runTests() {
  console.log('开始测试...');
  
  // 1. 登录测试
  console.log('\n--- 测试登录 ---');
  const loginSuccess = await login();
  if (!loginSuccess) {
    console.error('登录测试失败，终止后续测试');
    return;
  }
  
  // 2. 上传测试
  console.log('\n--- 测试文件上传 ---');
  await testUpload();
  
  console.log('\n所有测试完成');
}

// 运行测试
runTests(); 