/**
 * 验证地区隔离分润系统
 */

const mysql = require('mysql2/promise');

async function verifyRegionProfit() {
  const connection = await mysql.createConnection({
    host: 'localhost',
    user: 'root',
    password: 'wo587129955',
    database: 'mall'
  });

  try {
    console.log('🔍 验证地区隔离分润系统...\n');

    // 1. 检查地区数据
    const [regions] = await connection.execute('SELECT * FROM region ORDER BY id');
    console.log(`✅ 地区数据 (${regions.length}个):`);
    regions.forEach(region => {
      console.log(`   ${region.id}. ${region.name} (${region.code})`);
    });

    // 2. 检查团队地区分配
    const [teams] = await connection.execute(`
      SELECT t.id, t.name, t.region_id, r.name as region_name 
      FROM team t 
      LEFT JOIN region r ON t.region_id = r.id
    `);
    console.log(`\n✅ 团队地区分配 (${teams.length}个团队):`);
    teams.forEach(team => {
      console.log(`   团队${team.id}: ${team.name} -> ${team.region_name || '未分配'}`);
    });

    // 3. 检查分润规则
    const [rules] = await connection.execute('SELECT * FROM profit_rule WHERE status = 1');
    console.log(`\n✅ 分润规则 (${rules.length}条):`);
    rules.forEach(rule => {
      console.log(`   ${rule.name}: 平台${rule.platform_rate}% | 团长${rule.leader_rate}% | 成员${rule.user_rate}%`);
    });

    // 4. 检查用户地区分配
    const [users] = await connection.execute(`
      SELECT u.id, u.nickname, u.region_id, r.name as region_name 
      FROM user u 
      LEFT JOIN region r ON u.region_id = r.id 
      LIMIT 10
    `);
    console.log(`\n✅ 用户地区分配 (前10个用户):`);
    users.forEach(user => {
      console.log(`   用户${user.id}: ${user.nickname || '未设置昵称'} -> ${user.region_name || '未分配'}`);
    });

    // 5. 检查分润记录表结构
    const [profitLogStructure] = await connection.execute('DESCRIBE profit_log');
    console.log(`\n✅ 分润记录表结构:`);
    const hasRegionId = profitLogStructure.some(col => col.Field === 'region_id');
    const hasTeamId = profitLogStructure.some(col => col.Field === 'team_id');
    console.log(`   - region_id字段: ${hasRegionId ? '✅ 存在' : '❌ 不存在'}`);
    console.log(`   - team_id字段: ${hasTeamId ? '✅ 存在' : '❌ 不存在'}`);

    // 6. 地区业绩统计
    const [regionStats] = await connection.execute(`
      SELECT 
        r.name as region_name,
        COUNT(DISTINCT t.id) as team_count,
        COUNT(DISTINCT u.id) as user_count
      FROM region r
      LEFT JOIN team t ON r.id = t.region_id
      LEFT JOIN user u ON r.id = u.region_id
      GROUP BY r.id, r.name
      ORDER BY r.id
    `);
    console.log(`\n✅ 地区统计:`);
    regionStats.forEach(stat => {
      console.log(`   ${stat.region_name}: ${stat.team_count}个团队, ${stat.user_count}个用户`);
    });

    console.log('\n🎉 地区隔离分润系统验证完成！');
    console.log('\n📋 系统特性:');
    console.log('   ✅ 支持多地区管理');
    console.log('   ✅ 团队按地区隔离');
    console.log('   ✅ 用户按地区分配');
    console.log('   ✅ 分润记录支持地区标识');
    console.log('   ✅ 团队业绩按地区统计');
    console.log('   ✅ 团队长按地区区分');

  } catch (error) {
    console.error('❌ 验证失败:', error.message);
  } finally {
    await connection.end();
  }
}

verifyRegionProfit();
