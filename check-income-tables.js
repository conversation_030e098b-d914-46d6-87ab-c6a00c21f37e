const mysql = require('mysql2/promise');

async function checkIncomeTables() {
  let connection;
  
  try {
    // 连接数据库
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: 'wo587129955',
      database: 'mall'
    });
    
    console.log('🔍 检查收入相关数据表...\n');
    
    // 需要检查的收入相关表
    const incomeTables = [
      'profit_rule',        // 分润规则表
      'profit_config',      // 分润配置表
      'profit_log',         // 分润记录表
      'team_level_profit',  // 团队等级分润规则表
      'user',              // 用户表（包含balance, total_income字段）
      'team',              // 团队表（包含total_profit, total_income等字段）
      'wallet',            // 钱包表（如果存在）
      'income_log',        // 收入日志表（如果存在）
      'withdraw',          // 提现表（如果存在）
      'income_bill'        // 收入账单表（如果存在）
    ];
    
    console.log('1️⃣ 检查表存在性:');
    const tableResults = [];
    
    for (const tableName of incomeTables) {
      try {
        const [result] = await connection.execute(`
          SELECT COUNT(*) as table_exists 
          FROM information_schema.tables 
          WHERE table_schema = 'mall' AND table_name = ?
        `, [tableName]);
        
        const exists = result[0].table_exists > 0;
        tableResults.push({
          表名: tableName,
          存在: exists ? '✅ 是' : '❌ 否',
          状态: exists ? '正常' : '缺失'
        });
        
        if (exists) {
          // 如果表存在，检查记录数
          const [countResult] = await connection.execute(`SELECT COUNT(*) as record_count FROM ${tableName}`);
          tableResults[tableResults.length - 1].记录数 = countResult[0].record_count;
        } else {
          tableResults[tableResults.length - 1].记录数 = 0;
        }
      } catch (error) {
        tableResults.push({
          表名: tableName,
          存在: '❌ 否',
          状态: '检查失败',
          记录数: 0,
          错误: error.message
        });
      }
    }
    
    console.table(tableResults);
    
    // 2. 检查用户表中的收入相关字段
    console.log('\n2️⃣ 检查用户表收入字段:');
    try {
      const [userFields] = await connection.execute(`
        SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT, COLUMN_COMMENT
        FROM information_schema.COLUMNS 
        WHERE TABLE_SCHEMA = 'mall' 
        AND TABLE_NAME = 'user' 
        AND COLUMN_NAME IN ('balance', 'total_income')
      `);
      
      if (userFields.length > 0) {
        console.table(userFields);
      } else {
        console.log('❌ 用户表中缺少收入相关字段');
      }
    } catch (error) {
      console.log('❌ 检查用户表字段失败:', error.message);
    }
    
    // 3. 检查团队表中的收入相关字段
    console.log('\n3️⃣ 检查团队表收入字段:');
    try {
      const [teamFields] = await connection.execute(`
        SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT, COLUMN_COMMENT
        FROM information_schema.COLUMNS 
        WHERE TABLE_SCHEMA = 'mall' 
        AND TABLE_NAME = 'team' 
        AND COLUMN_NAME IN ('total_profit', 'total_income', 'month_income', 'today_income')
      `);
      
      if (teamFields.length > 0) {
        console.table(teamFields);
      } else {
        console.log('❌ 团队表中缺少收入相关字段');
      }
    } catch (error) {
      console.log('❌ 检查团队表字段失败:', error.message);
    }
    
    // 4. 检查分润规则数据
    console.log('\n4️⃣ 检查分润规则数据:');
    try {
      const [profitRules] = await connection.execute(`
        SELECT type, name, platform_rate, leader_rate, user_rate, min_amount, status
        FROM profit_rule
      `);
      
      if (profitRules.length > 0) {
        console.table(profitRules);
      } else {
        console.log('❌ 分润规则表为空');
      }
    } catch (error) {
      console.log('❌ 检查分润规则失败:', error.message);
    }
    
    // 5. 检查分润配置数据
    console.log('\n5️⃣ 检查分润配置数据:');
    try {
      const [profitConfigs] = await connection.execute(`
        SELECT config_key, config_value, config_type, description, status
        FROM profit_config
      `);
      
      if (profitConfigs.length > 0) {
        console.table(profitConfigs);
      } else {
        console.log('❌ 分润配置表为空');
      }
    } catch (error) {
      console.log('❌ 检查分润配置失败:', error.message);
    }
    
    // 6. 检查最近的分润记录
    console.log('\n6️⃣ 检查最近的分润记录:');
    try {
      const [profitLogs] = await connection.execute(`
        SELECT id, user_id, amount, profit_type, role, status, created_at
        FROM profit_log
        ORDER BY created_at DESC
        LIMIT 5
      `);
      
      if (profitLogs.length > 0) {
        console.table(profitLogs);
      } else {
        console.log('❌ 分润记录表为空');
      }
    } catch (error) {
      console.log('❌ 检查分润记录失败:', error.message);
    }
    
    // 7. 生成缺失表的创建建议
    console.log('\n7️⃣ 缺失表创建建议:');
    const missingTables = tableResults.filter(table => table.存在 === '❌ 否');
    
    if (missingTables.length > 0) {
      console.log('发现以下表缺失:');
      missingTables.forEach(table => {
        console.log(`- ${table.表名}`);
      });
      console.log('\n建议执行数据库建表语句.sql来创建缺失的表');
    } else {
      console.log('✅ 所有收入相关表都存在');
    }
    
  } catch (error) {
    console.error('❌ 检查失败:', error.message);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// 运行检查
checkIncomeTables();
