const axios = require('axios');
const jwt = require('jsonwebtoken');
const mysql = require('mysql2/promise');

async function testPriceFix() {
  let connection;
  try {
    console.log('🔧 开始测试价格修复...');
    
    // 连接数据库
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: 'wo587129955',
      database: 'mall'
    });
    
    // 生成JWT token
    const config = require('./config');
    const userId = 2;
    
    const token = jwt.sign(
      { id: userId, openid: 'test_openid_13800138000' },
      config.jwt.secret,
      { expiresIn: config.jwt.expiresIn }
    );
    
    console.log('🔑 生成的token:', token);
    
    // 1. 清理购物车
    console.log('\n🧹 1. 清理购物车...');
    await connection.execute('DELETE FROM cart WHERE user_id = ?', [userId]);
    console.log('购物车已清理');
    
    // 2. 添加商品到购物车
    console.log('\n🛒 2. 添加商品到购物车...');
    const testGoods = [
      { goodsId: 1, quantity: 2 }, // 测试商品1: 99.99 * 2 = 199.98
      { goodsId: 8, quantity: 1 }  // 休闲T恤: 88.00 * 1 = 88.00
    ];
    
    for (const item of testGoods) {
      const addCartResponse = await axios.post('http://localhost:4000/api/v1/client/cart/add', {
        goodsId: item.goodsId,
        quantity: item.quantity,
        specificationId: 0
      }, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
      
      console.log(`添加商品${item.goodsId}到购物车:`, addCartResponse.data);
    }
    
    // 3. 获取购物车列表
    console.log('\n📋 3. 获取购物车列表...');
    const cartResponse = await axios.get('http://localhost:4000/api/v1/client/cart/list', {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    console.log('购物车列表:', JSON.stringify(cartResponse.data, null, 2));
    
    // 4. 计算前端应该显示的价格
    console.log('\n🧮 4. 计算前端应该显示的价格...');
    let totalAmount = 0;
    if (cartResponse.data.status === 'success' && cartResponse.data.data.list) {
      cartResponse.data.data.list.forEach(item => {
        const subtotal = parseFloat(item.price) * item.quantity;
        totalAmount += subtotal;
        console.log(`- ${item.title}: ${item.price} × ${item.quantity} = ${subtotal}`);
      });
    }
    
    const freight = totalAmount >= 99 ? 0 : 10;
    const finalAmount = totalAmount + freight;
    
    console.log(`商品总价: ${totalAmount}`);
    console.log(`运费: ${freight}`);
    console.log(`最终金额: ${finalAmount}`);
    
    // 5. 创建订单（从购物车）
    console.log('\n📝 5. 创建订单（从购物车）...');
    const orderData = {
      addressId: 1,
      goods: testGoods,
      remark: '价格修复测试订单',
      paymentMethod: 'wechat',
      couponId: 0,
      fromCart: true
    };
    
    const createOrderResponse = await axios.post('http://localhost:4000/api/v1/client/order/create', orderData, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    console.log('订单创建响应:', createOrderResponse.data);
    
    if (createOrderResponse.data.status !== 'success') {
      console.error('❌ 订单创建失败');
      return;
    }
    
    const orderId = createOrderResponse.data.data.orderId;
    console.log('📋 订单ID:', orderId);
    
    // 6. 查询数据库中的订单信息
    console.log('\n🗄️ 6. 查询数据库中的订单信息...');
    const [orders] = await connection.execute('SELECT * FROM orders WHERE id = ?', [orderId]);
    const order = orders[0];
    
    console.log('数据库订单信息:');
    console.log(`- 商品金额: ${order.goods_amount}`);
    console.log(`- 运费: ${order.shipping_fee}`);
    console.log(`- 优惠金额: ${order.discount_amount}`);
    console.log(`- 总金额: ${order.total_amount}`);
    
    // 7. 获取订单详情
    console.log('\n🔍 7. 获取订单详情...');
    const orderDetailResponse = await axios.get(`http://localhost:4000/api/v1/client/order/detail/${orderId}`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    console.log('订单详情API响应:', JSON.stringify(orderDetailResponse.data, null, 2));
    
    // 8. 创建支付订单
    console.log('\n💳 8. 创建支付订单...');
    const paymentResponse = await axios.post('http://localhost:4000/api/v1/client/payment/create', {
      orderId: orderId,
      paymentMethod: 'wechat'
    }, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    console.log('支付订单创建响应:', JSON.stringify(paymentResponse.data, null, 2));
    
    // 9. 价格一致性检查
    console.log('\n✅ 9. 价格一致性检查...');
    const dbAmount = parseFloat(order.total_amount);
    const paymentAmount = paymentResponse.data.data.amount;
    const frontendAmount = finalAmount;
    
    console.log('价格对比:');
    console.log(`- 前端计算金额: ${frontendAmount}`);
    console.log(`- 数据库存储金额: ${dbAmount}`);
    console.log(`- 支付订单金额: ${paymentAmount}`);
    
    const frontendDbDiff = Math.abs(frontendAmount - dbAmount);
    const dbPaymentDiff = Math.abs(dbAmount - paymentAmount);
    const frontendPaymentDiff = Math.abs(frontendAmount - paymentAmount);
    
    console.log('\n差异分析:');
    console.log(`- 前端与数据库差异: ${frontendDbDiff}`);
    console.log(`- 数据库与支付差异: ${dbPaymentDiff}`);
    console.log(`- 前端与支付差异: ${frontendPaymentDiff}`);
    
    if (frontendDbDiff <= 0.01 && dbPaymentDiff <= 0.01 && frontendPaymentDiff <= 0.01) {
      console.log('🎉 所有价格匹配正常！');
    } else {
      console.log('❌ 发现价格不匹配问题！');
      
      if (frontendDbDiff > 0.01) {
        console.log('- 前端计算与数据库存储不一致');
      }
      if (dbPaymentDiff > 0.01) {
        console.log('- 数据库存储与支付金额不一致');
      }
      if (frontendPaymentDiff > 0.01) {
        console.log('- 前端计算与支付金额不一致');
      }
    }
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    if (error.response) {
      console.error('响应状态:', error.response.status);
      console.error('响应数据:', error.response.data);
    } else {
      console.error('其他错误:', error.code || error.message);
    }
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// 运行测试
testPriceFix();
