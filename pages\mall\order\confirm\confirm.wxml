<!--pages/mall/order/confirm/confirm.wxml-->
<view class="confirm-container">
  <!-- 收货地址 -->
  <view class="address-section" bindtap="onSelectAddress">
    <block wx:if="{{address}}">
      <view class="address-info">
        <view class="address-contact">
          <text class="address-name">{{address.name}}</text>
          <text class="address-phone">{{address.phone}}</text>
        </view>
        <view class="address-detail">{{address.province}}{{address.city}}{{address.district}}{{address.address}}</view>
      </view>
    </block>
    <view class="address-empty" wx:else>
      <text>请选择收货地址</text>
    </view>
    <view class="address-arrow">></view>
  </view>

  <!-- 商品清单 -->
  <view class="goods-section">
    <view class="section-title">商品清单</view>
    <view class="goods-list">
      <view class="goods-item" wx:for="{{orderGoods}}" wx:key="id">
        <image class="goods-image" src="{{item.cover || '/assets/images/goods-placeholder.svg'}}" mode="aspectFill"></image>
        <view class="goods-info">
          <view class="goods-name">{{item.name}}</view>
          <view class="goods-spec">规格：{{item.selectedSpec ? item.selectedSpec.name : '默认规格'}}</view>
          <view class="goods-price-quantity">
            <text class="goods-price">¥{{item.selectedSpec ? item.selectedSpec.price : item.price}}</text>
            <text class="goods-quantity">x{{item.quantity}}</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 配送方式 -->
  <view class="delivery-section">
    <view class="section-title">配送方式</view>
    <view class="delivery-options">
      <view class="delivery-option {{selectedDeliveryMethod.id === item.id ? 'active' : ''}}"
            wx:for="{{deliveryMethods}}"
            wx:key="id"
            bindtap="onSelectDeliveryMethod"
            data-method="{{item}}">
        <view class="delivery-info">
          <text class="delivery-name">{{item.name}}</text>
          <text class="delivery-desc" wx:if="{{item.description}}">{{item.description}}</text>
          <text class="delivery-threshold" wx:if="{{item.freeShippingThreshold > 0}}">满¥{{item.freeShippingThreshold}}免邮</text>
        </view>
        <view class="delivery-price">
          <text class="delivery-fee">{{totalAmount >= item.freeShippingThreshold ? '免邮' : '¥' + item.fee}}</text>
          <view class="delivery-check" wx:if="{{selectedDeliveryMethod.id === item.id}}">✓</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 支付方式 -->
  <view class="payment-section">
    <view class="section-title">支付方式</view>
    <view class="payment-options">
      <view class="payment-option {{paymentMethod === 'wechat' ? 'active' : ''}}" bindtap="onSelectPayment" data-method="wechat">
        <text class="payment-icon">💰</text>
        <text class="payment-name">微信支付</text>
        <view class="payment-check" wx:if="{{paymentMethod === 'wechat'}}">✓</view>
      </view>
      <!-- 添加支付宝支付选项用于测试 -->
      <view class="payment-option {{paymentMethod === 'alipay' ? 'active' : ''}}" bindtap="onSelectPayment" data-method="alipay">
        <text class="payment-icon">💳</text>
        <text class="payment-name">支付宝</text>
        <view class="payment-check" wx:if="{{paymentMethod === 'alipay'}}">✓</view>
      </view>
    </view>
  </view>

  <!-- 优惠券 -->
  <view class="coupon-section" bindtap="onSelectCoupon">
    <view class="section-title">优惠券</view>
    <view class="coupon-content">
      <text class="coupon-text">{{couponId > 0 ? '已选择优惠券' : '无可用优惠券'}}</text>
      <text class="coupon-arrow">></text>
    </view>
  </view>

  <!-- 订单备注 -->
  <view class="remark-section">
    <view class="section-title">订单备注:</view>
    <view class="remark-input-box">
      <textarea class="remark-input" placeholder="请输入备注内容..." value="{{remark}}" bindinput="onInputRemark" maxlength="100"></textarea>
    </view>
  </view>

  <!-- 广告位 -->
  <view class="ad-container">
    <ad unit-id="adunit-id" ad-type="banner" ad-theme="white" bindload="onAdLoad" binderror="onAdError"></ad>
  </view>

  <!-- 底部结算栏 -->
  <view class="bottom-bar">
    <view class="price-info">
      <view class="price-item">
        <text class="price-label">商品金额:</text>
        <text class="price-value">¥{{totalAmount}}</text>
      </view>
      <view class="price-item">
        <text class="price-label">运费:</text>
        <text class="price-value">¥{{freight}}</text>
      </view>
      <view class="price-item total">
        <text class="price-label">总计:</text>
        <text class="price-value">¥{{finalAmount}}</text>
      </view>
    </view>
    <view class="submit-btn {{submitting ? 'disabled' : ''}}" bindtap="onSubmitOrder">提交订单</view>
  </view>

  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{loading}}">
    <text class="loading-text">加载中...</text>
  </view>
</view> 