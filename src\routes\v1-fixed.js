const express = require('express');
const router = express.Router();

// 引入各个模块的路由
const systemRouter = require('./system');
const userRouter = require('./user');
const wifiRouter = require('./wifi');
const goodsRouter = require('./goods');
const orderRouter = require('./order');
const authRouter = require('./auth');
const logRouter = require('./log');
const advertisementRouter = require('./advertisement');
const uploadRouter = require('./upload');
const incomeRouter = require('./income');
const withdrawRouter = require('./withdraw');
const cartRouter = require('./cart');
const adsRouter = require('./ads');
const teamRouter = require('./team');

// 引入商品控制器（用于无认证访问）
const goodsController = require('../controllers/goods');
// 引入上传控制器
const uploadController = require('../controllers/upload');
// 引入数据库连接
const db = require('../database');
// 引入中间件和响应工具
const { verifyToken } = require('../middlewares/auth');
const { success, error } = require('../utils/response');
const config = require('../../config');

// 管理端路由
router.use('/admin/system', systemRouter);
router.use('/admin/user', userRouter);
router.use('/admin/wifi', wifiRouter);
router.use('/admin/goods', goodsRouter);
router.use('/admin/order', orderRouter);
router.use('/admin/auth', authRouter);
router.use('/admin/log', logRouter);
router.use('/admin/advertisement', advertisementRouter);
router.use('/admin/upload', uploadRouter);
router.use('/admin/income', incomeRouter);
router.use('/admin/withdraw', withdrawRouter);
router.use('/admin/team', teamRouter);

// 直接处理客户端商品公开API
router.get('/client/goods/list', (req, res) => {
  console.log('直接处理客户端商品列表请求，无需认证');
  goodsController.getGoodsList(req, res);
});

// 增加查询参数形式的商品详情路由
router.get('/client/goods/detail', (req, res) => {
  console.log('直接处理客户端商品详情查询参数请求，无需认证');
  // 从查询参数获取ID
  const { id } = req.query;
  if (!id) {
    return res.status(400).json({
      status: 'error',
      message: '商品ID不能为空'
    });
  }
  
  // 将ID添加到params
  req.params.id = id;
  goodsController.getGoodsDetail(req, res);
});

router.get('/client/goods/detail/:id', (req, res) => {
  console.log('直接处理客户端商品详情请求，无需认证');
  goodsController.getGoodsDetail(req, res);
});

router.get('/client/goods/categories', (req, res) => {
  console.log('直接处理客户端商品分类请求，无需认证');
  goodsController.getCategories(req, res);
});

// 直接处理客户端购物车公开API
router.get('/client/cart/list', (req, res) => {
  console.log('直接处理客户端购物车列表请求，无需认证');
  cartRouter.handle({ 
    method: 'GET', 
    url: '/client/list',
    baseUrl: '',
    originalUrl: '/api/v1/client/cart/list',
    path: '/client/list',
    params: {},
    query: req.query,
    headers: req.headers
  }, res);
});

// 直接处理客户端广告公开API
router.get('/client/ads/banner', (req, res) => {
  console.log('直接处理客户端首页轮播图广告请求，无需认证');
  adsRouter.handle({ 
    method: 'GET', 
    url: '/client/banner',
    baseUrl: '',
    originalUrl: '/api/v1/client/ads/banner',
    path: '/client/banner',
    params: {},
    query: req.query,
    headers: req.headers
  }, res);
});

// 添加客户端WiFi相关API
router.get('/client/wifi/detail/:id', async (req, res) => {
  console.log('直接处理客户端WiFi码详情请求');
  try {
    const { id } = req.params;
    const [wifi] = await db.query('SELECT * FROM wifi WHERE id = ?', [id]);
    
    if (!wifi || wifi.length === 0) {
      return error(res, 'WiFi码不存在', 404);
    }
    
    return success(res, wifi[0], '获取WiFi码详情成功');
  } catch (err) {
    console.error('获取WiFi详情失败:', err);
    return error(res, '获取WiFi详情失败: ' + err.message, 500);
  }
});

router.delete('/client/wifi/:id', async (req, res) => {
  console.log('直接处理客户端WiFi码删除请求');
  try {
    const { id } = req.params;
    
    // 检查WiFi是否存在
    const [wifi] = await db.query('SELECT * FROM wifi WHERE id = ?', [id]);
    
    if (!wifi || wifi.length === 0) {
      return error(res, 'WiFi码不存在', 404);
    }
    
    // 删除WiFi码
    await db.query('DELETE FROM wifi WHERE id = ?', [id]);
    
    console.log('删除WiFi码, ID:', id);
    
    return success(res, { id: parseInt(id) }, 'WiFi码删除成功');
  } catch (err) {
    console.error('删除WiFi码失败:', err);
    return error(res, '删除WiFi码失败: ' + err.message, 500);
  }
});

// 添加客户端文件上传API
router.post('/client/upload', 
  require('../middlewares/auth').verifyToken, 
  uploadController.uploadMiddleware, 
  uploadController.uploadSingle
);

// 客户端API路由
router.use('/client/wifi', wifiRouter);
router.use('/client/user', userRouter);
router.use('/client/auth', authRouter);
router.use('/client/order', orderRouter);
router.use('/client/payment', orderRouter);
router.use('/client/team', teamRouter); 
router.use('/client/income', incomeRouter);
router.use('/client/withdraw', withdrawRouter);

// 直接处理admin WIFI详情请求（测试用）
router.get('/admin/wifi/detail/:id', async (req, res) => {
  console.log('直接处理管理端WiFi码详情请求（v1.js中）', req.params);
  try {
    const { id } = req.params;
    
    if (!id || isNaN(parseInt(id))) {
      console.log('无效的ID参数:', id);
      return error(res, '无效的ID参数', 400);
    }
    
    // 添加调试日志
    console.log('正在查询WiFi记录，ID:', id);
    
    try {
      // 修改查询方式，确保返回的是数组
      const wifiResults = await db.query('SELECT * FROM wifi WHERE id = ?', [parseInt(id)]);
      console.log('查询结果类型:', typeof wifiResults);
      console.log('查询结果:', wifiResults);
      
      if (!wifiResults || !Array.isArray(wifiResults) || wifiResults.length === 0) {
        console.log('WiFi查询结果无效');
        return error(res, 'WiFi查询结果无效', 500);
      }
      
      // MySQL2返回的结果可能是[rows, fields]格式
      const rows = Array.isArray(wifiResults[0]) ? wifiResults[0] : wifiResults;
      
      // 检查查询结果
      if (!rows || rows.length === 0) {
        console.log('WiFi不存在(v1.js):', id);
        return error(res, 'WiFi码不存在', 404);
      }
      
      // 获取第一条记录
      const wifiRecord = rows[0];
      console.log('返回WiFi详情(v1.js):', wifiRecord);
      
      if (!wifiRecord) {
        console.log('WiFi记录为空:', id);
        return error(res, 'WiFi记录无效', 500);
      }
      
      // 添加或转换广告相关字段
      const enhancedWifiRecord = {
        id: wifiRecord.id || 0,
        title: wifiRecord.title || '',
        name: wifiRecord.name || '',
        password: wifiRecord.password || '',
        merchant_name: wifiRecord.merchant_name || '',
        qrcode: wifiRecord.qrcode || '',
        use_count: wifiRecord.use_count || 0,
        user_id: wifiRecord.user_id || 0,
        status: wifiRecord.status || 0,
        created_at: wifiRecord.created_at || new Date(),
        updated_at: wifiRecord.updated_at || new Date(),
        ad_enabled: wifiRecord.ad_enabled !== undefined ? wifiRecord.ad_enabled : (wifiRecord.ad_required === 1),
        ad_type: wifiRecord.ad_type || 'video',
        ad_content: wifiRecord.ad_content || wifiRecord.ad_url || '',
        ad_duration: wifiRecord.ad_duration || 5,
        ad_title: wifiRecord.ad_title || '推荐内容',
        ad_link: wifiRecord.ad_link || '',
        ad_view_count: wifiRecord.ad_view_count || 0,
        ad_click_count: wifiRecord.ad_click_count || 0
      };
      
      return success(res, enhancedWifiRecord, '获取WiFi码详情成功');
    } catch (dbErr) {
      console.error('数据库查询出错:', dbErr);
      return error(res, '数据库查询失败: ' + dbErr.message, 500);
    }
  } catch (err) {
    console.error('获取WiFi详情失败(v1.js)，详细错误:', err);
    console.error('错误堆栈:', err.stack);
    return error(res, '获取WiFi详情失败: ' + err.message, 500);
  }
});

module.exports = router; 