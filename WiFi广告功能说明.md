# WiFi广告功能说明

## 功能概述

WiFi广告功能允许商家在用户扫描WiFi二维码后，播放广告内容，用户观看完广告后才能连接WiFi。这是一种有效的广告变现方式。

## 数据库字段设计

WiFi表中已添加以下广告相关字段：

### 基础广告字段
- `ad_enabled` (BOOLEAN) - 是否启用广告
- `ad_type` (VARCHAR(20)) - 广告类型：video/image/text
- `ad_content` (TEXT) - 广告内容（URL或文本）
- `ad_duration` (INT) - 广告持续时间（秒）
- `ad_title` (VARCHAR(200)) - 广告标题
- `ad_link` (VARCHAR(500)) - 广告跳转链接

### 统计字段
- `ad_view_count` (INT) - 广告展示次数
- `ad_click_count` (INT) - 广告点击次数
- `ad_revenue_per_view` (DECIMAL) - 每次展示收益
- `ad_total_revenue` (DECIMAL) - 广告总收益

## 广告流程

1. **用户扫描WiFi二维码**
   - 小程序识别WiFi信息
   - 检查是否启用广告

2. **广告展示**
   - 如果启用广告，显示广告页面
   - 根据广告类型展示相应内容：
     - 视频广告：播放视频
     - 图片广告：显示图片
     - 文字广告：显示文字内容

3. **广告倒计时**
   - 显示倒计时（例如：5秒后可跳过）
   - 倒计时结束后显示"跳过"按钮

4. **连接WiFi**
   - 用户观看完广告或点击跳过后
   - 自动连接WiFi

5. **数据统计**
   - 记录广告展示次数
   - 记录用户点击行为
   - 计算广告收益

## API设计

### 1. 获取WiFi详情（包含广告信息）
```
GET /api/v1/wifi/detail/:id
```

返回数据包含：
```json
{
  "id": 2,
  "title": "星巴克WiFi",
  "name": "Starbucks_Guest",
  "password": "starbucks2024",
  "merchant_name": "星巴克咖啡",
  "ad_enabled": true,
  "ad_type": "video",
  "ad_content": "https://example.com/ad-video.mp4",
  "ad_duration": 10,
  "ad_title": "星巴克新品上市",
  "ad_link": "https://www.starbucks.com/menu"
}
```

### 2. 记录广告展示
```
POST /api/v1/wifi/ad/view/:id
```

### 3. 记录广告点击
```
POST /api/v1/wifi/ad/click/:id
```

## 前端实现要点

### 小程序端
1. 扫码后先判断是否有广告
2. 有广告则跳转到广告页面
3. 广告页面实现：
   - 视频播放器（video组件）
   - 图片展示（image组件）
   - 文字展示（text组件）
   - 倒计时组件
   - 跳过按钮（倒计时结束后显示）

### 管理后台
1. WiFi编辑页面添加广告配置：
   - 启用/禁用广告开关
   - 广告类型选择
   - 广告内容上传/输入
   - 广告时长设置
   - 广告标题和链接

2. 广告统计页面：
   - 展示次数统计
   - 点击率统计
   - 收益统计
   - 时间趋势图表

## 收益模式

1. **CPM（每千次展示付费）**
   - 设置每千次展示的收益
   - 自动计算总收益

2. **CPC（每次点击付费）**
   - 设置每次点击的收益
   - 记录点击并计算收益

3. **固定收费**
   - 商家支付固定费用投放广告
   - 不限展示次数

## 注意事项

1. **用户体验**
   - 广告时长不宜过长（建议5-15秒）
   - 提供跳过选项
   - 确保WiFi连接流程顺畅

2. **合规性**
   - 遵守广告法规
   - 不展示违法违规内容
   - 保护用户隐私

3. **技术优化**
   - 广告资源预加载
   - 视频压缩优化
   - 网络异常处理 