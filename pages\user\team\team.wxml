<!--pages/user/team/team.wxml-->
<wxs module="utils">
  // 格式化图片URL
  var formatImageUrl = function(url) {
    if (!url) {
      return '/assets/icons/user-placeholder.png';
    }

    // 如果已经是完整URL，直接返回
    if (url.indexOf('http://') === 0 || url.indexOf('https://') === 0) {
      return url;
    }

    // 如果是本地资源路径，直接返回
    if (url.indexOf('/assets/') === 0) {
      return url;
    }

    // 如果是uploads路径，添加服务器地址
    if (url.indexOf('/uploads/') === 0) {
      return 'http://localhost:4000' + url;
    }

    // 如果不是以/开头，添加/uploads/前缀
    if (url.indexOf('/') !== 0) {
      return 'http://localhost:4000/uploads/' + url;
    }

    // 其他情况返回默认图片
    return '/assets/icons/user-placeholder.png';
  };

  module.exports = {
    formatImageUrl: formatImageUrl
  };
</wxs>

<view class="team-container">
  <!-- 团队信息展示卡片 -->
  <view class="team-card">
    <view class="team-header">
      <view class="team-title">{{teamInfo.teamName || '我的团队'}}</view>
      <view class="team-level">LV.{{teamInfo.teamLevel || 1}}</view>
    </view>

    <!-- 主要统计数据 -->
    <view class="team-stats">
      <view class="stat-item">
        <view class="stat-value">{{teamInfo.memberCount || 0}}</view>
        <view class="stat-label">团队成员</view>
      </view>
      <view class="stat-item">
        <view class="stat-value">{{teamInfo.wifiCount || 0}}</view>
        <view class="stat-label">WiFi商家</view>
      </view>
      <view class="stat-item">
        <view class="stat-value">¥{{teamInfo.monthPerformance || '0.00'}}</view>
        <view class="stat-label">本月业绩</view>
      </view>
      <view class="stat-item">
        <view class="stat-value">¥{{teamInfo.totalIncome || '0.00'}}</view>
        <view class="stat-label">累计收益</view>
      </view>
    </view>

    <!-- 详细收益数据 -->
    <view class="income-details">
      <view class="income-row">
        <view class="income-item">
          <text class="income-label">今日收益</text>
          <text class="income-value">¥{{teamInfo.todayIncome || '0.00'}}</text>
        </view>
        <view class="income-item">
          <text class="income-label">本周收益</text>
          <text class="income-value">¥{{teamInfo.weekIncome || '0.00'}}</text>
        </view>
      </view>
      <view class="income-row">
        <view class="income-item">
          <text class="income-label">活跃成员</text>
          <text class="income-value">{{teamInfo.activeMembers || 0}}人</text>
        </view>
        <view class="income-item">
          <text class="income-label">本月新增</text>
          <text class="income-value">{{teamInfo.newMembersThisMonth || 0}}人</text>
        </view>
      </view>
    </view>

    <!-- 团队创建时间 -->
    <view wx:if="{{teamInfo.createTime}}" class="team-info">
      <text class="create-time">创建时间：{{teamInfo.createTime}}</text>
    </view>
  </view>

  <!-- 团队成员展示 -->
  <view class="team-members">
    <view class="section-header">团队成员</view>
    
    <!-- 无成员或未加入团队时显示 -->
    <view wx:if="{{!members.length && !loading}}" class="empty-team">
      <view class="empty-text">暂无团队成员</view>
      <button class="btn-create-team" bindtap="onCreateTeam">创建我的团队</button>
    </view>
    
    <!-- 成员列表 -->
    <view wx:else>
      <view class="member-list">
        <block wx:for="{{members}}" wx:key="id">
          <view class="member-item">
            <image class="member-avatar" src="{{utils.formatImageUrl(item.avatar)}}"></image>
            <view class="member-info">
              <view class="member-name">{{item.nickname || '团队成员'}}</view>
              <view class="member-role">{{item.role === 'leader' ? '团长' : '成员'}}</view>
            </view>
            <view class="member-joined">加入时间：{{item.joinedAt}}</view>
          </view>
        </block>
      </view>
      
      <view wx:if="{{hasMore && !loading}}" class="load-more" bindtap="onViewMoreMembers">加载更多</view>
    </view>
  </view>

  <!-- 底部按钮区域 -->
  <view class="action-buttons">
    <button class="btn-invite" bindtap="onInviteNewMember">邀请新成员</button>
    <button class="btn-income" bindtap="onViewIncomeDetails">收益明细</button>
  </view>

  <!-- 快捷分享区域 -->
  <view class="quick-share">
    <view class="share-title">快速分享邀请</view>
    <view class="share-buttons">
      <button class="share-btn wechat" bindtap="shareToFriend" open-type="share">
        <text class="share-icon">💬</text>
        <text class="share-text">微信好友</text>
      </button>
      <button class="share-btn timeline" bindtap="shareToTimeline">
        <text class="share-icon">🌟</text>
        <text class="share-text">朋友圈</text>
      </button>
      <button class="share-btn qrcode" bindtap="generateInviteQRCode">
        <text class="share-icon">📱</text>
        <text class="share-text">二维码</text>
      </button>
      <button class="share-btn copy" bindtap="copyInviteLink">
        <text class="share-icon">📋</text>
        <text class="share-text">复制链接</text>
      </button>
    </view>
  </view>

  <!-- 广告区域 -->
  <ad unit-id="adunit-id" binderror="onAdError" bindload="onAdLoad"></ad>

  <!-- 邀请弹窗 -->
  <view class="invite-modal" wx:if="{{showInviteModal}}">
    <view class="modal-mask" bindtap="closeInviteModal"></view>
    <view class="modal-content">
      <view class="modal-header">
        <text class="modal-title">邀请新成员</text>
        <text class="modal-close" bindtap="closeInviteModal">×</text>
      </view>

      <view class="modal-body">
        <view class="qr-section">
          <text class="section-title">扫描二维码加入团队</text>

          <!-- 如果有二维码URL，显示二维码 -->
          <image wx:if="{{qrCodeUrl}}" class="qr-code" src="{{qrCodeUrl}}" mode="aspectFit"></image>

          <!-- 如果没有二维码URL，显示备用信息 -->
          <view wx:else class="qr-fallback">
            <view class="fallback-icon">📱</view>
            <view class="fallback-text">二维码生成失败</view>
            <view class="fallback-tip">请使用下方邀请码</view>
          </view>
        </view>

        <view class="invite-info">
          <text class="invite-code">邀请码：{{inviteCode}}</text>
          <button class="copy-btn" bindtap="copyInviteLink">复制邀请链接</button>
        </view>

        <view class="share-tips">
          <text>分享给好友，邀请他们加入您的团队</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{loading}}">
    <text class="loading-text">加载中...</text>
  </view>
</view>