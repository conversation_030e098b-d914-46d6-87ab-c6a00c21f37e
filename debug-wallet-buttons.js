// 调试钱包按钮响应问题
// 这个文件用于在小程序中调试按钮点击事件

// 在钱包页面的JS文件中添加调试代码
const debugWalletButtons = {
  
  // 调试提现按钮
  onWithdraw: function () {
    console.log('🔧 调试: 提现按钮被点击');
    console.log('当前余额:', this.data.balance);
    
    // 检查余额
    if (parseFloat(this.data.balance) <= 0) {
      console.log('❌ 余额不足，无法提现');
      wx.showToast({
        title: '余额不足，无法提现',
        icon: 'none'
      });
      return;
    }
    
    console.log('✅ 余额充足，准备跳转到提现页面');
    console.log('跳转路径: /pages/user/withdraw/withdraw');
    
    // 检查页面是否存在
    try {
      wx.navigateTo({
        url: '/pages/user/withdraw/withdraw',
        success: function(res) {
          console.log('✅ 成功跳转到提现页面:', res);
        },
        fail: function(err) {
          console.error('❌ 跳转提现页面失败:', err);
          wx.showToast({
            title: '页面跳转失败',
            icon: 'none'
          });
        }
      });
    } catch (error) {
      console.error('❌ 提现按钮异常:', error);
      wx.showToast({
        title: '功能异常，请重试',
        icon: 'none'
      });
    }
  },

  // 调试明细按钮
  onViewDetails: function () {
    console.log('🔧 调试: 明细按钮被点击');
    console.log('跳转路径: /pages/user/income-details/income-details');
    
    try {
      wx.navigateTo({
        url: '/pages/user/income-details/income-details',
        success: function(res) {
          console.log('✅ 成功跳转到明细页面:', res);
        },
        fail: function(err) {
          console.error('❌ 跳转明细页面失败:', err);
          wx.showToast({
            title: '页面跳转失败',
            icon: 'none'
          });
        }
      });
    } catch (error) {
      console.error('❌ 明细按钮异常:', error);
      wx.showToast({
        title: '功能异常，请重试',
        icon: 'none'
      });
    }
  },

  // 调试充值按钮
  onRecharge: function () {
    console.log('🔧 调试: 充值按钮被点击');
    wx.showToast({
      title: '充值功能开发中',
      icon: 'none'
    });
  },

  // 检查页面路由配置
  checkPageRoutes: function() {
    console.log('🔧 检查页面路由配置...');
    
    // 检查app.json中的页面配置
    const pages = [
      'pages/user/withdraw/withdraw',
      'pages/user/withdraw-records/withdraw-records', 
      'pages/user/income-details/income-details'
    ];
    
    console.log('需要的页面路由:', pages);
    console.log('请确认这些页面已在app.json中注册');
  },

  // 检查数据状态
  checkDataStatus: function() {
    console.log('🔧 检查钱包数据状态...');
    console.log('余额:', this.data.balance);
    console.log('收入统计:', this.data.incomeStats);
    console.log('是否已登录:', this.data.isLoggedIn);
    console.log('是否正在加载:', this.data.loading);
  },

  // 测试API连接
  testAPIConnection: function() {
    console.log('🔧 测试API连接...');
    
    const API = require('../../../config/api');
    console.log('API配置:', API);
    
    if (!API || !API.income || !API.income.stats) {
      console.error('❌ API配置错误: API.income.stats 不存在');
      return false;
    }
    
    console.log('✅ API配置正常');
    return true;
  }
};

// 使用方法说明
console.log(`
🔧 钱包按钮调试工具使用说明:

1. 在钱包页面的onLoad方法中添加:
   this.checkPageRoutes();
   this.checkDataStatus();
   this.testAPIConnection();

2. 替换按钮事件处理方法:
   onWithdraw: debugWalletButtons.onWithdraw,
   onViewDetails: debugWalletButtons.onViewDetails,
   onRecharge: debugWalletButtons.onRecharge,

3. 在开发者工具控制台查看调试信息

4. 常见问题排查:
   - 检查app.json是否注册了页面路由
   - 检查页面文件是否存在
   - 检查API配置是否正确
   - 检查用户登录状态
   - 检查数据加载状态
`);

module.exports = debugWalletButtons;
