-- 修复team_apply表结构，添加缺失的字段

USE mall;

-- 检查并添加contact字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = 'mall' 
     AND TABLE_NAME = 'team_apply' 
     AND COLUMN_NAME = 'contact') = 0,
    'ALTER TABLE team_apply ADD COLUMN contact VARCHAR(50) NOT NULL COMMENT "联系人姓名" AFTER name',
    'SELECT "contact字段已存在" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加email字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = 'mall' 
     AND TABLE_NAME = 'team_apply' 
     AND COLUMN_NAME = 'email') = 0,
    'ALTER TABLE team_apply ADD COLUMN email VARCHAR(100) DEFAULT NULL COMMENT "电子邮箱" AFTER phone',
    'SELECT "email字段已存在" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 显示修改后的表结构
DESCRIBE team_apply;
