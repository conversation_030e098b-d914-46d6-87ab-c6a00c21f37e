const axios = require('axios');

async function testRegionAPI() {
  try {
    console.log('🧪 测试地区管理API...\n');
    
    // 首先登录获取token
    console.log('1️⃣ 登录获取token...');
    const loginResponse = await axios.post('http://localhost:4000/api/v1/admin/auth/admin-login', {
      username: 'mrx0927',
      password: 'hh20250701'
    });
    
    const token = loginResponse.data.data.token;
    console.log('✅ 登录成功，获取到token');
    
    // 测试获取地区列表
    console.log('\n2️⃣ 测试获取地区列表...');
    const listResponse = await axios.get('http://localhost:4000/api/v1/admin/region/list', {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    console.log('✅ 地区列表获取成功');
    console.log('状态码:', listResponse.status);
    console.log('数据:', JSON.stringify(listResponse.data, null, 2));
    
    // 测试创建地区
    console.log('\n3️⃣ 测试创建地区...');
    const createResponse = await axios.post('http://localhost:4000/api/v1/admin/region/create', {
      name: '测试地区',
      code: 'TEST001',
      parent_id: null,
      level: 1,
      status: 1,
      sort_order: 999
    }, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    console.log('✅ 地区创建成功');
    console.log('状态码:', createResponse.status);
    console.log('数据:', JSON.stringify(createResponse.data, null, 2));
    
  } catch (error) {
    console.log('\n❌ API测试失败!');
    console.log('错误状态码:', error.response?.status);
    console.log('错误信息:', error.response?.data);
    console.log('完整错误:', error.message);
  }
}

testRegionAPI();
