<!--pages/mall/order/list/list.wxml-->
<view class="container">
  <!-- 搜索栏 -->
  <view class="search-bar" wx:if="{{showSearch}}">
    <view class="search-input-wrapper">
      <input
        class="search-input"
        placeholder="搜索订单号、商品名称"
        value="{{searchKeyword}}"
        bindinput="onSearchInput"
        bindconfirm="onSearchConfirm"
      />
      <view class="search-btn" bindtap="onSearchConfirm">
        <text class="iconfont icon-search">🔍</text>
      </view>
    </view>
    <view class="filter-btn" bindtap="onShowFilter">
      <text class="iconfont icon-filter">筛选</text>
    </view>
  </view>

  <!-- 标签页导航 -->
  <view class="tabs">
    <view
      class="tab-item {{currentTab === index ? 'active' : ''}}"
      wx:for="{{tabs}}"
      wx:key="index"
      data-index="{{index}}"
      bindtap="onTabChange"
    >
      {{item.name}}
      <view class="tab-badge" wx:if="{{item.count > 0}}">{{item.count}}</view>
    </view>
    <view class="search-toggle" bindtap="onToggleSearch">
      <text class="iconfont">{{showSearch ? '收起' : '搜索'}}</text>
    </view>
  </view>

  <!-- 订单列表 -->
  <scroll-view
    class="order-list"
    scroll-y="{{true}}"
    refresher-enabled="{{true}}"
    refresher-triggered="{{refreshing}}"
    bindrefresherrefresh="onPullDownRefresh"
    bindscrolltolower="onReachBottom"
  >
    <view class="order-item" wx:for="{{orders}}" wx:key="id">
      <!-- 订单头部 -->
      <view class="order-header">
        <view class="order-info">
          <text class="order-no">订单号：{{item.order_no}}</text>
          <text class="order-time">{{item.created_at}}</text>
        </view>
        <view class="order-status {{item.status === 0 ? 'pending' : item.status === 4 ? 'cancelled' : ''}}">
          {{item.status_name}}
        </view>
      </view>

      <!-- 商品列表 -->
      <view class="goods-list">
        <view class="goods-item" wx:for="{{item.goods_list}}" wx:for-item="goods" wx:key="id">
          <image class="goods-image" src="{{goods.image}}" mode="aspectFill"></image>
          <view class="goods-info">
            <text class="goods-name">{{goods.name}}</text>
            <text class="goods-spec">{{goods.spec}}</text>
            <view class="goods-price-qty">
              <text class="goods-price">¥{{goods.price}}</text>
              <text class="goods-qty">x{{goods.quantity}}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 订单金额 -->
      <view class="order-amount">
        <text>共{{item.goods_count}}件商品，合计：</text>
        <text class="total-amount">¥{{item.total_amount}}</text>
      </view>

      <!-- 操作按钮 -->
      <view class="order-actions">
        <button
          class="action-btn secondary"
          data-id="{{item.id}}"
          bindtap="onViewDetail"
        >
          查看详情
        </button>

        <!-- 待付款状态 -->
        <block wx:if="{{item.status === 0}}">
          <button
            class="action-btn secondary"
            data-id="{{item.id}}"
            data-order-no="{{item.order_no}}"
            bindtap="onCancelOrder"
          >
            取消订单
          </button>
          <button
            class="action-btn primary"
            data-id="{{item.id}}"
            bindtap="onPayNow"
          >
            立即支付
          </button>
        </block>

        <!-- 待收货状态 -->
        <block wx:if="{{item.status === 2}}">
          <button
            class="action-btn secondary"
            data-id="{{item.id}}"
            bindtap="onViewLogistics"
          >
            查看物流
          </button>
          <button
            class="action-btn primary"
            data-id="{{item.id}}"
            bindtap="onConfirmReceive"
          >
            确认收货
          </button>
        </block>

        <!-- 待发货状态 -->
        <block wx:if="{{item.status === 1}}">
          <button
            class="action-btn secondary"
            data-id="{{item.id}}"
            bindtap="onViewLogistics"
          >
            查看物流
          </button>
        </block>

        <!-- 已完成或已取消状态 -->
        <block wx:if="{{item.status === 3 || item.status === 4}}">
          <button
            class="action-btn danger"
            data-id="{{item.id}}"
            data-order-no="{{item.order_no}}"
            bindtap="onDeleteOrder"
          >
            删除订单
          </button>
        </block>
      </view>
    </view>

    <!-- 加载状态 -->
    <view class="loading-more" wx:if="{{loading}}">
      <text>加载中...</text>
    </view>

    <!-- 没有更多数据 -->
    <view class="no-more" wx:if="{{!hasMore && orders.length > 0}}">
      <text>没有更多订单了</text>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{orders.length === 0 && !loading}}">
      <text class="empty-icon">📦</text>
      <text class="empty-text">{{searchKeyword ? '未找到相关订单' : '暂无订单'}}</text>
      <button class="empty-btn" bindtap="onGoShopping" wx:if="{{!searchKeyword}}">去逛逛</button>
      <button class="empty-btn" bindtap="onClearSearch" wx:if="{{searchKeyword}}">清除搜索</button>
    </view>
  </scroll-view>
</view>

<!-- 筛选弹窗 -->
<view class="filter-popup {{showFilterPopup ? 'show' : ''}}" catchtouchmove="preventTouchMove">
  <view class="filter-mask" bindtap="onHideFilter"></view>
  <view class="filter-content">
    <view class="filter-header">
      <text class="filter-title">筛选订单</text>
      <text class="filter-close" bindtap="onHideFilter">✕</text>
    </view>

    <view class="filter-section">
      <view class="filter-label">订单状态</view>
      <view class="filter-options">
        <view
          class="filter-option {{filterStatus === item.status ? 'active' : ''}}"
          wx:for="{{filterStatusOptions}}"
          wx:key="status"
          bindtap="onSelectFilterStatus"
          data-status="{{item.status}}"
        >
          {{item.name}}
        </view>
      </view>
    </view>

    <view class="filter-section">
      <view class="filter-label">下单时间</view>
      <view class="filter-options">
        <view
          class="filter-option {{filterTime === item.value ? 'active' : ''}}"
          wx:for="{{filterTimeOptions}}"
          wx:key="value"
          bindtap="onSelectFilterTime"
          data-time="{{item.value}}"
        >
          {{item.name}}
        </view>
      </view>
    </view>

    <view class="filter-section">
      <view class="filter-label">订单金额</view>
      <view class="filter-options">
        <view
          class="filter-option {{filterAmount === item.value ? 'active' : ''}}"
          wx:for="{{filterAmountOptions}}"
          wx:key="value"
          bindtap="onSelectFilterAmount"
          data-amount="{{item.value}}"
        >
          {{item.name}}
        </view>
      </view>
    </view>

    <view class="filter-actions">
      <button class="filter-reset" bindtap="onResetFilter">重置</button>
      <button class="filter-confirm" bindtap="onConfirmFilter">确定</button>
    </view>
  </view>
</view>