const crypto = require('crypto');
const bcrypt = require('bcryptjs');

/**
 * 加密工具类
 */

// 哈希盐值轮数
const SALT_ROUNDS = 10;

/**
 * 使用bcrypt加密密码
 * @param {string} password 原始密码
 * @returns {Promise<string>} 加密后的密码
 */
const hashPassword = async (password) => {
  return await bcrypt.hash(password, SALT_ROUNDS);
};

/**
 * 验证密码
 * @param {string} password 原始密码
 * @param {string} hash 加密后的密码
 * @returns {Promise<boolean>} 是否匹配
 */
const verifyPassword = async (password, hash) => {
  return await bcrypt.compare(password, hash);
};

/**
 * MD5加密
 * @param {string} content 要加密的内容
 * @returns {string} 加密后的字符串
 */
const md5 = (content) => {
  return crypto.createHash('md5').update(content).digest('hex');
};

/**
 * SHA256加密
 * @param {string} content 要加密的内容
 * @returns {string} 加密后的字符串
 */
const sha256 = (content) => {
  return crypto.createHash('sha256').update(content).digest('hex');
};

/**
 * 生成指定长度的随机字符串
 * @param {number} length 字符串长度
 * @returns {string} 随机字符串
 */
const randomString = (length = 16) => {
  return crypto.randomBytes(Math.ceil(length / 2))
    .toString('hex')
    .slice(0, length);
};

/**
 * AES加密
 * @param {string} content 要加密的内容
 * @param {string} key 密钥
 * @returns {string} 加密后的字符串
 */
const aesEncrypt = (content, key) => {
  const cipher = crypto.createCipher('aes-256-cbc', key);
  let encrypted = cipher.update(content, 'utf8', 'hex');
  encrypted += cipher.final('hex');
  return encrypted;
};

/**
 * AES解密
 * @param {string} encrypted 加密后的内容
 * @param {string} key 密钥
 * @returns {string} 解密后的字符串
 */
const aesDecrypt = (encrypted, key) => {
  const decipher = crypto.createDecipher('aes-256-cbc', key);
  let decrypted = decipher.update(encrypted, 'hex', 'utf8');
  decrypted += decipher.final('utf8');
  return decrypted;
};

module.exports = {
  hashPassword,
  verifyPassword,
  md5,
  sha256,
  randomString,
  aesEncrypt,
  aesDecrypt
}; 