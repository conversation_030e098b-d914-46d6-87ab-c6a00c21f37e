// pages/user/team/team.js
const app = getApp()
const API = require('../../../config/api')
const { request } = require('../../../utils/request')
const { showToast, showModal, formatImageUrl } = require('../../../utils/util')

Page({
  /**
   * 页面的初始数据
   */
  data: {
    teamInfo: {
      memberCount: 0,
      wifiCount: 0,
      monthPerformance: '0.00',
      totalIncome: '0.00',
      todayIncome: '0.00',
      weekIncome: '0.00',
      activeMembers: 0,
      newMembersThisMonth: 0,
      teamLevel: 1,
      teamName: '我的团队',
      createTime: '',
      isLeader: false
    },
    members: [],
    loading: true,
    isLoggedIn: false,
    page: 1,
    limit: 10,
    hasMore: true,
    showInviteModal: false,
    inviteCode: '',
    qrCodeUrl: '',
    shareData: null
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    console.log('团队页面加载')
    this.checkLoginStatus()

    // 启用分享功能
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    });
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    if (this.data.isLoggedIn) {
      this.fetchTeamInfo()
      this.fetchTeamMembers()
    }
  },

  /**
   * 检查登录状态
   */
  checkLoginStatus: function () {
    const isLoggedIn = app.globalData.isLoggedIn
    this.setData({ isLoggedIn })
    
    if (!isLoggedIn) {
      showModal({
        title: '提示',
        content: '请先登录',
        showCancel: false,
        success: () => {
          wx.navigateBack()
        }
      })
    }
  },

  /**
   * 获取团队信息
   */
  fetchTeamInfo: function () {
    this.setData({ loading: true })
    
    request({
      url: API.team.info,
      method: 'GET'
    }).then(res => {
      console.log('👥 团队信息响应:', res)
      // 兼容不同的响应格式：code: 0 或 code: 200 或 success: true 或 status: 'success'
      if ((res.code === 0 || res.code === 200 || res.success === true || res.status === 'success') && res.data) {
        // 处理新的API响应格式
        const teamData = res.data.team || res.data;
        const hasTeam = res.data.hasTeam !== false; // 默认为true，除非明确为false

        this.setData({
          teamInfo: {
            memberCount: teamData.memberCount || 0,
            wifiCount: teamData.wifiCount || 0,
            monthPerformance: teamData.monthPerformance || teamData.monthIncome || '0.00',
            totalIncome: teamData.totalIncome || '0.00',
            todayIncome: teamData.todayIncome || '0.00',
            weekIncome: teamData.weekIncome || '0.00',
            activeMembers: teamData.activeMembers || teamData.memberCount || 0,
            newMembersThisMonth: teamData.newMembersThisMonth || 0,
            teamLevel: teamData.teamLevel || teamData.level || 1,
            teamName: teamData.teamName || teamData.name || '我的团队',
            createTime: teamData.createTime || teamData.createdAt || '',
            isLeader: teamData.isLeader || (teamData.userRole === 'leader') || false,
            hasTeam: hasTeam
          },
          inviteCode: teamData.inviteCode || '',
          loading: false
        })
        console.log('✅ 团队信息更新成功，邀请码:', teamData.inviteCode)
      } else {
        console.error('❌ 团队信息响应格式错误:', res)
        // 设置默认数据以便测试
        this.setData({
          teamInfo: {
            memberCount: 1,
            wifiCount: 6,
            monthPerformance: '0.00',
            totalIncome: '0.00',
            todayIncome: '0.00',
            weekIncome: '0.00',
            activeMembers: 1,
            newMembersThisMonth: 0,
            teamLevel: 1,
            teamName: '我的团队',
            createTime: '2024-07-14',
            isLeader: true
          },
          inviteCode: 'TEAM000001',
          loading: false
        })
        showToast('获取团队信息失败')
      }
    }).catch(err => {
      console.error('获取团队信息失败', err)
      // 设置默认数据以便测试
      this.setData({
        teamInfo: {
          memberCount: 1,
          wifiCount: 6,
          monthPerformance: '0.00',
          totalIncome: '0.00',
          todayIncome: '0.00',
          weekIncome: '0.00',
          activeMembers: 1,
          newMembersThisMonth: 0,
          teamLevel: 1,
          teamName: '我的团队',
          createTime: '2024-07-14',
          isLeader: true
        },
        inviteCode: 'TEAM000001',
        loading: false
      })
      showToast('获取团队信息失败')
    })
  },

  /**
   * 获取团队成员
   */
  fetchTeamMembers: function () {
    if (!this.data.hasMore) return
    
    request({
      url: API.team.members,
      method: 'GET',
      data: {
        page: this.data.page,
        limit: this.data.limit
      }
    }).then(res => {
      console.log('👥 团队成员响应:', res)
      // 兼容不同的响应格式：code: 0 或 code: 200 或 success: true
      if ((res.code === 0 || res.code === 200 || res.success === true) && res.data) {
        const newMembers = res.data.list || []

        // 处理成员头像URL
        newMembers.forEach(member => {
          if (member.avatar) {
            member.avatar = formatImageUrl(member.avatar)
            console.log('处理后的成员头像URL:', member.avatar)
          }
        })

        const members = this.data.page === 1
          ? newMembers
          : [...this.data.members, ...newMembers]

        this.setData({
          members,
          hasMore: newMembers.length === this.data.limit,
          page: this.data.page + 1
        })
        console.log('✅ 团队成员更新成功，共', newMembers.length, '条记录')
      } else {
        console.error('❌ 团队成员响应格式错误:', res)
        showToast('获取团队成员失败')
      }
    }).catch(err => {
      console.error('获取团队成员失败', err)
      showToast('获取团队成员失败')
    })
  },

  /**
   * 邀请新成员
   */
  onInviteNewMember: function () {
    // 检查用户是否已加入团队
    if (!this.data.teamInfo || this.data.teamInfo.memberCount === 0) {
      wx.showToast({
        title: '您尚未加入团队，无法邀请',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    // 显示邀请选项
    wx.showActionSheet({
      itemList: ['生成邀请二维码', '复制邀请链接', '微信分享'],
      success: (res) => {
        switch (res.tapIndex) {
          case 0:
            this.generateInviteQRCode();
            break;
          case 1:
            this.copyInviteLink();
            break;
          case 2:
            this.shareToWechat();
            break;
        }
      }
    });
  },

  /**
   * 生成邀请二维码
   */
  generateInviteQRCode: function () {
    if (!this.data.inviteCode) {
      wx.showToast({
        title: '邀请码获取失败，请重试',
        icon: 'none'
      });
      return;
    }

    // 显示加载状态
    wx.showLoading({
      title: '生成二维码中...',
      mask: true
    });

    // 调用后端API生成邀请二维码
    // 使用小程序页面路径，扫码后在小程序中打开邀请页面
    const inviteUrl = `pages/invite/invite?code=${this.data.inviteCode}`;

    // 调试：检查token
    const token = wx.getStorageSync('token');
    console.log('🔑 当前token:', token ? `${token.substring(0, 20)}...` : '无token');
    console.log('🔑 邀请码:', this.data.inviteCode);

    // 使用封装的request工具，自动处理认证token
    request({
      url: 'http://localhost:4000/api/v1/client/team/invite-qrcode',
      method: 'GET',
      data: {
        inviteCode: this.data.inviteCode,
        inviteUrl: inviteUrl
      }
    }).then(res => {
      wx.hideLoading();

      if (res && res.status === 'success' && res.data && res.data.qrcode_url) {
        this.setData({
          qrCodeUrl: res.data.qrcode_url,
          showInviteModal: true
        });
      } else {
        // API失败，使用备用方案：显示邀请码文本
        this.showInviteCodeFallback();
      }
    }).catch(err => {
      wx.hideLoading();
      console.error('生成邀请二维码失败:', err);
      // 显示备用方案
      this.showInviteCodeFallback();
    });
  },

  /**
   * 显示邀请码备用方案
   */
  showInviteCodeFallback: function() {
    this.setData({
      qrCodeUrl: null,
      showInviteModal: true
    });

    wx.showToast({
      title: '二维码生成失败，请使用邀请码',
      icon: 'none',
      duration: 2000
    });
  },

  /**
   * 复制邀请链接
   */
  copyInviteLink: function () {
    if (!this.data.inviteCode) {
      wx.showToast({
        title: '邀请码获取失败，请重试',
        icon: 'none'
      });
      return;
    }

    const inviteLink = `https://your-domain.com/invite?code=${this.data.inviteCode}`;

    wx.setClipboardData({
      data: inviteLink,
      success: () => {
        wx.showToast({
          title: '邀请链接已复制到剪贴板',
          icon: 'success'
        });
      },
      fail: () => {
        wx.showToast({
          title: '复制失败，请重试',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 微信分享
   */
  shareToWechat: function () {
    const inviteCode = this.data.inviteCode || 'TEAM000001';

    // 设置分享数据
    this.setData({
      shareData: {
        title: '邀请您加入我的WiFi共享团队',
        path: `/pages/index/index?inviteCode=${inviteCode}`,
        imageUrl: '/assets/images/share-team.jpg'
      }
    });

    // 显示分享选项
    wx.showActionSheet({
      itemList: ['分享给微信好友', '分享到朋友圈', '查看分享指引'],
      success: (res) => {
        switch (res.tapIndex) {
          case 0:
            this.shareToFriend();
            break;
          case 1:
            this.shareToTimeline();
            break;
          case 2:
            this.showShareGuide();
            break;
        }
      }
    });
  },

  /**
   * 分享给好友
   */
  shareToFriend: function () {
    const inviteCode = this.data.inviteCode || 'TEAM000001';

    // 设置分享数据
    this.setData({
      shareData: {
        title: '邀请您加入我的WiFi共享团队',
        path: `/pages/index/index?inviteCode=${inviteCode}`,
        imageUrl: '/assets/images/share-team.jpg'
      }
    });

    // 直接提示分享成功（因为使用了open-type="share"）
    wx.showToast({
      title: '准备分享给好友',
      icon: 'success',
      duration: 1500
    });
  },

  /**
   * 分享到朋友圈
   */
  shareToTimeline: function () {
    // 启用朋友圈分享
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareTimeline']
    });

    wx.showModal({
      title: '分享到朋友圈',
      content: '1. 点击右上角"..."按钮\n2. 选择"分享到朋友圈"\n3. 好友看到后可加入您的团队',
      showCancel: true,
      cancelText: '取消',
      confirmText: '知道了',
      success: (res) => {
        if (res.confirm) {
          wx.showToast({
            title: '请点击右上角分享',
            icon: 'none',
            duration: 2000
          });
        }
      }
    });
  },

  /**
   * 显示分享指引
   */
  showShareGuide: function () {
    wx.showModal({
      title: '分享指引',
      content: '1. 点击右上角"..."按钮\n2. 选择"转发"或"分享到朋友圈"\n3. 好友通过分享链接即可加入团队\n\n您的邀请码：' + (this.data.inviteCode || 'TEAM000001'),
      showCancel: true,
      cancelText: '复制邀请码',
      confirmText: '知道了',
      success: (res) => {
        if (res.cancel) {
          // 复制邀请码
          this.copyInviteLink();
        }
      }
    });
  },

  /**
   * 关闭邀请弹窗
   */
  closeInviteModal: function () {
    this.setData({
      showInviteModal: false
    });
  },

  /**
   * 查看收益明细
   */
  onViewIncomeDetails: function () {
    wx.navigateTo({
      url: '/pages/user/wallet/wallet'
    })
  },

  /**
   * 查看更多成员
   */
  onViewMoreMembers: function () {
    if (this.data.hasMore) {
      this.fetchTeamMembers()
    }
  },

  /**
   * 创建团队
   */
  onCreateTeam: function () {
    const userId = app.globalData.userId;
    const nickname = app.globalData.userInfo ? app.globalData.userInfo.nickname : '';
    
    // 显示创建团队对话框
    wx.showModal({
      title: '创建团队',
      content: '创建团队后您将成为团长，可以邀请他人加入您的团队。确认创建吗？',
      success: (res) => {
        if (res.confirm) {
          wx.showLoading({
            title: '创建中...',
            mask: true
          });
          
          // 构建团队名称（默认使用用户昵称+团队）
          const teamName = nickname ? `${nickname}的团队` : `WiFi共享团队`;
          
          // 请求创建团队API
          request({
            url: `${API.team.create}`,
            method: 'POST',
            data: {
              name: teamName
            }
          }).then(res => {
            wx.hideLoading();
            console.log('👥 创建团队响应:', res)

            // 兼容不同的响应格式：code: 0 或 code: 200 或 success: true
            if (res.code === 0 || res.code === 200 || res.success === true) {
              showToast('创建团队成功');

              // 重新加载团队数据
              this.setData({ page: 1, hasMore: true });
              this.fetchTeamInfo();
              this.fetchTeamMembers();
            } else {
              showToast(res.msg || '创建团队失败');
            }
          }).catch(err => {
            wx.hideLoading();
            console.error('创建团队失败:', err);
            showToast('创建团队失败，请稍后再试');
          });
        }
      }
    });
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {
    if (this.data.isLoggedIn) {
      this.setData({ page: 1, hasMore: true })
      Promise.all([
        this.fetchTeamInfo(),
        this.fetchTeamMembers()
      ]).finally(() => {
        wx.stopPullDownRefresh()
      })
    } else {
      wx.stopPullDownRefresh()
    }
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {
    if (this.data.isLoggedIn && this.data.hasMore) {
      this.fetchTeamMembers()
    }
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {
    const shareData = this.data.shareData || {};
    const inviteCode = this.data.inviteCode || 'TEAM000001';

    return {
      title: shareData.title || '邀请您加入我的WiFi共享团队',
      path: shareData.path || `/pages/index/index?inviteCode=${inviteCode}`,
      imageUrl: shareData.imageUrl || '/assets/images/share-team.jpg',
      success: (res) => {
        console.log('分享成功', res);
        wx.showToast({
          title: '分享成功',
          icon: 'success'
        });
      },
      fail: (err) => {
        console.log('分享失败', err);
        wx.showToast({
          title: '分享失败',
          icon: 'none'
        });
      }
    }
  },

  /**
   * 分享到朋友圈
   */
  onShareTimeline: function () {
    const inviteCode = this.data.inviteCode || 'TEAM000001';

    return {
      title: '邀请您加入我的WiFi共享团队',
      query: `inviteCode=${inviteCode}`,
      imageUrl: '/assets/images/share-team.jpg'
    }
  },

  /**
   * 广告加载成功
   */
  onAdLoad: function () {
    console.log('广告加载成功')
  },

  /**
   * 广告加载失败
   */
  onAdError: function (err) {
    console.error('广告加载失败', err)
  }
}) 