const app = require('./app');
const http = require('http');
const fs = require('fs');
const path = require('path');
const { init: initDatabase } = require('./src/database');
const logger = require('./src/utils/logger');

// 确保日志目录存在
const logsDir = path.join(__dirname, 'logs');
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
}

// 确保上传目录存在
const uploadsDir = path.join(__dirname, 'uploads');
if (!fs.existsSync(uploadsDir)) {
  fs.mkdirSync(uploadsDir, { recursive: true });
  fs.mkdirSync(path.join(uploadsDir, 'images'), { recursive: true });
  fs.mkdirSync(path.join(uploadsDir, 'qrcodes'), { recursive: true });
}

const port = process.env.PORT || 4000;

// 初始化数据库并启动服务器
const startServer = async () => {
  try {
    // 初始化数据库连接
    const dbInitialized = await initDatabase();
    
    if (!dbInitialized) {
      logger.error('数据库初始化失败，服务器无法启动');
      process.exit(1);
    }
    
    const server = http.createServer(app);
    
    server.listen(port, () => {
      logger.info(`服务器运行在 http://localhost:${port}`);
    });
    
    // 处理未捕获的异常
    process.on('uncaughtException', (err) => {
      logger.error('未捕获的异常:', err);
    });
    
    // 处理未处理的Promise拒绝
    process.on('unhandledRejection', (reason, promise) => {
      logger.error('未处理的Promise拒绝:', reason);
    });
  } catch (err) {
    logger.error(`服务器启动失败: ${err.message}`);
    process.exit(1);
  }
};

// 启动服务器
startServer(); 