// 调试WiFi详情API问题
const axios = require('axios');
const db = require('./src/database');
const { success, error } = require('./src/utils/response');

async function debugWifiAPI() {
  try {
    // 初始化数据库连接
    await db.init();
    console.log('数据库连接已建立');
    
    // 1. 检查数据库中的WiFi记录
    console.log('\n1. 检查数据库中的WiFi记录...');
    try {
      const [wifi] = await db.query('SELECT * FROM wifi WHERE id = 2');
      console.log('数据库记录:');
      console.log(JSON.stringify(wifi, null, 2));
    } catch (err) {
      console.error('查询WiFi记录出错:', err.message);
    }
    
    // 2. 检查API端点 - 本地调用v1路由中的处理函数
    console.log('\n2. 模拟API调用...');
    
    // 创建模拟的req和res对象
    const req = {
      params: { id: '2' },
      headers: {},
      query: {}
    };
    
    const res = {
      status: (code) => {
        console.log(`响应状态码: ${code}`);
        return {
          json: (data) => {
            console.log('API响应数据:');
            console.log(JSON.stringify(data, null, 2));
          }
        };
      }
    };
    
    // 模拟调用API
    console.log('模拟调用 GET /api/v1/admin/wifi/detail/2');
    
    try {
      // 定义处理函数 (与v1.js中相同的逻辑)
      async function handleRequest() {
        try {
          const { id } = req.params;
          
          if (!id || isNaN(parseInt(id))) {
            console.log('无效的ID参数:', id);
            return error(res, '无效的ID参数', 400);
          }
          
          console.log('正在查询WiFi记录，ID:', id);
          const wifiResult = await db.query('SELECT * FROM wifi WHERE id = ?', [parseInt(id)]);
          console.log('查询结果类型:', typeof wifiResult);
          console.log('查询结果:', wifiResult);
          
          if (!wifiResult || wifiResult.length === 0) {
            console.log('WiFi不存在:', id);
            return error(res, 'WiFi码不存在', 404);
          }
          
          // 获取第一条记录
          const wifiRecord = wifiResult[0];
          console.log('获取第一条记录:', wifiRecord);
          
          // 添加或转换广告相关字段
          const enhancedWifiRecord = {
            ...wifiRecord,
            ad_enabled: wifiRecord.ad_enabled !== undefined ? wifiRecord.ad_enabled : (wifiRecord.ad_required === 1),
            ad_type: wifiRecord.ad_type || 'video',
            ad_content: wifiRecord.ad_content || wifiRecord.ad_url || null,
            ad_duration: wifiRecord.ad_duration || 5,
            ad_title: wifiRecord.ad_title || '推荐内容',
            ad_link: wifiRecord.ad_link || null,
            ad_view_count: wifiRecord.ad_view_count || 0,
            ad_click_count: wifiRecord.ad_click_count || 0
          };
          
          return success(res, enhancedWifiRecord, '获取WiFi码详情成功');
        } catch (err) {
          console.error('获取WiFi详情失败，详细错误:', err);
          console.error('错误堆栈:', err.stack);
          return error(res, '获取WiFi详情失败: ' + err.message, 500);
        }
      }
      
      // 执行处理函数
      await handleRequest();
    } catch (err) {
      console.error('模拟API调用出错:', err.message);
      console.error('错误堆栈:', err.stack);
    }
    
    // 3. 实际调用API端点
    console.log('\n3. 实际调用API端点...');
    try {
      const response = await axios.get('http://localhost:4000/api/v1/admin/wifi/detail/2', {
        headers: {
          'Authorization': 'Bearer test-token'
        }
      });
      console.log('实际API响应状态码:', response.status);
      console.log('实际API响应数据:', response.data);
    } catch (err) {
      console.error('实际API调用失败:');
      if (err.response) {
        console.error('响应状态码:', err.response.status);
        console.error('响应数据:', err.response.data);
      } else {
        console.error('错误信息:', err.message);
      }
    }
    
    // 4. 测试代理转发
    console.log('\n4. 测试代理转发...');
    try {
      const response = await axios.get('http://localhost:8081/api/v1/admin/wifi/detail/2', {
        headers: {
          'Authorization': 'Bearer test-token'
        }
      });
      console.log('代理API响应状态码:', response.status);
      console.log('代理API响应数据:', response.data);
    } catch (err) {
      console.error('代理API调用失败:');
      if (err.response) {
        console.error('响应状态码:', err.response.status);
        console.error('响应数据:', err.response.data);
      } else {
        console.error('错误信息:', err.message);
      }
    }
    
  } catch (err) {
    console.error('调试过程中出错:', err);
    console.error('错误堆栈:', err.stack);
  } finally {
    process.exit(0);
  }
}

debugWifiAPI(); 