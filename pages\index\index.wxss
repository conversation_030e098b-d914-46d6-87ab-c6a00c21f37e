/* pages/index/index.wxss */
/* 首页样式 - 专业美观设计 */

.container {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  padding-bottom: env(safe-area-inset-bottom);
}

/* ===== 轮播图区域 ===== */
.banner-section {
  position: relative;
  height: 400rpx;
  margin: 0 30rpx 40rpx;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.15);
}

.banner-swiper {
  width: 100%;
  height: 100%;
}

.banner-item {
  position: relative;
  width: 100%;
  height: 100%;
}

.banner-image {
  width: 100%;
  height: 100%;
  border-radius: 24rpx;
}

.banner-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.6));
  padding: 40rpx 30rpx 30rpx;
  border-radius: 0 0 24rpx 24rpx;
}

.banner-content {
  color: #fff;
}

.banner-title {
  display: block;
  font-size: 36rpx;
  font-weight: 600;
  margin-bottom: 10rpx;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
}

.banner-desc {
  display: block;
  font-size: 26rpx;
  opacity: 0.9;
  line-height: 1.4;
}

/* ===== WiFi功能区域 ===== */
.wifi-section {
  margin: 0 30rpx 40rpx;
}

.wifi-actions {
  display: flex;
  gap: 20rpx;
}

.wifi-action-card {
  flex: 1;
  background: #fff;
  border-radius: 20rpx;
  padding: 30rpx 24rpx;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.wifi-action-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 6rpx;
  transition: all 0.3s ease;
}

.create-card::before {
  background: linear-gradient(90deg, #07c160, #00d084);
}

.manage-card::before {
  background: linear-gradient(90deg, #1890ff, #36cfc9);
}

.wifi-action-card:active {
  transform: translateY(-4rpx);
  box-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.12);
}

.card-icon {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 20rpx;
}

.icon-image {
  width: 100%;
  height: 100%;
}

.card-content {
  margin-bottom: 20rpx;
}

.card-title {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.card-subtitle {
  display: block;
  font-size: 24rpx;
  color: #999;
  line-height: 1.4;
}

.card-arrow {
  position: absolute;
  top: 50%;
  right: 24rpx;
  transform: translateY(-50%);
}

.arrow-icon {
  font-size: 36rpx;
  color: #ccc;
  font-weight: 300;
}

/* ===== 联盟入驻区域 ===== */
.alliance-section {
  margin: 0 30rpx 40rpx;
}

.section-label {
  display: block;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 20rpx;
  font-weight: 500;
}

.alliance-card {
  background: #fff;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.alliance-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 6rpx;
  background: linear-gradient(90deg, #ff6b6b, #feca57);
}

.alliance-card:active {
  transform: translateY(-4rpx);
  box-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.12);
}

.alliance-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.alliance-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.alliance-icon {
  width: 80rpx;
  height: 80rpx;
  margin-right: 24rpx;
}

.leader-icon {
  width: 100%;
  height: 100%;
}

.alliance-text {
  flex: 1;
}

.alliance-title {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.alliance-subtitle {
  display: block;
  font-size: 24rpx;
  color: #999;
  line-height: 1.4;
}

.alliance-right {
  margin-left: 20rpx;
}

.alliance-badge {
  display: flex;
  align-items: center;
  background: linear-gradient(90deg, #ff6b6b, #feca57);
  color: #fff;
  padding: 16rpx 24rpx;
  border-radius: 50rpx;
  font-size: 24rpx;
  font-weight: 500;
}

.badge-text {
  margin-right: 8rpx;
}

.badge-arrow {
  font-size: 28rpx;
  font-weight: 300;
}

/* ===== 统计数据区域 ===== */
.stats-section {
  margin: 0 30rpx 40rpx;
}

.stats-card {
  background: #fff;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.08);
}

.stats-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 2rpx solid #f5f5f5;
}

.stats-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.stats-date {
  font-size: 24rpx;
  color: #999;
  background: #f8f9fa;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24rpx;
}

.stats-item {
  text-align: center;
  padding: 20rpx;
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  border-radius: 16rpx;
  transition: all 0.3s ease;
}

.stats-item:nth-child(1) {
  background: linear-gradient(135deg, #fff5f5, #fed7d7);
}

.stats-item:nth-child(2) {
  background: linear-gradient(135deg, #f0fff4, #c6f6d5);
}

.stats-item:nth-child(3) {
  background: linear-gradient(135deg, #f0f8ff, #bee3f8);
}

.stats-item:nth-child(4) {
  background: linear-gradient(135deg, #fefcbf, #faf089);
}

.stats-value {
  display: block;
  font-size: 36rpx;
  font-weight: 700;
  color: #333;
  margin-bottom: 8rpx;
}

.stats-label {
  display: block;
  font-size: 24rpx;
  color: #666;
}

/* ===== 登录区域 ===== */
.login-section {
  margin: 0 30rpx 40rpx;
}

.login-card {
  background: #fff;
  border-radius: 20rpx;
  padding: 40rpx 30rpx;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.08);
  text-align: center;
}

.login-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.login-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 30rpx;
}

.user-avatar {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: #f5f5f5;
}

.login-text {
  margin-bottom: 40rpx;
}

.login-title {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 12rpx;
}

.login-subtitle {
  display: block;
  font-size: 26rpx;
  color: #999;
  line-height: 1.5;
}

.login-btn {
  background: linear-gradient(90deg, #667eea, #764ba2);
  color: #fff;
  border: none;
  border-radius: 50rpx;
  padding: 24rpx 60rpx;
  font-size: 28rpx;
  font-weight: 500;
  box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
}

.login-btn:active {
  transform: translateY(-2rpx);
  box-shadow: 0 12rpx 32rpx rgba(102, 126, 234, 0.4);
}

.btn-text {
  color: #fff;
}

/* ===== 广告区域 ===== */
.ad-section {
  margin: 0 30rpx 40rpx;
}

.ad-container {
  background: #fff;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.08);
}

.ad-header {
  padding: 20rpx 30rpx;
  background: #f8f9fa;
  border-bottom: 2rpx solid #e9ecef;
}

.ad-section-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #666;
}

.ad-banner {
  position: relative;
  height: 240rpx;
  overflow: hidden;
}

.ad-image {
  width: 100%;
  height: 100%;
}

.ad-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
  padding: 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
}

.ad-content {
  flex: 1;
  color: #fff;
}

.ad-title {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
}

.ad-subtitle {
  display: block;
  font-size: 24rpx;
  opacity: 0.9;
}

.ad-action {
  margin-left: 20rpx;
}

.ad-btn {
  background: rgba(255, 255, 255, 0.2);
  color: #fff;
  padding: 12rpx 24rpx;
  border-radius: 30rpx;
  font-size: 24rpx;
  backdrop-filter: blur(10rpx);
  border: 2rpx solid rgba(255, 255, 255, 0.3);
}

/* ===== 底部安全区域 ===== */
.bottom-safe-area {
  height: 120rpx;
  height: calc(120rpx + env(safe-area-inset-bottom));
}

/* ===== 响应式设计 ===== */
@media (max-width: 375px) {
  .banner-section {
    height: 360rpx;
  }
  
  .card-title, .alliance-title, .stats-title, .login-title {
    font-size: 28rpx;
  }
  
  .stats-grid {
    gap: 16rpx;
  }
}

/* ===== 深色模式适配 ===== */
@media (prefers-color-scheme: dark) {
  .container {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  }
  
  .wifi-action-card,
  .alliance-card,
  .stats-card,
  .login-card,
  .ad-container {
    background: #2c3e50;
    color: #ecf0f1;
  }
  
  .card-title,
  .alliance-title,
  .stats-title,
  .login-title {
    color: #ecf0f1;
  }
  
  .card-subtitle,
  .alliance-subtitle,
  .login-subtitle {
    color: #bdc3c7;
  }
}

/* ===== 动画效果 ===== */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.wifi-action-card,
.alliance-card,
.stats-card,
.login-card,
.ad-container {
  animation: fadeInUp 0.6s ease-out;
}

.wifi-action-card:nth-child(2) {
  animation-delay: 0.1s;
}

.alliance-card {
  animation-delay: 0.2s;
}

.stats-card {
  animation-delay: 0.3s;
}

.ad-container {
  animation-delay: 0.4s;
} 