/**
 * 开发环境配置
 */
module.exports = {
  // 数据库配置
  database: {
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 3306,
    username: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || 'password',
    database: process.env.DB_NAME || 'mall',
    connectionLimit: 10
  },
  
  // JWT配置
  jwt: {
    secret: process.env.JWT_SECRET || 'wifi_share_dev_secret',
    expiresIn: process.env.JWT_EXPIRES_IN || '7d'
  },
  
  // 腾讯云COS对象存储配置
  cos: {
    secretId: process.env.COS_SECRET_ID || '',
    secretKey: process.env.COS_SECRET_KEY || '',
    region: process.env.COS_REGION || 'ap-guangzhou',
    bucket: process.env.COS_BUCKET || 'wifi-share-dev'
  },
  
  // 微信小程序配置
  wechat: {
    appId: process.env.WECHAT_APPID || 'wxd57d522936cb95a1',
    appSecret: process.env.WECHAT_SECRET || 'c80e673ab03d444e7158269103cb04d0'
  },
  
  // 跨域配置
  cors: {
    origin: '*', // 开发环境允许所有来源
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With', 'Accept', 'Origin'],
    exposedHeaders: ['Content-Length', 'Content-Type', 'Content-Disposition'],
    credentials: true
  },
  
  // 是否开启调试模式
  debug: true,
  
  // 上传文件配置
  upload: {
    baseDir: 'uploads',
    maxSize: 5 * 1024 * 1024, // 5MB
    allowTypes: ['image/jpeg', 'image/png', 'image/gif']
  }
}; 