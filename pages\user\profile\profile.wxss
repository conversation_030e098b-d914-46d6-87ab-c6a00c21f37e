/* pages/user/profile/profile.wxss */
.profile-container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 30rpx;
}

/* 广告区域 */
.ad-banner {
  position: relative;
  width: 100%;
  height: 200rpx;
}

.ad-image {
  width: 100%;
  height: 100%;
}

.ad-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.3);
}

.ad-text {
  color: white;
  font-size: 36rpx;
  font-weight: bold;
}

/* 腾讯广告容器样式 */
.ad-container {
  width: 100%;
  margin: 20rpx 0;
  padding: 0;
  display: flex;
  justify-content: center;
  background-color: #fff;
}

/* 用户信息区域 */
.user-section {
  background-color: white;
  padding: 30rpx;
  margin-bottom: 20rpx;
  border-radius: 10rpx;
}

.user-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.user-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.user-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  margin-right: 20rpx;
}

/* 头像选择按钮样式 */
.avatar-button {
  padding: 0;
  background: none;
  border: none;
  margin: 0;
  line-height: normal;
  position: relative;
}

.avatar-button::after {
  border: none;
}

.avatar-edit-icon {
  position: absolute;
  bottom: 0;
  right: 20rpx;
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 10rpx;
}

.user-detail {
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.user-nickname {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

/* 昵称输入框样式 */
.nickname-input {
  font-size: 32rpx;
  border-bottom: 1px solid #eee;
  padding: 8rpx 0;
  width: 100%;
  margin-bottom: 10rpx;
}

.user-id {
  font-size: 24rpx;
  color: #999;
}

.user-demote {
  font-size: 22rpx;
  color: #ff6b6b;
  margin-top: 6rpx;
  text-decoration: underline;
}

.user-right {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.logout-btn {
  font-size: 28rpx;
  padding: 10rpx 30rpx;
  background-color: #f8f8f8;
  color: #333;
  border-radius: 30rpx;
  border: 1px solid #ddd;
  margin: 0;
  line-height: 1.5;
}

.save-btn {
  font-size: 28rpx;
  padding: 10rpx 30rpx;
  background-color: #ff6b6b;
  color: white;
  border-radius: 30rpx;
  margin: 10rpx 0 0 0;
  line-height: 1.5;
}

.real-info-btn {
  font-size: 24rpx;
  padding: 8rpx 20rpx;
  background-color: #4CAF50;
  color: white;
  border-radius: 30rpx;
  border: none;
  margin: 5rpx 0 0 0;
  line-height: 1.5;
}

.get-avatar-btn {
  font-size: 24rpx;
  padding: 8rpx 20rpx;
  background-color: #07c160;
  color: white;
  border-radius: 30rpx;
  border: none;
  margin: 5rpx 0 0 0;
  line-height: 1.5;
}

.nickname-input-new {
  font-size: 24rpx;
  padding: 8rpx 20rpx;
  background-color: #f5f5f5;
  border: 1rpx solid #ddd;
  border-radius: 30rpx;
  margin: 5rpx 0 0 0;
  line-height: 1.5;
}

.save-info-btn {
  font-size: 24rpx;
  padding: 8rpx 20rpx;
  background-color: #ff6b35;
  color: white;
  border-radius: 30rpx;
  border: none;
  margin: 5rpx 0 0 0;
  line-height: 1.5;
}

/* 订单区域 */
.order-section {
  background-color: white;
  padding: 30rpx;
  margin-bottom: 20rpx;
  border-radius: 10rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
}

.section-action {
  display: flex;
  align-items: center;
}

.action-text {
  font-size: 28rpx;
  color: #999;
  margin-right: 10rpx;
}

.arrow-icon {
  font-size: 24rpx;
  color: #999;
}

.order-status-list {
  display: flex;
  justify-content: space-between;
}

.order-status-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.status-icon-wrapper {
  position: relative;
  margin-bottom: 10rpx;
}

.status-icon {
  font-size: 48rpx;
}

.status-badge {
  position: absolute;
  top: -10rpx;
  right: -10rpx;
  background-color: #ff6b6b;
  color: white;
  font-size: 20rpx;
  min-width: 32rpx;
  height: 32rpx;
  border-radius: 16rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0 6rpx;
}

.status-text {
  font-size: 24rpx;
  color: #333;
}

/* 菜单区域 */
.menu-section {
  background-color: white;
  border-radius: 10rpx;
  margin-bottom: 20rpx;
}

.menu-row {
  display: flex;
  border-bottom: 1rpx solid #f5f5f5;
}

.menu-item {
  flex: 1;
  padding: 30rpx;
  display: flex;
  align-items: center;
  position: relative;
}

.menu-divider {
  width: 1rpx;
  background-color: #f5f5f5;
}

.menu-icon {
  font-size: 36rpx;
  margin-right: 20rpx;
}

.menu-text {
  flex: 1;
  font-size: 28rpx;
}

.menu-badge {
  background-color: #ff6b6b;
  color: white;
  font-size: 20rpx;
  min-width: 32rpx;
  height: 32rpx;
  border-radius: 16rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0 6rpx;
  margin-right: 10rpx;
}

.menu-arrow {
  font-size: 24rpx;
  color: #999;
}

.menu-single-row {
  border-bottom: 1rpx solid #f5f5f5;
}

.menu-item-full {
  padding: 30rpx;
  display: flex;
  align-items: center;
}

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  padding: 30rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}

/* 空状态 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 50rpx 30rpx;
  background-color: white;
  border-radius: 10rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 30rpx;
}

.login-btn {
  font-size: 28rpx;
  padding: 15rpx 40rpx;
  background-color: #ff6b6b;
  color: white;
  border-radius: 30rpx;
}

/* 客服按钮样式 */
.contact-button {
  background: none;
  padding: 0;
  margin: 0;
  line-height: 1;
  border: none;
  display: flex;
  align-items: center;
  width: 100%;
  text-align: left;
  font-weight: normal;
  font-size: inherit;
}

.contact-button::after {
  border: none;
}

/* 头像昵称编辑弹窗样式 */
.avatar-edit-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 600rpx;
  background-color: white;
  border-radius: 20rpx;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #eee;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.modal-close {
  font-size: 40rpx;
  color: #999;
  cursor: pointer;
}

.modal-body {
  padding: 30rpx;
}

.avatar-section, .nickname-section {
  margin-bottom: 30rpx;
}

.section-title {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 20rpx;
}

.avatar-choose-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx;
  border: 2rpx dashed #ddd;
  border-radius: 10rpx;
  background-color: #fafafa;
  transition: all 0.3s ease;
}

.avatar-choose-btn:active {
  background-color: #f0f0f0;
  border-color: #ccc;
}

.avatar-choose-btn.choosing {
  opacity: 0.6;
  background-color: #f5f5f5;
}

.avatar-choose-btn[disabled] {
  opacity: 0.6;
  background-color: #f5f5f5;
}

.avatar-choose-btn::after {
  border: none;
}

.preview-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  margin-bottom: 10rpx;
  border: 2rpx solid #eee;
}

.choose-text {
  font-size: 24rpx;
  color: #666;
  text-align: center;
}

/* 头像提示信息 */
.avatar-tip {
  margin: 15rpx 0;
  padding: 12rpx 16rpx;
  background-color: #e8f4fd;
  border: 1rpx solid #bee5eb;
  border-radius: 8rpx;
}

.avatar-tip .tip-text {
  font-size: 22rpx;
  color: #0c5460;
  line-height: 1.5;
  text-align: center;
}

/* 开发者工具提示（保留兼容性） */
.dev-tip {
  margin: 15rpx 0;
  padding: 10rpx 15rpx;
  background-color: #fff3cd;
  border: 1rpx solid #ffeaa7;
  border-radius: 6rpx;
}

.dev-tip .tip-text {
  font-size: 22rpx;
  color: #856404;
  line-height: 1.4;
}

/* 头像选择选项 */
.avatar-options {
  display: flex;
  justify-content: space-between;
  margin-top: 20rpx;
  gap: 15rpx;
}

.option-btn {
  flex: 1;
  padding: 16rpx 12rpx;
  font-size: 22rpx;
  border-radius: 8rpx;
  text-align: center;
  transition: all 0.3s ease;
}

.option-btn::after {
  border: none;
}

/* 主要按钮样式 */
.option-btn.primary {
  color: #007aff;
  background-color: #f0f8ff;
  border: 1rpx solid #007aff;
}

.option-btn.primary:active {
  background-color: #007aff;
  color: white;
}

/* 次要按钮样式 */
.option-btn.secondary {
  color: #28a745;
  background-color: #f0fff4;
  border: 1rpx solid #28a745;
}

.option-btn.secondary:active {
  background-color: #28a745;
  color: white;
}

/* 默认按钮样式 */
.option-btn.default {
  color: #6c757d;
  background-color: #f8f9fa;
  border: 1rpx solid #dee2e6;
}

.option-btn.default:active {
  background-color: #6c757d;
  color: white;
}

/* 禁用状态 */
.option-btn[disabled] {
  opacity: 0.5;
  background-color: #f5f5f5 !important;
  color: #999 !important;
  border-color: #ddd !important;
}

.option-btn[disabled] {
  opacity: 0.5;
  background-color: #f5f5f5;
  color: #999;
  border-color: #ddd;
}

/* 头像选择中状态 */
.avatar-choose-btn.choosing {
  opacity: 0.7;
  background-color: #f0f0f0;
}

.avatar-choose-btn[disabled] {
  opacity: 0.6;
}

/* 开发者工具提示 */
.dev-tip {
  margin-top: 20rpx;
  padding: 16rpx;
  background-color: #fff3cd;
  border: 1rpx solid #ffeaa7;
  border-radius: 8rpx;
}

.tip-text {
  font-size: 22rpx;
  color: #856404;
  line-height: 1.4;
}

.nickname-input-modal {
  width: 100%;
  padding: 20rpx;
  border: 1rpx solid #ddd;
  border-radius: 10rpx;
  font-size: 28rpx;
}

.modal-footer {
  display: flex;
  border-top: 1rpx solid #eee;
}

.cancel-btn, .save-btn {
  flex: 1;
  padding: 30rpx;
  font-size: 28rpx;
  border: none;
  background-color: white;
}

.cancel-btn::after, .save-btn::after {
  border: none;
}

.cancel-btn {
  color: #666;
  border-right: 1rpx solid #eee;
}

.save-btn {
  color: #07c160;
  font-weight: bold;
}

/* 昵称输入提示 */
.nickname-tip {
  margin-top: 10rpx;
  padding: 8rpx 12rpx;
  background-color: #f0f9ff;
  border: 1rpx solid #bfdbfe;
  border-radius: 6rpx;
}

.nickname-tip .tip-text {
  font-size: 22rpx;
  color: #1e40af;
  line-height: 1.4;
}