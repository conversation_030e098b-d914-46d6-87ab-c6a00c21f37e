/* pages/user/address/edit.wxss */
.address-edit-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 120rpx;
}

/* 表单样式 */
.form-section {
  background-color: #fff;
  padding: 0 30rpx;
  margin-top: 20rpx;
}

.form-group {
  padding: 30rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.form-group:last-child {
  border-bottom: none;
}

.form-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 20rpx;
}

.form-input {
  width: 100%;
  height: 80rpx;
  font-size: 28rpx;
  color: #333;
}

.form-textarea {
  width: 100%;
  min-height: 120rpx;
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
}

.switch-group {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.switch-group .form-label {
  margin-bottom: 0;
}

/* 地区选择器样式 */
.region-picker {
  width: 100%;
  height: 80rpx;
  display: flex;
  align-items: center;
}

.picker-value {
  font-size: 28rpx;
  color: #333;
}

.picker-value.placeholder {
  color: #999;
}

/* 使用微信收货地址按钮样式 */
.wechat-address-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fff;
  height: 100rpx;
  margin: 20rpx 0;
}

.wechat-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 10rpx;
}

.wechat-text {
  font-size: 28rpx;
  color: #07c160;
}

/* 保存按钮样式 */
.save-btn {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100rpx;
  background-color: #ff6b00;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  font-weight: 500;
}

.save-btn.disabled {
  background-color: #ccc;
}

/* 加载图标样式 */
.loading-icon {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
  border-top: 4rpx solid #fff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
} 