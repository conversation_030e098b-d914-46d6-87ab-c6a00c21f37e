const db = require('./src/database');

async function checkAllUsers() {
  try {
    console.log('🔍 检查所有用户...\n');
    
    // 查询所有用户
    const users = await db.query('SELECT id, nickname, phone, team_id, is_leader FROM user ORDER BY id');
    console.log(`📊 总共有 ${users.length} 个用户:`);
    console.table(users);
    
    // 查询最近的联盟申请
    console.log('\n📋 最近的联盟申请:');
    const applications = await db.query(`
      SELECT id, user_id, name, status, created_at, updated_at
      FROM team_apply 
      ORDER BY id DESC 
      LIMIT 3
    `);
    console.table(applications);
    
    // 检查团队和用户的关联
    console.log('\n🔗 团队和用户关联检查:');
    const teamUserCheck = await db.query(`
      SELECT 
        t.id as team_id,
        t.name as team_name,
        t.leader_id,
        u.nickname as leader_nickname,
        u.team_id as user_team_id,
        u.is_leader
      FROM team t
      LEFT JOIN user u ON t.leader_id = u.id
      ORDER BY t.id DESC
      LIMIT 5
    `);
    console.table(teamUserCheck);
    
    process.exit(0);
  } catch (error) {
    console.error('❌ 检查失败:', error);
    process.exit(1);
  }
}

checkAllUsers();
