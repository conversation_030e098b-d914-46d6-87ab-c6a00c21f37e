// utils/imageService.js
// 图片服务工具类

/**
 * 图片服务配置
 */
const ImageService = {
  // 服务器配置
  serverConfig: {
    baseUrl: 'http://localhost:4000',
    uploadsPath: '/uploads/images/',
    timeout: 5000
  },

  // 占位图配置
  placeholders: {
    svg: '/assets/images/goods-placeholder.svg',
    base64: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjVmNWY1Ii8+CiAgPHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPuWVhuWTgeWbvueJhzwvdGV4dD4KPC9zdmc+'
  },

  /**
   * 检查图片URL是否可访问
   * @param {string} imageUrl 图片URL
   * @returns {Promise<boolean>} 是否可访问
   */
  async checkImageAvailability(imageUrl) {
    return new Promise((resolve) => {
      const img = wx.createImage()
      
      img.onload = () => {
        console.log('✅ 图片可访问:', imageUrl)
        resolve(true)
      }
      
      img.onerror = () => {
        console.log('❌ 图片不可访问:', imageUrl)
        resolve(false)
      }
      
      // 设置超时
      setTimeout(() => {
        console.log('⏰ 图片检查超时:', imageUrl)
        resolve(false)
      }, this.serverConfig.timeout)
      
      img.src = imageUrl
    })
  },

  /**
   * 获取优化的图片URL
   * @param {string} originalUrl 原始图片URL
   * @returns {string} 优化后的图片URL
   */
  getOptimizedImageUrl(originalUrl) {
    if (!originalUrl) {
      return this.placeholders.svg
    }

    // 如果是完整URL，直接返回
    if (originalUrl.startsWith('http://') || originalUrl.startsWith('https://')) {
      return originalUrl
    }

    // 如果是本地资源，直接返回
    if (originalUrl.startsWith('/assets/')) {
      return originalUrl
    }

    // 如果是uploads路径，构建完整URL
    if (originalUrl.startsWith('/uploads/')) {
      return `${this.serverConfig.baseUrl}${originalUrl}`
    }

    // 其他情况返回占位图
    return this.placeholders.svg
  },

  /**
   * 批量检查图片可用性
   * @param {Array} imageUrls 图片URL数组
   * @returns {Promise<Object>} 检查结果
   */
  async batchCheckImages(imageUrls) {
    const results = {
      total: imageUrls.length,
      available: 0,
      unavailable: 0,
      details: []
    }

    for (let i = 0; i < imageUrls.length; i++) {
      const url = imageUrls[i]
      const isAvailable = await this.checkImageAvailability(url)
      
      results.details.push({
        url: url,
        available: isAvailable,
        index: i
      })

      if (isAvailable) {
        results.available++
      } else {
        results.unavailable++
      }
    }

    console.log('📊 图片批量检查结果:', results)
    return results
  },

  /**
   * 生成服务器配置诊断报告
   * @returns {Object} 诊断报告
   */
  generateDiagnosticReport() {
    const report = {
      timestamp: new Date().toISOString(),
      serverConfig: this.serverConfig,
      commonIssues: [
        {
          issue: '静态文件服务未配置',
          description: 'Web服务器未正确配置/uploads/路径的静态文件服务',
          solutions: [
            '检查Nginx配置中的location /uploads/块',
            '确认Express.js中app.use(\'/uploads\', express.static())配置',
            '验证Apache中的Directory配置'
          ]
        },
        {
          issue: '文件权限问题',
          description: 'uploads目录或图片文件权限不正确',
          solutions: [
            '检查uploads目录权限: chmod 755 uploads/',
            '检查图片文件权限: chmod 644 uploads/images/*',
            '确认Web服务器用户有读取权限'
          ]
        },
        {
          issue: '文件路径不存在',
          description: '图片文件实际不存在于服务器上',
          solutions: [
            '检查文件是否真实存在',
            '验证上传功能是否正常工作',
            '清理数据库中的无效图片记录'
          ]
        },
        {
          issue: '服务器未启动',
          description: '后端服务器未运行或端口不正确',
          solutions: [
            '确认后端服务器在localhost:4000运行',
            '检查防火墙设置',
            '验证端口是否被占用'
          ]
        }
      ],
      recommendations: [
        '使用云存储服务(阿里云OSS、腾讯云COS)替代本地存储',
        '实现图片CDN加速',
        '添加图片上传时的文件验证',
        '定期清理无效的图片记录',
        '实现图片缩略图生成',
        '添加图片访问日志监控'
      ]
    }

    return report
  },

  /**
   * 获取图片错误统计
   * @returns {Object} 错误统计
   */
  getImageErrorStats() {
    const stats = wx.getStorageSync('imageErrorStats') || {
      totalErrors: 0,
      errorUrls: {},
      lastUpdate: Date.now()
    }

    return stats
  },

  /**
   * 记录图片错误
   * @param {string} imageUrl 出错的图片URL
   */
  recordImageError(imageUrl) {
    const stats = this.getImageErrorStats()
    stats.totalErrors++
    stats.errorUrls[imageUrl] = (stats.errorUrls[imageUrl] || 0) + 1
    stats.lastUpdate = Date.now()

    wx.setStorageSync('imageErrorStats', stats)
    
    // 如果错误太多，显示提示
    if (stats.totalErrors > 10 && stats.totalErrors % 10 === 0) {
      this.showServerConfigTip()
    }
  },

  /**
   * 显示服务器配置提示
   */
  showServerConfigTip() {
    wx.showModal({
      title: '图片服务提示',
      content: '检测到多个图片加载失败，建议检查服务器配置。是否查看解决方案？',
      confirmText: '查看方案',
      cancelText: '稍后处理',
      success: (res) => {
        if (res.confirm) {
          this.showConfigSolutions()
        }
      }
    })
  },

  /**
   * 显示配置解决方案
   */
  showConfigSolutions() {
    const solutions = [
      '1. 检查后端服务器是否运行在localhost:4000',
      '2. 确认/uploads/images/目录存在且有正确权限',
      '3. 验证Web服务器静态文件配置',
      '4. 检查图片文件是否真实存在',
      '5. 考虑使用云存储服务'
    ]

    wx.showModal({
      title: '服务器配置解决方案',
      content: solutions.join('\n'),
      showCancel: false,
      confirmText: '我知道了'
    })
  },

  /**
   * 清理错误统计
   */
  clearErrorStats() {
    wx.removeStorageSync('imageErrorStats')
    console.log('✅ 图片错误统计已清理')
  }
}

module.exports = ImageService
