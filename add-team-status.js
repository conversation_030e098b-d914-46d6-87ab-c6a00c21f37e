const mysql = require('mysql2/promise');

async function addTeamStatus() {
  let connection;
  
  try {
    // 创建数据库连接
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: 'wo587129955',
      database: 'mall'
    });

    console.log('✅ 数据库连接成功');

    // 检查是否已有status字段
    const [tableStructure] = await connection.execute('DESCRIBE team');
    const hasStatus = tableStructure.some(field => field.Field === 'status');
    
    if (hasStatus) {
      console.log('✅ status字段已存在');
    } else {
      console.log('🔧 添加status字段...');
      
      // 添加status字段
      await connection.execute(`
        ALTER TABLE team 
        ADD COLUMN status TINYINT DEFAULT 1 COMMENT '状态：0-禁用，1-正常'
      `);
      
      console.log('✅ 已添加status字段');
    }
    
    // 更新所有现有团队的状态为正常
    console.log('🔄 更新所有团队状态为正常...');
    const [updateResult] = await connection.execute(`
      UPDATE team SET status = 1 WHERE status IS NULL OR status = 0
    `);
    
    console.log(`✅ 已更新 ${updateResult.affectedRows} 个团队的状态`);
    
    // 查看更新后的团队数据
    console.log('\n📊 更新后的团队数据:');
    const [teams] = await connection.execute(`
      SELECT t.id, t.name, t.leader_id, t.status, u.nickname as leader_name
      FROM team t
      LEFT JOIN user u ON t.leader_id = u.id
      ORDER BY t.created_at DESC
    `);
    
    if (teams && teams.length > 0) {
      console.table(teams.map(team => ({
        团队ID: team.id,
        团队名称: team.name,
        团长ID: team.leader_id,
        团长昵称: team.leader_name,
        状态: team.status === 1 ? '正常' : '禁用'
      })));
    } else {
      console.log('❌ 没有找到团队数据');
    }
    
    console.log('\n✅ 团队状态修复完成！');
    
  } catch (error) {
    console.error('❌ 操作失败:', error);
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 数据库连接已关闭');
    }
  }
}

addTeamStatus();
