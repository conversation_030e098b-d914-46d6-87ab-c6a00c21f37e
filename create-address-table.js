// 创建用户地址表的脚本
const mysql = require('mysql2/promise');

async function createAddressTable() {
  let connection;
  
  try {
    console.log('开始创建用户地址表...');
    
    // 直接创建数据库连接，使用正确的密码
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: 'wo587129955', // 使用正确的密码
      database: 'mall' // 使用mall数据库
    });
    
    console.log('数据库连接成功');
    
    // 检查数据库中是否存在user_address表
    const [tables] = await connection.query(
      "SHOW TABLES LIKE 'user_address'"
    );
    
    if (tables.length > 0) {
      console.log('user_address表已存在，无需创建');
    } else {
      console.log('user_address表不存在，开始创建...');
      
      // 创建user_address表
      await connection.query(`
        CREATE TABLE user_address (
          id INT AUTO_INCREMENT PRIMARY KEY,
          user_id INT NOT NULL,
          name VARCHAR(50) NOT NULL,
          phone VARCHAR(20) NOT NULL,
          province VARCHAR(50) NOT NULL,
          city VARCHAR(50) NOT NULL,
          district VARCHAR(50) NOT NULL,
          address VARCHAR(255) NOT NULL,
          is_default TINYINT(1) DEFAULT 0,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          INDEX (user_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户收货地址表';
      `);
      
      console.log('user_address表创建成功');
    }
    
    // 验证表是否创建成功
    const [checkTable] = await connection.query(
      "DESCRIBE user_address"
    );
    
    console.log('表结构验证成功，字段数量:', checkTable.length);
    console.log('表字段:', checkTable.map(field => field.Field).join(', '));
    
    console.log('用户地址表创建和验证完成');
  } catch (error) {
    console.error('创建用户地址表失败:', error);
    console.error('错误详情:', JSON.stringify(error, null, 2));
  } finally {
    if (connection) {
      try {
        await connection.end();
        console.log('数据库连接已关闭');
      } catch (err) {
        console.error('关闭数据库连接失败:', err);
      }
    }
  }
}

// 执行创建表操作
createAddressTable().then(() => {
  console.log('脚本执行完毕');
  process.exit(0);
}).catch(err => {
  console.error('脚本执行失败:', err);
  process.exit(1);
}); 