const mysql = require('mysql2/promise');

// 数据库配置
const dbConfig = {
  host: 'localhost',
  user: 'root',
  password: 'wo587129955',
  database: 'wifi_share_system'
};

async function initTestAddresses() {
  let connection;
  
  try {
    console.log('🔗 连接数据库...');
    connection = await mysql.createConnection(dbConfig);
    
    // 检查是否已有地址数据
    const [existingAddresses] = await connection.execute(
      'SELECT COUNT(*) as count FROM user_address'
    );
    
    console.log(`📊 当前地址数量: ${existingAddresses[0].count}`);
    
    if (existingAddresses[0].count > 0) {
      console.log('✅ 数据库中已有地址数据');
      
      // 显示现有地址
      const [addresses] = await connection.execute(
        'SELECT id, user_id, name, phone, province, city, district, is_default FROM user_address ORDER BY user_id, is_default DESC'
      );
      
      console.log('\n📋 现有地址列表:');
      addresses.forEach(addr => {
        console.log(`  ID: ${addr.id}, 用户: ${addr.user_id}, 姓名: ${addr.name}, 地址: ${addr.province}${addr.city}${addr.district}, 默认: ${addr.is_default ? '是' : '否'}`);
      });
      return;
    }
    
    console.log('📝 插入测试地址数据...');
    
    // 插入测试地址数据
    const testAddresses = [
      {
        user_id: 1,
        name: '张三',
        phone: '13800138000',
        province: '广东省',
        city: '深圳市',
        district: '南山区',
        address: '科技园南区深南大道10000号',
        is_default: 1
      },
      {
        user_id: 1,
        name: '张三',
        phone: '13800138000',
        province: '广东省',
        city: '深圳市',
        district: '福田区',
        address: '华强北路1000号',
        is_default: 0
      },
      {
        user_id: 2,
        name: '李四',
        phone: '13900139000',
        province: '北京市',
        city: '北京市',
        district: '朝阳区',
        address: '三里屯路100号',
        is_default: 1
      }
    ];
    
    for (const address of testAddresses) {
      await connection.execute(
        `INSERT INTO user_address (user_id, name, phone, province, city, district, address, is_default, created_at, updated_at)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())`,
        [
          address.user_id,
          address.name,
          address.phone,
          address.province,
          address.city,
          address.district,
          address.address,
          address.is_default
        ]
      );
      
      console.log(`✅ 插入地址: ${address.name} - ${address.province}${address.city}${address.district}`);
    }
    
    console.log('🎉 测试地址数据初始化完成！');
    
    // 验证插入结果
    const [addresses] = await connection.execute(
      'SELECT id, user_id, name, phone, province, city, district, is_default FROM user_address ORDER BY user_id, is_default DESC'
    );
    
    console.log('\n📋 当前地址列表:');
    addresses.forEach(addr => {
      console.log(`  ID: ${addr.id}, 用户: ${addr.user_id}, 姓名: ${addr.name}, 地址: ${addr.province}${addr.city}${addr.district}, 默认: ${addr.is_default ? '是' : '否'}`);
    });
    
  } catch (error) {
    console.error('❌ 初始化测试地址失败:', error);
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔚 数据库连接已关闭');
    }
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  initTestAddresses();
}

module.exports = { initTestAddresses };
