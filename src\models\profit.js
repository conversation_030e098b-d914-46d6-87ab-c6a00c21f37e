const db = require('../database');
const logger = require('../utils/logger');

/**
 * 分润模型
 */
class ProfitModel {
  /**
   * 创建分润记录
   * @param {object} data 分润数据
   * @returns {Promise<object>} 分润记录
   */
  static async create(data) {
    try {
      const { 
        userId, amount, sourceType, sourceId, 
        description, orderNo, status = 0 
      } = data;
      
      const sql = `
        INSERT INTO profits (
          user_id, amount, source_type, source_id, 
          description, order_no, status, 
          created_at, updated_at
        )
        VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
      `;
      
      const result = await db.query(sql, [
        userId, amount, sourceType, sourceId, 
        description, orderNo, status
      ]);
      
      if (result.insertId) {
        // 如果状态为已结算(1)，更新用户余额
        if (status === 1) {
          await db.query(
            'UPDATE users SET balance = balance + ? WHERE id = ?',
            [amount, userId]
          );
        }
        
        return {
          id: result.insertId,
          ...data,
          createdAt: new Date()
        };
      } else {
        throw new Error('创建分润记录失败');
      }
    } catch (error) {
      logger.error(`创建分润记录失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 获取用户分润列表
   * @param {number} userId 用户ID
   * @param {object} params 查询参数
   * @returns {Promise<object>} 分润列表和分页信息
   */
  static async list({ userId, sourceType, status, startDate, endDate, page = 1, limit = 10 }) {
    try {
      let whereConditions = ['user_id = ?'];
      const params = [userId];
      
      // 来源类型筛选
      if (sourceType) {
        whereConditions.push('source_type = ?');
        params.push(sourceType);
      }
      
      // 状态筛选
      if (status !== undefined) {
        whereConditions.push('status = ?');
        params.push(status);
      }
      
      // 日期范围筛选
      if (startDate) {
        whereConditions.push('DATE(created_at) >= ?');
        params.push(startDate);
      }
      
      if (endDate) {
        whereConditions.push('DATE(created_at) <= ?');
        params.push(endDate);
      }
      
      const whereClause = whereConditions.join(' AND ');
      const offset = (page - 1) * limit;
      
      const countSql = `SELECT COUNT(*) as total FROM profits WHERE ${whereClause}`;
      const listSql = `
        SELECT id, user_id, amount, source_type, source_id, 
               description, order_no, status, created_at, updated_at
        FROM profits
        WHERE ${whereClause}
        ORDER BY created_at DESC
        LIMIT ?, ?
      `;
      
      const [countResult, listResult] = await Promise.all([
        db.query(countSql, params),
        db.query(listSql, [...params, offset, parseInt(limit)])
      ]);
      
      const total = countResult[0].total;
      
      // 统计总金额
      const statsSql = `
        SELECT 
          SUM(amount) as total_amount,
          SUM(CASE WHEN status = 1 THEN amount ELSE 0 END) as settled_amount,
          SUM(CASE WHEN status = 0 THEN amount ELSE 0 END) as unsettled_amount
        FROM profits
        WHERE ${whereClause}
      `;
      
      const statsResult = await db.query(statsSql, params);
      
      return {
        list: listResult,
        stats: {
          totalAmount: statsResult[0].total_amount || 0,
          settledAmount: statsResult[0].settled_amount || 0,
          unsettledAmount: statsResult[0].unsettled_amount || 0
        },
        pagination: {
          total,
          page: parseInt(page),
          limit: parseInt(limit),
          pages: Math.ceil(total / limit)
        }
      };
    } catch (error) {
      logger.error(`获取用户分润列表失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 获取分润详情
   * @param {number} id 分润ID
   * @returns {Promise<object|null>} 分润详情
   */
  static async getById(id) {
    try {
      const sql = `
        SELECT id, user_id, amount, source_type, source_id, 
               description, order_no, status, created_at, updated_at
        FROM profits
        WHERE id = ?
        LIMIT 1
      `;
      
      const result = await db.query(sql, [id]);
      
      return result.length > 0 ? result[0] : null;
    } catch (error) {
      logger.error(`获取分润详情失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 创建提现申请
   * @param {object} data 提现数据
   * @returns {Promise<object>} 提现申请
   */
  static async createWithdraw(data) {
    try {
      return await db.transaction(async (connection) => {
        const { userId, amount, bankName, bankAccount, accountName } = data;
        
        // 检查用户余额
        const userSql = 'SELECT balance FROM users WHERE id = ? FOR UPDATE';
        const userResult = await connection.execute(userSql, [userId]);
        
        if (userResult[0].length === 0) {
          throw new Error('用户不存在');
        }
        
        const balance = parseFloat(userResult[0][0].balance);
        
        if (balance < amount) {
          throw new Error('余额不足');
        }
        
        // 扣减用户余额
        await connection.execute(
          'UPDATE users SET balance = balance - ? WHERE id = ?',
          [amount, userId]
        );
        
        // 创建提现申请
        const withdrawSql = `
          INSERT INTO withdraws (
            user_id, amount, bank_name, bank_account, account_name,
            status, created_at, updated_at
          )
          VALUES (?, ?, ?, ?, ?, 0, NOW(), NOW())
        `;
        
        const result = await connection.execute(withdrawSql, [
          userId, amount, bankName, bankAccount, accountName
        ]);
        
        return {
          id: result[0].insertId,
          ...data,
          status: 0,  // 待审核
          createdAt: new Date()
        };
      });
    } catch (error) {
      logger.error(`创建提现申请失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 获取用户提现列表
   * @param {number} userId 用户ID
   * @param {object} params 查询参数
   * @returns {Promise<object>} 提现列表和分页信息
   */
  static async getWithdraws({ userId, status, page = 1, limit = 10 }) {
    try {
      let whereConditions = ['user_id = ?'];
      const params = [userId];
      
      // 状态筛选
      if (status !== undefined) {
        whereConditions.push('status = ?');
        params.push(status);
      }
      
      const whereClause = whereConditions.join(' AND ');
      const offset = (page - 1) * limit;
      
      const countSql = `SELECT COUNT(*) as total FROM withdraws WHERE ${whereClause}`;
      const listSql = `
        SELECT id, user_id, amount, bank_name, bank_account, account_name,
               status, remark, created_at, updated_at
        FROM withdraws
        WHERE ${whereClause}
        ORDER BY created_at DESC
        LIMIT ?, ?
      `;
      
      const [countResult, listResult] = await Promise.all([
        db.query(countSql, params),
        db.query(listSql, [...params, offset, parseInt(limit)])
      ]);
      
      const total = countResult[0].total;
      
      return {
        list: listResult,
        pagination: {
          total,
          page: parseInt(page),
          limit: parseInt(limit),
          pages: Math.ceil(total / limit)
        }
      };
    } catch (error) {
      logger.error(`获取用户提现列表失败: ${error.message}`);
      throw error;
    }
  }
  
  /**
   * 获取分润规则
   * @returns {Promise<object>} 分润规则
   */
  static async getProfitRules() {
    try {
      const sql = 'SELECT * FROM profit_rules WHERE status = 1';
      const result = await db.query(sql);
      
      // 将规则转换为对象
      const rules = {};
      
      for (const rule of result) {
        if (!rules[rule.type]) {
          rules[rule.type] = {};
        }
        
        rules[rule.type][rule.key] = rule.value;
      }
      
      return rules;
    } catch (error) {
      logger.error(`获取分润规则失败: ${error.message}`);
      throw error;
    }
  }
  
  /**
   * 更新分润规则
   * @param {string} type 规则类型
   * @param {object} rules 规则数据
   * @returns {Promise<boolean>} 更新是否成功
   */
  static async updateProfitRules(type, rules) {
    try {
      return await db.transaction(async (connection) => {
        // 更新或创建规则
        for (const [key, value] of Object.entries(rules)) {
          const checkSql = 'SELECT id FROM profit_rules WHERE type = ? AND key = ?';
          const checkResult = await connection.execute(checkSql, [type, key]);
          
          if (checkResult[0].length > 0) {
            // 更新规则
            await connection.execute(
              'UPDATE profit_rules SET value = ?, updated_at = NOW() WHERE type = ? AND key = ?',
              [value, type, key]
            );
          } else {
            // 创建规则
            await connection.execute(
              'INSERT INTO profit_rules (type, key, value, status, created_at, updated_at) VALUES (?, ?, ?, 1, NOW(), NOW())',
              [type, key, value]
            );
          }
        }
        
        return true;
      });
    } catch (error) {
      logger.error(`更新分润规则失败: ${error.message}`);
      throw error;
    }
  }
}

module.exports = ProfitModel; 