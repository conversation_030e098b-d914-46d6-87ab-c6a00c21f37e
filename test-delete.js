const axios = require('axios');

// 测试删除商品API
async function testDeleteGoods() {
  try {
    console.log('开始测试删除商品API...');
    
    // 测试删除ID为11的商品
    const response = await axios.delete('http://localhost:8081/api/v1/admin/goods/delete/11', {
      headers: {
        'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6MSwidXNlcm5hbWUiOiJtcngwOTI3Iiwicm9sZSI6ImFkbWluIiwiaWF0IjoxNzUxOTQ5NjQ2LCJleHAiOjE3NTI1NTQ0NDZ9.q2UP14q9hLNHA-UH8ZzgqwZULSW9hZg7rshQ_FLFG9I',
        'Content-Type': 'application/json'
      }
    });
    
    console.log('删除成功:', response.data);
  } catch (error) {
    console.error('删除失败:', error.response ? error.response.data : error.message);
    console.error('状态码:', error.response ? error.response.status : 'N/A');
  }
}

testDeleteGoods(); 