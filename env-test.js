/**
 * 环境配置测试文件
 */
const config = require('./config');

console.log('=== 环境配置测试 ===');
console.log(`当前环境: ${config.server.nodeEnv}`);
console.log(`服务器端口: ${config.server.port}`);
console.log(`调试模式: ${config.debug ? '开启' : '关闭'}`);
console.log('=== 数据库配置 ===');
console.log(`主机: ${config.database.host}`);
console.log(`端口: ${config.database.port}`);
console.log(`用户名: ${config.database.username}`);
console.log(`数据库名: ${config.database.database}`);
console.log('=== 跨域配置 ===');
console.log(`允许来源: ${JSON.stringify(config.cors.origin)}`);

console.log('\n完整配置:');
console.log(JSON.stringify(config, null, 2)); 