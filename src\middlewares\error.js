const logger = require('../utils/logger');
const config = require('../../config');

/**
 * 全局错误处理中间件
 */
function errorMiddleware(err, req, res, next) {
  // 记录错误
  logger.error(`${err.name}: ${err.message}\n${err.stack}`);
  
  // 确定HTTP状态码
  const statusCode = err.statusCode || 500;
  
  // 准备响应
  const response = {
    status: 'error',
    message: statusCode === 500 ? '服务器内部错误' : err.message
  };
  
  // 在开发环境下，提供更多错误详情
  if (config.debug && statusCode === 500) {
    response.error = {
      name: err.name,
      message: err.message,
      stack: err.stack
    };
  }
  
  // 发送响应
  res.status(statusCode).json(response);
}

/**
 * 自定义错误类
 */
class AppError extends Error {
  constructor(message, statusCode) {
    super(message);
    this.name = this.constructor.name;
    this.statusCode = statusCode;
    Error.captureStackTrace(this, this.constructor);
  }
}

/**
 * 404错误 - 资源未找到
 */
class NotFoundError extends AppError {
  constructor(message = '请求的资源不存在') {
    super(message, 404);
  }
}

/**
 * 401错误 - 未授权
 */
class UnauthorizedError extends AppError {
  constructor(message = '未授权，请登录') {
    super(message, 401);
  }
}

/**
 * 403错误 - 禁止访问
 */
class ForbiddenError extends AppError {
  constructor(message = '您没有权限执行此操作') {
    super(message, 403);
  }
}

/**
 * 400错误 - 错误请求
 */
class BadRequestError extends AppError {
  constructor(message = '请求参数错误') {
    super(message, 400);
  }
}

/**
 * 409错误 - 资源冲突
 */
class ConflictError extends AppError {
  constructor(message = '资源冲突') {
    super(message, 409);
  }
}

/**
 * 捕获未捕获的异常
 */
process.on('uncaughtException', (err) => {
  logger.error(`未捕获的异常: ${err.message}\n${err.stack}`);
  // 在生产环境可以考虑优雅退出或重启
  if (config.server.nodeEnv === 'production') {
    console.error('发生未捕获的异常，服务将在1秒后退出');
    setTimeout(() => {
      process.exit(1);
    }, 1000);
  }
});

/**
 * 捕获未处理的Promise拒绝
 */
process.on('unhandledRejection', (reason, promise) => {
  logger.error(`未处理的Promise拒绝: ${reason}`);
});

module.exports = {
  errorMiddleware,
  AppError,
  NotFoundError,
  UnauthorizedError,
  ForbiddenError,
  BadRequestError,
  ConflictError
}; 