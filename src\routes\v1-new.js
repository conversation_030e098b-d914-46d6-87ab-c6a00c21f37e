/**
 * API v1 版本路由配置
 */

const express = require('express');
const router = express.Router();

// 引入各个模块的路由
const withdrawRouter = require('./withdraw');
const incomeRouter = require('./income');
const teamRouter = require('./team');
const cartRouter = require('./cart-simple');
const goodsController = require('../controllers/goods');

// 配置提现相关路由
router.use('/client/withdraw', withdrawRouter);

// 配置收入相关路由
router.use('/client/income', incomeRouter);
console.log('✅ 已注册收入路由: /api/v1/client/income');

// 配置团队相关路由
router.use('/client/team', teamRouter);
console.log('✅ 已注册团队路由: /api/v1/client/team');

// 配置购物车相关路由
router.use('/client/cart', cartRouter);
console.log('✅ 已注册购物车路由: /api/v1/client/cart');

// 配置商品相关路由
router.get('/client/goods/list', (req, res) => {
  console.log('直接处理客户端商品列表请求，无需认证');
  goodsController.getGoodsList(req, res);
});

router.get('/client/goods/detail/:id', (req, res) => {
  console.log('直接处理客户端商品详情请求，无需认证');
  goodsController.getGoodsDetail(req, res);
});

router.get('/client/goods/categories', (req, res) => {
  console.log('直接处理客户端商品分类请求，无需认证');
  goodsController.getCategories(req, res);
});

console.log('✅ 已注册商品路由: /api/v1/client/goods');

// 健康检查接口
router.get('/health', (req, res) => {
  res.json({
    success: true,
    message: 'API v1 服务正常',
    timestamp: new Date().toISOString(),
    version: '1.0.0'
  });
});

// 404处理
router.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: '未找到请求的资源',
    path: req.originalUrl,
    method: req.method
  });
});

module.exports = router;
