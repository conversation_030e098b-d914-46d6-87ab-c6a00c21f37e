const db = require('../database');
const bcrypt = require('bcryptjs');
const logger = require('../utils/logger');

/**
 * 获取用户表名
 * @returns {string} 表名
 */
function getUserTableName() {
  // 从日志看，表名为"user"，直接返回
  logger.info(`使用固定表名 user 操作用户数据`);
  return 'user';
}

/**
 * 确保用户表中有is_demote字段
 * @returns {Promise<void>}
 */
async function ensureUserTableHasDemoteField() {
  try {
    const tableName = getUserTableName();
    
    // 检查表是否存在is_demote字段
    const columns = await db.query(`SHOW COLUMNS FROM ${tableName} LIKE 'is_demote'`);
    
    // 如果字段不存在，添加它
    if (columns.length === 0) {
      logger.info(`向${tableName}表添加is_demote字段`);
      await db.query(`ALTER TABLE ${tableName} ADD COLUMN is_demote TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否为降级用户（拒绝授权）'`);
      logger.info('is_demote字段添加成功');
    } else {
      logger.info('is_demote字段已存在');
    }
  } catch (error) {
    logger.error(`确保is_demote字段存在失败: ${error.message}`);
  }
}

// 在模块加载时确保字段存在
(async function() {
  try {
    await ensureUserTableHasDemoteField();
  } catch (error) {
    logger.error(`初始化用户表字段失败: ${error.message}`);
  }
})();

/**
 * 用户模型
 */
const User = {
  /**
   * 根据ID查找用户
   */
  findById: async (id) => {
    try {
      const tableName = getUserTableName();
      const user = await db.getOne(`SELECT * FROM ${tableName} WHERE id = ?`, [id]);
      
      if (user) {
        logger.info(`找到用户: ID=${id}`);
      } else {
        logger.warn(`未找到用户: ID=${id}`);
      }
      
      return user;
    } catch (error) {
      logger.error(`查找用户失败: ${error.message}`);
      return null;
    }
  },
  
  /**
   * 根据openid查找用户
   */
  findByOpenid: async (openid) => {
    try {
      const tableName = getUserTableName();
      return await db.getOne(`SELECT * FROM ${tableName} WHERE openid = ?`, [openid]);
    } catch (error) {
      logger.error(`根据openid查找用户失败: ${error.message}`);
      return null;
    }
  },
  
  /**
   * 创建用户
   */
  create: async (userData) => {
    try {
      const tableName = getUserTableName();
      
      // 检查表是否存在必要的列
      const columnsQuery = await db.query(`SHOW COLUMNS FROM ${tableName}`);
      const columns = columnsQuery.map(col => col.Field);
      
      // 构建动态SQL
      const fields = ['openid', 'nickname', 'avatar', 'gender', 'created_at', 'updated_at'];
      const values = ['?', '?', '?', '?', 'NOW()', 'NOW()'];
      const params = [
        userData.openid,
        userData.nickname || '微信用户',
        userData.avatar || '',
        userData.gender || 0
      ];
      
      // 可选字段
      const optionalFields = {
        'country': userData.country,
        'province': userData.province,
        'city': userData.city,
        'is_demote': userData.is_demote === true ? 1 : 0
      };
      
      // 添加存在的可选字段
      Object.keys(optionalFields).forEach(field => {
        if (columns.includes(field) && optionalFields[field] !== undefined) {
          fields.push(field);
          values.push('?');
          params.push(optionalFields[field]);
        }
      });
      
      const sql = `INSERT INTO ${tableName} (${fields.join(', ')}) VALUES (${values.join(', ')})`;
      logger.info(`执行创建用户SQL: ${sql}, 参数: ${JSON.stringify(params)}`);
      
      const result = await db.query(sql, params);
      
      if (result.insertId) {
        return {
          id: result.insertId,
          ...userData
        };
      }
      return null;
    } catch (error) {
      logger.error(`创建用户失败: ${error.message}`);
      return null;
    }
  },
  
  /**
   * 更新用户信息
   */
  update: async (id, userData) => {
    try {
      const tableName = getUserTableName();
      logger.info(`开始更新用户信息: ID=${id}, 数据: ${JSON.stringify(userData)}`);
      
      // 构建SET子句和参数
      const setClause = [];
      const params = [];
      
      Object.keys(userData).forEach(key => {
        setClause.push(`${key} = ?`);
        
        // 确保布尔值被转换为整数
        if (typeof userData[key] === 'boolean') {
          params.push(userData[key] ? 1 : 0);
          logger.info(`将字段 ${key} 的布尔值 ${userData[key]} 转换为整数: ${userData[key] ? 1 : 0}`);
        } else {
          params.push(userData[key]);
        }
      });
      
      // 添加更新时间
      setClause.push('updated_at = NOW()');
      
      // 添加ID参数
      params.push(id);
      
      const sql = `UPDATE ${tableName} SET ${setClause.join(', ')} WHERE id = ?`;
      logger.info(`执行更新用户SQL: ${sql}, 参数: ${JSON.stringify(params)}`);
      
      const result = await db.query(sql, params);
      
      if (result.affectedRows > 0) {
        logger.info(`更新用户成功: ID=${id}, 影响行数: ${result.affectedRows}`);
        return true;
      } else {
        logger.warn(`更新用户未影响任何行: ID=${id}`);
        return false;
      }
    } catch (error) {
      logger.error(`更新用户信息失败: ${error.message}, SQL错误: ${error.sqlMessage || '无'}, 错误码: ${error.code || '无'}`);
      return false;
    }
  }
};

module.exports = User; 