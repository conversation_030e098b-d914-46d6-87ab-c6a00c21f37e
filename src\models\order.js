const db = require('../database');
const logger = require('../utils/logger');

/**
 * 订单模型
 */
class OrderModel {
  /**
   * 创建订单
   * @param {object} orderData 订单数据
   * @returns {Promise<object>} 创建的订单信息
   */
  static async create(orderData) {
    try {
      // 开启事务
      return await db.transaction(async (connection) => {
        const {
          userId, items, address, totalAmount, remark = '', 
          paymentMethod = 'wechat', deliveryFee = 0
        } = orderData;

        // 生成订单号
        const orderNo = this.generateOrderNo();
        
        // 插入订单主表
        const orderSql = `
          INSERT INTO orders (
            order_no, user_id, status, total_amount, delivery_fee, 
            payment_method, payment_status, remark, address,
            created_at, updated_at
          )
          VALUES (?, ?, 0, ?, ?, ?, 0, ?, ?, NOW(), NOW())
        `;
        
        const orderResult = await connection.execute(
          orderSql, 
          [orderNo, userId, totalAmount, deliveryFee, paymentMethod, remark, JSON.stringify(address)]
        );
        
        const orderId = orderResult[0].insertId;
        
        if (!orderId) {
          throw new Error('创建订单失败');
        }
        
        // 插入订单详情
        const itemValues = [];
        const itemPlaceholders = [];
        
        for (const item of items) {
          itemValues.push(
            orderId, item.goodsId, item.goodsName, item.cover,
            item.price, item.quantity, item.specifications ? JSON.stringify(item.specifications) : '{}'
          );
          
          itemPlaceholders.push('(?, ?, ?, ?, ?, ?, ?)');
          
          // 更新库存
          await connection.execute(
            'UPDATE goods SET stock = stock - ?, sales = sales + ? WHERE id = ? AND stock >= ?',
            [item.quantity, item.quantity, item.goodsId, item.quantity]
          );
        }
        
        const itemSql = `
          INSERT INTO order_items (
            order_id, goods_id, goods_name, goods_cover,
            price, quantity, specifications
          )
          VALUES ${itemPlaceholders.join(', ')}
        `;
        
        await connection.execute(itemSql, itemValues);
        
        // 返回创建的订单
        return {
          id: orderId,
          orderNo,
          status: 0, // 待支付
          paymentStatus: 0, // 未支付
          totalAmount,
          deliveryFee,
          paymentMethod,
          remark,
          address,
          items,
          createdAt: new Date()
        };
      });
    } catch (error) {
      logger.error(`创建订单失败: ${error.message}`);
      throw error;
    }
  }
  
  /**
   * 生成订单号
   * @returns {string} 订单号
   */
  static generateOrderNo() {
    const now = new Date();
    const year = now.getFullYear().toString().slice(2);
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const hour = String(now.getHours()).padStart(2, '0');
    const minute = String(now.getMinutes()).padStart(2, '0');
    const second = String(now.getSeconds()).padStart(2, '0');
    const random = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
    
    return `${year}${month}${day}${hour}${minute}${second}${random}`;
  }

  /**
   * 获取订单列表
   * @param {object} params 查询参数
   * @returns {Promise<object>} 订单列表和分页信息
   */
  static async list({ userId, status, page = 1, limit = 10, keyword = '', timeRange = '', amountRange = '' }) {
    try {
      let whereConditions = ['user_id = ?'];
      const params = [userId];

      // 订单状态筛选
      if (status !== undefined && status !== -1) {
        whereConditions.push('status = ?');
        params.push(status);
      }

      // 关键词搜索（订单号、商品名称）
      if (keyword) {
        whereConditions.push('(order_no LIKE ? OR id IN (SELECT order_id FROM order_items WHERE goods_name LIKE ?))');
        params.push(`%${keyword}%`, `%${keyword}%`);
      }

      // 时间范围筛选
      if (timeRange) {
        const now = new Date();
        let startDate;

        switch (timeRange) {
          case '7d':
            startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
            break;
          case '30d':
            startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
            break;
          case '90d':
            startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
            break;
        }

        if (startDate) {
          whereConditions.push('created_at >= ?');
          params.push(startDate.toISOString().slice(0, 19).replace('T', ' '));
        }
      }

      // 金额范围筛选
      if (amountRange) {
        const [min, max] = amountRange.split('-');
        if (max === '+') {
          whereConditions.push('total_amount >= ?');
          params.push(parseFloat(min));
        } else {
          whereConditions.push('total_amount >= ? AND total_amount <= ?');
          params.push(parseFloat(min), parseFloat(max));
        }
      }
      
      const whereClause = whereConditions.join(' AND ');
      const offset = (page - 1) * limit;
      
      // 查询订单列表
      const countSql = `SELECT COUNT(*) as total FROM orders WHERE ${whereClause}`;
      const ordersSql = `
        SELECT id, order_no, status, payment_status, total_amount, 
               delivery_fee, payment_method, created_at
        FROM orders
        WHERE ${whereClause}
        ORDER BY created_at DESC
        LIMIT ?, ?
      `;
      
      const [countResult, ordersResult] = await Promise.all([
        db.query(countSql, params),
        db.query(ordersSql, [...params, offset, parseInt(limit)])
      ]);
      
      // 获取订单商品信息
      const orders = [];
      for (const order of ordersResult) {
        const itemsSql = `
          SELECT goods_id, goods_name, goods_cover, price, quantity, specifications
          FROM order_items
          WHERE order_id = ?
        `;
        
        const itemsResult = await db.query(itemsSql, [order.id]);
        
        // 处理商品规格信息
        const items = itemsResult.map(item => ({
          goodsId: item.goods_id,
          goodsName: item.goods_name,
          cover: item.goods_cover,
          price: item.price,
          quantity: item.quantity,
          specifications: JSON.parse(item.specifications || '{}')
        }));
        
        orders.push({
          id: order.id,
          orderNo: order.order_no,
          status: order.status,
          paymentStatus: order.payment_status,
          totalAmount: order.total_amount,
          deliveryFee: order.delivery_fee,
          paymentMethod: order.payment_method,
          createdAt: order.created_at,
          items
        });
      }
      
      const total = countResult[0].total;
      
      return {
        list: orders,
        pagination: {
          total,
          page: parseInt(page),
          limit: parseInt(limit),
          pages: Math.ceil(total / limit)
        }
      };
    } catch (error) {
      logger.error(`获取订单列表失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 获取订单详情
   * @param {string} orderNo 订单号
   * @param {number} userId 用户ID
   * @returns {Promise<object|null>} 订单详情
   */
  static async getByOrderNo(orderNo, userId) {
    try {
      // 查询订单主表
      const orderSql = `
        SELECT o.*, u.nickname, u.avatar, u.phone
        FROM orders o
        LEFT JOIN users u ON o.user_id = u.id
        WHERE o.order_no = ? AND o.user_id = ?
        LIMIT 1
      `;
      
      const orderResult = await db.query(orderSql, [orderNo, userId]);
      
      if (orderResult.length === 0) {
        return null;
      }
      
      const order = orderResult[0];
      
      // 查询订单商品
      const itemsSql = `
        SELECT goods_id, goods_name, goods_cover, price, quantity, specifications
        FROM order_items
        WHERE order_id = ?
      `;
      
      const itemsResult = await db.query(itemsSql, [order.id]);
      
      // 解析地址JSON
      let address = {};
      try {
        address = JSON.parse(order.address || '{}');
      } catch (e) {
        logger.error(`解析地址JSON失败: ${e.message}`);
      }
      
      // 处理商品规格信息
      const items = itemsResult.map(item => {
        let specifications = {};
        try {
          specifications = JSON.parse(item.specifications || '{}');
        } catch (e) {
          logger.error(`解析商品规格JSON失败: ${e.message}`);
        }
        
        return {
          goodsId: item.goods_id,
          goodsName: item.goods_name,
          cover: item.goods_cover,
          price: item.price,
          quantity: item.quantity,
          specifications
        };
      });
      
      return {
        id: order.id,
        orderNo: order.order_no,
        status: order.status,
        paymentStatus: order.payment_status,
        totalAmount: order.total_amount,
        deliveryFee: order.delivery_fee,
        paymentMethod: order.payment_method,
        remark: order.remark,
        address,
        items,
        logisticsNo: order.logistics_no,
        logisticsCompany: order.logistics_company,
        paymentTime: order.payment_time,
        deliveryTime: order.delivery_time,
        completionTime: order.completion_time,
        user: {
          id: order.user_id,
          nickname: order.nickname,
          avatar: order.avatar,
          phone: order.phone
        },
        createdAt: order.created_at,
        updatedAt: order.updated_at
      };
    } catch (error) {
      logger.error(`获取订单详情失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 更新订单状态
   * @param {string} orderNo 订单号
   * @param {number} status 订单状态
   * @param {object} extraData 额外数据（物流信息等）
   * @returns {Promise<boolean>} 更新是否成功
   */
  static async updateStatus(orderNo, status, extraData = {}) {
    try {
      const updates = ['status = ?', 'updated_at = NOW()'];
      const params = [status];
      
      // 根据状态添加相关字段
      if (status === 1) { // 待发货（已支付）
        updates.push('payment_status = 1');
        updates.push('payment_time = NOW()');
      } else if (status === 2) { // 待收货（已发货）
        if (extraData.logisticsNo && extraData.logisticsCompany) {
          updates.push('logistics_no = ?');
          updates.push('logistics_company = ?');
          params.push(extraData.logisticsNo, extraData.logisticsCompany);
        }
        updates.push('delivery_time = NOW()');
      } else if (status === 3) { // 已完成
        updates.push('completion_time = NOW()');
      }
      
      params.push(orderNo);
      
      const sql = `
        UPDATE orders
        SET ${updates.join(', ')}
        WHERE order_no = ?
      `;
      
      const result = await db.query(sql, params);
      return result.affectedRows > 0;
    } catch (error) {
      logger.error(`更新订单状态失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 取消订单
   * @param {string} orderNo 订单号
   * @param {number} userId 用户ID
   * @returns {Promise<boolean>} 取消是否成功
   */
  static async cancel(orderNo, userId) {
    try {
      return await db.transaction(async (connection) => {
        // 查询订单
        const orderSql = `
          SELECT id, status FROM orders 
          WHERE order_no = ? AND user_id = ? AND status = 0
        `;
        
        const orderResult = await connection.execute(orderSql, [orderNo, userId]);
        
        if (orderResult[0].length === 0) {
          throw new Error('订单不存在或状态不允许取消');
        }
        
        const orderId = orderResult[0][0].id;
        
        // 查询订单商品
        const itemsSql = `
          SELECT goods_id, quantity FROM order_items WHERE order_id = ?
        `;
        
        const itemsResult = await connection.execute(itemsSql, [orderId]);
        
        // 恢复商品库存
        for (const item of itemsResult[0]) {
          await connection.execute(
            'UPDATE goods SET stock = stock + ?, sales = sales - ? WHERE id = ?',
            [item.quantity, item.quantity, item.goods_id]
          );
        }
        
        // 更新订单状态为已取消(4)
        const updateSql = `
          UPDATE orders SET status = 4, updated_at = NOW() WHERE id = ?
        `;
        
        const updateResult = await connection.execute(updateSql, [orderId]);
        return updateResult[0].affectedRows > 0;
      });
    } catch (error) {
      logger.error(`取消订单失败: ${error.message}`);
      throw error;
    }
  }
}

module.exports = OrderModel; 