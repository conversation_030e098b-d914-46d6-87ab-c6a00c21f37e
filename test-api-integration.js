const axios = require('axios');

const BASE_URL = 'http://localhost:4000/api/v1';

// 测试用的管理员token（需要先登录获取）
let adminToken = '';

async function login() {
  try {
    console.log('🔐 管理员登录...');
    const response = await axios.post(`${BASE_URL}/admin/auth/admin-login`, {
      username: 'mrx0927',
      password: 'hh20250701'
    });
    
    if (response.data.code === 200) {
      adminToken = response.data.data.token;
      console.log('✅ 登录成功');
      return true;
    } else {
      console.log('❌ 登录失败:', response.data.message);
      return false;
    }
  } catch (error) {
    console.log('❌ 登录错误:', error.message);
    return false;
  }
}

async function testAllianceList() {
  try {
    console.log('\n📋 测试联盟申请列表（包含团队信息）...');
    const response = await axios.get(`${BASE_URL}/admin/alliance/list`, {
      headers: { Authorization: `Bearer ${adminToken}` },
      params: { page: 1, limit: 10 }
    });
    
    if (response.data.code === 200) {
      console.log('✅ 联盟申请列表获取成功');
      const applications = response.data.data.list;
      
      if (applications.length > 0) {
        console.log('联盟申请数据示例:');
        applications.slice(0, 3).forEach((app, index) => {
          console.log(`${index + 1}. 申请ID: ${app.id}, 申请人: ${app.name}, 状态: ${app.status_text}, 处理状态: ${app.process_status}, 关联团队: ${app.team_name || '无'}`);
        });
      } else {
        console.log('暂无联盟申请数据');
      }
    } else {
      console.log('❌ 获取联盟申请列表失败:', response.data.message);
    }
  } catch (error) {
    console.log('❌ 测试联盟申请列表错误:', error.message);
  }
}

async function testTeamList() {
  try {
    console.log('\n🏢 测试团队列表（包含来源信息）...');
    const response = await axios.get(`${BASE_URL}/admin/team/list`, {
      headers: { Authorization: `Bearer ${adminToken}` },
      params: { page: 1, limit: 10 }
    });
    
    if (response.data.code === 200) {
      console.log('✅ 团队列表获取成功');
      const teams = response.data.data.list;
      
      if (teams.length > 0) {
        console.log('团队数据示例:');
        teams.slice(0, 3).forEach((team, index) => {
          console.log(`${index + 1}. 团队ID: ${team.id}, 团队名: ${team.name}, 团长: ${team.leader_name}, 来源: ${team.team_source}, 联盟申请ID: ${team.alliance_apply_id || '无'}`);
        });
      } else {
        console.log('暂无团队数据');
      }
    } else {
      console.log('❌ 获取团队列表失败:', response.data.message);
    }
  } catch (error) {
    console.log('❌ 测试团队列表错误:', error.message);
  }
}

async function testAllianceDetail() {
  try {
    console.log('\n🔍 测试联盟申请详情（包含团队信息）...');
    
    // 先获取一个联盟申请ID
    const listResponse = await axios.get(`${BASE_URL}/admin/alliance/list`, {
      headers: { Authorization: `Bearer ${adminToken}` },
      params: { page: 1, limit: 1 }
    });
    
    if (listResponse.data.code === 200 && listResponse.data.data.list.length > 0) {
      const applicationId = listResponse.data.data.list[0].id;
      
      const response = await axios.get(`${BASE_URL}/admin/alliance/detail/${applicationId}`, {
        headers: { Authorization: `Bearer ${adminToken}` }
      });
      
      if (response.data.code === 200) {
        console.log('✅ 联盟申请详情获取成功');
        const detail = response.data.data;
        console.log(`申请详情: ID=${detail.id}, 申请人=${detail.name}, 状态=${detail.status_text}, 关联团队=${detail.team_name || '无'}`);
      } else {
        console.log('❌ 获取联盟申请详情失败:', response.data.message);
      }
    } else {
      console.log('⚠️  没有找到联盟申请数据，跳过详情测试');
    }
  } catch (error) {
    console.log('❌ 测试联盟申请详情错误:', error.message);
  }
}

async function testTeamDetail() {
  try {
    console.log('\n🔍 测试团队详情（包含联盟申请信息）...');
    
    // 先获取一个团队ID
    const listResponse = await axios.get(`${BASE_URL}/admin/team/list`, {
      headers: { Authorization: `Bearer ${adminToken}` },
      params: { page: 1, limit: 1 }
    });
    
    if (listResponse.data.code === 200 && listResponse.data.data.list.length > 0) {
      const teamId = listResponse.data.data.list[0].id;
      
      const response = await axios.get(`${BASE_URL}/admin/team/detail/${teamId}`, {
        headers: { Authorization: `Bearer ${adminToken}` }
      });
      
      if (response.data.code === 200) {
        console.log('✅ 团队详情获取成功');
        const detail = response.data.data;
        console.log(`团队详情: ID=${detail.id}, 团队名=${detail.name}, 团长=${detail.leader_name}, 来源=${detail.team_source}, 联盟申请ID=${detail.alliance_apply_id || '无'}`);
      } else {
        console.log('❌ 获取团队详情失败:', response.data.message);
      }
    } else {
      console.log('⚠️  没有找到团队数据，跳过详情测试');
    }
  } catch (error) {
    console.log('❌ 测试团队详情错误:', error.message);
  }
}

async function runTests() {
  console.log('🚀 开始测试联盟申请与团队管理的关联功能...\n');
  
  // 1. 登录
  const loginSuccess = await login();
  if (!loginSuccess) {
    console.log('❌ 登录失败，无法继续测试');
    return;
  }
  
  // 2. 测试各个接口
  await testAllianceList();
  await testTeamList();
  await testAllianceDetail();
  await testTeamDetail();
  
  console.log('\n✅ 所有测试完成！');
  console.log('\n📝 改进总结:');
  console.log('1. ✅ 联盟申请列表现在显示关联的团队信息');
  console.log('2. ✅ 团队列表现在显示来源信息（联盟申请 vs 手动创建）');
  console.log('3. ✅ 联盟申请详情包含完整的团队关联信息');
  console.log('4. ✅ 团队详情包含完整的联盟申请来源信息');
  console.log('5. ✅ 两个模块现在完全关联，可以相互跳转查看');
}

// 运行测试
runTests().catch(console.error);
