// pages/alliance/index.js
const app = getApp()
const API = require('../../config/api')
const { request } = require('../../utils/request')

// 直接使用微信原生API，不从util.js引入
Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 团长申请表单数据
    formData: {
      name: '',       // 团队名称
      contact: '',    // 联系人姓名
      phone: '',      // 联系电话
      email: '',      // 电子邮箱
      area: '',       // 区域
      description: '' // 简介
    },
    // 团队数据
    teamInfo: {
      memberCount: 0,   // 成员数量
      wifiCount: 0,     // WiFi数量
      totalProfit: 0.00 // 总收益
    },
    // 分润规则
    profitRules: {
      platformRate: 0, // 平台分润比例
      leaderRate: 0,   // 团长分润比例
      memberRate: 0    // 成员分润比例
    },
    // 区域选项
    areaOptions: [
      '华东地区', '华南地区', '华中地区', 
      '华北地区', '西南地区', '西北地区', '东北地区'
    ],
    selectedAreaIndex: -1,
    // 页面状态
    isLoggedIn: false,
    submitting: false,
    hasApplied: false,
    isLeader: false,
    applicationStatus: '', // pending, approved, rejected
    activeTab: 'apply',    // apply, team, profit
    // 成员列表分页
    membersList: [],
    page: 1,
    limit: 10,
    hasMore: true,
    loading: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    console.log('联盟入驻页面 onLoad - 全局登录状态:', app.globalData.isLoggedIn)
    this.setData({
      isLoggedIn: app.globalData.isLoggedIn || false
    })
    
    if (this.data.isLoggedIn) {
      this.checkUserStatus()
    }
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    console.log('联盟入驻页面 onShow - 全局登录状态:', app.globalData.isLoggedIn)
    
    // 每次页面显示时，重新检查登录状态
    const isLoggedIn = app.globalData.isLoggedIn || false
    console.log('联盟入驻页面 onShow - 当前页面登录状态:', this.data.isLoggedIn, '新登录状态:', isLoggedIn)
    
    // 更新登录状态
    this.setData({ isLoggedIn })
    
    if (isLoggedIn) {
      // 已登录，检查用户状态
      this.checkUserStatus()
    } else {
      // 未登录，提示用户登录
      wx.showModal({
        title: '提示',
        content: '请先登录',
        confirmText: '去登录',
        success: (res) => {
          if (res.confirm) {
            wx.switchTab({
              url: '/pages/user/profile/profile'
            })
          }
        }
      })
    }
  },

  /**
   * 检查用户状态
   */
  checkUserStatus: function () {
    console.log('检查用户状态')
    // 检查是否已是团长
    // 直接使用完整URL进行请求，避免API配置问题
    const apiBaseUrl = require('../../config/config').apiBaseUrl
    
    request({
      url: `${apiBaseUrl}/api/v1/client/user/info`,
      method: 'GET'
    }).then(res => {
      console.log('获取用户信息结果:', res)
      if (res.code === 0 && res.data) {
        const isLeader = res.data.is_leader === 1
        console.log('用户是否为团长:', isLeader)
        this.setData({ isLeader })
        
        if (isLeader) {
          // 已是团长，获取团队信息
          this.fetchTeamInfo()
          this.fetchProfitRules()
          this.fetchTeamMembers()
        } else {
          // 不是团长，检查申请状态
          this.checkApplicationStatus()
        }
      }
    }).catch(err => {
      console.error('获取用户信息失败', err)
    })
  },

  /**
   * 检查申请状态
   */
  checkApplicationStatus: function () {
    // 调用真实的API检查申请状态
    wx.request({
      url: 'http://localhost:4000/api/v1/client/alliance/status',
      method: 'GET',
      header: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${wx.getStorageSync('token')}`
      },
      success: (res) => {
        console.log('申请状态查询响应：', res)

        if (res.statusCode === 200 && res.data.code === 0) {
          const data = res.data.data
          this.setData({
            hasApplied: data.hasApplied,
            applicationStatus: data.status || 'pending'
          })
        } else {
          // 如果API调用失败，回退到本地存储
          const applicationData = wx.getStorageSync('alliance_application')
          if (applicationData) {
            this.setData({
              hasApplied: true,
              applicationStatus: applicationData.status || 'pending'
            })
          } else {
            this.setData({
              hasApplied: false,
              applicationStatus: ''
            })
          }
        }
      },
      fail: (err) => {
        console.error('申请状态查询失败：', err)
        // 如果API调用失败，回退到本地存储
        const applicationData = wx.getStorageSync('alliance_application')
        if (applicationData) {
          this.setData({
            hasApplied: true,
            applicationStatus: applicationData.status || 'pending'
          })
        } else {
          this.setData({
            hasApplied: false,
            applicationStatus: ''
          })
        }
      }
    })
  },
  
  /**
   * 获取团队信息
   */
  fetchTeamInfo: function () {
    // 直接使用完整URL进行请求，避免API配置问题
    const apiBaseUrl = require('../../config/config').apiBaseUrl
    
    request({
      url: `${apiBaseUrl}/api/v1/client/user/team`,
      method: 'GET'
    }).then(res => {
      if (res.code === 0 && res.data) {
        this.setData({
          teamInfo: {
            memberCount: res.data.member_count || 0,
            wifiCount: res.data.wifi_count || 0,
            totalProfit: res.data.total_profit || '0.00'
          }
        })
      }
    }).catch(err => {
      console.error('获取团队信息失败', err)
    })
  },

  /**
   * 获取分润规则
   */
  fetchProfitRules: function () {
    // 直接使用完整URL进行请求，避免API配置问题
    const apiBaseUrl = require('../../config/config').apiBaseUrl
    
    request({
      url: `${apiBaseUrl}/api/v1/client/profit/rules`,
      method: 'GET'
    }).then(res => {
      if (res.code === 0 && res.data) {
        this.setData({
          profitRules: {
            platformRate: res.data.platform_rate || 0,
            leaderRate: res.data.leader_rate || 0,
            memberRate: res.data.member_rate || 0
          }
        })
      }
    }).catch(err => {
      console.error('获取分润规则失败', err)
    })
  },

  /**
   * 获取团队成员
   */
  fetchTeamMembers: function (refresh = true) {
    if (this.data.loading) return
    
    this.setData({ loading: true })
    
    const page = refresh ? 1 : this.data.page
    
    // 直接使用完整URL进行请求，避免API配置问题
    const apiBaseUrl = require('../../config/config').apiBaseUrl
    
    request({
      url: `${apiBaseUrl}/api/v1/client/team/members`,
      method: 'GET',
      data: {
        page: page,
        limit: this.data.limit
      }
    }).then(res => {
      if (res.code === 0 && res.data) {
        const newMembers = res.data.list || []
        const membersList = refresh ? newMembers : [...this.data.membersList, ...newMembers]
        
        this.setData({
          membersList,
          page: page + 1,
          hasMore: newMembers.length === this.data.limit,
          loading: false
        })
      } else {
        this.setData({ loading: false })
      }
    }).catch(err => {
      console.error('获取团队成员失败', err)
      this.setData({ loading: false })
    })
  },

  /**
   * 输入框变化
   */
  onInputChange: function (e) {
    const { field } = e.currentTarget.dataset
    const { value } = e.detail
    
    this.setData({
      [`formData.${field}`]: value
    })
  },

  /**
   * 选择区域
   */
  onSelectArea: function (e) {
    const index = e.detail.value
    
    this.setData({
      selectedAreaIndex: index,
      'formData.area': this.data.areaOptions[index]
    })
  },

  /**
   * 切换标签页
   */
  onTabChange: function (e) {
    const tab = e.currentTarget.dataset.tab
    this.setData({ activeTab: tab })
    
    if (tab === 'team' && this.data.membersList.length === 0) {
      this.fetchTeamMembers()
    }
  },

  /**
   * 提交申请
   */
  onSubmit: function () {
    if (this.data.submitting) return
    
    const { formData } = this.data
    
    // 表单验证
    if (!formData.name) {
      wx.showToast({
        title: '请输入团队名称',
        icon: 'none'
      })
      return
    }
    if (!formData.contact) {
      wx.showToast({
        title: '请输入联系人',
        icon: 'none'
      })
      return
    }
    if (!formData.phone) {
      wx.showToast({
        title: '请输入联系电话',
        icon: 'none'
      })
      return
    }
    if (!formData.area) {
      wx.showToast({
        title: '请选择区域',
        icon: 'none'
      })
      return
    }
    
    this.setData({ submitting: true })

    console.log('申请数据：', formData)

    // 发送真实的API请求
    wx.request({
      url: 'http://localhost:4000/api/v1/client/alliance/apply',
      method: 'POST',
      header: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${wx.getStorageSync('token')}`
      },
      data: formData,
      success: (res) => {
        console.log('申请提交响应：', res)

        if (res.statusCode === 200 && res.data.code === 0) {
          wx.showToast({
            title: '申请提交成功',
            icon: 'success'
          })

          this.setData({
            hasApplied: true,
            applicationStatus: 'pending',
            submitting: false
          })

          // 记录申请数据到本地存储
          wx.setStorageSync('alliance_application', {
            ...formData,
            id: res.data.data.id,
            status: 'pending',
            applied_at: new Date().toISOString()
          })
        } else {
          wx.showToast({
            title: res.data.message || '申请提交失败',
            icon: 'none'
          })
          this.setData({ submitting: false })
        }
      },
      fail: (err) => {
        console.error('申请提交失败：', err)
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        })
        this.setData({ submitting: false })
      }
    })
  },

  /**
   * 查看申请进度
   */
  onCheckStatus: function () {
    wx.showToast({
      title: `申请状态: ${this.getStatusText()}`,
      icon: 'none'
    })
  },

  /**
   * 获取状态文本
   */
  getStatusText: function () {
    const { applicationStatus } = this.data
    
    switch (applicationStatus) {
      case 'pending':
      case '0':
        return '审核中'
      case 'approved':
      case '1':
        return '已通过'
      case 'rejected':
      case '2':
        return '已拒绝'
      default:
        return '未知'
    }
  },
  
  /**
   * 加载更多成员
   */
  loadMoreMembers: function () {
    if (this.data.hasMore && !this.data.loading) {
      this.fetchTeamMembers(false)
    }
  },

  /**
   * 跳转到收益明细
   */
  onViewProfitDetails: function () {
    wx.navigateTo({
      url: '/pages/user/wallet/wallet'
    })
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {
    if (this.data.isLoggedIn) {
      if (this.data.isLeader) {
        Promise.all([
          this.fetchTeamInfo(),
          this.fetchProfitRules(),
          this.fetchTeamMembers()
        ]).finally(() => {
          wx.stopPullDownRefresh()
        })
      } else {
        this.checkApplicationStatus().finally(() => {
          wx.stopPullDownRefresh()
        })
      }
    } else {
      wx.stopPullDownRefresh()
    }
  },
  
  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {
    if (this.data.activeTab === 'team') {
      this.loadMoreMembers()
    }
  },

  /**
   * 广告加载成功
   */
  onAdLoad: function () {
    console.log('广告加载成功')
  },

  /**
   * 广告加载失败
   */
  onAdError: function (err) {
    console.error('广告加载失败', err)
  }
}) 