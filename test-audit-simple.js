/**
 * 简单的联盟审核测试
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:4000/api/v1/admin/alliance';
const ADMIN_TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6MSwidXNlcm5hbWUiOiJtcngwOTI3Iiwicm9sZSI6ImFkbWluIiwiaWF0IjoxNzUyMzczODM0LCJleHAiOjE3NTI5Nzg2MzR9.uFW9QYuueQwMgf7KBd_CGExk_321QNSCvOz-lmqwAug';

async function testAudit() {
  try {
    console.log('🚀 测试联盟审核接口...');
    
    // 先获取申请列表
    const listResponse = await axios.get(`${BASE_URL}/list?page=1&limit=10`, {
      headers: {
        'Authorization': `Bearer ${ADMIN_TOKEN}`,
        'Content-Type': 'application/json'
      }
    });
    
    console.log('📋 申请列表:', JSON.stringify(listResponse.data, null, 2));
    
    const applications = listResponse.data.data.list;
    const pendingApp = applications.find(app => app.status === 0);
    
    if (!pendingApp) {
      console.log('⚠️ 没有待审核的申请');
      return;
    }
    
    console.log(`\n🔍 找到待审核申请: ID=${pendingApp.id}, 名称=${pendingApp.name}`);
    
    // 测试审核
    const auditResponse = await axios.post(`${BASE_URL}/audit/${pendingApp.id}`, {
      status: 1,
      remark: '测试审核通过'
    }, {
      headers: {
        'Authorization': `Bearer ${ADMIN_TOKEN}`,
        'Content-Type': 'application/json'
      }
    });
    
    console.log('✅ 审核结果:', JSON.stringify(auditResponse.data, null, 2));
    
  } catch (error) {
    if (error.response) {
      console.log('❌ 审核失败:', error.response.status, JSON.stringify(error.response.data, null, 2));
    } else {
      console.log('❌ 网络错误:', error.message);
    }
  }
}

testAudit();
