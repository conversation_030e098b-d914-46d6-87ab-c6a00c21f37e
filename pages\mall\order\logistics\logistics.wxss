/* pages/mall/order/logistics/logistics.wxss */
.container {
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 物流公司信息 */
.logistics-header {
  background-color: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.company-info {
  margin-bottom: 30rpx;
}

.company-name {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  display: block;
  margin-bottom: 16rpx;
}

.logistics-no {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 26rpx;
  color: #666;
}

.copy-btn {
  color: #07c160;
  font-size: 24rpx;
  padding: 8rpx 16rpx;
  border: 1rpx solid #07c160;
  border-radius: 20rpx;
}

.contact-actions {
  display: flex;
  gap: 20rpx;
}

.contact-btn {
  flex: 1;
  background-color: #f8f8f8;
  color: #333;
  border: none;
  border-radius: 8rpx;
  padding: 20rpx;
  font-size: 26rpx;
}

/* 收货信息 */
.receiver-info {
  background-color: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.info-row {
  display: flex;
  margin-bottom: 16rpx;
  font-size: 26rpx;
}

.info-row:last-child {
  margin-bottom: 0;
}

.label {
  color: #666;
  min-width: 120rpx;
}

.value {
  color: #333;
  flex: 1;
  line-height: 1.5;
}

/* 物流轨迹 */
.logistics-timeline {
  background-color: #fff;
  padding: 30rpx;
}

.timeline-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 30rpx;
}

.timeline-list {
  position: relative;
}

.timeline-list::before {
  content: '';
  position: absolute;
  left: 20rpx;
  top: 40rpx;
  bottom: 0;
  width: 2rpx;
  background-color: #e5e5e5;
}

.timeline-item {
  position: relative;
  padding-left: 80rpx;
  margin-bottom: 40rpx;
}

.timeline-item:last-child {
  margin-bottom: 0;
}

.timeline-dot {
  position: absolute;
  left: 12rpx;
  top: 8rpx;
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  background-color: #e5e5e5;
  border: 4rpx solid #fff;
  box-shadow: 0 0 0 2rpx #e5e5e5;
}

.timeline-item.current .timeline-dot {
  background-color: #07c160;
  box-shadow: 0 0 0 2rpx #07c160;
}

.timeline-content {
  background-color: #f8f8f8;
  border-radius: 12rpx;
  padding: 24rpx;
}

.timeline-item.current .timeline-content {
  background-color: #e8f5e8;
}

.timeline-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.timeline-status {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}

.timeline-item.current .timeline-status {
  color: #07c160;
}

.timeline-time {
  font-size: 24rpx;
  color: #999;
}

.timeline-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
  display: block;
  margin-bottom: 8rpx;
}

.timeline-location {
  font-size: 24rpx;
  color: #999;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  font-size: 28rpx;
  color: #999;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #07c160;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 30rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 错误状态 */
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  padding: 40rpx;
}

.error-icon {
  font-size: 120rpx;
  margin-bottom: 40rpx;
  opacity: 0.6;
  line-height: 1;
}

.error-text {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 16rpx;
}

.error-desc {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 40rpx;
}

.retry-btn {
  background-color: #07c160;
  color: #fff;
  border-radius: 40rpx;
  padding: 20rpx 60rpx;
  font-size: 28rpx;
  border: none;
}

/* 无物流信息状态 */
.no-logistics-container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding: 20rpx;
}

.order-info-card {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.order-header {
  margin-bottom: 20rpx;
}

.order-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

.order-details {
  border-top: 1rpx solid #f0f0f0;
  padding-top: 20rpx;
}

.detail-row {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.detail-row:last-child {
  margin-bottom: 0;
}

.detail-label {
  font-size: 28rpx;
  color: #666;
  width: 160rpx;
}

.detail-value {
  font-size: 28rpx;
  color: #333;
  flex: 1;
}

.status-pending {
  color: #ff9500;
}

.status-paid {
  color: #07c160;
}

.status-shipped {
  color: #576b95;
}

.status-delivered {
  color: #07c160;
}

.status-completed {
  color: #07c160;
}

.status-cancelled {
  color: #fa5151;
}

.no-logistics-info {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 60rpx 30rpx;
  text-align: center;
}

.no-logistics-icon {
  font-size: 120rpx;
  display: block;
  margin-bottom: 30rpx;
}

.no-logistics-title {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
  display: block;
  margin-bottom: 16rpx;
}

.no-logistics-desc {
  font-size: 26rpx;
  color: #999;
  display: block;
  margin-bottom: 40rpx;
}

.refresh-btn {
  background-color: #07c160;
  color: #fff;
  border-radius: 40rpx;
  padding: 20rpx 60rpx;
  font-size: 28rpx;
  border: none;
}
