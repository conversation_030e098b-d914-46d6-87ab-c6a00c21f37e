<!--pages/wifi/ad-view/ad-view.wxml-->
<!--广告观看页面-->

<view class="container">
  <!-- 顶部标题 -->
  <view class="header">
    <view class="title">WiFi连接</view>
    <view class="subtitle">观看广告后自动连接</view>
  </view>
  
  <!-- WiFi信息 -->
  <view class="wifi-info">
    <view class="wifi-name">
      <text class="label">WiFi名称:</text>
      <text class="value">{{ssid}}</text>
    </view>
  </view>
  
  <!-- 广告区域 -->
  <view class="ad-container">
    <!-- 广告加载中 -->
    <view class="loading-container" wx:if="{{!adLoaded && !adError}}">
      <view class="loading-spinner"></view>
      <text class="loading-text">广告加载中...</text>
    </view>
    
    <!-- 广告加载失败 -->
    <view class="error-container" wx:if="{{adError}}">
      <icon type="warn" size="64" color="#e74c3c"></icon>
      <text class="error-text">{{errorMsg || '广告加载失败'}}</text>
      <button class="btn btn-primary" bindtap="manualConnect">手动连接</button>
      <button class="btn btn-default" bindtap="goBack">返回</button>
    </view>
    
    <!-- 视频广告 -->
    <view class="video-ad" wx:if="{{adLoaded && adType === 'video' && showCountdown}}">
      <video 
        src="https://example.com/ad-video.mp4" 
        autoplay="{{true}}" 
        controls="{{false}}"
        loop="{{false}}"
        muted="{{false}}"
        show-fullscreen-btn="{{false}}"
        show-play-btn="{{false}}"
        show-center-play-btn="{{false}}"
        object-fit="cover"
        style="width: 100%; height: 400rpx;"
      ></video>
      
      <view class="countdown">
        <text>广告倒计时: {{countdown}}秒</text>
      </view>
    </view>
    
    <!-- Banner广告 -->
    <view class="banner-ad" wx:if="{{adLoaded && adType === 'banner' && showCountdown}}">
      <image 
        src="/assets/images/ad-banner.jpg" 
        mode="aspectFill"
        style="width: 100%; height: 300rpx;"
      ></image>
      
      <view class="ad-content">
        <text class="ad-title">精品推荐</text>
        <text class="ad-desc">查看更多优惠信息</text>
      </view>
      
      <view class="countdown">
        <text>广告倒计时: {{countdown}}秒</text>
      </view>
    </view>
    
    <!-- 准备连接 -->
    <view class="connect-container" wx:if="{{connectReady && !isConnecting && !connectSuccess && !connectFailed}}">
      <icon type="success" size="64" color="#27ae60"></icon>
      <text class="connect-text">广告观看完成</text>
      <text class="connect-subtext">点击下方按钮连接WiFi</text>
      <button class="btn btn-primary" bindtap="connectWifi">连接WiFi</button>
    </view>
    
    <!-- 连接中 -->
    <view class="connect-container" wx:if="{{isConnecting}}">
      <view class="loading-spinner"></view>
      <text class="connect-text">正在连接WiFi...</text>
      <text class="connect-subtext">请稍候</text>
    </view>
    
    <!-- 连接成功 -->
    <view class="connect-container" wx:if="{{connectSuccess}}">
      <icon type="success" size="64" color="#27ae60"></icon>
      <text class="connect-text">WiFi连接成功</text>
      <text class="connect-subtext">{{errorMsg || '您已成功连接到WiFi'}}</text>
      <button class="btn btn-primary" bindtap="goBack">返回</button>
    </view>
    
    <!-- 连接失败 -->
    <view class="connect-container" wx:if="{{connectFailed}}">
      <icon type="warn" size="64" color="#e74c3c"></icon>
      <text class="connect-text">WiFi连接失败</text>
      <text class="connect-subtext">{{errorMsg || '请尝试手动连接'}}</text>
      <button class="btn btn-primary" bindtap="manualConnect">复制密码</button>
      <button class="btn btn-default" bindtap="goBack">返回</button>
    </view>
  </view>
  
  <!-- 底部提示 -->
  <view class="footer">
    <text class="footer-text">由WiFi共享商业系统提供</text>
  </view>
</view> 