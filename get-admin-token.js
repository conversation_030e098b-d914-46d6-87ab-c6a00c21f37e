const axios = require('axios');

const BASE_URL = 'http://localhost:4000';

async function getAdminToken() {
  try {
    console.log('🔑 获取管理员token...\n');
    
    // 管理员登录
    console.log('使用 admin 账户登录...');
    const loginResponse = await axios.post(`${BASE_URL}/api/v1/admin/auth/admin-login`, {
      username: 'admin',
      password: 'admin123'
    });
    
    console.log('✅ 登录响应状态:', loginResponse.status);
    console.log('📄 登录响应数据:', JSON.stringify(loginResponse.data, null, 2));
    
    if (loginResponse.data && loginResponse.data.data && loginResponse.data.data.token) {
      const token = loginResponse.data.data.token;
      console.log('\n🎯 管理员Token:', token);
      
      // 测试token是否有效
      console.log('\n🧪 测试token有效性...');
      const testResponse = await axios.get(`${BASE_URL}/api/v1/admin/team/list`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        params: {
          page: 1,
          limit: 10
        }
      });
      
      console.log('✅ Token测试成功，团队列表响应状态:', testResponse.status);
      console.log('📄 团队列表数据:', JSON.stringify(testResponse.data, null, 2));
      
      if (testResponse.data && testResponse.data.data && testResponse.data.data.list) {
        console.log(`\n📊 找到 ${testResponse.data.data.list.length} 个团队:`);
        testResponse.data.data.list.forEach((team, index) => {
          console.log(`   ${index + 1}. ${team.name} (ID: ${team.id}) - 团长: ${team.leader_name || '未知'}`);
        });
      }
      
    } else {
      console.log('❌ 登录失败，未获取到token');
    }
    
  } catch (error) {
    console.error('❌ 获取token失败:', error.message);
    if (error.response) {
      console.error('响应状态:', error.response.status);
      console.error('响应数据:', error.response.data);
    }
  }
}

getAdminToken();
