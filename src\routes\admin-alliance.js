const express = require('express');
const router = express.Router();
const allianceController = require('../controllers/alliance');
const { verifyToken } = require('../middlewares/auth');

/**
 * 管理端联盟管理路由
 */

// 获取联盟申请列表
router.get('/list', verifyToken, allianceController.getAllianceList);

// 获取联盟申请详情
router.get('/detail/:id', verifyToken, allianceController.getAllianceDetail);

// 审核联盟申请
router.post('/audit/:id', verifyToken, allianceController.auditAlliance);

// 删除联盟申请
router.delete('/delete/:id', verifyToken, allianceController.deleteAlliance);

// 获取联盟统计信息
router.get('/stats', verifyToken, allianceController.getAllianceStats);

// 获取联盟申请关联的团队信息
router.get('/team-info/:id', verifyToken, allianceController.getAllianceTeamInfo);

module.exports = router;
