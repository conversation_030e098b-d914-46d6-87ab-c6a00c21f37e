const mysql = require('mysql2/promise');

async function initDatabase() {
  const connection = await mysql.createConnection({
    host: 'localhost',
    user: 'root',
    password: 'wo587129955',
    database: 'mall'
  });

  try {
    console.log('开始初始化数据库表...');

    // 创建用户表
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS user (
        id INT AUTO_INCREMENT PRIMARY KEY,
        phone VARCHAR(20) UNIQUE NOT NULL,
        password VARCHAR(255) NOT NULL,
        nickname VARCHAR(50),
        avatar VARCHAR(255),
        gender TINYINT DEFAULT 0,
        birthday DATE,
        balance DECIMAL(10,2) DEFAULT 0.00,
        status TINYINT DEFAULT 1,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
    `);
    console.log('✅ 用户表创建成功');

    // 创建商品分类表
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS category (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(50) NOT NULL,
        parent_id INT DEFAULT 0,
        sort_order INT DEFAULT 0,
        status TINYINT DEFAULT 1,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
    `);
    console.log('✅ 商品分类表创建成功');

    // 创建商品表
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS goods (
        id INT AUTO_INCREMENT PRIMARY KEY,
        title VARCHAR(100) NOT NULL,
        description TEXT,
        price DECIMAL(10,2) NOT NULL,
        original_price DECIMAL(10,2),
        cover VARCHAR(255),
        images TEXT,
        category_id INT,
        stock INT DEFAULT 0,
        sales INT DEFAULT 0,
        status TINYINT DEFAULT 1,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX (category_id),
        INDEX (status)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
    `);
    console.log('✅ 商品表创建成功');

    // 创建购物车表
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS cart (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        goods_id INT NOT NULL,
        quantity INT NOT NULL DEFAULT 1,
        specs_id INT DEFAULT 0,
        selected TINYINT DEFAULT 1,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX (user_id),
        INDEX (goods_id)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
    `);
    console.log('✅ 购物车表创建成功');

    // 创建订单表
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS orders (
        id INT AUTO_INCREMENT PRIMARY KEY,
        order_no VARCHAR(50) UNIQUE NOT NULL,
        user_id INT NOT NULL,
        receiver_name VARCHAR(50) NOT NULL,
        receiver_phone VARCHAR(20) NOT NULL,
        receiver_address TEXT NOT NULL,
        goods_amount DECIMAL(10,2) NOT NULL,
        shipping_fee DECIMAL(10,2) DEFAULT 0,
        discount_amount DECIMAL(10,2) DEFAULT 0,
        total_amount DECIMAL(10,2) NOT NULL,
        payment_method TINYINT,
        payment_time DATETIME,
        shipping_time DATETIME,
        completion_time DATETIME,
        logistics_company VARCHAR(50),
        logistics_no VARCHAR(50),
        status TINYINT NOT NULL DEFAULT 0,
        remark TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX (user_id),
        INDEX (status),
        INDEX (order_no)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
    `);
    console.log('✅ 订单表创建成功');

    // 插入测试用户
    await connection.execute(`
      INSERT IGNORE INTO user (openid, phone, nickname)
      VALUES ('test_openid_13800138000', '13800138000', '测试用户')
    `);
    console.log('✅ 测试用户创建成功');

    // 插入测试商品分类
    await connection.execute(`
      INSERT IGNORE INTO category (id, name) VALUES 
      (1, '电子产品'),
      (2, '服装鞋帽'),
      (3, '食品饮料')
    `);
    console.log('✅ 测试分类创建成功');

    // 插入测试商品
    await connection.execute(`
      INSERT IGNORE INTO goods (id, title, description, price, original_price, category_id, stock) VALUES 
      (1, '测试商品1', '这是一个测试商品', 99.99, 199.99, 1, 100),
      (2, '测试商品2', '这是另一个测试商品', 199.99, 299.99, 1, 50),
      (3, '测试商品3', '第三个测试商品', 299.99, 399.99, 2, 30)
    `);
    console.log('✅ 测试商品创建成功');

    console.log('🎉 数据库初始化完成！');

  } catch (error) {
    console.error('❌ 数据库初始化失败:', error);
  } finally {
    await connection.end();
  }
}

initDatabase();
