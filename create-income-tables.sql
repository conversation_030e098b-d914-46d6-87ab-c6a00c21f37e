-- 创建收入管理相关数据表
-- 执行时间：2025年1月29日

USE mall;

-- 1. 收入记录表（统一的收入记录）
CREATE TABLE IF NOT EXISTS `income_record` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '收入记录ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `team_id` int(11) DEFAULT NULL COMMENT '团队ID',
  `amount` decimal(10,2) NOT NULL COMMENT '收入金额',
  `type` varchar(20) NOT NULL COMMENT '收入类型：wifi_share,goods_sale,advertisement,referral,bonus',
  `source_type` tinyint(1) NOT NULL COMMENT '来源类型：1WiFi分润，2商品分润，3广告分润，4推荐奖励，5等级奖励',
  `source_id` int(11) DEFAULT NULL COMMENT '来源ID',
  `order_no` varchar(50) DEFAULT NULL COMMENT '关联订单号',
  `title` varchar(100) NOT NULL COMMENT '收入标题',
  `description` varchar(255) DEFAULT NULL COMMENT '收入描述',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0待确认，1已确认，2已取消',
  `settle_status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '结算状态：0未结算，1已结算',
  `settle_time` datetime DEFAULT NULL COMMENT '结算时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `team_id` (`team_id`),
  KEY `type` (`type`),
  KEY `source_type` (`source_type`),
  KEY `status` (`status`),
  KEY `created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='收入记录表';

-- 2. 提现记录表（独立的提现管理）
CREATE TABLE IF NOT EXISTS `withdraw_record` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '提现记录ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `amount` decimal(10,2) NOT NULL COMMENT '提现金额',
  `fee` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '手续费',
  `actual_amount` decimal(10,2) NOT NULL COMMENT '实际到账金额',
  `withdraw_type` varchar(20) NOT NULL DEFAULT 'wechat' COMMENT '提现方式：wechat,alipay,bank',
  `account_info` text DEFAULT NULL COMMENT '账户信息（JSON格式）',
  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态：0待审核，1审核通过，2审核拒绝，3处理中，4已完成，5已取消',
  `audit_user_id` int(11) DEFAULT NULL COMMENT '审核人ID',
  `audit_time` datetime DEFAULT NULL COMMENT '审核时间',
  `audit_remark` varchar(255) DEFAULT NULL COMMENT '审核备注',
  `process_time` datetime DEFAULT NULL COMMENT '处理时间',
  `complete_time` datetime DEFAULT NULL COMMENT '完成时间',
  `transaction_no` varchar(50) DEFAULT NULL COMMENT '交易流水号',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `status` (`status`),
  KEY `created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='提现记录表';

-- 3. 钱包交易记录表（所有资金变动记录）
CREATE TABLE IF NOT EXISTS `wallet_transaction` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '交易记录ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `type` varchar(20) NOT NULL COMMENT '交易类型：income,withdraw,refund,bonus,penalty',
  `amount` decimal(10,2) NOT NULL COMMENT '交易金额（正数为收入，负数为支出）',
  `balance_before` decimal(10,2) NOT NULL COMMENT '交易前余额',
  `balance_after` decimal(10,2) NOT NULL COMMENT '交易后余额',
  `related_type` varchar(20) DEFAULT NULL COMMENT '关联类型：income_record,withdraw_record,order等',
  `related_id` int(11) DEFAULT NULL COMMENT '关联记录ID',
  `title` varchar(100) NOT NULL COMMENT '交易标题',
  `description` varchar(255) DEFAULT NULL COMMENT '交易描述',
  `transaction_no` varchar(50) DEFAULT NULL COMMENT '交易流水号',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0失败，1成功，2处理中',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `type` (`type`),
  KEY `related_type_id` (`related_type`, `related_id`),
  KEY `created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='钱包交易记录表';

-- 4. 收入统计表（按日/月统计）
CREATE TABLE IF NOT EXISTS `income_statistics` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '统计ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `team_id` int(11) DEFAULT NULL COMMENT '团队ID',
  `stat_date` date NOT NULL COMMENT '统计日期',
  `stat_type` varchar(10) NOT NULL COMMENT '统计类型：daily,monthly',
  `total_income` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '总收入',
  `wifi_income` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT 'WiFi收入',
  `goods_income` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '商品收入',
  `ad_income` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '广告收入',
  `referral_income` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '推荐收入',
  `bonus_income` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '奖励收入',
  `withdraw_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '提现金额',
  `transaction_count` int(11) NOT NULL DEFAULT '0' COMMENT '交易笔数',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_date_type` (`user_id`, `stat_date`, `stat_type`),
  KEY `team_id` (`team_id`),
  KEY `stat_date` (`stat_date`),
  KEY `stat_type` (`stat_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='收入统计表';

-- 5. 更新用户表，添加缺失的收入字段
ALTER TABLE `user` 
ADD COLUMN IF NOT EXISTS `total_income` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '总收入' AFTER `balance`,
ADD COLUMN IF NOT EXISTS `today_income` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '今日收入' AFTER `total_income`,
ADD COLUMN IF NOT EXISTS `month_income` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '本月收入' AFTER `today_income`,
ADD COLUMN IF NOT EXISTS `total_withdraw` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '总提现金额' AFTER `month_income`;

-- 6. 插入测试数据
-- 为用户ID=1插入一些测试收入记录
INSERT INTO `income_record` (`user_id`, `team_id`, `amount`, `type`, `source_type`, `title`, `description`, `status`, `settle_status`) VALUES
(1, 1, 15.50, 'wifi_share', 1, 'WiFi分享收入', '用户使用WiFi产生的分润收入', 1, 1),
(1, 1, 8.30, 'advertisement', 3, '广告点击收入', '用户点击广告产生的收入', 1, 1),
(1, 1, 25.00, 'referral', 4, '推荐奖励', '成功推荐新用户获得的奖励', 1, 1),
(1, 1, 12.80, 'wifi_share', 1, 'WiFi分享收入', '用户使用WiFi产生的分润收入', 1, 1),
(1, 1, 5.20, 'advertisement', 3, '广告点击收入', '用户点击广告产生的收入', 1, 1)
ON DUPLICATE KEY UPDATE updated_at = CURRENT_TIMESTAMP;

-- 为用户ID=1插入钱包交易记录
INSERT INTO `wallet_transaction` (`user_id`, `type`, `amount`, `balance_before`, `balance_after`, `related_type`, `related_id`, `title`, `description`) VALUES
(1, 'income', 15.50, 0.00, 15.50, 'income_record', 1, 'WiFi分享收入', '用户使用WiFi产生的分润收入'),
(1, 'income', 8.30, 15.50, 23.80, 'income_record', 2, '广告点击收入', '用户点击广告产生的收入'),
(1, 'income', 25.00, 23.80, 48.80, 'income_record', 3, '推荐奖励', '成功推荐新用户获得的奖励'),
(1, 'income', 12.80, 48.80, 61.60, 'income_record', 4, 'WiFi分享收入', '用户使用WiFi产生的分润收入'),
(1, 'income', 5.20, 61.60, 66.80, 'income_record', 5, '广告点击收入', '用户点击广告产生的收入')
ON DUPLICATE KEY UPDATE updated_at = CURRENT_TIMESTAMP;

-- 更新用户余额和收入统计
UPDATE `user` SET 
  `balance` = 66.80,
  `total_income` = 66.80,
  `today_income` = 18.00,
  `month_income` = 66.80
WHERE `id` = 1;

-- 插入收入统计数据
INSERT INTO `income_statistics` (`user_id`, `team_id`, `stat_date`, `stat_type`, `total_income`, `wifi_income`, `ad_income`, `referral_income`, `transaction_count`) VALUES
(1, 1, CURDATE(), 'daily', 18.00, 12.80, 5.20, 0.00, 2),
(1, 1, DATE_FORMAT(CURDATE(), '%Y-%m-01'), 'monthly', 66.80, 28.30, 13.50, 25.00, 5)
ON DUPLICATE KEY UPDATE 
  `total_income` = VALUES(`total_income`),
  `wifi_income` = VALUES(`wifi_income`),
  `ad_income` = VALUES(`ad_income`),
  `referral_income` = VALUES(`referral_income`),
  `transaction_count` = VALUES(`transaction_count`),
  `updated_at` = CURRENT_TIMESTAMP;

-- 显示创建结果
SELECT '✅ 收入管理表创建完成' as message;

-- 显示表结构验证
SELECT 
  table_name as '表名',
  table_comment as '表说明'
FROM information_schema.tables 
WHERE table_schema = 'mall' 
AND table_name IN ('income_record', 'withdraw_record', 'wallet_transaction', 'income_statistics')
ORDER BY table_name;
