const axios = require('axios');

const BASE_URL = 'http://localhost:4000';

// 管理员token（需要先获取）
const ADMIN_TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6MSwidXNlcm5hbWUiOiJhZG1pbiIsInJvbGUiOiJhZG1pbiIsImlhdCI6MTczNjkyNzI0NCwiZXhwIjoxNzM3NTMyMDQ0fQ.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8';

async function testTeamAPI() {
  try {
    console.log('🧪 开始测试团队管理API...\n');
    
    // 1. 测试团队列表接口
    console.log('1️⃣ 测试团队列表接口');
    const teamListResponse = await axios.get(`${BASE_URL}/api/v1/admin/team/list`, {
      headers: {
        'Authorization': `Bearer ${ADMIN_TOKEN}`,
        'Content-Type': 'application/json'
      },
      params: {
        page: 1,
        limit: 10
      }
    });
    
    console.log('✅ 团队列表响应状态:', teamListResponse.status);
    console.log('📄 团队列表响应数据:', JSON.stringify(teamListResponse.data, null, 2));
    
    if (teamListResponse.data && teamListResponse.data.data && teamListResponse.data.data.list) {
      console.log(`📊 找到 ${teamListResponse.data.data.list.length} 个团队`);
      teamListResponse.data.data.list.forEach((team, index) => {
        console.log(`   ${index + 1}. ${team.name} (ID: ${team.id}) - 团长: ${team.leader_name || '未知'}`);
      });
    }
    
    // 2. 测试不带token的请求（应该返回401）
    console.log('\n2️⃣ 测试无token访问（应该返回401）');
    try {
      await axios.get(`${BASE_URL}/api/v1/admin/team/list`);
    } catch (error) {
      console.log('✅ 无token访问被正确拒绝:', error.response?.status, error.response?.data?.message);
    }
    
    // 3. 测试路由是否正确注册
    console.log('\n3️⃣ 测试路由注册情况');
    try {
      const response = await axios.get(`${BASE_URL}/api/v1/admin/team/list`, {
        headers: {
          'Authorization': `Bearer invalid_token`
        }
      });
    } catch (error) {
      if (error.response?.status === 401) {
        console.log('✅ 路由已正确注册，返回401认证错误');
      } else if (error.response?.status === 404) {
        console.log('❌ 路由未找到，返回404错误');
      } else {
        console.log('⚠️ 其他错误:', error.response?.status, error.response?.data);
      }
    }
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    if (error.response) {
      console.error('响应状态:', error.response.status);
      console.error('响应数据:', error.response.data);
    }
  }
}

testTeamAPI();
