// 创建提现相关的API接口
const express = require('express');
const mysql = require('mysql2/promise');

// 数据库连接配置
const dbConfig = {
  host: 'localhost',
  user: 'root',
  password: 'wo587129955',
  database: 'mall'
};

// 提现API路由
const withdrawRoutes = {
  
  // 1. 获取提现方式列表
  async getWithdrawMethods(req, res) {
    let connection;
    try {
      connection = await mysql.createConnection(dbConfig);
      
      const [methods] = await connection.execute(`
        SELECT 
          method_code,
          method_name,
          icon,
          min_amount,
          max_amount,
          fee_type,
          fee_rate,
          fee_amount,
          min_fee,
          max_fee,
          process_time,
          is_enabled
        FROM withdraw_method 
        WHERE is_enabled = 1 
        ORDER BY sort_order
      `);
      
      res.json({
        status: 'success',
        message: '获取提现方式成功',
        data: methods
      });
      
    } catch (error) {
      console.error('获取提现方式失败:', error);
      res.status(500).json({
        status: 'error',
        message: '获取提现方式失败',
        error: error.message
      });
    } finally {
      if (connection) await connection.end();
    }
  },
  
  // 2. 获取用户提现账户列表
  async getUserWithdrawAccounts(req, res) {
    let connection;
    try {
      const userId = req.user.id;
      connection = await mysql.createConnection(dbConfig);
      
      const [accounts] = await connection.execute(`
        SELECT 
          ua.id,
          ua.method_code,
          ua.account_name,
          ua.account_number,
          ua.account_holder,
          ua.bank_name,
          ua.is_default,
          ua.is_verified,
          ua.status,
          wm.method_name,
          wm.icon
        FROM user_withdraw_account ua
        LEFT JOIN withdraw_method wm ON ua.method_code = wm.method_code
        WHERE ua.user_id = ? AND ua.status = 1
        ORDER BY ua.is_default DESC, ua.created_at DESC
      `, [userId]);
      
      res.json({
        status: 'success',
        message: '获取提现账户成功',
        data: accounts
      });
      
    } catch (error) {
      console.error('获取提现账户失败:', error);
      res.status(500).json({
        status: 'error',
        message: '获取提现账户失败',
        error: error.message
      });
    } finally {
      if (connection) await connection.end();
    }
  },
  
  // 3. 添加提现账户
  async addWithdrawAccount(req, res) {
    let connection;
    try {
      const userId = req.user.id;
      const { method_code, account_name, account_number, account_holder, bank_name, is_default } = req.body;
      
      connection = await mysql.createConnection(dbConfig);
      
      // 如果设置为默认账户，先取消其他默认账户
      if (is_default) {
        await connection.execute(`
          UPDATE user_withdraw_account 
          SET is_default = 0 
          WHERE user_id = ? AND method_code = ?
        `, [userId, method_code]);
      }
      
      // 插入新账户
      const [result] = await connection.execute(`
        INSERT INTO user_withdraw_account 
        (user_id, method_code, account_name, account_number, account_holder, bank_name, is_default) 
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `, [userId, method_code, account_name, account_number, account_holder, bank_name, is_default || 0]);
      
      res.json({
        status: 'success',
        message: '添加提现账户成功',
        data: { id: result.insertId }
      });
      
    } catch (error) {
      console.error('添加提现账户失败:', error);
      res.status(500).json({
        status: 'error',
        message: '添加提现账户失败',
        error: error.message
      });
    } finally {
      if (connection) await connection.end();
    }
  },
  
  // 4. 计算提现手续费
  async calculateWithdrawFee(req, res) {
    let connection;
    try {
      const { method_code, amount } = req.query;
      
      connection = await mysql.createConnection(dbConfig);
      
      const [methods] = await connection.execute(`
        SELECT fee_type, fee_rate, fee_amount, min_fee, max_fee 
        FROM withdraw_method 
        WHERE method_code = ? AND is_enabled = 1
      `, [method_code]);
      
      if (methods.length === 0) {
        return res.status(400).json({
          status: 'error',
          message: '提现方式不存在或已禁用'
        });
      }
      
      const method = methods[0];
      let fee = 0;
      
      if (method.fee_type === 1) {
        // 按比例计算
        fee = parseFloat(amount) * parseFloat(method.fee_rate) / 100;
      } else {
        // 固定金额
        fee = parseFloat(method.fee_amount);
      }
      
      // 应用最小和最大手续费限制
      if (method.min_fee > 0 && fee < method.min_fee) {
        fee = method.min_fee;
      }
      if (method.max_fee > 0 && fee > method.max_fee) {
        fee = method.max_fee;
      }
      
      const actualAmount = parseFloat(amount) - fee;
      
      res.json({
        status: 'success',
        message: '计算手续费成功',
        data: {
          amount: parseFloat(amount),
          fee: parseFloat(fee.toFixed(2)),
          actual_amount: parseFloat(actualAmount.toFixed(2))
        }
      });
      
    } catch (error) {
      console.error('计算手续费失败:', error);
      res.status(500).json({
        status: 'error',
        message: '计算手续费失败',
        error: error.message
      });
    } finally {
      if (connection) await connection.end();
    }
  },
  
  // 5. 提交提现申请
  async submitWithdrawApplication(req, res) {
    let connection;
    try {
      const userId = req.user.id;
      const { account_id, amount, remark } = req.body;
      
      connection = await mysql.createConnection(dbConfig);
      await connection.beginTransaction();
      
      // 检查用户余额
      const [users] = await connection.execute(`
        SELECT balance FROM user WHERE id = ?
      `, [userId]);
      
      if (users.length === 0 || parseFloat(users[0].balance) < parseFloat(amount)) {
        await connection.rollback();
        return res.status(400).json({
          status: 'error',
          message: '余额不足'
        });
      }
      
      // 获取账户信息
      const [accounts] = await connection.execute(`
        SELECT ua.*, wm.fee_type, wm.fee_rate, wm.fee_amount, wm.min_fee, wm.max_fee
        FROM user_withdraw_account ua
        LEFT JOIN withdraw_method wm ON ua.method_code = wm.method_code
        WHERE ua.id = ? AND ua.user_id = ? AND ua.status = 1
      `, [account_id, userId]);
      
      if (accounts.length === 0) {
        await connection.rollback();
        return res.status(400).json({
          status: 'error',
          message: '提现账户不存在'
        });
      }
      
      const account = accounts[0];
      
      // 计算手续费
      let fee = 0;
      if (account.fee_type === 1) {
        fee = parseFloat(amount) * parseFloat(account.fee_rate) / 100;
      } else {
        fee = parseFloat(account.fee_amount);
      }
      
      if (account.min_fee > 0 && fee < account.min_fee) fee = account.min_fee;
      if (account.max_fee > 0 && fee > account.max_fee) fee = account.max_fee;
      
      const actualAmount = parseFloat(amount) - fee;
      const balanceBefore = parseFloat(users[0].balance);
      const balanceAfter = balanceBefore - parseFloat(amount);
      
      // 生成提现单号
      const withdrawNo = 'WD' + Date.now() + Math.random().toString(36).substr(2, 4).toUpperCase();
      
      // 插入提现申请
      const [result] = await connection.execute(`
        INSERT INTO withdraw_application 
        (user_id, withdraw_no, method_code, account_id, amount, fee, actual_amount, balance_before, balance_after, remark) 
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [userId, withdrawNo, account.method_code, account_id, amount, fee.toFixed(2), actualAmount.toFixed(2), balanceBefore, balanceAfter, remark]);
      
      // 扣除用户余额
      await connection.execute(`
        UPDATE user SET balance = balance - ? WHERE id = ?
      `, [amount, userId]);
      
      // 记录钱包交易
      await connection.execute(`
        INSERT INTO wallet_transaction 
        (user_id, type, amount, balance_before, balance_after, related_type, related_id, title, description) 
        VALUES (?, 'withdraw', ?, ?, ?, 'withdraw_application', ?, '提现申请', ?)
      `, [userId, -parseFloat(amount), balanceBefore, balanceAfter, result.insertId, `提现${amount}元到${account.account_name}`]);
      
      await connection.commit();
      
      res.json({
        status: 'success',
        message: '提现申请提交成功',
        data: {
          withdraw_no: withdrawNo,
          amount: parseFloat(amount),
          fee: parseFloat(fee.toFixed(2)),
          actual_amount: parseFloat(actualAmount.toFixed(2))
        }
      });
      
    } catch (error) {
      if (connection) await connection.rollback();
      console.error('提现申请失败:', error);
      res.status(500).json({
        status: 'error',
        message: '提现申请失败',
        error: error.message
      });
    } finally {
      if (connection) await connection.end();
    }
  },
  
  // 6. 获取提现记录
  async getWithdrawRecords(req, res) {
    let connection;
    try {
      const userId = req.user.id;
      const { page = 1, limit = 10, status } = req.query;
      const offset = (page - 1) * limit;
      
      connection = await mysql.createConnection(dbConfig);
      
      let whereClause = 'WHERE wa.user_id = ?';
      let params = [userId];
      
      if (status !== undefined) {
        whereClause += ' AND wa.status = ?';
        params.push(status);
      }
      
      const [records] = await connection.execute(`
        SELECT 
          wa.id,
          wa.withdraw_no,
          wa.method_code,
          wa.amount,
          wa.fee,
          wa.actual_amount,
          wa.status,
          wa.apply_time,
          wa.audit_time,
          wa.complete_time,
          wa.failure_reason,
          wa.remark,
          wm.method_name,
          ua.account_name
        FROM withdraw_application wa
        LEFT JOIN withdraw_method wm ON wa.method_code = wm.method_code
        LEFT JOIN user_withdraw_account ua ON wa.account_id = ua.id
        ${whereClause}
        ORDER BY wa.apply_time DESC
        LIMIT ? OFFSET ?
      `, [...params, parseInt(limit), parseInt(offset)]);
      
      // 获取总数
      const [countResult] = await connection.execute(`
        SELECT COUNT(*) as total 
        FROM withdraw_application wa 
        ${whereClause}
      `, params);
      
      res.json({
        status: 'success',
        message: '获取提现记录成功',
        data: {
          list: records,
          total: countResult[0].total,
          page: parseInt(page),
          limit: parseInt(limit)
        }
      });
      
    } catch (error) {
      console.error('获取提现记录失败:', error);
      res.status(500).json({
        status: 'error',
        message: '获取提现记录失败',
        error: error.message
      });
    } finally {
      if (connection) await connection.end();
    }
  }
};

console.log('📋 提现API接口创建完成！');
console.log('\n🚀 可用接口:');
console.log('1. GET  /api/v1/client/withdraw/methods - 获取提现方式');
console.log('2. GET  /api/v1/client/withdraw/accounts - 获取用户提现账户');
console.log('3. POST /api/v1/client/withdraw/accounts - 添加提现账户');
console.log('4. GET  /api/v1/client/withdraw/calculate-fee - 计算手续费');
console.log('5. POST /api/v1/client/withdraw/apply - 提交提现申请');
console.log('6. GET  /api/v1/client/withdraw/records - 获取提现记录');

module.exports = withdrawRoutes;
