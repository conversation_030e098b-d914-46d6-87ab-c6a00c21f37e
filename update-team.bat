@echo off
echo 开始检查和更新团队表结构...

echo.
echo 1. 查看团队表结构:
mysql -u root -pwo587129955 -D mall -e "DESCRIBE team;"

echo.
echo 2. 添加邀请码字段:
mysql -u root -pwo587129955 -D mall -e "ALTER TABLE team ADD COLUMN IF NOT EXISTS invite_code VARCHAR(20) UNIQUE COMMENT '团队邀请码';"

echo.
echo 3. 添加团队等级字段:
mysql -u root -pwo587129955 -D mall -e "ALTER TABLE team ADD COLUMN IF NOT EXISTS level INT DEFAULT 1 COMMENT '团队等级';"

echo.
echo 4. 查看现有团队数据:
mysql -u root -pwo587129955 -D mall -e "SELECT id, name, invite_code, level FROM team;"

echo.
echo 5. 生成邀请码:
mysql -u root -pwo587129955 -D mall -e "UPDATE team SET invite_code = CONCAT('TEAM', LPAD(id, 6, '0')) WHERE invite_code IS NULL OR invite_code = '';"

echo.
echo 6. 设置默认等级:
mysql -u root -pwo587129955 -D mall -e "UPDATE team SET level = 1 WHERE level IS NULL OR level = 0;"

echo.
echo 7. 查看更新后的数据:
mysql -u root -pwo587129955 -D mall -e "SELECT id, name, invite_code, level, created_at FROM team;"

echo.
echo 8. 验证邀请码唯一性:
mysql -u root -pwo587129955 -D mall -e "SELECT invite_code, COUNT(*) as count FROM team WHERE invite_code IS NOT NULL GROUP BY invite_code HAVING COUNT(*) > 1;"

echo.
echo 团队表更新完成！
pause
