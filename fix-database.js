const mysql = require('mysql2/promise');

async function fixDatabase() {
  let connection;
  
  try {
    // 连接数据库
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: 'wo587129955',
      database: 'mall'
    });
    
    console.log('🔧 开始修复team_apply表结构...\n');
    
    // 1. 检查contact字段是否存在
    console.log('1️⃣ 检查contact字段...');
    const [contactCheck] = await connection.execute(`
      SELECT COUNT(*) as count 
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = 'mall' 
      AND TABLE_NAME = 'team_apply' 
      AND COLUMN_NAME = 'contact'
    `);
    
    if (contactCheck[0].count === 0) {
      console.log('添加contact字段...');
      await connection.execute(`
        ALTER TABLE team_apply 
        ADD COLUMN contact VARCHAR(50) NOT NULL COMMENT '联系人姓名' AFTER name
      `);
      console.log('✅ contact字段添加成功');
    } else {
      console.log('✅ contact字段已存在');
    }
    
    // 2. 检查email字段是否存在
    console.log('\n2️⃣ 检查email字段...');
    const [emailCheck] = await connection.execute(`
      SELECT COUNT(*) as count 
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = 'mall' 
      AND TABLE_NAME = 'team_apply' 
      AND COLUMN_NAME = 'email'
    `);
    
    if (emailCheck[0].count === 0) {
      console.log('添加email字段...');
      await connection.execute(`
        ALTER TABLE team_apply 
        ADD COLUMN email VARCHAR(100) DEFAULT NULL COMMENT '电子邮箱' AFTER phone
      `);
      console.log('✅ email字段添加成功');
    } else {
      console.log('✅ email字段已存在');
    }
    
    // 3. 显示修改后的表结构
    console.log('\n3️⃣ 修改后的表结构:');
    const [columns] = await connection.execute('DESCRIBE team_apply');
    console.table(columns);
    
    console.log('\n🎉 数据库表结构修复完成！');
    
  } catch (error) {
    console.error('❌ 修复失败:', error.message);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// 运行修复
fixDatabase();
