/* pages/wifi/detail/detail.wxss */
/* WiFi码详情页面样式 */

.container {
  padding: 0;
  background-color: #f8f9fa;
  min-height: 100vh;
}

/* 加载状态 */
.loading-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
  height: 70vh;
}

.loading-spinner {
  width: 80rpx;
  height: 80rpx;
  border: 6rpx solid rgba(0, 0, 0, 0.1);
  border-top: 6rpx solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  margin-top: 30rpx;
  font-size: 28rpx;
  color: #666;
}

/* 内容区域 */
.content {
  width: 100%;
  padding: 30rpx;
  box-sizing: border-box;
}

/* WiFi码信息卡片 */
.wifi-card {
  background-color: #fff;
  border-radius: 24rpx;
  padding: 40rpx 30rpx;
  box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.08);
  margin-bottom: 30rpx;
}

.wifi-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.wifi-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.wifi-status {
  padding: 6rpx 20rpx;
  border-radius: 30rpx;
  font-size: 24rpx;
  font-weight: 500;
}

.wifi-status.active {
  background-color: #e1f5e6;
  color: #27ae60;
}

.wifi-status.inactive {
  background-color: #feeaea;
  color: #e74c3c;
}

/* 广告模式开关 */
.ad-mode-toggle {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  margin-bottom: 20rpx;
  border-bottom: 2rpx dashed #eee;
}

.toggle-switch {
  position: relative;
  width: 90rpx;
  height: 46rpx;
  border-radius: 23rpx;
  background-color: #e0e0e0;
  margin-right: 20rpx;
  transition: all 0.3s ease;
}

.toggle-switch.on {
  background-color: #FF5722;
}

.toggle-button {
  position: absolute;
  left: 4rpx;
  top: 4rpx;
  width: 38rpx;
  height: 38rpx;
  border-radius: 50%;
  background-color: #fff;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

.toggle-switch.on .toggle-button {
  left: 48rpx;
}

.toggle-label {
  font-size: 28rpx;
  color: #666;
}

/* 广告模式文本 */
.ad-mode-text {
  color: #FF5722 !important;
  font-weight: bold !important;
}

/* 二维码区域 */
.qrcode-section {
  display: flex;
  justify-content: center;
  margin: 20rpx 0 40rpx;
}

.qrcode-wrapper {
  text-align: center;
  padding: 10rpx;
  position: relative;
}

.qrcode-image {
  width: 400rpx;
  height: 400rpx;
  border-radius: 12rpx;
}

.qrcode-tip {
  margin-top: 10rpx;
  font-size: 24rpx;
  color: #999;
}

/* WiFi信息 */
.wifi-info {
  margin-top: 30rpx;
  border-top: 2rpx dashed #eee;
  padding-top: 30rpx;
}

.wifi-info-item {
  position: relative;
  display: flex;
  margin-bottom: 20rpx;
  align-items: center;
}

.info-label {
  width: 180rpx;
  font-size: 28rpx;
  color: #666;
}

.info-value {
  flex: 1;
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
  word-break: break-all;
}

.password-toggle {
  margin-left: 20rpx;
  font-size: 32rpx;
}

.copy-hint {
  position: absolute;
  right: 0;
  font-size: 22rpx;
  color: #999;
}

/* 使用统计 */
.stats-section {
  background-color: #fff;
  border-radius: 24rpx;
  padding: 30rpx;
  box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.08);
  margin-bottom: 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
}

.section-title::before {
  content: '';
  display: inline-block;
  width: 8rpx;
  height: 32rpx;
  background-color: #3498db;
  margin-right: 12rpx;
  border-radius: 4rpx;
}

.stats-grid {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  margin-bottom: 20rpx;
}

.stat-item {
  width: 48%;
  background-color: #f8f9fa;
  border-radius: 16rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  text-align: center;
  box-sizing: border-box;
}

.stat-value {
  display: block;
  font-size: 42rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.stat-label {
  display: block;
  font-size: 26rpx;
  color: #666;
}

/* 详细统计信息 */
.stats-details {
  margin-top: 30rpx;
  padding-top: 20rpx;
  border-top: 2rpx dashed #eee;
}

.stats-detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 0;
  border-bottom: 2rpx solid #f5f5f5;
}

.detail-label {
  font-size: 28rpx;
  color: #666;
}

.detail-value {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
}

.toggle-stats-btn {
  margin-top: 20rpx;
  text-align: center;
  padding: 12rpx 0;
  color: #3498db;
  font-size: 28rpx;
}

.toggle-icon {
  margin-left: 8rpx;
  font-size: 24rpx;
}

/* 操作按钮 */
.action-section {
  margin-bottom: 30rpx;
}

.action-buttons {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.action-btn {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20rpx 0;
  margin: 0 10rpx;
  border-radius: 16rpx;
  background-color: #fff;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  border: none;
}

.action-btn::after {
  border: none;
}

.btn-icon {
  font-size: 40rpx;
  margin-bottom: 10rpx;
}

.btn-text {
  font-size: 26rpx;
  color: #333;
}

.print-btn {
  background-color: #f8f9fa;
}

.edit-btn {
  background-color: #f8f9fa;
}

.delete-btn {
  background-color: #feeaea;
}

.delete-btn .btn-text {
  color: #e74c3c;
}

.share-button {
  margin-top: 20rpx;
}

.btn-primary {
  background-color: #3498db;
  color: #fff;
  border-radius: 16rpx;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx 0;
  font-size: 32rpx;
  border: none;
}

.btn-primary::after {
  border: none;
}

.btn-primary .btn-text {
  color: #fff;
  margin-left: 10rpx;
}

/* 广告区域 */
.ad-section {
  background-color: #fff;
  border-radius: 24rpx;
  padding: 30rpx;
  box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.08);
  margin-bottom: 30rpx;
}

.ad-banner {
  position: relative;
  border-radius: 16rpx;
  overflow: hidden;
  height: 180rpx;
}

.ad-image {
  width: 100%;
  height: 100%;
}

.ad-content {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20rpx;
  background: linear-gradient(to top, rgba(0,0,0,0.7), transparent);
  color: #fff;
}

.ad-title {
  font-size: 30rpx;
  font-weight: bold;
  margin-bottom: 6rpx;
  display: block;
}

.ad-subtitle {
  font-size: 24rpx;
  display: block;
}

/* 推荐商品 */
.recommend-section {
  background-color: #fff;
  border-radius: 24rpx;
  padding: 30rpx;
  box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.08);
  margin-bottom: 30rpx;
}

.goods-grid {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

.goods-item {
  width: 30%;
  margin-bottom: 20rpx;
}

.goods-image {
  width: 100%;
  height: 180rpx;
  border-radius: 12rpx;
}

.goods-info {
  padding: 10rpx 0;
}

.goods-name {
  font-size: 26rpx;
  color: #333;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: block;
}

.goods-price {
  font-size: 28rpx;
  font-weight: bold;
  color: #e74c3c;
  margin-top: 6rpx;
  display: block;
}

/* 元数据信息 */
.meta-section {
  text-align: center;
  padding: 20rpx 0;
}

.meta-text {
  font-size: 24rpx;
  color: #999;
}

/* 底部间距 */
.bottom-spacing {
  height: 100rpx;
}

/* 动画效果 */
.wifi-card, .stats-section, .action-section, .ad-section, .recommend-section {
  animation: slideUp 0.6s ease forwards;
  transform: translateY(20rpx);
  opacity: 0;
}

@keyframes slideUp {
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.wifi-card { animation-delay: 0.1s; }
.stats-section { animation-delay: 0.2s; }
.action-section { animation-delay: 0.3s; }
.ad-section { animation-delay: 0.4s; }
.recommend-section { animation-delay: 0.5s; } 

/* 服务器生成的二维码 */
.server-qrcode {
  border-radius: 8rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  position: relative;
}

/* 广告标签 */
.ad-label {
  position: absolute;
  bottom: 20rpx;
  right: 20rpx;
  background-color: #FF5722;
  color: #FFFFFF;
  font-size: 24rpx;
  padding: 6rpx 12rpx;
  border-radius: 8rpx;
  box-shadow: 0 2rpx 8rpx rgba(255, 87, 34, 0.3);
} 