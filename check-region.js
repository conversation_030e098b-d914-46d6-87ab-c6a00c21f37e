const mysql = require('mysql2/promise');

async function checkRegionTable() {
  const connection = await mysql.createConnection({
    host: 'localhost',
    user: 'root',
    password: 'wo587129955',
    database: 'mall'
  });
  
  try {
    console.log('🔍 检查region表...');
    
    // 检查表是否存在
    const [tables] = await connection.execute(
      "SELECT table_name FROM information_schema.tables WHERE table_schema = 'mall' AND table_name = 'region'"
    );
    
    if (tables.length === 0) {
      console.log('❌ region表不存在，正在创建...');
      
      // 创建region表
      await connection.execute(`
        CREATE TABLE region (
          id int(11) NOT NULL AUTO_INCREMENT,
          name varchar(100) NOT NULL COMMENT '地区名称',
          code varchar(50) NOT NULL COMMENT '地区编码',
          parent_id int(11) DEFAULT NULL COMMENT '父级地区ID',
          level tinyint(4) DEFAULT 1 COMMENT '地区级别：1省/直辖市，2市，3区/县',
          status tinyint(1) DEFAULT 1 COMMENT '状态：0禁用，1启用',
          sort_order int(11) DEFAULT 0 COMMENT '排序',
          created_at timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
          updated_at timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
          PRIMARY KEY (id),
          UNIQUE KEY code (code),
          KEY parent_id (parent_id),
          KEY level (level),
          KEY status (status)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='地区表'
      `);
      
      console.log('✅ region表创建成功');
      
      // 插入一些示例数据
      await connection.execute(`
        INSERT INTO region (name, code, parent_id, level, status, sort_order) VALUES
        ('北京市', '110000', NULL, 1, 1, 1),
        ('上海市', '310000', NULL, 1, 1, 2),
        ('广东省', '440000', NULL, 1, 1, 3),
        ('浙江省', '330000', NULL, 1, 1, 4),
        ('江苏省', '320000', NULL, 1, 1, 5)
      `);
      
      // 获取省份ID并插入市级数据
      const [provinces] = await connection.execute('SELECT id, name, code FROM region WHERE level = 1');
      
      for (const province of provinces) {
        if (province.code === '440000') { // 广东省
          await connection.execute(`
            INSERT INTO region (name, code, parent_id, level, status, sort_order) VALUES
            ('广州市', '440100', ?, 2, 1, 1),
            ('深圳市', '440300', ?, 2, 1, 2),
            ('珠海市', '440400', ?, 2, 1, 3),
            ('东莞市', '441900', ?, 2, 1, 4)
          `, [province.id, province.id, province.id, province.id]);
        } else if (province.code === '330000') { // 浙江省
          await connection.execute(`
            INSERT INTO region (name, code, parent_id, level, status, sort_order) VALUES
            ('杭州市', '330100', ?, 2, 1, 1),
            ('宁波市', '330200', ?, 2, 1, 2),
            ('温州市', '330300', ?, 2, 1, 3)
          `, [province.id, province.id, province.id]);
        }
      }
      
      console.log('✅ 示例数据插入成功');
    } else {
      console.log('✅ region表已存在');
    }
    
    // 查询现有数据
    const [regions] = await connection.execute('SELECT * FROM region ORDER BY level, sort_order, id');
    console.log(`📊 当前地区数量: ${regions.length}`);
    
    if (regions.length > 0) {
      console.log('📋 地区列表:');
      regions.forEach(region => {
        const levelName = region.level === 1 ? '省/市' : region.level === 2 ? '市' : '区/县';
        console.log(`  - ID: ${region.id}, 名称: ${region.name}, 编码: ${region.code}, 级别: ${levelName}, 状态: ${region.status ? '启用' : '禁用'}`);
      });
    }
    
  } catch (error) {
    console.error('❌ 错误:', error.message);
  } finally {
    await connection.end();
  }
}

checkRegionTable();
