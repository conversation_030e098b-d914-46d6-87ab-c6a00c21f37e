const axios = require('axios');
const jwt = require('jsonwebtoken');

async function testIncomeAPI() {
  try {
    console.log('🧪 测试收入API接口...\n');
    
    // 1. 生成测试token
    const testUser = {
      id: 1,
      openid: 'oW20C7mVlW8e3W2AgUGtDTJeAbQU'
    };
    
    const token = jwt.sign(
      testUser,
      'wifi_share_secret_key',
      { expiresIn: '24h' }
    );
    
    console.log('1️⃣ 生成的测试token:', token.substring(0, 50) + '...\n');
    
    // 2. 测试收入统计接口
    console.log('2️⃣ 测试收入统计接口...');
    try {
      const statsResponse = await axios.get('http://localhost:4000/api/v1/client/income/stats', {
        headers: {
          'Authorization': `Bearer ${token}`
        },
        timeout: 10000
      });
      
      console.log('✅ 收入统计接口成功:');
      console.log('状态码:', statsResponse.status);
      console.log('响应数据:', JSON.stringify(statsResponse.data, null, 2));
      
    } catch (statsError) {
      if (statsError.response) {
        console.log('❌ 收入统计接口失败:');
        console.log('状态码:', statsError.response.status);
        console.log('错误信息:', JSON.stringify(statsError.response.data, null, 2));
      } else {
        console.log('❌ 收入统计请求失败:', statsError.message);
      }
    }
    
    // 3. 测试收入明细接口
    console.log('\n3️⃣ 测试收入明细接口...');
    try {
      const detailsResponse = await axios.get('http://localhost:4000/api/v1/client/income/details', {
        headers: {
          'Authorization': `Bearer ${token}`
        },
        params: {
          page: 1,
          limit: 10
        },
        timeout: 10000
      });
      
      console.log('✅ 收入明细接口成功:');
      console.log('状态码:', detailsResponse.status);
      console.log('响应数据:', JSON.stringify(detailsResponse.data, null, 2));
      
    } catch (detailsError) {
      if (detailsError.response) {
        console.log('❌ 收入明细接口失败:');
        console.log('状态码:', detailsError.response.status);
        console.log('错误信息:', JSON.stringify(detailsError.response.data, null, 2));
      } else {
        console.log('❌ 收入明细请求失败:', detailsError.message);
      }
    }
    
    // 4. 测试钱包交易记录接口
    console.log('\n4️⃣ 测试钱包交易记录接口...');
    try {
      const transactionResponse = await axios.get('http://localhost:4000/api/v1/client/income/transactions', {
        headers: {
          'Authorization': `Bearer ${token}`
        },
        params: {
          page: 1,
          limit: 10
        },
        timeout: 10000
      });
      
      console.log('✅ 钱包交易记录接口成功:');
      console.log('状态码:', transactionResponse.status);
      console.log('响应数据:', JSON.stringify(transactionResponse.data, null, 2));
      
    } catch (transactionError) {
      if (transactionError.response) {
        console.log('❌ 钱包交易记录接口失败:');
        console.log('状态码:', transactionError.response.status);
        console.log('错误信息:', JSON.stringify(transactionError.response.data, null, 2));
      } else {
        console.log('❌ 钱包交易记录请求失败:', transactionError.message);
      }
    }
    
    // 5. 直接查询数据库验证数据
    console.log('\n5️⃣ 验证数据库数据...');
    await verifyDatabaseData();
    
  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error.message);
  }
}

// 验证数据库数据
async function verifyDatabaseData() {
  const mysql = require('mysql2/promise');
  
  try {
    const connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: 'wo587129955',
      database: 'mall'
    });
    
    // 查询用户收入信息
    console.log('📊 用户收入信息:');
    const [userIncome] = await connection.execute(`
      SELECT 
        id,
        nickname,
        balance,
        total_income,
        today_income,
        month_income,
        total_withdraw
      FROM user 
      WHERE id = 1
    `);
    console.table(userIncome);
    
    // 查询收入记录
    console.log('\n📋 收入记录:');
    const [incomeRecords] = await connection.execute(`
      SELECT 
        id,
        amount,
        type,
        title,
        description,
        status,
        created_at
      FROM income_record 
      WHERE user_id = 1 
      ORDER BY created_at DESC 
      LIMIT 5
    `);
    console.table(incomeRecords);
    
    // 查询钱包交易记录
    console.log('\n💰 钱包交易记录:');
    const [transactions] = await connection.execute(`
      SELECT 
        id,
        type,
        amount,
        balance_before,
        balance_after,
        title,
        created_at
      FROM wallet_transaction 
      WHERE user_id = 1 
      ORDER BY created_at DESC 
      LIMIT 5
    `);
    console.table(transactions);
    
    // 查询收入统计
    console.log('\n📈 收入统计:');
    const [statistics] = await connection.execute(`
      SELECT 
        stat_date,
        stat_type,
        total_income,
        wifi_income,
        ad_income,
        referral_income,
        transaction_count
      FROM income_statistics 
      WHERE user_id = 1 
      ORDER BY stat_date DESC
    `);
    console.table(statistics);
    
    await connection.end();
    
  } catch (error) {
    console.error('❌ 数据库验证失败:', error.message);
  }
}

// 运行测试
testIncomeAPI();
