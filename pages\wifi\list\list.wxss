/* pages/wifi/list/list.wxss */
/* WiFi码列表页面样式 */

.container {
  min-height: 100vh;
  background: #f5f6fa;
  padding-bottom: 120rpx;
}

/* 搜索栏 */
.search-section {
  padding: 20rpx;
  background: #ffffff;
  border-bottom: 1rpx solid #e9ecef;
  position: sticky;
  top: 0;
  z-index: 10;
}

.search-bar {
  background: #f8f9fa;
  border-radius: 25rpx;
  padding: 20rpx;
}

.search-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.search-input {
  flex: 1;
  height: 60rpx;
  padding: 0 80rpx 0 20rpx;
  font-size: 28rpx;
  background: transparent;
  border: none;
}

.search-icon {
  position: absolute;
  right: 20rpx;
  color: #999999;
  font-size: 32rpx;
}

.clear-icon {
  position: absolute;
  right: 20rpx;
  color: #999999;
  font-size: 28rpx;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.clear-icon.show {
  opacity: 1;
}

/* 列表区域 */
.list-section {
  padding: 20rpx;
}

/* 加载状态 */
.loading-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 80rpx 20rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

.loading-spinner.small {
  width: 40rpx;
  height: 40rpx;
  border-width: 4rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #666666;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* WiFi码列表 */
.wifi-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.wifi-item {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.wifi-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 6rpx;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  transform: scaleY(0);
  transition: transform 0.3s ease;
}

.wifi-item:active::before {
  transform: scaleY(1);
}

/* WiFi码信息 */
.wifi-info {
  margin-bottom: 25rpx;
}

.wifi-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.wifi-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  flex: 1;
  margin-right: 20rpx;
}

.wifi-status {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  font-weight: 600;
}

.wifi-status.active {
  background: rgba(7, 193, 96, 0.1);
  color: #07c160;
}

.wifi-status.inactive {
  background: rgba(255, 71, 87, 0.1);
  color: #ff4757;
}

.wifi-details {
  margin-bottom: 20rpx;
}

.wifi-detail-item {
  display: flex;
  margin-bottom: 8rpx;
  font-size: 26rpx;
}

.detail-label {
  color: #666666;
  width: 140rpx;
  flex-shrink: 0;
}

.detail-value {
  color: #333333;
  flex: 1;
  word-break: break-all;
}

.wifi-stats {
  display: flex;
  justify-content: space-between;
  padding: 20rpx 0;
  border-top: 1rpx solid #f1f2f6;
  margin-bottom: 15rpx;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.stat-value {
  font-size: 28rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 22rpx;
  color: #999999;
}

.wifi-meta {
  font-size: 24rpx;
  color: #999999;
}

/* 操作按钮 */
.wifi-actions {
  display: flex;
  gap: 15rpx;
  justify-content: flex-end;
}

.action-btn {
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 600;
  border: none;
  transition: all 0.3s ease;
}

.action-btn::after {
  border: none;
}

.edit-btn {
  background: rgba(24, 144, 255, 0.1);
  color: #1890ff;
}

.edit-btn:active {
  background: rgba(24, 144, 255, 0.2);
}

.share-btn {
  background: rgba(7, 193, 96, 0.1);
  color: #07c160;
}

.share-btn:active {
  background: rgba(7, 193, 96, 0.2);
}

.delete-btn {
  background: rgba(255, 71, 87, 0.1);
  color: #ff4757;
}

.delete-btn:active {
  background: rgba(255, 71, 87, 0.2);
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 120rpx 40rpx;
  text-align: center;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 40rpx;
  opacity: 0.6;
}

.empty-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #666666;
  margin-bottom: 16rpx;
}

.empty-subtitle {
  font-size: 26rpx;
  color: #999999;
  margin-bottom: 40rpx;
  line-height: 1.6;
}

.empty-action-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #ffffff;
  border: none;
  border-radius: 25rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
  font-weight: 600;
}

.empty-action-btn::after {
  border: none;
}

/* 加载更多 */
.load-more {
  padding: 40rpx 20rpx;
  text-align: center;
}

.load-more-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
}

.load-more-text {
  font-size: 26rpx;
  color: #999999;
  margin-left: 16rpx;
}

.load-more-end .load-more-text {
  margin-left: 0;
}

/* 悬浮创建按钮 */
.fab-btn {
  position: fixed;
  bottom: 120rpx;
  right: 40rpx;
  width: 100rpx;
  height: 100rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 20rpx rgba(102, 126, 234, 0.4);
  z-index: 100;
  transition: all 0.3s ease;
}

.fab-btn:active {
  transform: scale(0.95);
  box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.6);
}

.fab-icon {
  font-size: 48rpx;
  font-weight: 300;
  color: #ffffff;
  line-height: 1;
}

/* 动画效果 */
.wifi-item {
  animation: slideIn 0.6s ease forwards;
  transform: translateY(20rpx);
  opacity: 0;
}

@keyframes slideIn {
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.wifi-item:nth-child(1) { animation-delay: 0.1s; }
.wifi-item:nth-child(2) { animation-delay: 0.2s; }
.wifi-item:nth-child(3) { animation-delay: 0.3s; }
.wifi-item:nth-child(4) { animation-delay: 0.4s; }
.wifi-item:nth-child(5) { animation-delay: 0.5s; } 