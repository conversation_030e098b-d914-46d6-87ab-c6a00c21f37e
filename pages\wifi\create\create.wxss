/* pages/wifi/create/create.wxss */
/* WiFi码创建页面样式 */

.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 0;
}

/* 顶部说明区域 */
.header-section {
  padding: 60rpx 40rpx 40rpx;
  text-align: center;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10rpx);
}

.wifi-icon {
  margin-bottom: 30rpx;
}

.wifi-image {
  width: 120rpx;
  height: 120rpx;
  filter: brightness(0) invert(1);
}

.description {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.description .title {
  font-size: 36rpx;
  font-weight: bold;
  color: #ffffff;
  margin-bottom: 10rpx;
}

.description .subtitle {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
}

/* 表单区域 */
.form-section {
  background: #ffffff;
  border-radius: 40rpx 40rpx 0 0;
  padding: 60rpx 40rpx 40rpx;
  margin-top: 40rpx;
  min-height: calc(100vh - 400rpx);
}

.form-item {
  margin-bottom: 50rpx;
}

.form-label {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
}

.required {
  color: #ff4757;
  margin-left: 8rpx;
  font-size: 30rpx;
}

.form-input-wrapper {
  position: relative;
  background: #f8f9fa;
  border-radius: 16rpx;
  border: 2rpx solid #e9ecef;
  transition: all 0.3s ease;
}

.form-input-wrapper:focus-within {
  border-color: #667eea;
  background: #ffffff;
  box-shadow: 0 0 0 6rpx rgba(102, 126, 234, 0.1);
}

.form-input {
  width: 100%;
  height: 90rpx;
  padding: 0 20rpx 0 20rpx;
  font-size: 28rpx;
  color: #333333;
  background: transparent;
  border: none;
  box-sizing: border-box;
}

.form-input.error {
  border-color: #ff4757 !important;
  background: #fff5f5 !important;
}

.input-counter {
  position: absolute;
  right: 20rpx;
  top: 50%;
  transform: translateY(-50%);
  font-size: 22rpx;
  color: #999999;
}

.form-error {
  margin-top: 10rpx;
  padding-left: 20rpx;
  font-size: 24rpx;
  color: #ff4757;
}

/* 操作按钮区域 */
.action-section {
  padding: 40rpx;
  background: #ffffff;
}

.action-buttons {
  display: flex;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.btn {
  flex: 1;
  height: 96rpx;
  border-radius: 48rpx;
  font-size: 30rpx;
  font-weight: 600;
  border: none;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.btn::after {
  border: none;
}

.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #ffffff;
  box-shadow: 0 8rpx 20rpx rgba(102, 126, 234, 0.3);
}

.btn-primary:not(.loading):not([disabled]):active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.4);
}

.btn-primary.loading {
  background: linear-gradient(135deg, #9bb5ff 0%, #a584d4 100%);
}

.btn-secondary {
  background: #f8f9fa;
  color: #6c757d;
  border: 2rpx solid #e9ecef;
}

.btn-secondary:not([disabled]):active {
  background: #e9ecef;
  transform: translateY(2rpx);
}

.btn[disabled] {
  opacity: 0.6;
  transform: none !important;
}

.help-tip {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx;
  color: #6c757d;
  font-size: 26rpx;
}

.help-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  background: #6c757d;
  color: #ffffff;
  font-size: 22rpx;
  margin-right: 12rpx;
}

.help-text {
  text-decoration: underline;
}

/* 广告区域 */
.ad-section {
  padding: 20rpx 40rpx;
  background: #ffffff;
}

.ad-banner {
  position: relative;
  height: 160rpx;
  border-radius: 16rpx;
  overflow: hidden;
  background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
}

.ad-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.ad-content {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background: rgba(0, 0, 0, 0.4);
  color: #ffffff;
}

.ad-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.ad-subtitle {
  font-size: 24rpx;
  opacity: 0.9;
}

/* 底部间距 */
.bottom-spacing {
  height: 100rpx;
  background: #ffffff;
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.form-item {
  animation: fadeIn 0.6s ease forwards;
}

.form-item:nth-child(1) { animation-delay: 0.1s; }
.form-item:nth-child(2) { animation-delay: 0.2s; }
.form-item:nth-child(3) { animation-delay: 0.3s; }
.form-item:nth-child(4) { animation-delay: 0.4s; }

/* 加载动画 */
.btn-primary.loading::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 40rpx;
  height: 40rpx;
  margin: -20rpx 0 0 -60rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
  border-top: 4rpx solid #ffffff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
} 