const db = require('../database');
const logger = require('../utils/logger');

/**
 * 平台收益统计服务
 */
class PlatformStatsService {
  
  /**
   * 更新平台实时统计
   * @param {string} profitType 收益类型
   * @param {number} totalAmount 总金额
   * @param {number} platformAmount 平台分润
   * @param {number} regionId 地区ID
   */
  static async updateRealtimeStats(profitType, totalAmount, platformAmount, regionId = null) {
    try {
      await db.transaction(async (connection) => {
        const today = new Date().toISOString().split('T')[0];
        
        // 更新全平台总收益
        await connection.query(`
          INSERT INTO platform_stats_realtime (stat_key, region_id, profit_type, stat_value, count_value)
          VALUES ('total_revenue', NULL, NULL, ?, 1)
          ON DUPLICATE KEY UPDATE 
            stat_value = stat_value + VALUES(stat_value),
            count_value = count_value + 1
        `, [totalAmount]);
        
        // 更新今日收益
        await connection.query(`
          INSERT INTO platform_stats_realtime (stat_key, region_id, profit_type, stat_value, count_value)
          VALUES ('today_revenue', NULL, NULL, ?, 1)
          ON DUPLICATE KEY UPDATE 
            stat_value = stat_value + VALUES(stat_value),
            count_value = count_value + 1
        `, [totalAmount]);
        
        // 更新按类型统计
        await connection.query(`
          INSERT INTO platform_stats_realtime (stat_key, region_id, profit_type, stat_value, count_value)
          VALUES ('total_revenue', NULL, ?, ?, 1)
          ON DUPLICATE KEY UPDATE 
            stat_value = stat_value + VALUES(stat_value),
            count_value = count_value + 1
        `, [profitType, totalAmount]);
        
        // 更新平台分润统计
        await connection.query(`
          INSERT INTO platform_stats_realtime (stat_key, region_id, profit_type, stat_value, count_value)
          VALUES ('total_platform_profit', NULL, NULL, ?, 1)
          ON DUPLICATE KEY UPDATE 
            stat_value = stat_value + VALUES(stat_value),
            count_value = count_value + 1
        `, [platformAmount]);
        
        // 如果有地区信息，更新地区统计
        if (regionId) {
          await connection.query(`
            INSERT INTO platform_stats_realtime (stat_key, region_id, profit_type, stat_value, count_value)
            VALUES ('total_revenue', ?, NULL, ?, 1)
            ON DUPLICATE KEY UPDATE 
              stat_value = stat_value + VALUES(stat_value),
              count_value = count_value + 1
          `, [regionId, totalAmount]);
        }
      });
      
      logger.info(`平台统计更新成功: ${profitType} ¥${totalAmount}`);
    } catch (error) {
      logger.error(`更新平台统计失败: ${error.message}`);
      throw error;
    }
  }
  
  /**
   * 获取平台收益统计
   * @param {string} timeRange 时间范围：today,week,month,year,all
   * @param {number} regionId 地区ID（可选）
   * @returns {Promise<object>} 统计数据
   */
  static async getPlatformStats(timeRange = 'all', regionId = null) {
    try {
      let timeCondition = '';
      switch (timeRange) {
        case 'today':
          timeCondition = 'AND DATE(pl.created_at) = CURDATE()';
          break;
        case 'week':
          timeCondition = 'AND YEARWEEK(pl.created_at) = YEARWEEK(NOW())';
          break;
        case 'month':
          timeCondition = 'AND YEAR(pl.created_at) = YEAR(NOW()) AND MONTH(pl.created_at) = MONTH(NOW())';
          break;
        case 'year':
          timeCondition = 'AND YEAR(pl.created_at) = YEAR(NOW())';
          break;
      }
      
      let regionCondition = '';
      const params = [];
      if (regionId) {
        regionCondition = 'AND pl.region_id = ?';
        params.push(regionId);
      }
      
      // 获取分润统计
      const profitStats = await db.query(`
        SELECT 
          pl.source_type as profit_type,
          COUNT(*) as transaction_count,
          SUM(CASE WHEN pl.role = 'platform' THEN pl.amount ELSE 0 END) as platform_amount,
          SUM(CASE WHEN pl.role = 'leader' THEN pl.amount ELSE 0 END) as leader_amount,
          SUM(CASE WHEN pl.role = 'member' THEN pl.amount ELSE 0 END) as member_amount,
          SUM(pl.amount) as total_profit
        FROM profit_log pl
        WHERE 1=1 ${timeCondition} ${regionCondition}
        GROUP BY pl.source_type
        ORDER BY pl.source_type
      `, params);
      
      // 获取总计
      const totalStats = await db.query(`
        SELECT 
          COUNT(*) as total_transactions,
          SUM(CASE WHEN pl.role = 'platform' THEN pl.amount ELSE 0 END) as total_platform_amount,
          SUM(CASE WHEN pl.role = 'leader' THEN pl.amount ELSE 0 END) as total_leader_amount,
          SUM(CASE WHEN pl.role = 'member' THEN pl.amount ELSE 0 END) as total_member_amount,
          SUM(pl.amount) as total_profit,
          COUNT(DISTINCT pl.user_id) as active_users,
          COUNT(DISTINCT pl.team_id) as active_teams
        FROM profit_log pl
        WHERE 1=1 ${timeCondition} ${regionCondition}
      `, params);
      
      // 获取用户统计
      const userStats = await db.query(`
        SELECT 
          COUNT(*) as total_users,
          SUM(balance) as total_balance,
          AVG(balance) as avg_balance
        FROM user
        ${regionId ? 'WHERE region_id = ?' : ''}
      `, regionId ? [regionId] : []);
      
      // 获取团队统计
      const teamStats = await db.query(`
        SELECT 
          COUNT(*) as total_teams,
          AVG(member_count) as avg_team_size
        FROM team
        ${regionId ? 'WHERE region_id = ?' : ''}
      `, regionId ? [regionId] : []);
      
      const result = {
        timeRange,
        regionId,
        summary: {
          totalTransactions: totalStats[0]?.total_transactions || 0,
          totalPlatformAmount: parseFloat(totalStats[0]?.total_platform_amount || 0).toFixed(2),
          totalLeaderAmount: parseFloat(totalStats[0]?.total_leader_amount || 0).toFixed(2),
          totalMemberAmount: parseFloat(totalStats[0]?.total_member_amount || 0).toFixed(2),
          totalProfit: parseFloat(totalStats[0]?.total_profit || 0).toFixed(2),
          activeUsers: totalStats[0]?.active_users || 0,
          activeTeams: totalStats[0]?.active_teams || 0
        },
        byType: profitStats.map(stat => ({
          profitType: stat.profit_type,
          transactionCount: stat.transaction_count,
          platformAmount: parseFloat(stat.platform_amount).toFixed(2),
          leaderAmount: parseFloat(stat.leader_amount).toFixed(2),
          memberAmount: parseFloat(stat.member_amount).toFixed(2),
          totalProfit: parseFloat(stat.total_profit).toFixed(2)
        })),
        users: {
          totalUsers: userStats[0]?.total_users || 0,
          totalBalance: parseFloat(userStats[0]?.total_balance || 0).toFixed(2),
          avgBalance: parseFloat(userStats[0]?.avg_balance || 0).toFixed(2)
        },
        teams: {
          totalTeams: teamStats[0]?.total_teams || 0,
          avgTeamSize: parseFloat(teamStats[0]?.avg_team_size || 0).toFixed(1)
        },
        generatedAt: new Date()
      };
      
      return result;
    } catch (error) {
      logger.error(`获取平台统计失败: ${error.message}`);
      throw error;
    }
  }
  
  /**
   * 获取收益趋势数据
   * @param {number} days 天数
   * @param {number} regionId 地区ID（可选）
   * @returns {Promise<Array>} 趋势数据
   */
  static async getRevenueTrend(days = 7, regionId = null) {
    try {
      let regionCondition = '';
      const params = [days];
      if (regionId) {
        regionCondition = 'AND pl.region_id = ?';
        params.push(regionId);
      }
      
      const trendData = await db.query(`
        SELECT 
          DATE(pl.created_at) as date,
          pl.source_type as profit_type,
          SUM(CASE WHEN pl.role = 'platform' THEN pl.amount ELSE 0 END) as platform_amount,
          SUM(pl.amount) as total_amount,
          COUNT(*) as transaction_count
        FROM profit_log pl
        WHERE pl.created_at >= DATE_SUB(NOW(), INTERVAL ? DAY) ${regionCondition}
        GROUP BY DATE(pl.created_at), pl.source_type
        ORDER BY date DESC, profit_type
      `, params);
      
      return trendData.map(item => ({
        date: item.date,
        profitType: item.profit_type,
        platformAmount: parseFloat(item.platform_amount).toFixed(2),
        totalAmount: parseFloat(item.total_amount).toFixed(2),
        transactionCount: item.transaction_count
      }));
    } catch (error) {
      logger.error(`获取收益趋势失败: ${error.message}`);
      throw error;
    }
  }
  
  /**
   * 刷新团队业绩缓存
   * @param {number} teamId 团队ID
   */
  static async refreshTeamCache(teamId) {
    try {
      await db.query('CALL RefreshTeamPerformanceCache(?)', [teamId]);
      logger.info(`团队${teamId}业绩缓存刷新成功`);
    } catch (error) {
      logger.error(`刷新团队业绩缓存失败: ${error.message}`);
      throw error;
    }
  }
  
  /**
   * 记录余额变动
   * @param {object} data 余额变动数据
   */
  static async logBalanceChange(data) {
    try {
      await db.query(`
        INSERT INTO balance_log (
          user_id, amount, type, before_balance, after_balance,
          source_type, source_id, profit_log_id, remark, created_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
      `, [
        data.userId,
        data.amount,
        data.type,
        data.beforeBalance,
        data.afterBalance,
        data.sourceType || null,
        data.sourceId || null,
        data.profitLogId || null,
        data.remark || null
      ]);
      
      logger.info(`余额变动记录成功: 用户${data.userId} ${data.type} ¥${data.amount}`);
    } catch (error) {
      logger.error(`记录余额变动失败: ${error.message}`);
      throw error;
    }
  }
}

module.exports = PlatformStatsService;
