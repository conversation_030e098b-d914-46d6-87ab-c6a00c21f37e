const db = require('./src/database');

async function fixTeamLeader() {
  try {
    console.log('🔧 修复团队团长信息...\n');
    
    // 查询团队ID 10的信息
    console.log('📋 当前团队ID 10的信息:');
    const team10 = await db.query('SELECT * FROM team WHERE id = 10');
    console.table(team10);
    
    // 查询对应的联盟申请
    console.log('\n📋 对应的联盟申请信息:');
    const application = await db.query('SELECT * FROM team_apply WHERE id = 26');
    console.table(application);
    
    // 检查用户ID 1是否存在
    console.log('\n👤 检查用户ID 1是否存在:');
    const user1 = await db.query('SELECT * FROM user WHERE id = 1');
    console.log(`用户ID 1存在: ${user1.length > 0}`);
    
    if (user1.length === 0) {
      console.log('\n❌ 用户ID 1不存在，需要创建或使用其他用户');
      
      // 选择一个存在的用户作为团长（比如用户ID 3）
      console.log('\n🔄 将团队10的团长改为用户ID 3...');
      
      // 更新团队的leader_id
      await db.query('UPDATE team SET leader_id = 3 WHERE id = 10');

      // 更新团队成员表
      await db.query('UPDATE team_member SET user_id = 3 WHERE team_id = 10');

      // 更新联盟申请的user_id
      await db.query('UPDATE team_apply SET user_id = 3 WHERE id = 26');

      console.log('✅ 团队信息已更新');
      
      // 查询更新后的信息
      console.log('\n✅ 更新后的团队信息:');
      const updatedTeam = await db.query(`
        SELECT 
          t.id as team_id,
          t.name as team_name,
          t.leader_id,
          u.nickname as leader_nickname,
          u.phone as leader_phone
        FROM team t
        LEFT JOIN user u ON t.leader_id = u.id
        WHERE t.id = 10
      `);
      console.table(updatedTeam);
    } else {
      console.log('\n✅ 用户ID 1存在，团队信息正常');
    }
    
    process.exit(0);
  } catch (error) {
    console.error('❌ 修复失败:', error);
    process.exit(1);
  }
}

fixTeamLeader();
