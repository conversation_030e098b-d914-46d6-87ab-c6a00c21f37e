// 类型定义
interface AudioListItem {
  data: {
    link: string;
  };
}

interface DurationItem {
  duration: number;
}

interface Args {
  params: {
    audio_list_data: AudioListItem[];
    texts: string[];
    duration_data: DurationItem[];
    bg_big_text: string;
    bg_audio_url: string;
    bg_video_url: string;
  };
}

interface Output {
  initialAudioData: string;
  result: any[];
  keywords: string[];
  bg_audio_data: string;
  bg_video_data: string;
  bg_big_text_data: string[];
  bg_big_text_timeline: Array<{ start: number; end: number }>;
}

async function main({ params }: Args): Promise<Output> {
  const { audio_list_data, texts, duration_data, bg_big_text, bg_audio_url, bg_video_url } = params;

  // 参数验证和默认值处理
  console.log('Input validation:');
  console.log('audio_list_data:', audio_list_data);
  console.log('texts:', texts);
  console.log('duration_data:', duration_data);
  console.log('bg_big_text:', bg_big_text);
  console.log('bg_audio_url:', bg_audio_url);
  console.log('bg_video_url:', bg_video_url);

  // 检查必需参数
  if (!audio_list_data || !Array.isArray(audio_list_data) || audio_list_data.length === 0) {
    throw new Error("audio_list_data is required and must be a non-empty array");
  }

  if (!texts || !Array.isArray(texts) || texts.length === 0) {
    throw new Error("texts is required and must be a non-empty array");
  }

  if (!duration_data || !Array.isArray(duration_data) || duration_data.length === 0) {
    throw new Error("duration_data is required and must be a non-empty array");
  }

  // 检查数组长度一致性
  if (audio_list_data.length !== texts.length || texts.length !== duration_data.length) {
    throw new Error(`Array length mismatch: audio_list_data(${audio_list_data.length}), texts(${texts.length}), duration_data(${duration_data.length})`);
  }

  // 验证数据结构
  for (let i = 0; i < audio_list_data.length; i++) {
    if (!audio_list_data[i] || !audio_list_data[i].data || !audio_list_data[i].data.link) {
      throw new Error(`Invalid audio_list_data structure at index ${i}: missing data.link`);
    }
    if (typeof duration_data[i].duration !== 'number' || duration_data[i].duration <= 0) {
      throw new Error(`Invalid duration at index ${i}: must be a positive number`);
    }
    if (typeof texts[i] !== 'string' || texts[i].trim() === '') {
      throw new Error(`Invalid text at index ${i}: must be a non-empty string`);
    }
  }

  // 提取数据
  const duration_list = duration_data.map(item => item.duration);
  const audio_list = audio_list_data.map(item => item.data.link);

  let audioStartTime = 0;
  let maxDuration = 0;
  const initialAudioData: {
    audio_url: string;
    duration: number;
    volume: number;
    start: number;
    end: number;
  }[] = [];

  // 构建音频数据
  for (let i = 0; i < audio_list.length; i++) {
    const duration = duration_list[i];
    initialAudioData.push({
      audio_url: audio_list[i],
      duration: duration,
      volume: 2,
      start: audioStartTime,
      end: audioStartTime + duration
    });
    audioStartTime += duration;
    maxDuration = audioStartTime;
  }

  // 背景音频数据
  const bg_audio_data = [
    {
      audio_url: bg_audio_url || '',
      duration: maxDuration,
      start: 0,
      end: maxDuration,
      volume: 2
    }
  ];

  // 视频数据
  const video_data = [
    {
      video_url: bg_video_url || '',
      duration: maxDuration,
      width: 1920,
      height: 1080,
      start: 0,
      end: maxDuration
    }
  ];

  // 生成字幕
  const { result, keywords } = generateSubtitles(duration_list, texts);

  return {
    initialAudioData: JSON.stringify(initialAudioData),
    result,
    keywords,
    bg_audio_data: JSON.stringify(bg_audio_data),
    bg_video_data: JSON.stringify(video_data),
    bg_big_text_data: [bg_big_text || ''],
    bg_big_text_timeline: [
      {
        start: 0,
        end: maxDuration
      }
    ]
  };
}

// ----------------------------

const presetAnimations = [
  '渐显', '羽化向右擦开', '放大', '向上露出', '向下滑动',
  '向下飞入', '站起', '逐字旋转', '甩出', '右上弹入',
  '滑动上升', '旋入'
];

// 拆分文本函数：平均拆成两段（适用于长度 > 8）
function splitText(text: string, maxSegments = 2): string[] {
  if (maxSegments <= 1 || text.length <= 8) {
    return [text];
  }

  const mid = Math.floor(text.length / 2);
  return [text.slice(0, mid), text.slice(mid)];
}

// 主字幕生成函数
function generateSubtitles(durationList: number[], texts: string[]): { result: any[], keywords: string[] } {
  const result: any[] = [];
  const keywords: string[] = [];
  let globalOffset = 0;

  for (let i = 0; i < texts.length; i++) {
    const rawText = texts[i].replace(/\s+/g, '');
    const duration = durationList[i];

    const segmentCount = rawText.length > 8 ? 2 : 1;
    const segments = splitText(rawText, segmentCount);

    let positionGroup: { x: number; y: number }[];
    if (segments.length === 1) {
      positionGroup = [{ x: 0, y: 0 }];
    } else {
      positionGroup = [{ x: -573, y: 523 }, { x: 573, y: -523 }];
    }

    const keyword = rawText.length >= 2 ? rawText.slice(-2) : '';
    if (keyword) {
      keywords.push(keyword);
    }

    for (let j = 0; j < segments.length; j++) {
      const segment = segments[j];
      const position = positionGroup[j] || positionGroup[positionGroup.length - 1];
      const animation = presetAnimations[Math.floor(Math.random() * presetAnimations.length)];

      result.push({
        text_positions: position,
        text_in_animation: [animation],
        texts: [segment],
        timelines: [{
          start: globalOffset,
          end: globalOffset + duration
        }],
        keywords: [keyword]
      });
    }

    globalOffset += duration;
  }

  return { result, keywords };
}

// 导出函数
export { main };
