<!-- 提现记录页面 -->
<view class="records-container">
  <!-- 统计信息卡片 -->
  <view class="stats-card" wx:if="{{showStats}}">
    <view class="stats-title">提现统计</view>
    <view class="stats-content">
      <view class="stats-item">
        <view class="stats-value">{{stats.totalAmount || '0.00'}}</view>
        <view class="stats-label">总提现金额</view>
      </view>
      <view class="stats-item">
        <view class="stats-value">{{stats.totalCount || 0}}</view>
        <view class="stats-label">提现次数</view>
      </view>
      <view class="stats-item">
        <view class="stats-value">{{stats.successCount || 0}}</view>
        <view class="stats-label">成功次数</view>
      </view>
    </view>
  </view>

  <!-- 筛选器 -->
  <view class="filter-bar" wx:if="{{showFilter}}">
    <view 
      class="filter-item {{filterStatus === '' ? 'active' : ''}}"
      bindtap="onFilterChange"
      data-status=""
    >
      全部
    </view>
    <view 
      class="filter-item {{filterStatus === '0' ? 'active' : ''}}"
      bindtap="onFilterChange"
      data-status="0"
    >
      待审核
    </view>
    <view 
      class="filter-item {{filterStatus === '4' ? 'active' : ''}}"
      bindtap="onFilterChange"
      data-status="4"
    >
      已完成
    </view>
    <view 
      class="filter-item {{filterStatus === '2' ? 'active' : ''}}"
      bindtap="onFilterChange"
      data-status="2"
    >
      已拒绝
    </view>
  </view>

  <!-- 提现记录列表 -->
  <view class="records-list">
    <view 
      wx:for="{{records}}" 
      wx:key="id"
      class="record-item"
      bindtap="onRecordTap"
      data-record="{{item}}"
    >
      <!-- 记录头部 -->
      <view class="record-header">
        <view class="record-type">
          <image
            src="{{item.withdraw_type === 'wechat' ? '/assets/icons/user.png' : '/assets/icons/cart.png'}}"
            mode="aspectFit"
          />
          <text>{{item.withdraw_type === 'wechat' ? '微信' : (item.bank_name || '银行卡')}}</text>
        </view>
        <view class="record-status status-{{item.status}}">
          {{getStatusText(item.status)}}
        </view>
      </view>
      
      <!-- 提现金额 -->
      <view class="record-amount">-¥{{item.amount}}</view>
      
      <!-- 详细信息 -->
      <view class="record-details">
        <view class="detail-row">
          <text class="label">提现金额：</text>
          <text class="value">¥{{item.amount}}</text>
        </view>
        <view class="detail-row">
          <text class="label">手续费：</text>
          <text class="value">¥{{item.fee}}</text>
        </view>
        <view class="detail-row">
          <text class="label">实际到账：</text>
          <text class="value">¥{{item.actual_amount}}</text>
        </view>
        <view class="detail-row">
          <text class="label">申请时间：</text>
          <text class="value">{{item.apply_time}}</text>
        </view>
        <view wx:if="{{item.complete_time}}" class="detail-row">
          <text class="label">完成时间：</text>
          <text class="value">{{item.complete_time}}</text>
        </view>
        <view wx:if="{{item.failure_reason}}" class="detail-row">
          <text class="label">失败原因：</text>
          <text class="value" style="color: #ff3b30;">{{item.failure_reason}}</text>
        </view>
        <view wx:if="{{item.withdraw_no}}" class="detail-row">
          <text class="label">提现单号：</text>
          <text class="value">{{item.withdraw_no}}</text>
        </view>
      </view>
    </view>
  </view>
  
  <!-- 空状态 -->
  <view wx:if="{{records.length === 0 && !loading}}" class="empty-state">
    <image src="/assets/icons/user-placeholder.png" mode="aspectFit" />
    <text>暂无提现记录</text>
  </view>

  <!-- 加载更多 -->
  <view wx:if="{{records.length > 0}}" class="load-more">
    <text wx:if="{{loading}}" class="loading">加载中...</text>
    <text wx:elif="{{hasMore}}" class="load-more-text">上拉加载更多</text>
    <text wx:else class="no-more">没有更多记录了</text>
  </view>

  <!-- 下拉刷新提示 -->
  <view wx:if="{{refreshing}}" class="refresh-container">
    <image class="refresh-icon" src="/assets/icons/wifi.png" mode="aspectFit" />
    <text>刷新中...</text>
  </view>
</view>
