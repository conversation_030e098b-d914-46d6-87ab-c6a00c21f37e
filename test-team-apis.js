/**
 * 团队API测试脚本
 * 测试团队成员列表和邀请二维码功能
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:4000/api/v1';
const TEST_TOKEN = 'test-token';

async function testTeamAPIs() {
  console.log('🚀 开始测试团队相关API...\n');

  try {
    // 1. 测试团队信息API
    console.log('1. 测试团队信息API...');
    const teamInfoResponse = await axios.get(`${BASE_URL}/client/team/info`, {
      headers: {
        'Authorization': `Bearer ${TEST_TOKEN}`
      }
    });
    
    console.log('团队信息响应状态:', teamInfoResponse.status);
    console.log('团队信息数据:', JSON.stringify(teamInfoResponse.data, null, 2));
    
    const teamData = teamInfoResponse.data.data;
    const inviteCode = teamData.team ? teamData.team.inviteCode : null;
    console.log('提取的邀请码:', inviteCode);
    
    if (teamInfoResponse.data.status === 'success') {
      console.log('✅ 团队信息API测试成功\n');
    } else {
      console.log('❌ 团队信息API测试失败\n');
      return;
    }

    // 2. 测试团队成员列表API
    console.log('2. 测试团队成员列表API...');
    const membersResponse = await axios.get(`${BASE_URL}/client/team/members?page=1&limit=10`, {
      headers: {
        'Authorization': `Bearer ${TEST_TOKEN}`
      }
    });
    
    console.log('团队成员响应状态:', membersResponse.status);
    console.log('团队成员数据:', JSON.stringify(membersResponse.data, null, 2));
    
    if (membersResponse.data.status === 'success') {
      console.log('✅ 团队成员列表API测试成功\n');
    } else {
      console.log('❌ 团队成员列表API测试失败\n');
    }

    // 3. 测试邀请二维码API
    if (inviteCode) {
      console.log('3. 测试邀请二维码API...');
      const qrcodeResponse = await axios.get(`${BASE_URL}/client/team/invite-qrcode`, {
        params: {
          inviteCode: inviteCode
        },
        headers: {
          'Authorization': `Bearer ${TEST_TOKEN}`
        }
      });
      
      console.log('邀请二维码响应状态:', qrcodeResponse.status);
      console.log('邀请二维码数据:', JSON.stringify(qrcodeResponse.data, null, 2));
      
      if (qrcodeResponse.data.status === 'success') {
        console.log('✅ 邀请二维码API测试成功');
        console.log('二维码URL:', qrcodeResponse.data.data.qrcode_url);
      } else {
        console.log('❌ 邀请二维码API测试失败');
      }
    } else {
      console.log('⚠️  无法测试邀请二维码API，因为没有获取到邀请码');
    }

    console.log('\n🎉 所有团队API测试完成！');

  } catch (error) {
    console.error('❌ 测试过程中出错:', error.message);
    if (error.response) {
      console.error('错误响应状态:', error.response.status);
      console.error('错误响应数据:', error.response.data);
    }
  }
}

// 运行测试
testTeamAPIs();
