// 团队功能测试服务器
const express = require('express');
const mysql = require('mysql2');

const app = express();
app.use(express.json());

// 数据库连接
const db = mysql.createConnection({
  host: 'localhost',
  user: 'root',
  password: 'wo587129955',
  database: 'mall'
});

// 测试数据库连接
app.get('/test', (req, res) => {
  db.query('SELECT 1 as test', (err, results) => {
    if (err) {
      res.json({ success: false, error: err.message });
    } else {
      res.json({ success: true, message: '数据库连接正常' });
    }
  });
});

// 查看团队表结构
app.get('/structure', (req, res) => {
  db.query('DESCRIBE team', (err, results) => {
    if (err) {
      res.json({ success: false, error: err.message });
    } else {
      const hasInviteCode = results.some(field => field.Field === 'invite_code');
      const hasLevel = results.some(field => field.Field === 'level');
      
      res.json({ 
        success: true, 
        structure: results,
        hasInviteCode,
        hasLevel
      });
    }
  });
});

// 查看团队数据
app.get('/teams', (req, res) => {
  db.query('SELECT * FROM team', (err, results) => {
    if (err) {
      res.json({ success: false, error: err.message });
    } else {
      res.json({ success: true, teams: results });
    }
  });
});

// 模拟团队信息API
app.get('/team-info', (req, res) => {
  const mockData = {
    id: 1,
    teamName: '我的团队',
    memberCount: 1,
    wifiCount: 6,
    monthPerformance: '0.00',
    totalIncome: '0.00',
    todayIncome: '0.00',
    weekIncome: '0.00',
    activeMembers: 1,
    newMembersThisMonth: 0,
    teamLevel: 1,
    createTime: '2024-07-14',
    isLeader: true,
    inviteCode: 'TEAM000001'
  };
  
  res.json({
    code: 0,
    success: true,
    data: mockData,
    message: '获取团队信息成功'
  });
});

const PORT = 3003;
app.listen(PORT, () => {
  console.log(`团队测试服务器启动成功！`);
  console.log(`访问地址: http://localhost:${PORT}`);
  console.log(`测试链接:`);
  console.log(`- 数据库测试: http://localhost:${PORT}/test`);
  console.log(`- 表结构: http://localhost:${PORT}/structure`);
  console.log(`- 团队数据: http://localhost:${PORT}/teams`);
  console.log(`- 团队信息: http://localhost:${PORT}/team-info`);
});
