const mysql = require('mysql2/promise');

async function checkWithdrawSystem() {
  let connection;
  
  try {
    // 连接数据库
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: 'wo587129955',
      database: 'mall'
    });
    
    console.log('🔍 检查提现系统相关数据...\n');
    
    // 1. 检查现有提现相关表
    console.log('1️⃣ 现有提现相关表:');
    const [tables] = await connection.execute(`
      SELECT table_name, table_comment 
      FROM information_schema.tables 
      WHERE table_schema = 'mall' 
      AND (table_name LIKE '%withdraw%' 
           OR table_name LIKE '%bank%'
           OR table_name LIKE '%payment%'
           OR table_comment LIKE '%提现%'
           OR table_comment LIKE '%银行%'
           OR table_comment LIKE '%支付%')
      ORDER BY table_name
    `);
    
    if (tables.length > 0) {
      console.table(tables);
    } else {
      console.log('❌ 未找到提现相关表');
    }
    
    // 2. 检查withdraw表结构
    console.log('\n2️⃣ withdraw表结构:');
    try {
      const [withdrawColumns] = await connection.execute(`
        SELECT column_name, column_type, column_comment, is_nullable, column_default
        FROM information_schema.columns 
        WHERE table_schema = 'mall' 
        AND table_name = 'withdraw' 
        ORDER BY ordinal_position
      `);
      
      if (withdrawColumns.length > 0) {
        console.table(withdrawColumns);
        
        // 检查withdraw表数据
        const [withdrawData] = await connection.execute('SELECT COUNT(*) as count FROM withdraw');
        console.log(`\n📊 withdraw表记录数: ${withdrawData[0].count}`);
      } else {
        console.log('❌ withdraw表不存在');
      }
    } catch (error) {
      console.log('❌ withdraw表检查失败:', error.message);
    }
    
    // 3. 检查bank_card表结构
    console.log('\n3️⃣ bank_card表结构:');
    try {
      const [bankColumns] = await connection.execute(`
        SELECT column_name, column_type, column_comment, is_nullable, column_default
        FROM information_schema.columns 
        WHERE table_schema = 'mall' 
        AND table_name = 'bank_card' 
        ORDER BY ordinal_position
      `);
      
      if (bankColumns.length > 0) {
        console.table(bankColumns);
        
        // 检查bank_card表数据
        const [bankData] = await connection.execute('SELECT COUNT(*) as count FROM bank_card');
        console.log(`\n📊 bank_card表记录数: ${bankData[0].count}`);
      } else {
        console.log('❌ bank_card表不存在');
      }
    } catch (error) {
      console.log('❌ bank_card表检查失败:', error.message);
    }
    
    // 4. 检查withdraw_record表（我们之前创建的）
    console.log('\n4️⃣ withdraw_record表结构:');
    try {
      const [withdrawRecordColumns] = await connection.execute(`
        SELECT column_name, column_type, column_comment, is_nullable, column_default
        FROM information_schema.columns 
        WHERE table_schema = 'mall' 
        AND table_name = 'withdraw_record' 
        ORDER BY ordinal_position
      `);
      
      if (withdrawRecordColumns.length > 0) {
        console.table(withdrawRecordColumns);
        
        // 检查withdraw_record表数据
        const [recordData] = await connection.execute('SELECT COUNT(*) as count FROM withdraw_record');
        console.log(`\n📊 withdraw_record表记录数: ${recordData[0].count}`);
      } else {
        console.log('❌ withdraw_record表不存在');
      }
    } catch (error) {
      console.log('❌ withdraw_record表检查失败:', error.message);
    }
    
    // 5. 检查提现配置
    console.log('\n5️⃣ 提现配置:');
    try {
      const [configs] = await connection.execute(`
        SELECT config_key, config_value, config_type, description 
        FROM profit_config 
        WHERE config_key LIKE '%withdraw%'
        ORDER BY config_key
      `);
      
      if (configs.length > 0) {
        console.table(configs);
      } else {
        console.log('❌ 未找到提现配置');
      }
    } catch (error) {
      console.log('❌ 提现配置检查失败:', error.message);
    }
    
    // 6. 检查用户余额信息
    console.log('\n6️⃣ 用户余额信息 (user_id=1):');
    try {
      const [userBalance] = await connection.execute(`
        SELECT 
          id,
          nickname,
          balance,
          total_income,
          today_income,
          month_income,
          total_withdraw
        FROM user 
        WHERE id = 1
      `);
      
      if (userBalance.length > 0) {
        console.table(userBalance);
      } else {
        console.log('❌ 未找到用户数据');
      }
    } catch (error) {
      console.log('❌ 用户余额检查失败:', error.message);
    }
    
    console.log('\n📋 检查总结:');
    console.log('- 需要完善提现方式管理（微信、支付宝、银行卡）');
    console.log('- 需要完善提现流程状态管理');
    console.log('- 需要添加提现手续费计算');
    console.log('- 需要添加提现限额控制');
    console.log('- 需要完善提现审核流程');
    
  } catch (error) {
    console.error('❌ 检查失败:', error.message);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// 运行检查
checkWithdrawSystem();
