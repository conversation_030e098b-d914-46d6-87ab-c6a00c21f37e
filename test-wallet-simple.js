const axios = require('axios');

async function testWalletSimple() {
  try {
    console.log('🧪 简单测试钱包API...\n');
    
    // 首先登录获取token
    console.log('1️⃣ 登录...');
    const loginResponse = await axios.post('http://localhost:4000/api/v1/admin/auth/admin-login', {
      username: 'mrx0927',
      password: 'hh20250701'
    });
    
    if (loginResponse.data.code !== 200) {
      console.log('❌ 登录失败:', loginResponse.data);
      return;
    }
    
    const token = loginResponse.data.data.token;
    console.log('✅ 登录成功');
    
    // 测试获取钱包列表
    console.log('\n2️⃣ 测试钱包列表API...');
    const listResponse = await axios.get('http://localhost:4000/api/v1/admin/wallet/list?page=1&limit=20', {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    console.log('✅ 钱包列表API响应成功');
    console.log('状态码:', listResponse.status);
    console.log('响应数据:', JSON.stringify(listResponse.data, null, 2));
    
  } catch (error) {
    console.log('\n❌ 测试失败!');
    if (error.response) {
      console.log('状态码:', error.response.status);
      console.log('响应数据:', error.response.data);
    } else {
      console.log('错误信息:', error.message);
    }
  }
}

testWalletSimple();
