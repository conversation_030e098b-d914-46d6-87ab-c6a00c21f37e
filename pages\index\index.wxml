<!--pages/index/index.wxml-->
<!--首页模板 - 按照UI示意图设计-->

<view class="container">
  <!-- 轮播图区域(广告位) -->
  <view class="banner-section">
    <swiper 
      wx:if="{{bannerList && bannerList.length > 0}}"
      class="banner-swiper" 
      indicator-dots="{{true}}" 
      indicator-color="rgba(255,255,255,0.5)"
      indicator-active-color="#fff"
      autoplay="{{true}}" 
      interval="{{4000}}" 
      duration="{{800}}" 
      circular="{{true}}"
      style="height: 350rpx;"
    >
      <swiper-item wx:for="{{bannerList}}" wx:key="id" bindtap="onBannerTap" data-item="{{item}}">
        <view class="banner-item">
          <image src="{{item.image}}" class="banner-image" mode="aspectFill" binderror="onImageError" data-index="{{index}}" lazy-load="{{false}}"></image>
          <view class="banner-overlay">
            <view class="banner-content">
              <text class="banner-title">{{item.title || ''}}</text>
              <text class="banner-desc">{{item.desc || ''}}</text>
            </view>
          </view>
        </view>
      </swiper-item>
    </swiper>
    <!-- 轮播图为空时显示占位图 -->
    <view wx:else class="banner-placeholder">
      <image src="/assets/images/banner1.jpg" class="banner-image" mode="aspectFill"></image>
      <view class="banner-overlay">
        <view class="banner-content">
          <text class="banner-title">WiFi共享商城</text>
          <text class="banner-desc">轻松分享，快速赚钱</text>
        </view>
      </view>
    </view>
  </view>

  <!-- WiFi功能区域 -->
  <view class="wifi-section">
    <view class="wifi-actions">
      <!-- 创建WiFi码 -->
      <view class="wifi-action-card create-card" bindtap="onQuickActionTap" data-item="{{quickActions[0]}}">
        <view class="card-icon">
          <image src="/assets/icons/wifi-create.png" class="icon-image" mode="aspectFit"></image>
        </view>
        <view class="card-content">
          <text class="card-title">创建WiFi码</text>
          <text class="card-subtitle">快速生成分享码</text>
        </view>
        <view class="card-arrow">
          <text class="arrow-icon">›</text>
        </view>
      </view>

      <!-- 我的WiFi码 -->
      <view class="wifi-action-card manage-card" bindtap="onQuickActionTap" data-item="{{quickActions[1]}}">
        <view class="card-icon">
          <image src="/assets/icons/wifi-manage.png" class="icon-image" mode="aspectFit"></image>
        </view>
        <view class="card-content">
          <text class="card-title">我的WiFi码</text>
          <text class="card-subtitle">管理已创建的码</text>
        </view>
        <view class="card-arrow">
          <text class="arrow-icon">›</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 联盟入驻区域 -->
  <view class="alliance-section">
    <text class="section-label">联盟入驻</text>
    <view class="alliance-card" bindtap="onAllianceTap">
      <view class="alliance-content">
        <view class="alliance-left">
          <view class="alliance-icon">
            <image src="/assets/icons/team-leader.png" class="leader-icon" mode="aspectFit"></image>
          </view>
          <view class="alliance-text">
            <text class="alliance-title">我是老板</text>
            <text class="alliance-subtitle">加入联盟，分享收益，开启创业之路</text>
          </view>
        </view>
        <view class="alliance-right">
          <view class="alliance-badge">
            <text class="badge-text">立即申请</text>
            <text class="badge-arrow">›</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 统计数据区域 (登录后显示) -->
  <view class="stats-section" wx:if="{{isLogin}}">
    <view class="stats-card">
      <view class="stats-header">
        <text class="stats-title">今日数据</text>
        <text class="stats-date">{{todayDate}}</text>
      </view>
      <view class="stats-grid">
        <view class="stats-item">
          <text class="stats-value">¥{{statsData.todayIncome}}</text>
          <text class="stats-label">今日收益</text>
        </view>
        <view class="stats-item">
          <text class="stats-value">{{statsData.totalWifiCodes}}</text>
          <text class="stats-label">WiFi码总数</text>
        </view>
        <view class="stats-item">
          <text class="stats-value">{{statsData.totalUsers}}</text>
          <text class="stats-label">团队人数</text>
        </view>
        <view class="stats-item">
          <text class="stats-value">¥{{statsData.totalIncome}}</text>
          <text class="stats-label">累计收益</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 登录提示区域 (未登录时显示) -->
  <view class="login-section" wx:if="{{!isLogin}}">
    <view class="login-card">
      <view class="login-content">
        <view class="login-icon">
          <image src="/assets/icons/user-placeholder.png" class="user-avatar" mode="aspectFit"></image>
        </view>
        <view class="login-text">
          <text class="login-title">欢迎使用WiFi共享商城</text>
          <text class="login-subtitle">登录后可查看收益数据和管理WiFi码</text>
        </view>
        <button class="login-btn" bindtap="onLoginTap">
          <text class="btn-text">立即登录</text>
        </button>
      </view>
    </view>
  </view>

  <!-- 广告区域 -->
  <view class="ad-section">
    <view class="ad-container">
      <view class="ad-header">
        <text class="ad-section-title">广告位</text>
      </view>
      <view class="ad-banner" bindtap="onAdTap">
        <image src="{{adData.image}}" class="ad-image" mode="aspectFill"></image>
        <view class="ad-overlay">
          <view class="ad-content">
            <text class="ad-title">{{adData.title}}</text>
            <text class="ad-subtitle">{{adData.subtitle}}</text>
          </view>
          <view class="ad-action">
            <text class="ad-btn">了解详情</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 底部安全间距 -->
  <view class="bottom-safe-area"></view>
</view> 