<!--pages/user/address/edit.wxml-->
<view class="address-edit-container">
  <view class="form-section">
    <!-- 表单内容 -->
    <view class="form-group">
      <view class="form-label">收货人</view>
      <input class="form-input" placeholder="请输入收货人姓名" value="{{address.name}}" bindinput="onInputChange" data-field="name" />
    </view>
    
    <view class="form-group">
      <view class="form-label">手机号码</view>
      <input class="form-input" type="number" placeholder="请输入手机号码" value="{{address.phone}}" bindinput="onInputChange" data-field="phone" maxlength="11" />
    </view>
    
    <view class="form-group">
      <view class="form-label">所在地区</view>
      <picker mode="region" bindchange="onRegionChange" value="{{region}}" class="region-picker">
        <view class="picker-value {{!region[0] ? 'placeholder' : ''}}">
          {{region[0] ? region[0] + ' ' + region[1] + ' ' + region[2] : '请选择所在地区'}}
        </view>
      </picker>
    </view>
    
    <view class="form-group">
      <view class="form-label">详细地址</view>
      <textarea class="form-textarea" placeholder="请输入详细地址，如街道、门牌号、楼层等" value="{{address.address}}" bindinput="onInputChange" data-field="address" auto-height />
    </view>
    
    <view class="form-group switch-group">
      <view class="form-label">设为默认地址</view>
      <switch checked="{{address.is_default}}" bindchange="onDefaultChange" color="#ff6b00" />
    </view>
  </view>
  
  <!-- 使用微信收货地址 -->
  <view class="wechat-address-btn" bindtap="onUseWechatAddress">
    <image class="wechat-icon" src="/assets/icons/wechat.png" mode="aspectFit"></image>
    <text class="wechat-text">使用微信收货地址</text>
  </view>
  
  <!-- 保存按钮 -->
  <view class="save-btn {{loading ? 'disabled' : ''}}" bindtap="{{loading ? '' : 'onSaveAddress'}}">
    <text wx:if="{{!loading}}">保存</text>
    <view wx:else class="loading-icon"></view>
  </view>
</view> 