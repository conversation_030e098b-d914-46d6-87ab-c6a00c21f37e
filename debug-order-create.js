const axios = require('axios');
const jwt = require('jsonwebtoken');
const mysql = require('mysql2/promise');

async function debugOrderCreate() {
  let connection;
  try {
    console.log('🔍 调试订单创建问题...');
    
    // 连接数据库
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: 'wo587129955',
      database: 'mall'
    });
    
    // 生成JWT token
    const config = require('./config');
    const userId = 2;
    
    const token = jwt.sign(
      { id: userId, openid: 'test_openid_13800138000' },
      config.jwt.secret,
      { expiresIn: config.jwt.expiresIn }
    );
    
    console.log('🔑 Token生成成功');
    
    // 1. 检查用户是否存在
    console.log('\n👤 1. 检查用户...');
    const [users] = await connection.execute('SELECT id, openid, nickname FROM user WHERE id = ?', [userId]);
    console.log('用户信息:', users[0]);
    
    // 2. 检查地址是否存在
    console.log('\n📍 2. 检查地址...');
    const [addresses] = await connection.execute('SELECT * FROM user_address WHERE user_id = ? LIMIT 1', [userId]);
    console.log('地址信息:', addresses[0]);
    
    if (!addresses[0]) {
      console.log('❌ 用户没有地址，创建一个测试地址...');
      await connection.execute(
        'INSERT INTO user_address (user_id, name, phone, province, city, district, address, is_default) VALUES (?, ?, ?, ?, ?, ?, ?, ?)',
        [userId, 'Test User', '13800138000', '广东省', '深圳市', '南山区', '测试地址123号', 1]
      );
      
      const [newAddresses] = await connection.execute('SELECT * FROM user_address WHERE user_id = ? ORDER BY id DESC LIMIT 1', [userId]);
      console.log('创建的地址:', newAddresses[0]);
    }
    
    // 3. 清理并添加购物车商品
    console.log('\n🛒 3. 准备购物车商品...');
    await connection.execute('DELETE FROM cart WHERE user_id = ?', [userId]);
    
    // 添加商品到购物车
    await connection.execute(
      'INSERT INTO cart (user_id, goods_id, quantity, specs_id, selected) VALUES (?, ?, ?, ?, ?)',
      [userId, 1, 2, 0, 1]
    );
    await connection.execute(
      'INSERT INTO cart (user_id, goods_id, quantity, specs_id, selected) VALUES (?, ?, ?, ?, ?)',
      [userId, 8, 1, 0, 1]
    );
    
    // 检查购物车
    const [cartItems] = await connection.execute(
      'SELECT c.*, g.title, g.price, g.stock FROM cart c LEFT JOIN goods g ON c.goods_id = g.id WHERE c.user_id = ? AND c.selected = 1',
      [userId]
    );
    console.log('购物车商品:', cartItems);
    
    // 4. 检查商品是否存在
    console.log('\n📦 4. 检查商品...');
    const goodsIds = cartItems.map(item => item.goods_id);
    console.log('商品ID列表:', goodsIds);
    
    const [goods] = await connection.execute('SELECT * FROM goods WHERE id IN (?)', [goodsIds]);
    console.log('商品详情:', goods);
    
    // 5. 测试订单创建（直接购买）
    console.log('\n📝 5. 测试直接购买订单创建...');
    const directOrderData = {
      addressId: addresses[0]?.id || 1,
      goods: [
        { goodsId: 1, quantity: 1, specificationId: 0 }
      ],
      remark: '测试直接购买',
      paymentMethod: 'wechat',
      couponId: 0,
      fromCart: false
    };
    
    console.log('直接购买订单数据:', directOrderData);
    
    try {
      const directOrderResponse = await axios.post('http://localhost:4000/api/v1/client/order/create', directOrderData, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        timeout: 10000
      });
      
      console.log('✅ 直接购买订单创建成功:', directOrderResponse.data);
    } catch (directError) {
      console.log('❌ 直接购买订单创建失败:', directError.response ? directError.response.data : directError.message);
    }
    
    // 6. 测试订单创建（从购物车）
    console.log('\n🛒 6. 测试从购物车订单创建...');
    const cartOrderData = {
      addressId: addresses[0]?.id || 1,
      goods: [], // 空数组，应该从购物车获取
      remark: '测试从购物车创建',
      paymentMethod: 'wechat',
      couponId: 0,
      fromCart: true
    };
    
    console.log('购物车订单数据:', cartOrderData);
    
    try {
      const cartOrderResponse = await axios.post('http://localhost:4000/api/v1/client/order/create', cartOrderData, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        timeout: 10000
      });
      
      console.log('✅ 购物车订单创建成功:', cartOrderResponse.data);
    } catch (cartError) {
      console.log('❌ 购物车订单创建失败:', cartError.response ? cartError.response.data : cartError.message);
    }
    
  } catch (error) {
    console.error('❌ 调试失败:', error.message);
    if (error.response) {
      console.error('响应状态:', error.response.status);
      console.error('响应数据:', error.response.data);
    }
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// 运行调试
debugOrderCreate();
