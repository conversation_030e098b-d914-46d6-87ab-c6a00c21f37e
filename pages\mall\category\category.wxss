/* pages/mall/category/category.wxss */
.category-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
}

/* 搜索框样式 */
.search-box {
  padding: 20rpx;
  background-color: #fff;
  position: sticky;
  top: 0;
  z-index: 10;
}

.search-input {
  height: 70rpx;
  background-color: #f5f5f5;
  border-radius: 35rpx;
  padding: 0 30rpx;
  font-size: 28rpx;
}

/* 分类内容区域 */
.category-content {
  display: flex;
  flex: 1;
  overflow: hidden;
}

/* 左侧分类导航 */
.category-nav {
  width: 180rpx;
  height: 100%;
  background-color: #f8f8f8;
  overflow-y: auto;
}

.category-nav-item {
  height: 100rpx;
  line-height: 100rpx;
  text-align: center;
  font-size: 28rpx;
  color: #666;
  border-left: 6rpx solid transparent;
  position: relative;
}

.category-nav-item.active {
  color: #07c160;
  background-color: #fff;
  border-left-color: #07c160;
  font-weight: 500;
}

/* 右侧商品列表 */
.goods-list-container {
  flex: 1;
  background-color: #fff;
  padding: 20rpx;
  overflow-y: auto;
}

.goods-grid {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

.goods-item {
  width: 48%;
  margin-bottom: 20rpx;
  background-color: #fff;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.goods-image {
  width: 100%;
  height: 240rpx;
  background-color: #f5f5f5;
}

.goods-info {
  padding: 16rpx;
}

.goods-name {
  font-size: 26rpx;
  color: #333;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  line-height: 1.4;
  height: 72rpx;
}

.goods-price {
  font-size: 28rpx;
  color: #ff4d4f;
  font-weight: 500;
  margin-top: 10rpx;
}

/* 加载更多 */
.load-more, .no-more {
  text-align: center;
  font-size: 24rpx;
  color: #999;
  padding: 20rpx 0;
}

/* 空状态 */
.empty-goods {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

/* 广告容器 */
.ad-container {
  margin: 20rpx;
  border-radius: 12rpx;
  overflow: hidden;
}

/* 加载状态 */
.loading-container {
  text-align: center;
  padding: 30rpx 0;
}

.loading-text {
  color: #999;
  font-size: 28rpx;
} 