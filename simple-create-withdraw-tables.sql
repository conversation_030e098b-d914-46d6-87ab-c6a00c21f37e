-- 简化的提现系统表创建脚本
USE mall;

-- 1. 微信支付账户表
CREATE TABLE IF NOT EXISTS wechat_account (
  id int(11) NOT NULL AUTO_INCREMENT,
  user_id int(11) NOT NULL,
  openid varchar(100) NOT NULL,
  nickname varchar(100) DEFAULT NULL,
  real_name varchar(50) DEFAULT NULL,
  is_verified tinyint(1) DEFAULT 0,
  is_default tinyint(1) DEFAULT 1,
  status tinyint(1) DEFAULT 1,
  created_at datetime DEFAULT CURRENT_TIMESTAMP,
  updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (id)
);

-- 2. 银行卡表（重新创建）
DROP TABLE IF EXISTS bank_card;
CREATE TABLE bank_card (
  id int(11) NOT NULL AUTO_INCREMENT,
  user_id int(11) NOT NULL,
  bank_name varchar(50) NOT NULL,
  bank_code varchar(20) DEFAULT NULL,
  card_number varchar(50) NOT NULL,
  card_number_mask varchar(50) NOT NULL,
  card_holder varchar(50) NOT NULL,
  card_type tinyint(1) DEFAULT 1,
  phone varchar(20) DEFAULT NULL,
  is_default tinyint(1) DEFAULT 0,
  is_verified tinyint(1) DEFAULT 0,
  status tinyint(1) DEFAULT 1,
  created_at datetime DEFAULT CURRENT_TIMESTAMP,
  updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (id)
);

-- 3. 提现申请表（重新创建）
DROP TABLE IF EXISTS withdraw;
CREATE TABLE withdraw (
  id int(11) NOT NULL AUTO_INCREMENT,
  user_id int(11) NOT NULL,
  withdraw_no varchar(50) NOT NULL,
  amount decimal(10,2) NOT NULL,
  fee decimal(10,2) DEFAULT 0.00,
  actual_amount decimal(10,2) NOT NULL,
  withdraw_type varchar(20) NOT NULL,
  account_id int(11) NOT NULL,
  account_info text DEFAULT NULL,
  status tinyint(1) DEFAULT 0,
  apply_time datetime DEFAULT CURRENT_TIMESTAMP,
  audit_time datetime DEFAULT NULL,
  complete_time datetime DEFAULT NULL,
  transaction_id varchar(100) DEFAULT NULL,
  failure_reason varchar(255) DEFAULT NULL,
  remark varchar(255) DEFAULT NULL,
  created_at datetime DEFAULT CURRENT_TIMESTAMP,
  updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (id)
);

-- 4. 提现配置表
CREATE TABLE IF NOT EXISTS withdraw_config (
  id int(11) NOT NULL AUTO_INCREMENT,
  config_key varchar(50) NOT NULL,
  config_value text NOT NULL,
  config_type varchar(20) DEFAULT 'string',
  description varchar(255) DEFAULT NULL,
  status tinyint(1) DEFAULT 1,
  created_at datetime DEFAULT CURRENT_TIMESTAMP,
  updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (id),
  UNIQUE KEY config_key (config_key)
);

-- 5. 银行信息表
CREATE TABLE IF NOT EXISTS bank_info (
  id int(11) NOT NULL AUTO_INCREMENT,
  bank_code varchar(20) NOT NULL,
  bank_name varchar(50) NOT NULL,
  bank_logo varchar(255) DEFAULT NULL,
  is_support tinyint(1) DEFAULT 1,
  min_amount decimal(10,2) DEFAULT 1.00,
  max_amount decimal(10,2) DEFAULT 50000.00,
  fee_rate decimal(5,4) DEFAULT 0.0000,
  sort_order int(11) DEFAULT 0,
  status tinyint(1) DEFAULT 1,
  created_at datetime DEFAULT CURRENT_TIMESTAMP,
  updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (id),
  UNIQUE KEY bank_code (bank_code)
);

-- 插入提现配置数据
INSERT INTO withdraw_config (config_key, config_value, config_type, description) VALUES
('min_withdraw_amount', '10', 'number', '最小提现金额'),
('max_withdraw_amount', '50000', 'number', '最大提现金额'),
('daily_withdraw_limit', '100000', 'number', '每日提现限额'),
('wechat_fee_rate', '0.006', 'number', '微信提现手续费率'),
('bank_fee_rate', '0.001', 'number', '银行卡提现手续费率'),
('wechat_min_fee', '0.1', 'number', '微信提现最小手续费'),
('bank_min_fee', '2', 'number', '银行卡提现最小手续费'),
('auto_audit_enabled', 'true', 'boolean', '是否启用自动审核'),
('auto_audit_limit', '1000', 'number', '自动审核金额限制'),
('notification_enabled', 'true', 'boolean', '是否启用提现通知')
ON DUPLICATE KEY UPDATE 
  config_value = VALUES(config_value),
  description = VALUES(description),
  updated_at = CURRENT_TIMESTAMP;

-- 插入银行信息数据
INSERT INTO bank_info (bank_code, bank_name, min_amount, max_amount, fee_rate, sort_order) VALUES
('ICBC', '中国工商银行', 10.00, 50000.00, 0.001, 1),
('ABC', '中国农业银行', 10.00, 50000.00, 0.001, 2),
('BOC', '中国银行', 10.00, 50000.00, 0.001, 3),
('CCB', '中国建设银行', 10.00, 50000.00, 0.001, 4),
('COMM', '交通银行', 10.00, 50000.00, 0.001, 5),
('CMB', '招商银行', 10.00, 50000.00, 0.001, 6),
('CITIC', '中信银行', 10.00, 50000.00, 0.001, 7),
('CEB', '光大银行', 10.00, 50000.00, 0.001, 8),
('CMBC', '中国民生银行', 10.00, 50000.00, 0.001, 9),
('PAB', '平安银行', 10.00, 50000.00, 0.001, 10)
ON DUPLICATE KEY UPDATE 
  bank_name = VALUES(bank_name),
  min_amount = VALUES(min_amount),
  max_amount = VALUES(max_amount),
  updated_at = CURRENT_TIMESTAMP;

-- 为测试用户插入微信账户
INSERT INTO wechat_account (user_id, openid, nickname, real_name, is_verified, is_default) VALUES
(1, 'oW20C7mVlW8e3W2AgUGtDTJeAbQU', '润生', '张润生', 1, 1)
ON DUPLICATE KEY UPDATE 
  nickname = VALUES(nickname),
  updated_at = CURRENT_TIMESTAMP;

-- 为测试用户插入银行卡
INSERT INTO bank_card (user_id, bank_name, bank_code, card_number, card_number_mask, card_holder, phone, is_verified) VALUES
(1, '招商银行', 'CMB', 'encrypted_6217000123456789', '**** **** **** 6789', '张润生', '***********', 1)
ON DUPLICATE KEY UPDATE updated_at = CURRENT_TIMESTAMP;

-- 验证表创建结果
SELECT 
  table_name as '表名',
  table_comment as '表说明'
FROM information_schema.tables 
WHERE table_schema = 'mall' 
AND table_name IN ('bank_card', 'wechat_account', 'withdraw', 'withdraw_config', 'bank_info')
ORDER BY table_name;
