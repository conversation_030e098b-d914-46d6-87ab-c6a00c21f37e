# 提现系统完整实现方案

## 🎯 需求分析

根据您提供的截图，需要实现：
1. **提现按钮点击** → 弹出提现方式选择页面
2. **提现方式选择** → 微信零钱、支付宝、银行卡
3. **提现流程** → 类似微信钱包的体验
4. **移除下方页面** → 简化界面

## 📊 数据库设计

### 1. 提现配置表 (withdraw_config)
```sql
CREATE TABLE withdraw_config (
  id int(11) NOT NULL AUTO_INCREMENT,
  type_code varchar(20) NOT NULL COMMENT '提现类型代码',
  type_name varchar(50) NOT NULL COMMENT '提现方式名称',
  icon varchar(100) DEFAULT NULL COMMENT '图标路径',
  min_amount decimal(10,2) NOT NULL DEFAULT '1.00' COMMENT '最小提现金额',
  max_amount decimal(10,2) NOT NULL DEFAULT '50000.00' COMMENT '最大提现金额',
  daily_limit decimal(10,2) NOT NULL DEFAULT '10000.00' COMMENT '每日限额',
  fee_rate decimal(5,4) NOT NULL DEFAULT '0.0100' COMMENT '手续费比例',
  min_fee decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '最小手续费',
  max_fee decimal(10,2) NOT NULL DEFAULT '100.00' COMMENT '最大手续费',
  process_time varchar(50) DEFAULT '实时到账' COMMENT '到账时间',
  is_enabled tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用',
  sort_order int(11) NOT NULL DEFAULT '0' COMMENT '排序',
  PRIMARY KEY (id),
  UNIQUE KEY type_code (type_code)
);

-- 插入配置数据
INSERT INTO withdraw_config VALUES
(1, 'wechat', '微信零钱', '/assets/icons/wechat.png', 1.00, 20000.00, 5000.00, 0.0060, 0.10, 25.00, '实时到账', 1, 1),
(2, 'alipay', '支付宝', '/assets/icons/alipay.png', 1.00, 20000.00, 5000.00, 0.0060, 0.10, 25.00, '实时到账', 1, 2),
(3, 'bank_card', '银行卡', '/assets/icons/bank.png', 50.00, 50000.00, 20000.00, 0.0100, 1.00, 50.00, '1-3个工作日', 1, 3);
```

### 2. 用户提现账户表 (user_withdraw_account)
```sql
CREATE TABLE user_withdraw_account (
  id int(11) NOT NULL AUTO_INCREMENT,
  user_id int(11) NOT NULL COMMENT '用户ID',
  account_type varchar(20) NOT NULL COMMENT '账户类型',
  account_name varchar(100) NOT NULL COMMENT '账户名称',
  account_info text NOT NULL COMMENT '账户信息JSON',
  is_default tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否默认',
  is_verified tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已验证',
  status tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态',
  created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (id),
  KEY user_id (user_id)
);
```

### 3. 提现申请表 (withdraw_application)
```sql
CREATE TABLE withdraw_application (
  id int(11) NOT NULL AUTO_INCREMENT,
  user_id int(11) NOT NULL COMMENT '用户ID',
  amount decimal(10,2) NOT NULL COMMENT '提现金额',
  fee decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '手续费',
  actual_amount decimal(10,2) NOT NULL COMMENT '实际到账金额',
  withdraw_type varchar(20) NOT NULL COMMENT '提现方式',
  account_info text NOT NULL COMMENT '账户信息JSON',
  status tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态：0待审核，1审核通过，2审核拒绝，3处理中，4已完成，5已取消',
  apply_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  audit_time datetime DEFAULT NULL,
  complete_time datetime DEFAULT NULL,
  transaction_no varchar(50) DEFAULT NULL COMMENT '交易流水号',
  remark varchar(255) DEFAULT NULL,
  PRIMARY KEY (id),
  KEY user_id (user_id)
);
```

## 🎨 前端页面设计

### 1. 钱包页面修改 (pages/user/wallet/wallet.wxml)
```xml
<!-- 账户余额卡片 -->
<view class="balance-card">
  <view class="balance-title">账户余额</view>
  <view class="balance-amount">¥{{walletData.balance || '0.00'}}</view>
  <view class="balance-subtitle">可提现余额</view>
</view>

<!-- 操作按钮 -->
<view class="action-buttons">
  <view class="action-btn" bindtap="onWithdraw">
    <image class="action-icon" src="/assets/icons/withdraw.png"></image>
    <text class="action-text">提现</text>
  </view>
  <view class="action-btn" bindtap="onRecharge">
    <image class="action-icon" src="/assets/icons/recharge.png"></image>
    <text class="action-text">充值</text>
  </view>
  <view class="action-btn" bindtap="onBill">
    <image class="action-icon" src="/assets/icons/bill.png"></image>
    <text class="action-text">明细</text>
  </view>
</view>

<!-- 收益来源 -->
<view class="income-sources">
  <view class="section-title">收益来源</view>
  <view class="income-item" wx:for="{{incomeTypes}}" wx:key="type">
    <view class="income-info">
      <text class="income-name">{{item.name}}</text>
      <text class="income-amount">¥{{item.amount}}</text>
    </view>
  </view>
</view>

<!-- 收支明细 -->
<view class="transaction-list">
  <view class="section-title">收支明细</view>
  <view class="transaction-item" wx:for="{{transactions}}" wx:key="id">
    <view class="transaction-info">
      <text class="transaction-title">{{item.title}}</text>
      <text class="transaction-time">{{item.time}}</text>
    </view>
    <text class="transaction-amount {{item.type === 'income' ? 'positive' : 'negative'}}">
      {{item.type === 'income' ? '+' : '-'}}¥{{item.amount}}
    </text>
  </view>
</view>
```

### 2. 提现方式选择页面 (pages/user/withdraw/withdraw.wxml)
```xml
<view class="withdraw-container">
  <!-- 头部余额显示 -->
  <view class="balance-header">
    <view class="balance-title">可提现余额</view>
    <view class="balance-amount">¥{{balance}}</view>
    <view class="balance-note">提现金额将转入您选择的账户</view>
  </view>

  <!-- 提现方式选择 -->
  <view class="withdraw-methods">
    <view class="section-title">选择提现方式</view>
    <view class="method-list">
      <view 
        class="method-item {{selectedMethod === item.type_code ? 'selected' : ''}}"
        wx:for="{{withdrawMethods}}" 
        wx:key="type_code"
        bindtap="selectMethod"
        data-method="{{item.type_code}}"
      >
        <view class="method-info">
          <image class="method-icon" src="{{item.icon}}"></image>
          <view class="method-details">
            <text class="method-name">{{item.type_name}}</text>
            <text class="method-desc">{{item.process_time}} · 手续费{{item.fee_rate * 100}}%</text>
          </view>
        </view>
        <view class="method-radio">
          <view class="radio-inner {{selectedMethod === item.type_code ? 'checked' : ''}}"></view>
        </view>
      </view>
    </view>
  </view>

  <!-- 提现金额输入 -->
  <view class="amount-input-section">
    <view class="section-title">提现金额</view>
    <view class="amount-input-wrapper">
      <text class="currency-symbol">¥</text>
      <input 
        class="amount-input" 
        type="digit" 
        placeholder="请输入提现金额"
        value="{{withdrawAmount}}"
        bindinput="onAmountInput"
      />
    </view>
    <view class="amount-tips">
      <text class="tip-item">最小提现：¥{{currentMethod.min_amount}}</text>
      <text class="tip-item">每日限额：¥{{currentMethod.daily_limit}}</text>
    </view>
  </view>

  <!-- 手续费显示 -->
  <view class="fee-info" wx:if="{{withdrawAmount > 0}}">
    <view class="fee-item">
      <text class="fee-label">提现金额</text>
      <text class="fee-value">¥{{withdrawAmount}}</text>
    </view>
    <view class="fee-item">
      <text class="fee-label">手续费</text>
      <text class="fee-value">¥{{calculatedFee}}</text>
    </view>
    <view class="fee-item total">
      <text class="fee-label">实际到账</text>
      <text class="fee-value">¥{{actualAmount}}</text>
    </view>
  </view>

  <!-- 提现按钮 -->
  <view class="withdraw-button-wrapper">
    <button 
      class="withdraw-button {{canWithdraw ? 'enabled' : 'disabled'}}"
      bindtap="confirmWithdraw"
      disabled="{{!canWithdraw}}"
    >
      确认提现
    </button>
  </view>
</view>
```

### 3. 提现页面逻辑 (pages/user/withdraw/withdraw.js)
```javascript
const request = require('../../../utils/request.js')

Page({
  data: {
    balance: 0,
    withdrawMethods: [],
    selectedMethod: '',
    currentMethod: {},
    withdrawAmount: '',
    calculatedFee: 0,
    actualAmount: 0,
    canWithdraw: false
  },

  onLoad() {
    this.loadUserBalance()
    this.loadWithdrawMethods()
  },

  // 加载用户余额
  async loadUserBalance() {
    try {
      const result = await request.get('/income/stats')
      if (result.success) {
        this.setData({
          balance: result.data.balance || 0
        })
      }
    } catch (error) {
      console.error('加载余额失败:', error)
    }
  },

  // 加载提现方式
  async loadWithdrawMethods() {
    try {
      const result = await request.get('/withdraw/methods')
      if (result.success) {
        this.setData({
          withdrawMethods: result.data,
          selectedMethod: result.data[0]?.type_code || '',
          currentMethod: result.data[0] || {}
        })
      }
    } catch (error) {
      console.error('加载提现方式失败:', error)
    }
  },

  // 选择提现方式
  selectMethod(e) {
    const method = e.currentTarget.dataset.method
    const currentMethod = this.data.withdrawMethods.find(m => m.type_code === method)
    
    this.setData({
      selectedMethod: method,
      currentMethod: currentMethod
    })
    
    // 重新计算手续费
    this.calculateFee()
  },

  // 输入提现金额
  onAmountInput(e) {
    const amount = parseFloat(e.detail.value) || 0
    this.setData({
      withdrawAmount: e.detail.value
    })
    
    this.calculateFee()
  },

  // 计算手续费
  calculateFee() {
    const amount = parseFloat(this.data.withdrawAmount) || 0
    const method = this.data.currentMethod
    
    if (amount <= 0 || !method.fee_rate) {
      this.setData({
        calculatedFee: 0,
        actualAmount: 0,
        canWithdraw: false
      })
      return
    }

    // 计算手续费
    let fee = amount * method.fee_rate
    fee = Math.max(fee, method.min_fee) // 最小手续费
    fee = Math.min(fee, method.max_fee) // 最大手续费
    fee = Math.round(fee * 100) / 100 // 保留两位小数

    const actualAmount = amount - fee

    // 验证提现条件
    const canWithdraw = amount >= method.min_amount && 
                       amount <= method.max_amount && 
                       amount <= this.data.balance

    this.setData({
      calculatedFee: fee,
      actualAmount: actualAmount,
      canWithdraw: canWithdraw
    })
  },

  // 确认提现
  async confirmWithdraw() {
    if (!this.data.canWithdraw) return

    try {
      wx.showLoading({ title: '提交中...' })

      const result = await request.post('/withdraw/apply', {
        amount: parseFloat(this.data.withdrawAmount),
        withdraw_type: this.data.selectedMethod,
        fee: this.data.calculatedFee,
        actual_amount: this.data.actualAmount
      })

      wx.hideLoading()

      if (result.success) {
        wx.showToast({
          title: '提现申请已提交',
          icon: 'success'
        })
        
        setTimeout(() => {
          wx.navigateBack()
        }, 1500)
      } else {
        wx.showToast({
          title: result.message || '提现申请失败',
          icon: 'none'
        })
      }
    } catch (error) {
      wx.hideLoading()
      wx.showToast({
        title: '网络错误，请重试',
        icon: 'none'
      })
    }
  }
})
```

## 🔧 后端API实现

### 1. 提现方式接口 (/api/v1/client/withdraw/methods)
```javascript
// 获取提现方式列表
router.get('/methods', async (req, res) => {
  try {
    const [methods] = await db.execute(`
      SELECT type_code, type_name, icon, min_amount, max_amount, 
             daily_limit, fee_rate, min_fee, max_fee, process_time
      FROM withdraw_config 
      WHERE is_enabled = 1 
      ORDER BY sort_order
    `)

    res.json({
      status: 'success',
      message: '获取提现方式成功',
      data: methods
    })
  } catch (error) {
    res.status(500).json({
      status: 'error',
      message: '获取提现方式失败'
    })
  }
})
```

### 2. 提现申请接口 (/api/v1/client/withdraw/apply)
```javascript
// 提现申请
router.post('/apply', async (req, res) => {
  try {
    const { amount, withdraw_type, fee, actual_amount } = req.body
    const userId = req.user.id

    // 验证用户余额
    const [userResult] = await db.execute(
      'SELECT balance FROM user WHERE id = ?',
      [userId]
    )

    if (!userResult.length || userResult[0].balance < amount) {
      return res.status(400).json({
        status: 'error',
        message: '余额不足'
      })
    }

    // 生成交易流水号
    const transactionNo = 'WD' + Date.now() + Math.random().toString(36).substr(2, 4).toUpperCase()

    // 插入提现申请
    await db.execute(`
      INSERT INTO withdraw_application 
      (user_id, amount, fee, actual_amount, withdraw_type, transaction_no, status) 
      VALUES (?, ?, ?, ?, ?, ?, 0)
    `, [userId, amount, fee, actual_amount, withdraw_type, transactionNo])

    // 冻结用户余额
    await db.execute(
      'UPDATE user SET balance = balance - ? WHERE id = ?',
      [amount, userId]
    )

    res.json({
      status: 'success',
      message: '提现申请已提交',
      data: { transaction_no: transactionNo }
    })
  } catch (error) {
    res.status(500).json({
      status: 'error',
      message: '提现申请失败'
    })
  }
})
```

## 🎨 样式设计 (pages/user/withdraw/withdraw.wxss)
```css
.withdraw-container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.balance-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 40rpx;
  border-radius: 20rpx;
  text-align: center;
  margin-bottom: 30rpx;
}

.balance-title {
  font-size: 28rpx;
  opacity: 0.9;
  margin-bottom: 10rpx;
}

.balance-amount {
  font-size: 60rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.balance-note {
  font-size: 24rpx;
  opacity: 0.8;
}

.withdraw-methods {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  color: #333;
}

.method-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.method-item:last-child {
  border-bottom: none;
}

.method-item.selected {
  background-color: #f8f9ff;
  border-radius: 10rpx;
  margin: 0 -20rpx;
  padding: 30rpx 20rpx;
}

.method-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.method-icon {
  width: 60rpx;
  height: 60rpx;
  margin-right: 20rpx;
}

.method-name {
  font-size: 32rpx;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.method-desc {
  font-size: 24rpx;
  color: #999;
}

.method-radio {
  width: 40rpx;
  height: 40rpx;
  border: 2rpx solid #ddd;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.method-item.selected .method-radio {
  border-color: #667eea;
}

.radio-inner {
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
  background-color: transparent;
}

.radio-inner.checked {
  background-color: #667eea;
}

.amount-input-section {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
}

.amount-input-wrapper {
  display: flex;
  align-items: center;
  border: 2rpx solid #e0e0e0;
  border-radius: 10rpx;
  padding: 20rpx;
  margin: 20rpx 0;
}

.currency-symbol {
  font-size: 36rpx;
  color: #333;
  margin-right: 10rpx;
}

.amount-input {
  flex: 1;
  font-size: 36rpx;
  color: #333;
}

.amount-tips {
  display: flex;
  justify-content: space-between;
  margin-top: 10rpx;
}

.tip-item {
  font-size: 24rpx;
  color: #999;
}

.fee-info {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
}

.fee-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15rpx 0;
}

.fee-item.total {
  border-top: 1rpx solid #f0f0f0;
  margin-top: 10rpx;
  padding-top: 20rpx;
  font-weight: bold;
}

.fee-label {
  font-size: 28rpx;
  color: #666;
}

.fee-value {
  font-size: 28rpx;
  color: #333;
}

.fee-item.total .fee-value {
  color: #667eea;
  font-size: 32rpx;
}

.withdraw-button-wrapper {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20rpx;
  background: white;
  border-top: 1rpx solid #f0f0f0;
}

.withdraw-button {
  width: 100%;
  height: 88rpx;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: bold;
}

.withdraw-button.enabled {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.withdraw-button.disabled {
  background: #f0f0f0;
  color: #ccc;
}
```

## 🎉 实现效果

完成后的提现系统将具备：

1. **完整的提现流程** - 从余额显示到提现完成
2. **多种提现方式** - 微信、支付宝、银行卡
3. **智能手续费计算** - 根据配置自动计算
4. **用户友好界面** - 类似微信钱包的体验
5. **完整的数据管理** - 提现记录、状态跟踪
6. **安全的资金管理** - 余额验证、交易记录

这个方案提供了完整的提现系统实现，包括数据库设计、前端页面、后端API和样式设计，可以直接使用。
