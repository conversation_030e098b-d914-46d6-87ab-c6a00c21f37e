// MySQL连接和表结构检查
const mysql = require('mysql2');

console.log('🔍 MySQL连接测试开始...');

// 连接配置
const config = {
  host: 'localhost',
  user: 'root',
  password: 'wo587129955',
  database: 'mall'
};

const connection = mysql.createConnection(config);

connection.connect((err) => {
  if (err) {
    console.error('❌ 连接失败:', err.message);
    console.log('\n💡 常见解决方案:');
    console.log('1. 检查MySQL服务是否启动: net start mysql');
    console.log('2. 确认密码是否正确');
    console.log('3. 检查端口3306是否开放');
    return;
  }

  console.log('✅ MySQL连接成功!');
  
  // 检查team表
  connection.query('DESCRIBE team', (err, results) => {
    if (err) {
      console.error('❌ team表不存在或查询失败:', err.message);
    } else {
      console.log('\n📋 team表结构:');
      results.forEach(field => {
        console.log(`${field.Field} - ${field.Type} - ${field.Null} - ${field.Key}`);
      });
      
      const hasInviteCode = results.some(f => f.Field === 'invite_code');
      const hasLevel = results.some(f => f.Field === 'level');
      
      console.log(`\ninvite_code字段: ${hasInviteCode ? '✅' : '❌'}`);
      console.log(`level字段: ${hasLevel ? '✅' : '❌'}`);
    }
    
    // 查看现有数据
    connection.query('SELECT * FROM team LIMIT 3', (err, results) => {
      if (err) {
        console.error('❌ 查询team数据失败:', err.message);
      } else {
        console.log('\n📊 team表数据:');
        console.table(results);
      }
      
      connection.end();
      console.log('\n🔌 连接已关闭');
    });
  });
});
