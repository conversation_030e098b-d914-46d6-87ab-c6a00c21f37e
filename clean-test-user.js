/**
 * 清理测试用户数据脚本
 * 用于删除开发过程中创建的测试用户
 */
const db = require('./src/database');
const logger = require('./src/utils/logger');

// 确定要使用的用户表名
async function getUserTableName() {
  try {
    const tableCheck = await db.getOne(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = DATABASE() AND table_name IN ('users', 'user')
      LIMIT 1
    `);
    
    if (!tableCheck || !tableCheck.table_name) {
      console.error('无法确定用户表名，尝试检查其他可能的表名');
      
      // 尝试获取所有表来查看可能的用户表
      const allTables = await db.query(`
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = DATABASE()
      `);
      
      console.log('数据库中存在的表:');
      allTables.forEach(t => console.log(`- ${t.table_name}`));
      
      // 默认使用users表
      return 'users';
    }
    
    const tableName = tableCheck.table_name;
    console.log(`使用表名 ${tableName} 操作用户数据`);
    return tableName;
  } catch (error) {
    console.error(`获取用户表名失败: ${error.message}`);
    return 'users'; // 默认使用users表
  }
}

// 清理测试用户数据
async function cleanTestUsers() {
  try {
    // 获取用户表名
    const tableName = await getUserTableName();
    
    // 查找可能的测试用户
    console.log(`开始查询测试用户数据，使用表: ${tableName}`);
    
    const testUsers = await db.query(`
      SELECT id, openid, nickname 
      FROM ${tableName} 
      WHERE 
        openid LIKE 'mock_%' OR 
        id = 999 OR 
        nickname LIKE 'test_%' OR 
        nickname LIKE '%测试%'
    `);
    
    if (!testUsers || testUsers.length === 0) {
      console.log('没有找到测试用户数据');
      return;
    }
    
    console.log(`找到 ${testUsers.length} 个测试用户:`);
    testUsers.forEach(user => {
      console.log(`- ID: ${user.id}, OpenID: ${user.openid || '无'}, 昵称: ${user.nickname || '无'}`);
    });
    
    // 删除这些用户
    console.log('开始删除测试用户...');
    
    const userIds = testUsers.map(user => user.id);
    const result = await db.query(`DELETE FROM ${tableName} WHERE id IN (?)`, [userIds]);
    
    console.log(`已成功删除 ${result.affectedRows} 个测试用户`);
  } catch (error) {
    console.error(`清理测试用户失败: ${error.message}`);
  } finally {
    // 数据库连接由连接池管理，不需要手动关闭
    console.log('操作完成');
  }
}

// 执行清理
cleanTestUsers(); 