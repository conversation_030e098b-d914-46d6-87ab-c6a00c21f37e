const express = require('express');
const router = express.Router();
const { body } = require('express-validator');
const userController = require('../controllers/user');
const { verifyToken, checkRole } = require('../middlewares/auth');
const db = require('../../config/database');
const logger = require('../utils/logger');
const { ForbiddenError, NotFoundError } = require('../middlewares/error');

/**
 * 创建用户表（如果不存在）
 */
async function ensureUserTableExists() {
  try {
    logger.info('检查并确保用户表存在');
    
    // 检查数据库中是否存在users表或user表
    const tableCheck = await db.getOne(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = DATABASE() AND table_name IN ('users', 'user')
    `);
    
    if (!tableCheck) {
      logger.info('未找到用户表，准备创建');
      
      // 创建用户表
      await db.query(`
        CREATE TABLE IF NOT EXISTS \`user\` (
          \`id\` int(11) NOT NULL AUTO_INCREMENT COMMENT '用户ID',
          \`openid\` varchar(50) NOT NULL COMMENT '微信openid',
          \`unionid\` varchar(50) DEFAULT NULL COMMENT '微信unionid',
          \`nickname\` varchar(50) DEFAULT NULL COMMENT '昵称',
          \`avatar\` varchar(255) DEFAULT NULL COMMENT '头像',
          \`gender\` tinyint(1) DEFAULT '0' COMMENT '性别：0未知，1男，2女',
          \`phone\` varchar(20) DEFAULT NULL COMMENT '手机号',
          \`balance\` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '账户余额',
          \`team_id\` int(11) DEFAULT NULL COMMENT '所属团队ID',
          \`parent_id\` int(11) DEFAULT NULL COMMENT '上级用户ID',
          \`is_leader\` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否团长：0否，1是',
          \`level\` tinyint(1) NOT NULL DEFAULT '0' COMMENT '用户等级',
          \`status\` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0禁用，1启用',
          \`created_at\` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
          \`updated_at\` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
          PRIMARY KEY (\`id\`),
          UNIQUE KEY \`openid\` (\`openid\`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='小程序用户表';
      `);
      
      logger.info('用户表创建成功');
      return 'user';
    } else {
      return tableCheck.table_name;
    }
  } catch (error) {
    logger.error(`确保用户表存在失败: ${error.message}`);
    return 'user'; // 默认返回user
  }
}

/**
 * @route   GET /api/v1/client/user/info
 * @desc    获取当前用户信息
 * @access  Private
 */
router.get('/info', verifyToken, userController.getUserInfo);

/**
 * @route   PUT /api/v1/client/user/info
 * @desc    更新用户信息
 * @access  Private
 */
router.put('/info', [
  verifyToken,
  body('nickname').optional().isLength({ min: 2, max: 20 }).withMessage('昵称长度应为2-20个字符'),
  body('avatar').optional().isURL().withMessage('头像应为有效的URL'),
  body('gender').optional().isInt({ min: 0, max: 2 }).withMessage('性别值无效')
], userController.updateUserInfo);

// 更新用户信息的POST路由，与PUT方法功能相同
router.post('/update', verifyToken, userController.updateUserInfo);

/**
 * @route   POST /api/v1/client/user/phone
 * @desc    绑定手机号
 * @access  Private
 */
router.post('/phone', [
  body('phone').notEmpty().isMobilePhone('zh-CN').withMessage('请输入有效的手机号'),
  body('code').notEmpty().isLength({ min: 4, max: 6 }).withMessage('验证码格式不正确')
], userController.bindPhone);

// 以下是管理端接口，实际项目中应该放在单独的管理API路由中，这里为了示例放在一起

/**
 * @route   GET /api/v1/admin/user/list
 * @desc    获取用户列表（管理端）
 * @access  Admin
 */
router.get('/admin/list', userController.getUserList);

/**
 * @route   GET /api/v1/admin/user/detail/:id
 * @desc    获取用户详情（管理端）
 * @access  Admin
 */
router.get('/admin/detail/:id', userController.getUserDetail);

/**
 * @route   PUT /api/v1/admin/user/status/:id
 * @desc    设置用户状态（管理端）
 * @access  Admin
 */
router.put('/admin/status/:id', [
  body('status').isInt({ min: 0, max: 1 }).withMessage('状态值无效')
], userController.setUserStatus);

/**
 * 获取用户列表
 * GET /api/users
 */
router.get('/', verifyToken, checkRole('admin'), async (req, res, next) => {
  try {
    const { page = 1, limit = 10 } = req.query;
    const offset = (page - 1) * limit;
    
    // 查询用户总数
    const countResult = await db.getOne('SELECT COUNT(*) as total FROM user');
    const total = countResult.total;
    
    // 查询用户列表
    const users = await db.query(
      'SELECT id, openid, nickname, phone, avatar, balance, is_leader, status, created_at, updated_at FROM user LIMIT ? OFFSET ?',
      [parseInt(limit), parseInt(offset)]
    );
    
    res.json({
      status: 'success',
      data: {
        users,
        pagination: {
          total,
          page: parseInt(page),
          limit: parseInt(limit),
          pages: Math.ceil(total / limit)
        }
      }
    });
  } catch (error) {
    next(error);
  }
});

/**
 * 获取用户列表（管理端）
 * GET /api/user/list
 */
router.get('/list', verifyToken, async (req, res, next) => {
  try {
    const { page = 1, limit = 10, keyword = '', status, is_leader } = req.query;
    const pageNum = parseInt(page);
    const limitNum = parseInt(limit);
    const offset = (pageNum - 1) * limitNum;
    
    // 构建查询条件
    let whereClause = 'WHERE 1=1';
    const whereParams = [];
    
    if (keyword) {
      whereClause += ' AND (nickname LIKE ? OR phone LIKE ?)';
      const searchKeyword = `%${keyword}%`;
      whereParams.push(searchKeyword, searchKeyword);
    }
    
    if (status !== undefined && status !== '') {
      whereClause += ' AND status = ?';
      whereParams.push(parseInt(status));
    }
    
    // 添加is_leader过滤条件
    if (is_leader !== undefined && is_leader !== '') {
      whereClause += ' AND is_leader = ?';
      whereParams.push(parseInt(is_leader));
    }
    
    // 查询用户总数
    const countSql = `SELECT COUNT(*) as total FROM user ${whereClause}`;
    const countResult = await db.getOne(countSql, whereParams);
    const total = countResult.total;
    
    // 查询用户列表 - 使用字符串拼接方式处理LIMIT和OFFSET
    const listSql = `SELECT id, openid, nickname, phone, avatar, balance, is_leader, status, created_at, updated_at 
                     FROM user ${whereClause} 
                     ORDER BY created_at DESC 
                     LIMIT ${limitNum} OFFSET ${offset}`;
    const users = await db.query(listSql, whereParams);
    
    res.json({
      status: 'success',
      data: {
        list: users,
        total
      }
    });
  } catch (error) {
    next(error);
  }
});

/**
 * 获取用户统计数据（管理端）
 * GET /api/user/stats
 */
router.get('/stats', verifyToken, async (req, res, next) => {
  try {
    // 总用户数
    const totalUsers = await db.getOne('SELECT COUNT(*) as count FROM user');
    
    // 活跃用户数（状态为1）
    const activeUsers = await db.getOne('SELECT COUNT(*) as count FROM user WHERE status = 1');
    
    // 团长用户数
    const leaderUsers = await db.getOne('SELECT COUNT(*) as count FROM user WHERE is_leader = 1');
    
    // 今日新增用户
    const todayUsers = await db.getOne(
      'SELECT COUNT(*) as count FROM user WHERE DATE(created_at) = CURDATE()'
    );
    
    // 本月新增用户
    const monthUsers = await db.getOne(
      'SELECT COUNT(*) as count FROM user WHERE YEAR(created_at) = YEAR(NOW()) AND MONTH(created_at) = MONTH(NOW())'
    );
    
    // 用户余额统计
    const balanceStats = await db.getOne(
      'SELECT SUM(balance) as total_balance, AVG(balance) as avg_balance FROM user WHERE status = 1'
    );
    
    res.json({
      status: 'success',
      data: {
        total_users: totalUsers.count,
        active_users: activeUsers.count,
        leader_users: leaderUsers.count,
        today_users: todayUsers.count,
        month_users: monthUsers.count,
        total_balance: parseFloat(balanceStats.total_balance || 0),
        avg_balance: parseFloat(balanceStats.avg_balance || 0)
      }
    });
  } catch (error) {
    next(error);
  }
});

/**
 * 获取用户标签列表（管理端）
 * GET /api/user/tag/list
 */
router.get('/tag/list', verifyToken, async (req, res, next) => {
  try {
    // 查询所有标签及其用户数量
    const tags = await db.query(`
      SELECT 
        ut.id,
        ut.name,
        ut.description,
        ut.color,
        ut.status,
        ut.created_at,
        COUNT(utr.user_id) as user_count
      FROM user_tag ut
      LEFT JOIN user_tag_relation utr ON ut.id = utr.tag_id
      WHERE ut.status = 1
      GROUP BY ut.id
      ORDER BY ut.created_at DESC
    `);
    
    res.json({
      status: 'success',
      data: tags
    });
  } catch (error) {
    next(error);
  }
});

/**
 * 获取用户个人资料
 * GET /api/users/profile
 */
router.get('/profile', verifyToken, userController.getUserProfile);

/**
 * 获取单个用户信息
 * GET /api/users/:id
 */
router.get('/:id', verifyToken, async (req, res, next) => {
  try {
    const { id } = req.params;
    
    // 检查权限：只有管理员或本人可以访问
    if (req.user.role !== 'admin' && req.user.id !== parseInt(id)) {
      return next(new ForbiddenError('您没有权限查看此用户信息'));
    }
    
    // 查询用户信息
    const user = await db.getOne(
      'SELECT id, openid, nickname, phone, avatar, balance, is_leader, status, created_at, updated_at FROM user WHERE id = ?',
      [id]
    );
    
    if (!user) {
      return next(new NotFoundError('用户不存在'));
    }
    
    res.json({
      status: 'success',
      data: { user }
    });
  } catch (error) {
    next(error);
  }
});

/**
 * 获取用户详情（管理端）
 * GET /api/user/detail/:id
 */
router.get('/detail/:id', verifyToken, async (req, res, next) => {
  try {
    const { id } = req.params;
    
    // 验证用户ID
    if (!id || id === 'undefined' || isNaN(parseInt(id))) {
      return res.status(400).json({
        status: 'error',
        message: '无效的用户ID'
      });
    }
    
    // 查询用户信息
    const user = await db.getOne(
      'SELECT * FROM user WHERE id = ?',
      [id]
    );
    
    if (!user) {
      return res.status(404).json({
        status: 'error',
        message: '用户不存在'
      });
    }
    
    res.json({
      status: 'success',
      data: user
    });
  } catch (error) {
    next(error);
  }
});

/**
 * 更新用户状态（管理端）
 * PUT /api/user/status/:id
 */
router.put('/status/:id', verifyToken, async (req, res, next) => {
  try {
    const { id } = req.params;
    
    // 验证用户ID
    if (!id || id === 'undefined' || isNaN(parseInt(id))) {
      return res.status(400).json({
        status: 'error',
        message: '无效的用户ID'
      });
    }
    
    const { status } = req.body;
    
    await db.query(
      'UPDATE user SET status = ?, updated_at = NOW() WHERE id = ?',
      [status, id]
    );
    
    res.json({
      status: 'success',
      message: '更新用户状态成功'
    });
  } catch (error) {
    next(error);
  }
});

/**
 * 更新用户信息（管理端）
 * PUT /api/user/update/:id
 */
router.put('/update/:id', verifyToken, async (req, res, next) => {
  try {
    const { id } = req.params;
    
    // 验证用户ID
    if (!id || id === 'undefined' || isNaN(parseInt(id))) {
      return res.status(400).json({
        status: 'error',
        message: '无效的用户ID'
      });
    }
    
    const { nickname, phone, level, status, balance } = req.body;
    
    // 构建更新字段
    const updateFields = [];
    const updateValues = [];
    
    if (nickname !== undefined) {
      updateFields.push('nickname = ?');
      updateValues.push(nickname);
    }
    
    if (phone !== undefined) {
      updateFields.push('phone = ?');
      updateValues.push(phone);
    }
    
    if (level !== undefined) {
      updateFields.push('level = ?');
      updateValues.push(parseInt(level));
    }
    
    if (status !== undefined) {
      updateFields.push('status = ?');
      updateValues.push(parseInt(status));
    }
    
    if (balance !== undefined) {
      updateFields.push('balance = ?');
      updateValues.push(parseFloat(balance));
    }
    
    if (updateFields.length === 0) {
      return res.status(400).json({
        status: 'error',
        message: '没有需要更新的字段'
      });
    }
    
    updateFields.push('updated_at = NOW()');
    updateValues.push(id);
    
    const updateSql = `UPDATE user SET ${updateFields.join(', ')} WHERE id = ?`;
    await db.query(updateSql, updateValues);
    
    res.json({
      status: 'success',
      message: '更新用户信息成功'
    });
  } catch (error) {
    next(error);
  }
});

/**
 * 调整用户余额（管理端）
 * POST /api/user/balance/:id
 */
router.post('/balance/:id', verifyToken, async (req, res, next) => {
  try {
    const { id } = req.params;
    
    // 验证用户ID
    if (!id || id === 'undefined' || isNaN(parseInt(id))) {
      return res.status(400).json({
        status: 'error',
        message: '无效的用户ID'
      });
    }
    
    const { amount, type, remark } = req.body; // type: 'add' 或 'subtract'
    
    if (!amount || amount <= 0) {
      return res.status(400).json({
        status: 'error',
        message: '金额必须大于0'
      });
    }
    
    if (!['add', 'subtract'].includes(type)) {
      return res.status(400).json({
        status: 'error',
        message: '类型必须是add或subtract'
      });
    }
    
    // 开始事务
    const transaction = await db.beginTransaction();
    
    try {
      // 获取当前用户信息
      const user = await transaction.query(
        'SELECT id, balance FROM user WHERE id = ?',
        [id]
      );
      
      if (user.length === 0) {
        await transaction.rollback();
        return res.status(404).json({
          status: 'error',
          message: '用户不存在'
        });
      }
      
      const currentBalance = parseFloat(user[0].balance);
      let newBalance;
      
      if (type === 'add') {
        newBalance = currentBalance + parseFloat(amount);
      } else {
        newBalance = currentBalance - parseFloat(amount);
        if (newBalance < 0) {
          await transaction.rollback();
          return res.status(400).json({
            status: 'error',
            message: '余额不足'
          });
        }
      }
      
      // 更新用户余额
      await transaction.query(
        'UPDATE user SET balance = ?, updated_at = NOW() WHERE id = ?',
        [newBalance, id]
      );
      
      // 记录余额变动日志（如果有日志表的话）
      // await transaction.query(
      //   'INSERT INTO balance_log (user_id, amount, type, before_balance, after_balance, remark, created_at) VALUES (?, ?, ?, ?, ?, ?, NOW())',
      //   [id, amount, type, currentBalance, newBalance, remark || '']
      // );
      
      await transaction.commit();
      
      res.json({
        status: 'success',
        message: '余额调整成功',
        data: {
          before_balance: currentBalance,
          after_balance: newBalance
        }
      });
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  } catch (error) {
    next(error);
  }
});

/**
 * 批量操作用户（管理端）
 * POST /api/user/batch
 */
router.post('/batch', verifyToken, async (req, res, next) => {
  try {
    const { user_ids, action, data } = req.body;
    
    if (!user_ids || !Array.isArray(user_ids) || user_ids.length === 0) {
      return res.status(400).json({
        status: 'error',
        message: '用户ID列表不能为空'
      });
    }
    
    const placeholders = user_ids.map(() => '?').join(',');
    
    switch (action) {
      case 'enable':
        await db.query(
          `UPDATE user SET status = 1, updated_at = NOW() WHERE id IN (${placeholders})`,
          user_ids
        );
        break;
        
      case 'disable':
        await db.query(
          `UPDATE user SET status = 0, updated_at = NOW() WHERE id IN (${placeholders})`,
          user_ids
        );
        break;
        
      case 'set_level':
        if (!data || data.level === undefined) {
          return res.status(400).json({
            status: 'error',
            message: '请提供用户等级'
          });
        }
        await db.query(
          `UPDATE user SET level = ?, updated_at = NOW() WHERE id IN (${placeholders})`,
          [parseInt(data.level), ...user_ids]
        );
        break;
        
      default:
        return res.status(400).json({
          status: 'error',
          message: '不支持的操作类型'
        });
    }
    
    res.json({
      status: 'success',
      message: '批量操作成功'
    });
  } catch (error) {
    next(error);
  }
});

/**
 * 分配用户标签（管理端）
 * POST /api/user/assign-tags/:id
 */
router.post('/assign-tags/:id', verifyToken, async (req, res, next) => {
  try {
    const { id } = req.params;
    const { tag_ids } = req.body;
    
    if (!tag_ids || !Array.isArray(tag_ids)) {
      return res.status(400).json({
        status: 'error',
        message: '标签ID列表格式错误'
      });
    }
    
    // 检查用户是否存在
    const user = await db.getOne('SELECT id FROM user WHERE id = ?', [id]);
    if (!user) {
      return res.status(404).json({
        status: 'error',
        message: '用户不存在'
      });
    }
    
    // 开始事务
    const transaction = await db.beginTransaction();
    
    try {
      // 先删除用户现有的所有标签
      await transaction.query(
        'DELETE FROM user_tag_relation WHERE user_id = ?',
        [id]
      );
      
      // 添加新的标签关联
      if (tag_ids.length > 0) {
        // 逐个插入标签关联
        for (const tag_id of tag_ids) {
          await transaction.query(
            'INSERT INTO user_tag_relation (user_id, tag_id, created_at) VALUES (?, ?, NOW())',
            [id, tag_id]
          );
        }
      }
      
      await transaction.commit();
      
      res.json({
        status: 'success',
        message: '分配标签成功'
      });
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  } catch (error) {
    next(error);
  }
});

/**
 * 创建用户标签（管理端）
 * POST /api/user/tag/create
 */
router.post('/tag/create', verifyToken, async (req, res, next) => {
  try {
    const { name, description, color } = req.body;
    
    if (!name) {
      return res.status(400).json({
        status: 'error',
        message: '标签名称不能为空'
      });
    }
    
    // 检查标签名称是否已存在
    const existingTag = await db.getOne(
      'SELECT id FROM user_tag WHERE name = ? AND status = 1',
      [name]
    );
    
    if (existingTag) {
      return res.status(400).json({
        status: 'error',
        message: '标签名称已存在'
      });
    }
    
    // 创建新标签
    const result = await db.query(
      'INSERT INTO user_tag (name, description, color, created_at) VALUES (?, ?, ?, NOW())',
      [name, description || '', color || '#409EFF']
    );
    
    const newTag = {
      id: result.insertId,
      name,
      description: description || '',
      color: color || '#409EFF',
      user_count: 0
    };
    
    res.json({
      status: 'success',
      data: newTag,
      message: '标签创建成功'
    });
  } catch (error) {
    next(error);
  }
});

/**
 * 更新用户标签（管理端）
 * PUT /api/user/tag/update/:id
 */
router.put('/tag/update/:id', verifyToken, async (req, res, next) => {
  try {
    const { id } = req.params;
    const { name, description, color } = req.body;
    
    if (!name) {
      return res.status(400).json({
        status: 'error',
        message: '标签名称不能为空'
      });
    }
    
    // 检查标签是否存在
    const existingTag = await db.getOne(
      'SELECT id FROM user_tag WHERE id = ? AND status = 1',
      [id]
    );
    
    if (!existingTag) {
      return res.status(404).json({
        status: 'error',
        message: '标签不存在'
      });
    }
    
    // 检查名称是否与其他标签重复
    const duplicateTag = await db.getOne(
      'SELECT id FROM user_tag WHERE name = ? AND id != ? AND status = 1',
      [name, id]
    );
    
    if (duplicateTag) {
      return res.status(400).json({
        status: 'error',
        message: '标签名称已存在'
      });
    }
    
    // 更新标签
    await db.query(
      'UPDATE user_tag SET name = ?, description = ?, color = ?, updated_at = NOW() WHERE id = ?',
      [name, description || '', color || '#409EFF', id]
    );
    
    res.json({
      status: 'success',
      message: '标签更新成功'
    });
  } catch (error) {
    next(error);
  }
});

/**
 * 获取用户消费统计（管理端）
 * GET /api/user/consumption-stats/:id
 */
router.get('/consumption-stats/:id', verifyToken, async (req, res, next) => {
  try {
    const { id } = req.params;
    
    // 验证用户ID
    if (!id || id === 'undefined' || isNaN(parseInt(id))) {
      return res.status(400).json({
        status: 'error',
        message: '无效的用户ID'
      });
    }
    
    // 检查用户是否存在
    const user = await db.getOne('SELECT id FROM user WHERE id = ?', [id]);
    if (!user) {
      return res.status(404).json({
        status: 'error',
        message: '用户不存在'
      });
    }
    
    // 模拟消费统计数据（实际项目中应该从订单表查询）
    const stats = {
      total_orders: Math.floor(Math.random() * 50) + 10,
      total_amount: parseFloat((Math.random() * 5000 + 1000).toFixed(2)),
      avg_order_amount: parseFloat((Math.random() * 200 + 50).toFixed(2)),
      last_order_time: '2023-06-15 14:30:20',
      favorite_category: '数码产品',
      monthly_stats: [
        { month: '2023-01', orders: 3, amount: 450.00 },
        { month: '2023-02', orders: 5, amount: 680.50 },
        { month: '2023-03', orders: 2, amount: 320.00 },
        { month: '2023-04', orders: 4, amount: 590.30 },
        { month: '2023-05', orders: 6, amount: 780.20 },
        { month: '2023-06', orders: 3, amount: 420.00 }
      ]
    };
    
    res.json({
      status: 'success',
      data: stats
    });
  } catch (error) {
    next(error);
  }
});

/**
 * 获取用户WiFi统计（管理端）
 * GET /api/user/wifi-stats/:id
 */
router.get('/wifi-stats/:id', verifyToken, async (req, res, next) => {
  try {
    const { id } = req.params;
    
    // 验证用户ID
    if (!id || id === 'undefined' || isNaN(parseInt(id))) {
      return res.status(400).json({
        status: 'error',
        message: '无效的用户ID'
      });
    }
    
    // 检查用户是否存在
    const user = await db.getOne('SELECT id FROM user WHERE id = ?', [id]);
    if (!user) {
      return res.status(404).json({
        status: 'error',
        message: '用户不存在'
      });
    }
    
    // 模拟WiFi统计数据（实际项目中应该从wifi表查询）
    const stats = {
      total_wifi: Math.floor(Math.random() * 20) + 5,
      total_scans: Math.floor(Math.random() * 1000) + 100,
      avg_scans_per_wifi: Math.floor(Math.random() * 50) + 10,
      most_popular_wifi: {
        id: 1,
        title: '星巴克WiFi',
        scan_count: 156
      },
      recent_activity: [
        { date: '2023-06-15', scans: 23 },
        { date: '2023-06-14', scans: 18 },
        { date: '2023-06-13', scans: 31 },
        { date: '2023-06-12', scans: 25 },
        { date: '2023-06-11', scans: 19 }
      ]
    };
    
    res.json({
      status: 'success',
      data: stats
    });
  } catch (error) {
    next(error);
  }
});

/**
 * 获取用户的标签（管理端）
 * GET /api/user/tags/:id
 */
router.get('/tags/:id', verifyToken, async (req, res, next) => {
  try {
    const { id } = req.params;
    
    // 查询用户的标签
    const userTags = await db.query(`
      SELECT ut.id, ut.name, ut.description, ut.color
      FROM user_tag ut
      INNER JOIN user_tag_relation utr ON ut.id = utr.tag_id
      WHERE utr.user_id = ? AND ut.status = 1
      ORDER BY utr.created_at DESC
    `, [id]);
    
    res.json({
      status: 'success',
      data: userTags
    });
  } catch (error) {
    next(error);
  }
});

/**
 * 测试数据库连接和标签表（管理端）
 * GET /api/user/tag/test
 */
router.get('/tag/test', verifyToken, async (req, res, next) => {
  try {
    console.log('测试数据库连接和标签表');
    
    // 检查表是否存在
    const tables = await db.query("SHOW TABLES LIKE 'user_tag%'");
    console.log('用户标签相关表:', tables);
    
    // 查看表结构
    if (tables.length > 0) {
      const structure = await db.query("DESCRIBE user_tag");
      console.log('user_tag表结构:', structure);
      
      // 查看现有数据
      const existingData = await db.query("SELECT * FROM user_tag");
      console.log('现有数据:', existingData);
      
      // 尝试插入测试数据
      const insertResult = await db.query(
        "INSERT INTO user_tag (name, description, color, created_at) VALUES (?, ?, ?, NOW())",
        [`测试标签${Date.now()}`, '这是一个测试标签', '#ff0000']
      );
      console.log('插入结果:', insertResult);
      
      // 再次查看数据
      const newData = await db.query("SELECT * FROM user_tag ORDER BY created_at DESC LIMIT 5");
      console.log('插入后的数据:', newData);
    }
    
    res.json({
      status: 'success',
      data: {
        tables,
        message: '数据库测试完成，请查看控制台日志'
      }
    });
  } catch (error) {
    console.error('数据库测试错误:', error);
    res.status(500).json({
      status: 'error',
      message: error.message
    });
  }
});

/**
 * 删除用户标签（管理端）
 * DELETE /api/user/tag/delete/:id
 */
router.delete('/tag/delete/:id', verifyToken, async (req, res, next) => {
  try {
    const { id } = req.params;
    
    // 检查标签是否存在
    const existingTag = await db.getOne(
      'SELECT id FROM user_tag WHERE id = ? AND status = 1',
      [id]
    );
    
    if (!existingTag) {
      return res.status(404).json({
        status: 'error',
        message: '标签不存在'
      });
    }
    
    // 开始事务
    const transaction = await db.beginTransaction();
    
    try {
      // 删除用户标签关联
      await transaction.query(
        'DELETE FROM user_tag_relation WHERE tag_id = ?',
        [id]
      );
      
      // 软删除标签（设置status为0）
      await transaction.query(
        'UPDATE user_tag SET status = 0, updated_at = NOW() WHERE id = ?',
        [id]
      );
      
      await transaction.commit();
    
    res.json({
      status: 'success',
      message: '标签删除成功'
    });
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  } catch (error) {
    next(error);
  }
});

/**
 * @route   GET /api/v1/client/user/address/list
 * @desc    获取用户收货地址列表
 * @access  Private
 */
router.get('/address/list', verifyToken, async (req, res, next) => {
  try {
    const userId = req.user.id;
    
    console.log('获取地址列表请求');
    
    // 查询用户的收货地址列表
    const addresses = await db.query(
      'SELECT * FROM user_address WHERE user_id = ? ORDER BY is_default DESC, id DESC',
      [userId]
    );
    
    res.json({
      status: 'success',
      data: addresses
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @route   GET /api/v1/client/user/address/default
 * @desc    获取默认收货地址
 * @access  Private
 */
router.get('/address/default', verifyToken, async (req, res, next) => {
  try {
    const userId = req.user.id;

    console.log('获取默认地址请求');

    // 查询用户的默认收货地址
    const address = await db.getOne(
      'SELECT * FROM user_address WHERE user_id = ? AND is_default = 1',
      [userId]
    );

    if (!address) {
      return res.status(404).json({
        status: 'error',
        message: '未设置默认地址'
      });
    }

    res.json({
      status: 'success',
      data: address
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @route   GET /api/v1/client/user/address/detail
 * @desc    获取收货地址详情
 * @access  Private
 */
router.get('/address/detail', verifyToken, async (req, res, next) => {
  try {
    const userId = req.user.id;
    const { id } = req.query;
    
    console.log('获取地址详情请求:', { id });
    
    if (!id) {
      return res.status(400).json({
        status: 'error',
        message: '地址ID不能为空'
      });
    }
    
    // 查询地址详情
    const address = await db.getOne(
      'SELECT * FROM user_address WHERE id = ? AND user_id = ?',
      [id, userId]
    );
    
    if (!address) {
      return res.status(404).json({
        status: 'error',
        message: '地址不存在或无权访问'
      });
    }
    
    res.json({
      status: 'success',
      data: address
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @route   POST /api/v1/client/user/address/add
 * @desc    添加收货地址
 * @access  Private
 */
router.post('/address/add', verifyToken, async (req, res, next) => {
  try {
    const userId = req.user.id;
    const { name, phone, province, city, district, address, is_default } = req.body;
    
    console.log('添加收货地址请求:', req.body);
    
    // 验证必填字段
    if (!name || !phone || !province || !city || !district || !address) {
      return res.status(400).json({
        status: 'error',
        message: '请填写完整的地址信息'
      });
    }
    
    // 开始事务
    const transaction = await db.beginTransaction();
    
    try {
      // 如果设置为默认地址，先将其他地址设为非默认
      if (is_default === 1) {
        await transaction.query(
          'UPDATE user_address SET is_default = 0 WHERE user_id = ?',
          [userId]
        );
      }
      
      // 插入新地址
      const result = await transaction.query(
        'INSERT INTO user_address (user_id, name, phone, province, city, district, address, is_default, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())',
        [userId, name, phone, province, city, district, address, is_default || 0]
      );
      
      await transaction.commit();
      
      res.json({
        status: 'success',
        message: '添加地址成功',
        data: {
          id: result.insertId
        }
      });
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  } catch (error) {
    next(error);
  }
});

/**
 * @route   POST /api/v1/client/user/address/update
 * @desc    更新收货地址
 * @access  Private
 */
router.post('/address/update', verifyToken, async (req, res, next) => {
  try {
    const userId = req.user.id;
    const { id, name, phone, province, city, district, address, is_default } = req.body;
    
    console.log('更新收货地址请求:', req.body);
    
    // 验证必填字段
    if (!id || !name || !phone || !province || !city || !district || !address) {
      return res.status(400).json({
        status: 'error',
        message: '请填写完整的地址信息'
      });
    }
    
    // 检查地址是否存在且属于当前用户
    const existingAddress = await db.getOne(
      'SELECT id FROM user_address WHERE id = ? AND user_id = ?',
      [id, userId]
    );
    
    if (!existingAddress) {
      return res.status(404).json({
        status: 'error',
        message: '地址不存在或无权修改'
      });
    }
    
    // 开始事务
    const transaction = await db.beginTransaction();
    
    try {
      // 如果设置为默认地址，先将其他地址设为非默认
      if (is_default === 1) {
        await transaction.query(
          'UPDATE user_address SET is_default = 0 WHERE user_id = ?',
          [userId]
        );
      }
      
      // 更新地址
      await transaction.query(
        'UPDATE user_address SET name = ?, phone = ?, province = ?, city = ?, district = ?, address = ?, is_default = ?, updated_at = NOW() WHERE id = ? AND user_id = ?',
        [name, phone, province, city, district, address, is_default || 0, id, userId]
      );
      
      await transaction.commit();
      
      res.json({
        status: 'success',
        message: '更新地址成功'
      });
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  } catch (error) {
    next(error);
  }
});

/**
 * @route   POST /api/v1/client/user/address/delete
 * @desc    删除收货地址
 * @access  Private
 */
router.post('/address/delete', verifyToken, async (req, res, next) => {
  try {
    const userId = req.user.id;
    const { id } = req.body;
    
    console.log('删除收货地址请求:', { id });
    
    if (!id) {
      return res.status(400).json({
        status: 'error',
        message: '地址ID不能为空'
      });
    }
    
    // 检查地址是否存在且属于当前用户
    const existingAddress = await db.getOne(
      'SELECT id, is_default FROM user_address WHERE id = ? AND user_id = ?',
      [id, userId]
    );
    
    if (!existingAddress) {
      return res.status(404).json({
        status: 'error',
        message: '地址不存在或无权删除'
      });
    }
    
    // 删除地址
    await db.query(
      'DELETE FROM user_address WHERE id = ? AND user_id = ?',
      [id, userId]
    );
    
    res.json({
      status: 'success',
      message: '删除地址成功'
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @route   POST /api/v1/client/user/address/set-default
 * @desc    设置默认收货地址
 * @access  Private
 */
router.post('/address/set-default', verifyToken, async (req, res, next) => {
  try {
    const userId = req.user.id;
    const { id } = req.body;
    
    console.log('设置默认地址请求:', { id });
    
    if (!id) {
      return res.status(400).json({
        status: 'error',
        message: '地址ID不能为空'
      });
    }
    
    // 检查地址是否存在且属于当前用户
    const existingAddress = await db.getOne(
      'SELECT id FROM user_address WHERE id = ? AND user_id = ?',
      [id, userId]
    );
    
    if (!existingAddress) {
      return res.status(404).json({
        status: 'error',
        message: '地址不存在或无权操作'
      });
    }
    
    // 开始事务
    const transaction = await db.beginTransaction();
    
    try {
      // 先将所有地址设为非默认
      await transaction.query(
        'UPDATE user_address SET is_default = 0 WHERE user_id = ?',
        [userId]
      );
      
      // 将指定地址设为默认
      await transaction.query(
        'UPDATE user_address SET is_default = 1 WHERE id = ? AND user_id = ?',
        [id, userId]
      );
      
      await transaction.commit();
      
      res.json({
        status: 'success',
        message: '设置默认地址成功'
    });
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  } catch (error) {
    next(error);
  }
});

module.exports = router; 