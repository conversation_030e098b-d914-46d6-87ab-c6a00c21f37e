/**
 * 测试图标修复效果
 */

const { iconMapping, getIconPath, processCategoryIcons, convertHttpIcon } = require('./utils/iconMapping.js');

console.log('🧪 测试图标映射功能...\n');

// 1. 测试图标映射
console.log('1️⃣ 测试Element UI图标映射:');
const testIcons = [
  'el-icon-mobile-phone',
  'el-icon-house', 
  'el-icon-shopping-bag-1',
  'el-icon-coffee-cup',
  'el-icon-reading',
  'el-icon-bicycle'
];

testIcons.forEach(iconName => {
  const mappedPath = getIconPath(iconName);
  console.log(`${iconName} → ${mappedPath}`);
});

// 2. 测试分类数据处理
console.log('\n2️⃣ 测试分类数据处理:');
const mockCategories = [
  { id: 1, name: '数码产品', icon: 'el-icon-mobile-phone' },
  { id: 2, name: '家居用品', icon: 'el-icon-house' },
  { id: 3, name: '服装鞋帽', icon: 'el-icon-shopping-bag-1' },
  { id: 4, name: '食品饮料', icon: 'el-icon-coffee-cup' },
  { id: 5, name: '图书文具', icon: 'el-icon-reading' },
  { id: 6, name: '运动户外', icon: 'el-icon-bicycle' }
];

console.log('原始数据:');
console.table(mockCategories);

const processedCategories = processCategoryIcons(mockCategories);
console.log('\n处理后数据:');
console.table(processedCategories);

// 3. 测试HTTP转换
console.log('\n3️⃣ 测试HTTP协议转换:');
const httpUrls = [
  'http://localhost:4000/uploads/images/test.jpg',
  'https://example.com/image.png',
  '/assets/icons/cart.png',
  'el-icon-mobile-phone'
];

httpUrls.forEach(url => {
  const convertedUrl = convertHttpIcon(url);
  console.log(`${url} → ${convertedUrl}`);
});

// 4. 检查图标文件是否存在
console.log('\n4️⃣ 检查图标文件存在性:');
const fs = require('fs');
const path = require('path');

Object.values(iconMapping).forEach(iconPath => {
  // 移除开头的斜杠，构建相对路径
  const relativePath = iconPath.startsWith('/') ? iconPath.substring(1) : iconPath;
  const fullPath = path.join(__dirname, relativePath);
  
  try {
    const exists = fs.existsSync(fullPath);
    console.log(`${iconPath}: ${exists ? '✅ 存在' : '❌ 不存在'}`);
  } catch (error) {
    console.log(`${iconPath}: ❌ 检查失败 - ${error.message}`);
  }
});

// 5. 生成图标创建建议
console.log('\n5️⃣ 图标创建建议:');
console.log('缺失的图标可以通过以下方式创建:');
console.log('1. 打开 assets/icons/category/create-icons.html');
console.log('2. 点击对应分类的"下载PNG"按钮');
console.log('3. 将下载的图标放入正确目录');
console.log('4. 更新 iconMapping.js 中的路径');

console.log('\n✅ 图标映射测试完成!');

// 6. 输出当前配置状态
console.log('\n6️⃣ 当前配置状态:');
console.log('- urlCheck: false (已禁用URL检查)');
console.log('- 图标映射: 已配置临时方案');
console.log('- HTTP转换: 支持自动转换');
console.log('- 错误处理: 支持默认图标降级');

module.exports = {
  testIcons,
  mockCategories,
  processedCategories
};
