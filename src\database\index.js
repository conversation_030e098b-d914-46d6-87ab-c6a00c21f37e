const mysql = require('mysql2/promise');
const config = require('../../config');
const logger = require('../utils/logger');
const bcrypt = require('bcryptjs');

// 创建数据库连接池
let pool;

/**
 * 初始化数据库连接
 */
async function init() {
  try {
    // 先创建不指定数据库的连接
    const rootPool = mysql.createPool({
      host: 'localhost',
      port: 3306,
      user: 'root',
      password: 'wo587129955',
      waitForConnections: true,
      connectionLimit: 2,
      queueLimit: 0
    });
    
    // 创建数据库
    logger.info('检查数据库是否存在...');
    await rootPool.query(`CREATE DATABASE IF NOT EXISTS mall CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci`);
    logger.info(`数据库 mall 已就绪`);
    
    // 关闭根连接池
    await rootPool.end();
    
    // 创建正式连接池
    pool = mysql.createPool({
      host: 'localhost',
      port: 3306,
      user: 'root',
      password: 'wo587129955',
      database: 'mall',
      waitForConnections: true,
      connectionLimit: 10,
      queueLimit: 0
    });
    
    // 测试连接
    const connection = await pool.getConnection();
    logger.info('数据库连接池初始化成功');
    connection.release();
    
    // 检查并创建必要的表
    await createTablesIfNotExist();
    
    return pool;
  } catch (error) {
    logger.error('数据库连接失败:', error);
    throw error;
  }
}

/**
 * 执行SQL查询
 * @param {string} sql - SQL语句
 * @param {Array} params - 查询参数
 * @returns {Promise<Array>} - 查询结果
 */
async function query(sql, params = []) {
  if (!pool) {
    await init();
  }
  
  try {
    logger.info('执行SQL查询', { sql, params });
    // 确保参数类型正确，数字保持数字类型，其他类型转为字符串
    const sanitizedParams = params.map(param => {
      if (typeof param === 'number') {
        return param; // 保持数字类型
      } else if (param === null) {
        return param; // null保持不变
      } else {
        return String(param); // 其他类型转为字符串
      }
    });
    const [rows] = await pool.execute(sql, sanitizedParams);
    return rows;
  } catch (error) {
    logger.error('SQL执行失败:', error);
    throw error;
  }
}

/**
 * 获取单条记录
 * @param {string} sql - SQL语句
 * @param {Array} params - 查询参数
 * @returns {Promise<Object>} - 查询结果
 */
async function getOne(sql, params = []) {
  const rows = await query(sql, params);
  return rows[0];
}

/**
 * 创建事务
 * @returns {Promise<Object>} - 事务对象
 */
async function beginTransaction() {
  if (!pool) {
    await init();
  }
  
  const connection = await pool.getConnection();
  await connection.beginTransaction();
  
  return {
    query: async (sql, params = []) => {
      const [rows] = await connection.execute(sql, params);
      return rows;
    },
    commit: async () => {
      await connection.commit();
      connection.release();
    },
    rollback: async () => {
      await connection.rollback();
      connection.release();
    }
  };
}

/**
 * 检查并创建必要的表
 */
async function createTablesIfNotExist() {
  try {
    // 使用数据库
    await pool.query(`USE ${config.database.database}`);
    
    // 检查是否存在用户表，如果不存在则创建
    const checkUserTableSql = `
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = ? AND table_name = 'users'
    `;
    
    const userTable = await query(checkUserTableSql, [config.database.database]);
    
    if (userTable.length === 0) {
      logger.info('创建必要的数据库表...');
      
      // 创建用户表
      await query(`
        CREATE TABLE users (
          id INT AUTO_INCREMENT PRIMARY KEY,
          username VARCHAR(50) NOT NULL UNIQUE,
          password VARCHAR(255) NOT NULL,
          nickname VARCHAR(50),
          phone VARCHAR(20),
          avatar VARCHAR(255),
          role ENUM('admin', 'user', 'agent') DEFAULT 'user',
          status TINYINT DEFAULT 1,
          openid VARCHAR(50),
          parent_id INT,
          team_level TINYINT DEFAULT 0,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          INDEX (openid),
          INDEX (parent_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
      `);
      
      logger.info('用户表创建成功');
      
      // 添加默认管理员账户
      const adminPassword = await bcrypt.hash('admin123', 10);
      await query(`
        INSERT INTO users (username, password, nickname, role, status, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, NOW(), NOW())
      `, ['admin', adminPassword, '系统管理员', 'admin', 1]);
      
      logger.info('默认管理员账户创建成功');
    }
  } catch (error) {
    logger.error('创建数据库表失败:', error);
    throw error;
  }
}

module.exports = {
  init,
  query,
  getOne,
  beginTransaction
}; 