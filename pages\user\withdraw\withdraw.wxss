/* 提现页面样式 */

.withdraw-container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 200rpx;
}

/* 余额卡片 */
.balance-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20rpx;
  padding: 40rpx;
  color: white;
  text-align: center;
  margin-bottom: 30rpx;
  box-shadow: 0 8rpx 32rpx rgba(102, 126, 234, 0.3);
}

.balance-title {
  font-size: 28rpx;
  opacity: 0.9;
  margin-bottom: 10rpx;
}

.balance-amount {
  font-size: 60rpx;
  font-weight: bold;
  margin: 20rpx 0;
}

.balance-tip {
  font-size: 24rpx;
  opacity: 0.8;
}

/* 通用区块样式 */
.amount-section,
.method-section,
.quick-amount-section,
.notice-section {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
}

/* 金额输入区域 */
.amount-input-wrapper {
  display: flex;
  align-items: center;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  background: #fafafa;
}

.currency-symbol {
  font-size: 36rpx;
  color: #666;
  margin-right: 10rpx;
}

.amount-input {
  flex: 1;
  font-size: 36rpx;
  color: #333;
  font-weight: bold;
}

.amount-tips {
  display: flex;
  justify-content: space-between;
  font-size: 24rpx;
  color: #999;
  margin-bottom: 20rpx;
}

.fee-info {
  display: flex;
  justify-content: space-between;
  font-size: 28rpx;
  color: #ff6b35;
  padding: 20rpx;
  background: #fff5f0;
  border-radius: 8rpx;
  border-left: 4rpx solid #ff6b35;
}

/* 提现方式 */
.method-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  margin-bottom: 20rpx;
  border: 2rpx solid #f0f0f0;
  border-radius: 16rpx;
  background: #fafafa;
  transition: all 0.3s ease;
}

.method-item.selected {
  border-color: #007aff;
  background: #f0f8ff;
  box-shadow: 0 4rpx 16rpx rgba(0, 122, 255, 0.2);
}

.method-icon {
  width: 60rpx;
  height: 60rpx;
  margin-right: 20rpx;
}

.method-icon image {
  width: 100%;
  height: 100%;
}

.method-info {
  flex: 1;
}

.method-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.method-desc {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.method-fee {
  font-size: 24rpx;
  color: #999;
}

.method-check {
  width: 40rpx;
  height: 40rpx;
}

.method-check image {
  width: 100%;
  height: 100%;
}

/* 添加银行卡 */
.add-card-item {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 30rpx;
  border: 2rpx dashed #ccc;
  border-radius: 16rpx;
  color: #666;
  font-size: 28rpx;
}

.add-icon {
  font-size: 40rpx;
  margin-right: 10rpx;
  color: #007aff;
}

/* 快捷金额 */
.quick-amounts {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.quick-amount-item {
  flex: 1;
  min-width: 120rpx;
  padding: 20rpx;
  text-align: center;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #333;
  background: #fafafa;
}

.quick-amount-item:active {
  background: #e0e0e0;
}

/* 提现说明 */
.notice-list {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.notice-list text {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
}

/* 提现按钮 */
.submit-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20rpx 30rpx;
  background: white;
  border-top: 1rpx solid #f0f0f0;
  box-shadow: 0 -4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.submit-btn {
  width: 100%;
  height: 88rpx;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: bold;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.submit-btn.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.submit-btn.disabled {
  background: #f0f0f0;
  color: #ccc;
}

/* 确认弹窗 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.confirm-modal {
  width: 600rpx;
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  margin: 40rpx;
}

.modal-title {
  font-size: 36rpx;
  font-weight: bold;
  text-align: center;
  margin-bottom: 30rpx;
  color: #333;
}

.modal-content {
  margin-bottom: 40rpx;
}

.confirm-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.confirm-item:last-child {
  border-bottom: none;
}

.confirm-item .label {
  font-size: 28rpx;
  color: #666;
}

.confirm-item .value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.confirm-item .value.highlight {
  color: #ff6b35;
  font-weight: bold;
  font-size: 32rpx;
}

.modal-buttons {
  display: flex;
  gap: 20rpx;
}

.cancel-btn,
.confirm-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 40rpx;
  font-size: 30rpx;
  border: none;
}

.cancel-btn {
  background: #f0f0f0;
  color: #666;
}

.confirm-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .withdraw-container {
    padding: 15rpx;
  }
  
  .balance-card {
    padding: 30rpx;
  }
  
  .balance-amount {
    font-size: 50rpx;
  }
  
  .method-item {
    padding: 24rpx;
  }
  
  .quick-amount-item {
    min-width: 100rpx;
    padding: 16rpx;
    font-size: 26rpx;
  }
}

/* 动画效果 */
.method-item {
  animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.confirm-modal {
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}
