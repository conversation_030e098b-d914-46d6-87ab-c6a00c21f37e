// 简单的删除脚本
const db = require('./src/database');

async function deleteTestOrder() {
  try {
    console.log('开始查找测试订单...');
    
    // 初始化数据库
    await db.init();
    
    // 查找金额为1065.00的订单
    const orders = await db.query(
      'SELECT id, order_no, user_id, status, total_amount, created_at FROM orders WHERE total_amount = ? ORDER BY created_at DESC',
      [1065.00]
    );

    console.log('找到的订单:', orders);

    if (orders && orders.length > 0) {
      for (const order of orders) {
        console.log(`准备删除订单: ID=${order.id}, 订单号=${order.order_no}, 金额=${order.total_amount}`);
        
        // 删除订单商品
        await db.query('DELETE FROM order_goods WHERE order_id = ?', [order.id]);
        console.log(`已删除订单商品，订单ID: ${order.id}`);
        
        // 删除订单项目（如果有order_items表）
        await db.query('DELETE FROM order_items WHERE order_id = ?', [order.id]);
        console.log(`已删除订单项目，订单ID: ${order.id}`);
        
        // 删除订单
        await db.query('DELETE FROM orders WHERE id = ?', [order.id]);
        console.log(`已删除订单，订单ID: ${order.id}`);
      }
      
      console.log('测试订单删除完成！');
    } else {
      console.log('未找到金额为1065.00的订单，查看所有最近的订单:');
      
      // 查看最近的订单
      const recentOrders = await db.query(
        'SELECT id, order_no, user_id, status, total_amount, created_at FROM orders ORDER BY created_at DESC LIMIT 10'
      );
      
      console.log('最近的订单:', recentOrders);
      
      // 如果有订单，询问是否要删除最新的测试订单
      if (recentOrders && recentOrders.length > 0) {
        const latestOrder = recentOrders[0];
        console.log(`最新订单: ID=${latestOrder.id}, 订单号=${latestOrder.order_no}, 金额=${latestOrder.total_amount}`);
        
        // 删除最新的订单（假设是测试订单）
        await db.query('DELETE FROM order_goods WHERE order_id = ?', [latestOrder.id]);
        await db.query('DELETE FROM order_items WHERE order_id = ?', [latestOrder.id]);
        await db.query('DELETE FROM orders WHERE id = ?', [latestOrder.id]);
        
        console.log(`已删除最新的测试订单: ${latestOrder.order_no}`);
      }
    }

  } catch (error) {
    console.error('操作失败:', error);
  }
}

deleteTestOrder();
