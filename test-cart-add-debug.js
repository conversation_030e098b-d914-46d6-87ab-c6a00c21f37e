const axios = require('axios');
const jwt = require('jsonwebtoken');
const mysql = require('mysql2/promise');

async function testCartAdd() {
  let connection;
  try {
    console.log('开始测试购物车添加功能...');

    // 连接数据库检查数据
    console.log('1. 连接数据库检查数据...');
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: 'wo587129955',
      database: 'mall'
    });

    // 检查用户是否存在
    const [users] = await connection.execute('SELECT id, openid, nickname FROM user WHERE id = 2');
    console.log('用户信息:', users);

    if (users.length === 0) {
      console.error('用户ID 2 不存在');
      return;
    }

    // 检查商品是否存在
    const [goods] = await connection.execute('SELECT id, title, price, stock, status FROM goods WHERE id = 1');
    console.log('商品信息:', goods);

    if (goods.length === 0) {
      console.error('商品ID 1 不存在');
      return;
    }

    // 直接生成JWT token，使用数据库中的用户ID
    console.log('\n2. 生成测试token...');
    const config = require('./config');
    const userId = 2;

    const token = jwt.sign(
      { id: userId, openid: users[0].openid },
      config.jwt.secret,
      { expiresIn: config.jwt.expiresIn }
    );

    console.log('生成的token:', token);

    // 测试添加商品到购物车
    console.log('\n3. 测试添加商品到购物车...');
    const cartAddResponse = await axios.post('http://localhost:4000/api/v1/client/cart/add', {
      goodsId: 1,
      quantity: 1,
      specificationId: 0
    }, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      timeout: 10000
    });

    console.log('购物车添加响应:', cartAddResponse.data);

    // 检查购物车数据
    console.log('\n4. 检查购物车数据...');
    const [cartItems] = await connection.execute('SELECT * FROM cart WHERE user_id = 2');
    console.log('购物车数据:', cartItems);

  } catch (error) {
    console.error('测试失败:', error.message);
    if (error.response) {
      console.error('响应状态:', error.response.status);
      console.error('响应数据:', error.response.data);
    } else if (error.code === 'ECONNREFUSED') {
      console.error('连接被拒绝，请确保服务器正在运行');
    } else {
      console.error('其他错误:', error.code || error.message);
    }
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// 运行测试
testCartAdd();
