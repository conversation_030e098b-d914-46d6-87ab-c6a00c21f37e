const mysql = require('mysql2/promise');

async function checkIncomeTables() {
  let connection;
  
  try {
    // 连接数据库
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: 'wo587129955',
      database: 'mall'
    });
    
    console.log('🔍 检查收入相关数据表...\n');
    
    // 1. 检查现有的收入相关表
    console.log('1️⃣ 现有收入相关表:');
    const [tables] = await connection.execute(`
      SELECT table_name, table_comment 
      FROM information_schema.tables 
      WHERE table_schema = 'mall' 
      AND (table_name LIKE '%income%' 
           OR table_name LIKE '%profit%' 
           OR table_name LIKE '%wallet%'
           OR table_name LIKE '%withdraw%'
           OR table_name LIKE '%balance%'
           OR table_comment LIKE '%收入%'
           OR table_comment LIKE '%分润%'
           OR table_comment LIKE '%钱包%')
      ORDER BY table_name
    `);
    
    if (tables.length > 0) {
      console.table(tables);
    } else {
      console.log('❌ 未找到收入相关表');
    }
    
    // 2. 检查用户表中的收入字段
    console.log('\n2️⃣ 用户表收入字段:');
    const [userColumns] = await connection.execute(`
      SELECT column_name, column_type, column_comment 
      FROM information_schema.columns 
      WHERE table_schema = 'mall' 
      AND table_name = 'user' 
      AND (column_name LIKE '%income%' 
           OR column_name LIKE '%balance%' 
           OR column_name LIKE '%profit%'
           OR column_comment LIKE '%收入%'
           OR column_comment LIKE '%余额%')
      ORDER BY ordinal_position
    `);
    
    if (userColumns.length > 0) {
      console.table(userColumns);
    } else {
      console.log('❌ 用户表中未找到收入相关字段');
    }
    
    // 3. 检查分润相关表
    console.log('\n3️⃣ 分润相关表:');
    const profitTables = ['profit_rule', 'profit_config', 'profit_log', 'team_level_profit'];
    
    for (const tableName of profitTables) {
      const [tableExists] = await connection.execute(`
        SELECT COUNT(*) as count 
        FROM information_schema.tables 
        WHERE table_schema = 'mall' AND table_name = ?
      `, [tableName]);
      
      if (tableExists[0].count > 0) {
        const [rowCount] = await connection.execute(`SELECT COUNT(*) as count FROM ${tableName}`);
        console.log(`✅ ${tableName}: 存在 (${rowCount[0].count} 条记录)`);
      } else {
        console.log(`❌ ${tableName}: 不存在`);
      }
    }
    
    // 4. 检查是否需要创建的收入管理表
    console.log('\n4️⃣ 需要的收入管理表:');
    const requiredTables = [
      'income_record',      // 收入记录表
      'withdraw_record',    // 提现记录表
      'wallet_transaction', // 钱包交易记录表
      'income_statistics'   // 收入统计表
    ];
    
    for (const tableName of requiredTables) {
      const [tableExists] = await connection.execute(`
        SELECT COUNT(*) as count 
        FROM information_schema.tables 
        WHERE table_schema = 'mall' AND table_name = ?
      `, [tableName]);
      
      if (tableExists[0].count > 0) {
        const [rowCount] = await connection.execute(`SELECT COUNT(*) as count FROM ${tableName}`);
        console.log(`✅ ${tableName}: 存在 (${rowCount[0].count} 条记录)`);
      } else {
        console.log(`❌ ${tableName}: 不存在 - 需要创建`);
      }
    }
    
    console.log('\n📊 总结:');
    console.log('- 基础分润表: 已存在');
    console.log('- 用户收入字段: 已存在');
    console.log('- 团队收入字段: 已存在');
    console.log('- 收入管理表: 需要补充');
    
  } catch (error) {
    console.error('❌ 检查失败:', error.message);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// 运行检查
checkIncomeTables();
