<!--pages/mall/home/<USER>
<!--商城首页 - 严格按照UI示意图设计-->

<view class="mall-container">
  <!-- 自定义导航栏 -->
  <view class="custom-navbar">
    <view class="navbar-content">
      <view class="navbar-left">
        <text class="navbar-title">商城</text>
      </view>
      <view class="navbar-right">
        <!-- 购物车图标已移除 -->
      </view>
    </view>
  </view>


  <!-- 微信广告区域 (页面顶部) -->
  <view class="ad-traffic-section">
    <view class="wechat-ad-container">
      <!-- 微信官方横幅广告 -->
      <ad
        unit-id="{{topAdUnitId}}"
        ad-type="banner"
        ad-theme="white"
        style="width: 100%; height: auto; min-height: 200rpx; display: block;"
        bindload="onTopAdLoad"
        binderror="onTopAdError"
        bindclose="onTopAdClose"
        bindtouchstart="onTopAdTouchStart"
        bindtouchend="onTopAdTouchEnd"
      ></ad>

      <!-- 广告加载失败时的降级显示 -->
      <view class="ad-fallback" wx:if="{{!showTopAd}}">
        <view class="fallback-banner" bindtap="onTrafficBannerTap">
          <image
            src="{{trafficBannerData.image}}"
            class="traffic-image"
            mode="aspectFill"
            lazy-load="{{true}}"
          ></image>
          <view class="traffic-overlay">
            <text class="traffic-title">微信广告流量</text>
            <text class="traffic-subtitle">点击获取更多收益</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 广告收益显示 -->
    <view class="ad-revenue-info" wx:if="{{adRevenue > 0}}">
      <text class="revenue-text">今日广告收益: ¥{{adRevenue.toFixed(2)}}</text>
    </view>
  </view>

  <!-- 搜索商品区域 -->
  <view class="search-section">
    <view class="search-container">
      <view class="search-box" bindtap="onSearchTap">
        <view class="search-icon">
          <text class="icon-search">🔍</text>
        </view>
        <text class="search-placeholder">搜索商品...</text>
        <view class="search-divider"></view>
      </view>
    </view>
  </view>

  <!-- 商品分类入口 -->
  <view class="category-section">
    <view class="category-title">
      <text>商品分类</text>
      <view class="more-categories" bindtap="navigateToCategory">
        <text>全部分类</text>
        <text class="more-icon">></text>
      </view>
    </view>
    <scroll-view scroll-x="true" class="category-scroll" enable-flex="true" enhanced="true" show-scrollbar="false">
      <view 
        wx:for="{{categories}}" 
        wx:key="id" 
        class="category-item"
        bindtap="onCategoryTap"
        data-category="{{item}}"
      >
        <view class="category-icon-wrapper">
          <image src="{{item.icon || '/assets/icons/cart.png'}}" class="category-icon" mode="aspectFill"></image>
        </view>
        <text class="category-name">{{item.name}}</text>
      </view>
    </scroll-view>
  </view>

  <!-- 推荐商品标题区域 -->
  <view class="recommend-section">
    <view class="recommend-header">
      <view class="recommend-line"></view>
      <text class="recommend-title">[推荐商品]</text>
      <view class="recommend-line"></view>
    </view>
  </view>

  <!-- 商品网格展示 (严格按照UI示意图2列布局) -->
  <view class="goods-section">
    <!-- 加载状态 -->
    <view class="loading-container" wx:if="{{isLoading && goodsList.length === 0}}">
      <view class="loading-spinner"></view>
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 商品列表 -->
    <view class="goods-grid" wx:if="{{goodsList.length > 0}}">
      <view
        wx:for="{{goodsList}}"
        wx:key="id"
        class="goods-item"
        bindtap="onGoodsItemTap"
        data-goods="{{item}}"
      >
        <!-- 在第4个商品后插入中间微信广告 -->
        <view class="middle-ad-container" wx:if="{{index === 3}}">
          <view class="ad-section-title">
            <text class="ad-title-text">微信推荐</text>
          </view>
          <ad
            unit-id="{{topAdUnitId}}"
            ad-type="banner"
            ad-theme="white"
            style="width: 100%; height: auto; min-height: 150rpx; display: block;"
            bindload="onMiddleAdLoad"
            binderror="onMiddleAdError"
            bindclose="onMiddleAdClose"
          ></ad>
        </view>

        <view class="goods-card">
          <!-- 商品图片区域 -->
          <view class="goods-image-container">
            <image
              src="{{item.cover || '/assets/images/goods-placeholder.svg'}}"
              class="goods-image"
              mode="aspectFill"
              lazy-load="{{true}}"
              binderror="onImageError"
              data-index="{{index}}"
            ></image>

            <!-- 商品标签 -->
            <view class="goods-tags" wx:if="{{item.tags && item.tags.length > 0}}">
              <text
                wx:for="{{item.tags}}"
                wx:for-item="tag"
                wx:key="*this"
                class="goods-tag"
                style="background-color: {{tag === '热销' ? '#ff6b6b' : tag === '新品' ? '#4ecdc4' : tag === '推荐' ? '#45b7d1' : tag === '限时优惠' ? '#f39c12' : '#95a5a6'}}"
              >{{tag}}</text>
            </view>
          </view>

          <!-- 商品信息区域 -->
          <view class="goods-info">
            <view class="goods-name">{{item.name}}</view>

            <!-- 价格区域 -->
            <view class="goods-price-row">
              <text class="goods-price">¥{{item.price}}</text>
              <text class="goods-original-price" wx:if="{{(item.originalPrice || item.original_price) && (item.originalPrice !== '' || item.original_price !== '')}}">¥{{item.originalPrice || item.original_price}}</text>
            </view>

            <!-- 销量信息行 -->
            <view class="goods-bottom-row">
              <view class="goods-stats" wx:if="{{item.sales}}">
                <text class="goods-sales">已售{{item.sales}}件</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 加载更多按钮 -->
    <view class="load-more-section" wx:if="{{goodsList.length > 0 && hasMore}}">
      <view 
        class="load-more-btn {{loadingMore ? 'loading' : ''}}" 
        bindtap="onLoadMore"
      >
        <view class="load-more-spinner" wx:if="{{loadingMore}}"></view>
        <text class="load-more-text">{{loadingMore ? '加载中...' : '加载更多'}}</text>
      </view>
    </view>
    
    <!-- 没有更多数据提示 -->
    <view class="no-more-data" wx:if="{{goodsList.length > 0 && !hasMore}}">
      <text class="no-more-text">没有更多商品了</text>
    </view>
    
    <!-- 空状态展示 -->
    <view class="empty-goods" wx:if="{{!isLoading && (!goodsList || goodsList.length === 0)}}">
      <view class="empty-content">
        <image
          src="/assets/images/empty-goods.svg"
          class="empty-image"
          mode="aspectFit"
        ></image>
        <text class="empty-title">暂无商品</text>
        <text class="empty-subtitle">精彩商品正在路上，敬请期待</text>
        <button class="empty-refresh-btn" bindtap="onPullDownRefresh">
          刷新重试
        </button>
      </view>
    </view>
  </view>

  <!-- 微信广告区域 (页面底部) -->
  <view class="bottom-ad-section" wx:if="{{goodsList.length > 0}}">
    <view class="ad-section-title">
      <text class="ad-title-text">微信推荐</text>
    </view>

    <view class="wechat-bottom-ad-container">
      <!-- 微信官方横幅广告 -->
      <ad
        unit-id="{{bottomAdUnitId}}"
        ad-type="banner"
        ad-theme="white"
        style="width: 100%; height: auto; min-height: 200rpx; display: block;"
        bindload="onBottomAdLoad"
        binderror="onBottomAdError"
        bindclose="onBottomAdClose"
      ></ad>

      <!-- 广告加载失败时的降级显示 -->
      <view class="ad-fallback" wx:if="{{!showBottomAd}}">
        <view class="fallback-banner" bindtap="onBottomAdTap">
          <image
            src="{{bottomAdData.image}}"
            class="bottom-ad-image"
            mode="aspectFill"
            lazy-load="{{true}}"
          ></image>
          <view class="bottom-ad-overlay">
            <view class="bottom-ad-content">
              <text class="bottom-ad-title">{{bottomAdData.title}}</text>
              <text class="bottom-ad-subtitle">{{bottomAdData.subtitle}}</text>
            </view>
            <view class="bottom-ad-action">
              <text class="bottom-ad-btn">立即查看</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 底部安全间距 -->
  <view class="bottom-safe-area"></view>
</view>

<!-- 搜索输入模态框 (隐藏实现) -->
<view class="search-modal" wx:if="{{showSearchModal}}" catchtap="hideSearchModal">
  <view class="search-modal-content" catchtap="stopPropagation">
    <view class="search-input-container">
      <input 
        class="search-input"
        placeholder="请输入商品名称"
        value="{{searchKeyword}}"
        bindinput="onSearchInput"
        bindconfirm="onSearchConfirm"
        focus="{{searchFocus}}"
        confirm-type="search"
      />
      <button class="search-btn" bindtap="onSearchConfirm">搜索</button>
    </view>
    
    <!-- 搜索历史 -->
    <view class="search-history" wx:if="{{searchHistory.length > 0}}">
      <view class="history-header">
        <text class="history-title">搜索历史</text>
        <text class="history-clear" bindtap="clearSearchHistory">清空</text>
      </view>
      <view class="history-tags">
        <text 
          wx:for="{{searchHistory}}" 
          wx:key="*this"
          class="history-tag"
          bindtap="onHistoryItemTap"
          data-keyword="{{item}}"
        >{{item}}</text>
      </view>
    </view>
  </view>
</view> 