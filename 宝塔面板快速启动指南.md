# 🚀 宝塔面板快速启动WiFi共享商城后端服务

## 📋 快速启动步骤

### 1. 环境准备
在宝塔面板中安装以下软件：
- **Node.js** (版本16+)
- **MySQL** (版本8.0)
- **PM2** (进程管理器)

### 2. 数据库配置
1. 创建数据库：`mall`
2. 字符集：`utf8mb4`
3. 记住MySQL的root密码

### 3. 修改配置文件
编辑 `wifi-share-server/.env` 文件，修改数据库密码：
```env
DB_PASSWORD=您的MySQL密码
```

### 4. 安装依赖
在终端中执行：
```bash
cd /www/wwwroot/您的网站目录/wifi-share-server
npm install
```

### 5. 初始化数据库
```bash
node init-database.js
```

### 6. 启动服务
```bash
# 方法一：使用PM2（推荐）
pm2 start server.js --name wifi-share-server

# 方法二：直接启动
node server.js
```

### 7. 开放端口
在宝塔面板"安全"中开放端口：**4000**

### 8. 验证服务
访问：`http://您的服务器IP:4000/api/v1/health`

## 🔧 常用管理命令

```bash
# 查看服务状态
pm2 status

# 查看日志
pm2 logs wifi-share-server

# 重启服务
pm2 restart wifi-share-server

# 停止服务
pm2 stop wifi-share-server
```

## ✅ 成功标志
- 端口4000被监听
- API接口返回正常响应
- 数据库连接成功

---

**服务启动后，您的API地址为：**
`http://您的服务器IP:4000/api/v1/client`
