const axios = require('axios');

async function testUserUpdateAPI() {
  try {
    console.log('🧪 测试用户更新API...\n');
    
    // 模拟前端发送的数据
    const testData = {
      nickname: '测试用户更新',
      avatar: 'https://example.com/test-avatar.jpg',
      gender: 1,
      country: '中国',
      province: '广东省',
      city: '深圳市'
    };
    
    console.log('📤 发送的测试数据:', JSON.stringify(testData, null, 2));
    
    // 发送POST请求到用户更新接口
    const response = await axios.post('http://localhost:4000/api/v1/client/user/update', testData, {
      headers: {
        'Content-Type': 'application/json',
        // 这里需要一个有效的JWT token，实际测试时需要先登录获取
        'Authorization': 'Bearer your-jwt-token-here'
      }
    });
    
    console.log('✅ API响应成功:');
    console.log('状态码:', response.status);
    console.log('响应数据:', JSON.stringify(response.data, null, 2));
    
  } catch (error) {
    console.error('❌ API测试失败:');
    
    if (error.response) {
      // 服务器响应了错误状态码
      console.log('状态码:', error.response.status);
      console.log('错误信息:', JSON.stringify(error.response.data, null, 2));
    } else if (error.request) {
      // 请求发送了但没有收到响应
      console.log('请求超时或网络错误');
    } else {
      // 其他错误
      console.log('错误:', error.message);
    }
  }
}

// 测试不包含可选字段的数据
async function testUserUpdateWithoutOptionalFields() {
  try {
    console.log('\n🧪 测试不包含可选字段的用户更新...\n');
    
    // 模拟只包含必需字段的数据
    const testData = {
      nickname: '简单测试用户',
      avatar: 'https://example.com/simple-avatar.jpg',
      gender: 0
      // 不包含 country, province, city
    };
    
    console.log('📤 发送的测试数据:', JSON.stringify(testData, null, 2));
    
    const response = await axios.post('http://localhost:4000/api/v1/client/user/update', testData, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer your-jwt-token-here'
      }
    });
    
    console.log('✅ API响应成功:');
    console.log('状态码:', response.status);
    console.log('响应数据:', JSON.stringify(response.data, null, 2));
    
  } catch (error) {
    console.error('❌ API测试失败:');
    
    if (error.response) {
      console.log('状态码:', error.response.status);
      console.log('错误信息:', JSON.stringify(error.response.data, null, 2));
    } else if (error.request) {
      console.log('请求超时或网络错误');
    } else {
      console.log('错误:', error.message);
    }
  }
}

// 检查服务器状态
async function checkServerStatus() {
  try {
    console.log('🔍 检查服务器状态...\n');
    
    const response = await axios.get('http://localhost:4000/api/v1/client/user/info', {
      headers: {
        'Authorization': 'Bearer your-jwt-token-here'
      },
      timeout: 5000
    });
    
    console.log('✅ 服务器运行正常');
    console.log('状态码:', response.status);
    
  } catch (error) {
    if (error.code === 'ECONNREFUSED') {
      console.log('❌ 服务器未启动，请先启动后端服务');
      console.log('启动命令: cd wifi-share-server && node app.js');
    } else if (error.response && error.response.status === 401) {
      console.log('⚠️  服务器运行正常，但需要有效的JWT token');
    } else {
      console.log('❌ 服务器连接失败:', error.message);
    }
  }
}

// 运行所有测试
async function runAllTests() {
  console.log('🚀 开始用户更新API测试\n');
  
  await checkServerStatus();
  
  console.log('\n' + '='.repeat(50));
  await testUserUpdateAPI();
  
  console.log('\n' + '='.repeat(50));
  await testUserUpdateWithoutOptionalFields();
  
  console.log('\n🏁 测试完成');
  console.log('\n💡 注意事项:');
  console.log('1. 需要先启动后端服务: node app.js');
  console.log('2. 需要有效的JWT token才能测试完整功能');
  console.log('3. 如果看到401错误，说明需要先登录获取token');
}

// 如果直接运行此脚本
if (require.main === module) {
  runAllTests().catch(console.error);
}

module.exports = {
  testUserUpdateAPI,
  testUserUpdateWithoutOptionalFields,
  checkServerStatus
};
