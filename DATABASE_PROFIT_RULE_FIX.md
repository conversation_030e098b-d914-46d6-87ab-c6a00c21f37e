# 数据库profit_rule表结构修复说明

## 错误描述
在执行数据库建表语句时遇到错误：
```
Unknown column 'user_rate' in 'field list'
```

## 问题分析

### 1. 错误原因
- 数据库中现有的 `profit_rule` 表结构与建表语句不匹配
- 现有表缺少 `user_rate` 和 `min_amount` 字段
- `type` 字段类型不匹配（现有：`tinyint`，期望：`varchar(20)`）
- 部分字段缺少默认值

### 2. 表结构对比

#### 现有表结构（有问题）
```sql
+---------------+--------------+------+-----+---------+----------------+
| Field         | Type         | Null | Key | Default | Extra          |
+---------------+--------------+------+-----+---------+----------------+
| id            | int          | NO   | PRI | NULL    | auto_increment |
| name          | varchar(50)  | NO   |     | NULL    |                |
| type          | tinyint      | NO   |     | NULL    |                |
| platform_rate | decimal(5,2) | NO   |     | NULL    |                |
| leader_rate   | decimal(5,2) | NO   |     | NULL    |                |
| member_rate   | decimal(5,2) | NO   |     | NULL    |                |
| status        | tinyint(1)   | NO   |     | 1       |                |
| created_at    | datetime     | NO   |     | NULL    |                |
| updated_at    | datetime     | NO   |     | NULL    |                |
+---------------+--------------+------+-----+---------+----------------+
```

#### 期望表结构（修复后）
```sql
+---------------+---------------+------+-----+---------+----------------+
| Field         | Type          | Null | Key | Default | Extra          |
+---------------+---------------+------+-----+---------+----------------+
| id            | int           | NO   | PRI | NULL    | auto_increment |
| name          | varchar(50)   | NO   |     | NULL    |                |
| type          | varchar(20)   | NO   | UNI | NULL    |                |
| platform_rate | decimal(5,2)  | NO   |     | NULL    |                |
| leader_rate   | decimal(5,2)  | NO   |     | NULL    |                |
| user_rate     | int           | NO   |     | 0       |                |
| min_amount    | decimal(10,2) | YES  |     | 0.00    |                |
| member_rate   | decimal(5,2)  | NO   |     | 0.00    |                |
| status        | tinyint(1)    | NO   |     | 1       |                |
| created_at    | datetime      | NO   |     | NOW()   |                |
| updated_at    | datetime      | NO   |     | NOW()   |                |
+---------------+---------------+------+-----+---------+----------------+
```

## 修复步骤

### 1. 添加缺少的字段
```sql
-- 添加user_rate字段
ALTER TABLE profit_rule ADD COLUMN user_rate int(11) NOT NULL DEFAULT 0 COMMENT '用户分润比例（百分比）' AFTER leader_rate;

-- 添加min_amount字段
ALTER TABLE profit_rule ADD COLUMN min_amount decimal(10,2) DEFAULT 0.00 COMMENT '最小分润金额' AFTER user_rate;
```

### 2. 修改字段类型
```sql
-- 修改type字段类型
ALTER TABLE profit_rule MODIFY COLUMN type varchar(20) NOT NULL COMMENT '业务类型：wifi_share,goods_sale,advertisement';

-- 修改member_rate字段，添加默认值
ALTER TABLE profit_rule MODIFY COLUMN member_rate decimal(5,2) NOT NULL DEFAULT 0.00 COMMENT '成员分润比例（百分比）';

-- 修改created_at字段，添加默认值
ALTER TABLE profit_rule MODIFY COLUMN created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';

-- 修改updated_at字段，添加默认值和自动更新
ALTER TABLE profit_rule MODIFY COLUMN updated_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
```

### 3. 添加唯一键约束
```sql
-- 为type字段添加唯一键约束
ALTER TABLE profit_rule ADD UNIQUE KEY type (type);
```

### 4. 插入默认数据
```sql
-- 插入默认分润规则数据
INSERT INTO profit_rule (type, name, platform_rate, leader_rate, user_rate, min_amount, status) VALUES 
('wifi_share', 'WiFi分享', 40, 40, 20, 1.00, 1),
('goods_sale', '商品销售', 50, 30, 20, 5.00, 1),
('advertisement', '广告点击', 60, 30, 10, 0.10, 1)
ON DUPLICATE KEY UPDATE 
  name = VALUES(name),
  platform_rate = VALUES(platform_rate),
  leader_rate = VALUES(leader_rate),
  user_rate = VALUES(user_rate),
  min_amount = VALUES(min_amount),
  updated_at = CURRENT_TIMESTAMP;
```

## 完整修复脚本

```sql
-- profit_rule表结构修复脚本
USE mall;

-- 1. 添加缺少的字段
ALTER TABLE profit_rule ADD COLUMN user_rate int(11) NOT NULL DEFAULT 0 COMMENT '用户分润比例（百分比）' AFTER leader_rate;
ALTER TABLE profit_rule ADD COLUMN min_amount decimal(10,2) DEFAULT 0.00 COMMENT '最小分润金额' AFTER user_rate;

-- 2. 修改字段类型和默认值
ALTER TABLE profit_rule MODIFY COLUMN type varchar(20) NOT NULL COMMENT '业务类型：wifi_share,goods_sale,advertisement';
ALTER TABLE profit_rule MODIFY COLUMN member_rate decimal(5,2) NOT NULL DEFAULT 0.00 COMMENT '成员分润比例（百分比）';
ALTER TABLE profit_rule MODIFY COLUMN created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE profit_rule MODIFY COLUMN updated_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';

-- 3. 添加唯一键约束
ALTER TABLE profit_rule ADD UNIQUE KEY type (type);

-- 4. 插入默认数据
INSERT INTO profit_rule (type, name, platform_rate, leader_rate, user_rate, min_amount, status) VALUES 
('wifi_share', 'WiFi分享', 40, 40, 20, 1.00, 1),
('goods_sale', '商品销售', 50, 30, 20, 5.00, 1),
('advertisement', '广告点击', 60, 30, 10, 0.10, 1)
ON DUPLICATE KEY UPDATE 
  name = VALUES(name),
  platform_rate = VALUES(platform_rate),
  leader_rate = VALUES(leader_rate),
  user_rate = VALUES(user_rate),
  min_amount = VALUES(min_amount),
  updated_at = CURRENT_TIMESTAMP;

-- 5. 验证修复结果
SELECT * FROM profit_rule;
DESCRIBE profit_rule;
```

## 修复验证

### 修复后的数据
```
+----+----------+---------------+---------------+-------------+-----------+------------+-------------+--------+---------------------+---------------------+
| id | name     | type          | platform_rate | leader_rate | user_rate | min_amount | member_rate | status | created_at          | updated_at          |
+----+----------+---------------+---------------+-------------+-----------+------------+-------------+--------+---------------------+---------------------+
|  1 | WiFi分享 | wifi_share    |         40.00 |       40.00 |        20 |       1.00 |        0.00 |      1 | 2025-07-29 19:57:02 | 2025-07-29 19:57:02 |
|  2 | 商品销售 | goods_sale    |         50.00 |       30.00 |        20 |       5.00 |        0.00 |      1 | 2025-07-29 19:57:02 | 2025-07-29 19:57:02 |
|  3 | 广告点击 | advertisement |         60.00 |       30.00 |        10 |       0.10 |        0.00 |      1 | 2025-07-29 19:57:02 | 2025-07-29 19:57:02 |
+----+----------+---------------+---------------+-------------+-----------+------------+-------------+--------+---------------------+---------------------+
```

## 预防措施

### 1. 数据库版本管理
- 建议使用数据库迁移脚本管理表结构变更
- 为每次表结构修改创建对应的迁移文件
- 在部署前先在测试环境验证迁移脚本

### 2. 表结构一致性检查
```sql
-- 检查表结构的脚本
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'mall' AND TABLE_NAME = 'profit_rule'
ORDER BY ORDINAL_POSITION;
```

### 3. 建表语句更新
确保建表语句文件与实际数据库结构保持同步，避免类似问题再次发生。

## 总结

**问题根源**：数据库表结构与建表语句不匹配，缺少必要字段
**解决方案**：通过ALTER TABLE语句修复表结构，添加缺少的字段和约束
**修复效果**：表结构与建表语句完全匹配，数据插入成功

修复完成时间：2025-01-29
