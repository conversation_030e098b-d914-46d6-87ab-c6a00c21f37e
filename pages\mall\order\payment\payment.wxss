/* pages/mall/order/payment/payment.wxss */
.payment-container {
  padding-bottom: 120rpx;
  background-color: #f5f5f5;
}

/* 支付金额 */
.amount-section {
  background-color: #fff;
  padding: 60rpx 0;
  margin-bottom: 20rpx;
  text-align: center;
}

.amount-title {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 20rpx;
}

.amount-value {
  font-size: 60rpx;
  color: #333;
  font-weight: bold;
}

/* 订单信息 */
.order-section {
  background-color: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 20rpx;
}

.order-info {
  
}

.info-item {
  display: flex;
  margin-bottom: 15rpx;
  font-size: 28rpx;
  color: #666;
}

.info-label {
  width: 160rpx;
}

.info-value {
  flex: 1;
}

/* 支付方式 */
.payment-method-section {
  background-color: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.payment-methods {
  
}

.payment-method {
  display: flex;
  align-items: center;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #eee;
  position: relative;
}

.payment-method:last-child {
  border-bottom: none;
}

.payment-method.active {
  color: #07c160;
}

.method-icon {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
  font-size: 36rpx;
}

.method-info {
  flex: 1;
}

.method-name {
  font-size: 30rpx;
  color: #333;
}

.method-desc {
  font-size: 24rpx;
  color: #999;
  margin-top: 6rpx;
}

.method-check {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  background-color: #07c160;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
}

.check-icon {
  font-size: 24rpx;
}

/* 广告容器 */
.ad-container {
  margin: 20rpx;
  border-radius: 12rpx;
  overflow: hidden;
}

/* 底部操作栏 */
.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100rpx;
  display: flex;
  background-color: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.cancel-btn {
  flex: 1;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 30rpx;
  color: #666;
  background-color: #f5f5f5;
}

.confirm-btn {
  flex: 2;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 30rpx;
  color: #fff;
  background-color: #07c160;
}

.confirm-btn.disabled {
  background-color: #ccc;
}

/* 加载状态 */
.loading-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999;
}

.loading-text {
  color: #999;
  font-size: 28rpx;
} 