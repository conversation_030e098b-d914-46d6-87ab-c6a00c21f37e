const express = require('express');
const router = express.Router();
const { verifyToken, checkRole } = require('../middlewares/auth');
const logger = require('../utils/logger');

/**
 * 获取操作日志
 * GET /api/v1/admin/log/operation
 */
router.get('/operation', verifyToken, checkRole('admin'), async (req, res, next) => {
  try {
    const { page = 1, limit = 20, username, module, start_time, end_time } = req.query;
    
    // 模拟操作日志数据
    const logs = [
      {
        id: 1,
        user_id: 1,
        username: 'admin',
        module: '系统管理',
        action: '更新系统配置',
        method: 'PUT',
        url: '/api/v1/admin/system/config/update',
        ip: '127.0.0.1',
        user_agent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)',
        params: '{"site_name":"华红WIFI共享商业系统"}',
        result: 'success',
        created_at: '2025-01-07 15:30:00'
      },
      {
        id: 2,
        user_id: 2,
        username: 'operation',
        module: 'WiFi管理',
        action: '创建WiFi热点',
        method: 'POST',
        url: '/api/v1/admin/wifi/create',
        ip: '*************',
        user_agent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)',
        params: '{"name":"星巴克-朝阳店","password":"starbucks2024"}',
        result: 'success',
        created_at: '2025-01-07 14:20:00'
      },
      {
        id: 3,
        user_id: 3,
        username: 'finance',
        module: '提现管理',
        action: '审核提现申请',
        method: 'PUT',
        url: '/api/v1/admin/withdraw/audit/1',
        ip: '*************',
        user_agent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)',
        params: '{"status":1,"remark":"审核通过"}',
        result: 'success',
        created_at: '2025-01-07 11:00:00'
      }
    ];

    // 生成更多模拟日志
    for (let i = 4; i <= 20; i++) {
      logs.push({
        id: i,
        user_id: Math.floor(Math.random() * 4) + 1,
        username: ['admin', 'operation', 'finance', 'service01'][Math.floor(Math.random() * 4)],
        module: ['系统管理', 'WiFi管理', '用户管理', '订单管理'][Math.floor(Math.random() * 4)],
        action: ['查看列表', '创建记录', '更新记录', '删除记录'][Math.floor(Math.random() * 4)],
        method: ['GET', 'POST', 'PUT', 'DELETE'][Math.floor(Math.random() * 4)],
        url: '/api/v1/admin/xxx',
        ip: `192.168.1.${100 + Math.floor(Math.random() * 10)}`,
        user_agent: 'Mozilla/5.0',
        params: '{}',
        result: Math.random() > 0.1 ? 'success' : 'error',
        created_at: new Date(Date.now() - i * 3600000).toISOString().replace('T', ' ').slice(0, 19)
      });
    }

    // 按时间倒序
    logs.sort((a, b) => new Date(b.created_at) - new Date(a.created_at));

    // 分页
    const total = logs.length;
    const start = (parseInt(page) - 1) * parseInt(limit);
    const end = start + parseInt(limit);
    const list = logs.slice(start, end);

    res.json({
      status: 'success',
      data: {
        list,
        total,
        page: parseInt(page),
        limit: parseInt(limit)
      }
    });
  } catch (error) {
    next(error);
  }
});

/**
 * 获取登录日志
 * GET /api/v1/admin/log/login
 */
router.get('/login', verifyToken, checkRole('admin'), async (req, res, next) => {
  try {
    const { page = 1, limit = 20, username, ip, start_time, end_time } = req.query;
    
    // 生成模拟登录日志
    const logs = [];
    for (let i = 1; i <= 30; i++) {
      logs.push({
        id: i,
        user_id: Math.floor(Math.random() * 4) + 1,
        username: ['admin', 'operation', 'finance', 'service01'][Math.floor(Math.random() * 4)],
        ip: `192.168.1.${100 + Math.floor(Math.random() * 20)}`,
        location: ['北京市', '上海市', '广州市', '深圳市'][Math.floor(Math.random() * 4)],
        browser: ['Chrome', 'Firefox', 'Safari', 'Edge'][Math.floor(Math.random() * 4)],
        os: ['Windows 10', 'macOS', 'Ubuntu', 'Windows 11'][Math.floor(Math.random() * 4)],
        status: Math.random() > 0.2 ? 1 : 0,
        message: Math.random() > 0.2 ? '登录成功' : '密码错误',
        created_at: new Date(Date.now() - i * 7200000).toISOString().replace('T', ' ').slice(0, 19)
      });
    }

    // 分页
    const total = logs.length;
    const start = (parseInt(page) - 1) * parseInt(limit);
    const end = start + parseInt(limit);
    const list = logs.slice(start, end);

    res.json({
      status: 'success',
      data: {
        list,
        total,
        page: parseInt(page),
        limit: parseInt(limit)
      }
    });
  } catch (error) {
    next(error);
  }
});

/**
 * 获取错误日志
 * GET /api/v1/admin/log/error
 */
router.get('/error', verifyToken, checkRole('admin'), async (req, res, next) => {
  try {
    const { page = 1, limit = 20, level, start_time, end_time } = req.query;
    
    // 生成模拟错误日志
    const logs = [];
    for (let i = 1; i <= 15; i++) {
      const hasStack = Math.random() > 0.3; // 70%的概率有堆栈信息
      const errorMessages = [
        'Connection timeout',
        'Invalid parameter',
        'File not found',
        'Permission denied',
        'Database connection failed',
        'Redis connection refused',
        'API rate limit exceeded',
        'Memory allocation failed'
      ];
      const selectedMessage = errorMessages[Math.floor(Math.random() * errorMessages.length)];
      
      // 生成模拟堆栈信息
      const stackTrace = hasStack ? `Error: ${selectedMessage}
    at Object.<anonymous> (/app/src/api/wifi.js:45:15)
    at Module._compile (internal/modules/cjs/loader.js:1063:30)
    at Object.Module._extensions..js (internal/modules/cjs/loader.js:1092:10)
    at Module.load (internal/modules/cjs/loader.js:928:32)
    at Function.Module._load (internal/modules/cjs/loader.js:769:14)
    at Module.require (internal/modules/cjs/loader.js:952:19)
    at require (internal/modules/cjs/helpers.js:88:18)
    at Object.<anonymous> (/app/src/router/index.js:12:18)
    at Module._compile (internal/modules/cjs/loader.js:1063:30)
    at Object.Module._extensions..js (internal/modules/cjs/loader.js:1092:10)` : null;
      
      logs.push({
        id: i,
        level: ['error', 'warning'][Math.floor(Math.random() * 2)],
        module: ['API', 'Database', 'Redis', 'File'][Math.floor(Math.random() * 4)],
        message: selectedMessage,
        stack: stackTrace,
        user_id: Math.floor(Math.random() * 4) + 1,
        username: ['admin', 'operation', 'finance', 'service01'][Math.floor(Math.random() * 4)],
        ip: `192.168.1.${100 + Math.floor(Math.random() * 20)}`,
        url: '/api/v1/admin/xxx',
        created_at: new Date(Date.now() - i * 10800000).toISOString().replace('T', ' ').slice(0, 19)
      });
    }

    // 分页
    const total = logs.length;
    const start = (parseInt(page) - 1) * parseInt(limit);
    const end = start + parseInt(limit);
    const list = logs.slice(start, end);

    res.json({
      status: 'success',
      data: {
        list,
        total,
        page: parseInt(page),
        limit: parseInt(limit)
      }
    });
  } catch (error) {
    next(error);
  }
});

module.exports = router; 