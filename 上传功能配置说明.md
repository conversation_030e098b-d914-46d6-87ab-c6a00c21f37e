# WiFi共享商业系统 - 上传功能配置说明

## 概述

本系统的上传功能用于管理员上传商品图片、广告图片等多种图片资源。上传功能由服务端处理并存储文件，提供文件URL给前端使用。

## 功能说明

1. **单文件上传**：用于上传单个文件
2. **多文件上传**：用于批量上传多个文件（最多10个）
3. **文件删除**：根据文件名删除已上传的文件

## API接口

### 单文件上传

- **接口路径**：`/api/v1/admin/upload`
- **请求方式**：POST
- **请求头**：
  - `Content-Type`: `multipart/form-data`
  - `Authorization`: `Bearer <管理员token>`
- **请求参数**：
  - `file`: 文件对象
- **响应格式**：
  ```json
  {
    "code": 200,
    "data": {
      "url": "http://example.com/uploads/images/1633245678_abcd1234.jpg",
      "filename": "1633245678_abcd1234.jpg",
      "originalname": "product.jpg",
      "size": 12345,
      "mimetype": "image/jpeg"
    },
    "message": "文件上传成功"
  }
  ```

### 多文件上传

- **接口路径**：`/api/v1/admin/upload/multiple`
- **请求方式**：POST
- **请求头**：
  - `Content-Type`: `multipart/form-data`
  - `Authorization`: `Bearer <管理员token>`
- **请求参数**：
  - `files`: 多个文件对象
- **响应格式**：
  ```json
  {
    "code": 200,
    "data": [
      {
        "url": "http://example.com/uploads/images/1633245678_abcd1234.jpg",
        "filename": "1633245678_abcd1234.jpg",
        "originalname": "product1.jpg",
        "size": 12345,
        "mimetype": "image/jpeg"
      },
      {
        "url": "http://example.com/uploads/images/1633245679_efgh5678.jpg",
        "filename": "1633245679_efgh5678.jpg",
        "originalname": "product2.jpg",
        "size": 23456,
        "mimetype": "image/jpeg"
      }
    ],
    "message": "文件上传成功"
  }
  ```

### 删除文件

- **接口路径**：`/api/v1/admin/upload/:filename`
- **请求方式**：DELETE
- **请求头**：
  - `Authorization`: `Bearer <管理员token>`
- **路径参数**：
  - `filename`: 文件名
- **响应格式**：
  ```json
  {
    "code": 200,
    "message": "文件删除成功"
  }
  ```

## 技术实现

1. **后端技术**：
   - 使用`multer`中间件处理文件上传
   - 使用`uuid`生成唯一文件名
   - 存储在服务器本地文件系统，保存路径：`uploads/images/`

2. **前端技术**：
   - 使用`FormData`封装文件数据
   - 使用`axios`发送请求

## 配置选项

在`config/env/development.js`中配置上传参数：

```javascript
// 上传文件配置
upload: {
  baseDir: 'uploads',
  maxSize: 5 * 1024 * 1024, // 5MB
  allowTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp']
}
```

## 临时解决方案

对于前端开发阶段，可以在`wifi-share-admin/src/api/upload.js`中将`useMock`设置为`true`，启用模拟上传功能。这样前端可以在后端服务不可用或正在开发时正常工作。

模拟功能将使用FileReader API创建本地数据URL，并返回模拟的上传成功响应。

## 安全注意事项

1. 只有管理员可以访问上传API
2. 文件大小限制在5MB以内
3. 只允许上传指定类型的图片文件（JPEG, PNG, GIF, WEBP）
4. 生成随机文件名以防止文件名冲突和安全问题
5. 验证文件名以防止目录遍历攻击 