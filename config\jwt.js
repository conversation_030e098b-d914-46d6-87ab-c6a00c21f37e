const jwt = require('jsonwebtoken');
const config = require('./index');

// 确保JWT配置存在，如果不存在则使用默认值
const jwtConfig = config.jwt || {
  secret: 'wifi_share_secret_key',
  expiresIn: '7d'
};

/**
 * 生成JWT令牌
 * @param {Object} payload - 令牌载荷
 * @returns {string} - JWT令牌
 */
function generateToken(payload) {
  try {
    console.log('生成JWT令牌，payload:', payload);
    console.log('JWT密钥:', jwtConfig.secret);
    console.log('JWT过期时间:', jwtConfig.expiresIn);
    
    return jwt.sign(payload, jwtConfig.secret, {
      expiresIn: jwtConfig.expiresIn
    });
  } catch (err) {
    console.error('生成JWT令牌失败:', err);
    throw err;
  }
}

/**
 * 验证JWT令牌
 * @param {string} token - JWT令牌
 * @returns {Object} 解码后的载荷数据
 */
function verifyToken(token) {
  try {
    return jwt.verify(token, jwtConfig.secret);
  } catch (err) {
    console.error('验证JWT令牌失败:', err);
    throw err;
  }
}

module.exports = {
  generateToken,
  verifyToken
}; 