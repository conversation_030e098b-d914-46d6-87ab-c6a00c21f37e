// 简单的团队表更新脚本
console.log('开始更新团队表结构...');

const mysql = require('mysql2');

// 创建连接
const connection = mysql.createConnection({
  host: 'localhost',
  user: 'root',
  password: 'wo587129955',
  database: 'mall'
});

// 连接数据库
connection.connect((err) => {
  if (err) {
    console.error('数据库连接失败:', err);
    return;
  }
  console.log('✅ 数据库连接成功');
  
  // 执行更新步骤
  updateTeamTable();
});

function updateTeamTable() {
  console.log('\n📋 检查团队表结构...');
  
  // 1. 查看表结构
  connection.query('DESCRIBE team', (err, results) => {
    if (err) {
      console.error('查询表结构失败:', err);
      connection.end();
      return;
    }
    
    console.log('当前团队表字段:');
    results.forEach(field => {
      console.log(`- ${field.Field} (${field.Type})`);
    });
    
    // 检查字段是否存在
    const hasInviteCode = results.some(field => field.Field === 'invite_code');
    const hasLevel = results.some(field => field.Field === 'level');
    
    console.log(`\ninvite_code 字段: ${hasInviteCode ? '✅ 存在' : '❌ 不存在'}`);
    console.log(`level 字段: ${hasLevel ? '✅ 存在' : '❌ 不存在'}`);
    
    // 2. 添加缺失字段
    addMissingFields(hasInviteCode, hasLevel);
  });
}

function addMissingFields(hasInviteCode, hasLevel) {
  let fieldsToAdd = [];
  
  if (!hasInviteCode) {
    fieldsToAdd.push({
      name: 'invite_code',
      sql: "ALTER TABLE team ADD COLUMN invite_code VARCHAR(20) UNIQUE COMMENT '团队邀请码'"
    });
  }
  
  if (!hasLevel) {
    fieldsToAdd.push({
      name: 'level',
      sql: "ALTER TABLE team ADD COLUMN level INT DEFAULT 1 COMMENT '团队等级'"
    });
  }
  
  if (fieldsToAdd.length === 0) {
    console.log('\n✅ 所有必要字段都已存在');
    generateInviteCodes();
    return;
  }
  
  console.log(`\n🔧 需要添加 ${fieldsToAdd.length} 个字段...`);
  
  // 逐个添加字段
  let index = 0;
  function addNextField() {
    if (index >= fieldsToAdd.length) {
      console.log('✅ 所有字段添加完成');
      generateInviteCodes();
      return;
    }
    
    const field = fieldsToAdd[index];
    console.log(`添加 ${field.name} 字段...`);
    
    connection.query(field.sql, (err) => {
      if (err && !err.message.includes('Duplicate column')) {
        console.error(`添加 ${field.name} 字段失败:`, err.message);
      } else {
        console.log(`✅ ${field.name} 字段添加成功`);
      }
      
      index++;
      addNextField();
    });
  }
  
  addNextField();
}

function generateInviteCodes() {
  console.log('\n🎯 生成邀请码...');
  
  // 查看现有团队
  connection.query('SELECT id, name, invite_code, level FROM team', (err, teams) => {
    if (err) {
      console.error('查询团队失败:', err);
      connection.end();
      return;
    }
    
    console.log(`\n📊 找到 ${teams.length} 个团队:`);
    teams.forEach(team => {
      console.log(`- ID: ${team.id}, 名称: ${team.name || '未命名'}, 邀请码: ${team.invite_code || '无'}`);
    });
    
    // 为没有邀请码的团队生成邀请码
    const teamsWithoutCode = teams.filter(team => !team.invite_code);
    
    if (teamsWithoutCode.length === 0) {
      console.log('\n✅ 所有团队都已有邀请码');
      verifyResults();
      return;
    }
    
    console.log(`\n🔄 为 ${teamsWithoutCode.length} 个团队生成邀请码...`);
    
    const updateSql = "UPDATE team SET invite_code = CONCAT('TEAM', LPAD(id, 6, '0')), level = IFNULL(level, 1) WHERE invite_code IS NULL OR invite_code = ''";
    
    connection.query(updateSql, (err, result) => {
      if (err) {
        console.error('生成邀请码失败:', err);
      } else {
        console.log(`✅ 成功更新 ${result.affectedRows} 个团队的邀请码`);
      }
      
      verifyResults();
    });
  });
}

function verifyResults() {
  console.log('\n🔍 验证更新结果...');
  
  connection.query('SELECT id, name, invite_code, level, created_at FROM team ORDER BY id', (err, teams) => {
    if (err) {
      console.error('验证失败:', err);
    } else {
      console.log('\n📊 最终团队数据:');
      teams.forEach(team => {
        console.log(`ID: ${team.id} | 名称: ${team.name || '未命名'} | 邀请码: ${team.invite_code} | 等级: ${team.level} | 创建时间: ${team.created_at}`);
      });
      
      // 检查邀请码唯一性
      connection.query(`
        SELECT invite_code, COUNT(*) as count 
        FROM team 
        WHERE invite_code IS NOT NULL 
        GROUP BY invite_code 
        HAVING COUNT(*) > 1
      `, (err, duplicates) => {
        if (err) {
          console.error('检查重复邀请码失败:', err);
        } else if (duplicates.length > 0) {
          console.log('\n⚠️ 发现重复的邀请码:');
          duplicates.forEach(dup => {
            console.log(`- ${dup.invite_code}: ${dup.count} 次`);
          });
        } else {
          console.log('\n✅ 所有邀请码都是唯一的');
        }
        
        console.log('\n🎉 团队表更新完成！');
        connection.end();
      });
    }
  });
}

// 处理错误
connection.on('error', (err) => {
  console.error('数据库连接错误:', err);
});
