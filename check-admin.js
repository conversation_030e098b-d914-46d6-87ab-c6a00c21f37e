const db = require('./src/database');

async function checkAdmin() {
  try {
    console.log('🔍 检查管理员账户...\n');
    
    // 检查admin_user表是否存在
    console.log('1️⃣ 检查admin_user表是否存在');
    try {
      const adminUsers = await db.query('SELECT * FROM admin_user');
      console.log('✅ admin_user表存在');
      console.log(`📊 管理员账户数量: ${adminUsers.length}`);
      if (adminUsers.length > 0) {
        console.table(adminUsers);
      }
    } catch (error) {
      console.log('❌ admin_user表不存在:', error.message);
    }
    
    // 检查user表中的管理员
    console.log('\n2️⃣ 检查user表中的管理员账户');
    try {
      const userAdmins = await db.query("SELECT * FROM user WHERE role = 'admin' OR is_leader = 1");
      console.log('✅ user表查询成功');
      console.log(`📊 管理员/团长用户数量: ${userAdmins.length}`);
      if (userAdmins.length > 0) {
        console.table(userAdmins);
      }
    } catch (error) {
      console.log('❌ user表查询失败:', error.message);
    }
    
    // 检查数据库中所有表
    console.log('\n3️⃣ 检查数据库中的所有表');
    try {
      const tables = await db.query('SHOW TABLES');
      console.log('✅ 数据库表列表:');
      console.table(tables);
    } catch (error) {
      console.log('❌ 查询表列表失败:', error.message);
    }
    
    // 尝试创建admin_user表和管理员账户
    console.log('\n4️⃣ 尝试创建admin_user表和管理员账户');
    try {
      // 创建admin_user表
      await db.query(`
        CREATE TABLE IF NOT EXISTS admin_user (
          id int(11) NOT NULL AUTO_INCREMENT COMMENT '管理员ID',
          username varchar(50) NOT NULL COMMENT '用户名',
          password varchar(64) NOT NULL COMMENT '密码',
          real_name varchar(50) DEFAULT NULL COMMENT '真实姓名',
          avatar varchar(255) DEFAULT NULL COMMENT '头像',
          role_id int(11) DEFAULT NULL COMMENT '角色ID',
          email varchar(100) DEFAULT NULL COMMENT '电子邮箱',
          phone varchar(20) DEFAULT NULL COMMENT '手机号',
          last_login_time datetime DEFAULT NULL COMMENT '最后登录时间',
          last_login_ip varchar(50) DEFAULT NULL COMMENT '最后登录IP',
          status tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0禁用，1启用',
          created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
          updated_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
          PRIMARY KEY (id),
          UNIQUE KEY username (username)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='管理员用户表'
      `);
      console.log('✅ admin_user表创建成功');
      
      // 插入管理员账户
      await db.query(`
        INSERT INTO admin_user (username, password, real_name, status) 
        SELECT 'mrx0927', '5eb688175b8e2581bab3e2c657880533', '超级管理员', 1 
        FROM dual 
        WHERE NOT EXISTS (SELECT 1 FROM admin_user WHERE username = 'mrx0927')
      `);
      console.log('✅ 管理员账户创建成功');
      
      // 再次查询管理员账户
      const newAdminUsers = await db.query('SELECT * FROM admin_user');
      console.log('📊 创建后的管理员账户:');
      console.table(newAdminUsers);
      
    } catch (error) {
      console.log('❌ 创建失败:', error.message);
    }
    
    process.exit(0);
  } catch (error) {
    console.error('❌ 检查失败:', error);
    process.exit(1);
  }
}

checkAdmin();
