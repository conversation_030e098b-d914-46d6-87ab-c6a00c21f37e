const express = require('express');
const router = express.Router();
const { verifyToken, adminAuth } = require('../middlewares/auth');
const logger = require('../utils/logger');
const { success, error } = require('../utils/response');
const db = require('../database');

// 模拟数据 - 分润规则
const profitRules = {
  wifi_share: {
    name: 'WiFi分享',
    platform_rate: 40,  // 平台分润比例
    leader_rate: 40,    // 团长分润比例
    user_rate: 20,      // 用户分润比例
    status: 1  // 启用状态
  },
  goods_sale: {
    name: '商品销售',
    platform_rate: 50,  // 平台分润比例
    leader_rate: 30,    // 团长分润比例
    user_rate: 20,      // 用户分润比例
    status: 1  // 启用状态
  },
  advertisement: {
    name: '广告点击',
    platform_rate: 60,  // 平台分润比例
    leader_rate: 30,    // 团长分润比例
    user_rate: 10,      // 用户分润比例
    status: 1  // 启用状态
  },
  min_withdraw_amount: 50,    // 最小提现金额
  withdraw_fee_rate: 1,       // 提现手续费比例
  updated_at: '2025-07-09 16:30:00'
};

// 模拟数据 - 分润账单列表
const billList = [
  {
    id: 101,
    bill_no: 'SB20250708001',
    type: 'wifi_share',  // wifi_share, goods_sale, advertisement
    amount: 0.60,
    share_amount: 0.42,
    platform_amount: 0.18,
    user_id: 1001,
    user_name: '张三',
    user_phone: '138****1001',
    source_id: 1,
    source_type: 'wifi',
    status: 1,  // 1待结算，2已结算，3已取消
    remark: 'WiFi码分享使用收益',
    settle_time: null,
    created_at: '2025-07-08 10:15:22'
  },
  {
    id: 102,
    bill_no: 'SB20250708002',
    type: 'wifi_share',
    amount: 0.40,
    share_amount: 0.28,
    platform_amount: 0.12,
    user_id: 1002,
    user_name: '李四',
    user_phone: '138****1002',
    source_id: 2,
    source_type: 'wifi',
    status: 2,
    remark: 'WiFi码分享使用收益',
    settle_time: '2025-07-08 16:32:45',
    created_at: '2025-07-08 09:27:18'
  },
  {
    id: 103,
    bill_no: 'SB20250708003',
    type: 'goods_sale',
    amount: 25.00,
    share_amount: 2.50,
    platform_amount: 22.50,
    user_id: 1003,
    user_name: '王五',
    user_phone: '138****1003',
    source_id: 1,
    source_type: 'order',
    status: 1,
    remark: '商品销售分润',
    settle_time: null,
    created_at: '2025-07-08 14:52:36'
  },
  {
    id: 104,
    bill_no: 'SB20250708004',
    type: 'advertisement',
    amount: 5.60,
    share_amount: 1.12,
    platform_amount: 4.48,
    user_id: 1001,
    user_name: '张三',
    user_phone: '138****1001',
    source_id: 3,
    source_type: 'ad_click',
    status: 2,
    remark: '广告点击收益',
    settle_time: '2025-07-08 18:21:05',
    created_at: '2025-07-08 16:05:43'
  }
];

// 获取分润规则
router.get('/rules', verifyToken, adminAuth, (req, res) => {
  try {
    logger.info('获取分润规则');
    return success(res, profitRules, '获取分润规则成功');
  } catch (err) {
    logger.error('获取分润规则失败:', err);
    return error(res, '获取分润规则失败');
  }
});

// 更新分润规则
router.post('/rules/update', verifyToken, adminAuth, (req, res) => {
  try {
    const { wifi_share, goods_sale, advertisement } = req.body;
    
    // 更新规则
    if (wifi_share) {
      profitRules.wifi_share = { ...profitRules.wifi_share, ...wifi_share };
    }
    
    if (goods_sale) {
      profitRules.goods_sale = { ...profitRules.goods_sale, ...goods_sale };
    }
    
    if (advertisement) {
      profitRules.advertisement = { ...profitRules.advertisement, ...advertisement };
    }
    
    // 更新时间
    profitRules.updated_at = new Date().toISOString().replace('T', ' ').slice(0, 19);
    
    logger.info('更新分润规则成功');
    return success(res, profitRules, '更新分润规则成功');
  } catch (err) {
    logger.error('更新分润规则失败:', err);
    return error(res, '更新分润规则失败');
  }
});

// 获取分润账单列表
router.get('/bill/list', verifyToken, adminAuth, (req, res) => {
  try {
    const { page = 1, limit = 10, keyword, type, status, start_date, end_date } = req.query;
    
    // 过滤账单
    let filteredBills = [...billList];
    
    // 按关键词搜索
    if (keyword) {
      filteredBills = filteredBills.filter(bill => 
        bill.user_name.includes(keyword) ||
        bill.user_phone.includes(keyword) ||
        bill.bill_no.includes(keyword)
      );
    }
    
    // 按类型筛选
    if (type) {
      filteredBills = filteredBills.filter(bill => bill.type === type);
    }
    
    // 按状态筛选
    if (status !== undefined && status !== '') {
      filteredBills = filteredBills.filter(bill => bill.status === parseInt(status));
    }
    
    // 按日期筛选
    if (start_date && end_date) {
      filteredBills = filteredBills.filter(bill => {
        const billDate = new Date(bill.created_at);
        const startDate = new Date(start_date);
        const endDate = new Date(end_date);
        endDate.setHours(23, 59, 59, 999);
        return billDate >= startDate && billDate <= endDate;
      });
    }
    
    // 计算分页
    const total = filteredBills.length;
    const pageNum = parseInt(page);
    const pageSize = parseInt(limit);
    const start = (pageNum - 1) * pageSize;
    const end = start + pageSize;
    const list = filteredBills.slice(start, end);
    
    // 计算统计数据
    const totalAmount = filteredBills.reduce((sum, bill) => sum + bill.amount, 0);
    const totalShareAmount = filteredBills.reduce((sum, bill) => sum + bill.share_amount, 0);
    const totalPlatformAmount = filteredBills.reduce((sum, bill) => sum + bill.platform_amount, 0);
    
    logger.info('获取分润账单列表成功');
    return success(res, {
      list,
      total,
      page: pageNum,
      limit: pageSize,
      stats: {
        total_amount: totalAmount.toFixed(2),
        total_share_amount: totalShareAmount.toFixed(2),
        total_platform_amount: totalPlatformAmount.toFixed(2)
      }
    }, '获取分润账单列表成功');
  } catch (err) {
    logger.error('获取分润账单列表失败:', err);
    return error(res, '获取分润账单列表失败');
  }
});

// 获取分润账单详情
router.get('/bill/detail/:id', verifyToken, adminAuth, (req, res) => {
  try {
    const { id } = req.params;
    const bill = billList.find(b => b.id === parseInt(id));
    
    if (!bill) {
      return error(res, '账单不存在', 404);
    }
    
    // 构造返回数据
    const detailData = {
      detail: {
        id: bill.id,
        amount: bill.amount,
        source_type: bill.type === 'wifi_share' ? 1 : (bill.type === 'goods_sale' ? 2 : 3),
        source_id: bill.source_id,
        bill_no: bill.bill_no,
        status: bill.status,
        created_at: bill.created_at,
        settle_time: bill.settle_time,
        remark: bill.remark
      },
      user_info: {
        id: bill.user_id,
        nickname: bill.user_name,
        phone: bill.user_phone,
        avatar: '/img/default-avatar.png',
        is_leader: bill.user_id % 2 === 0 ? 0 : 1,
        balance: (Math.random() * 1000).toFixed(2)
      },
      source_info: bill.type === 'wifi_share' ? {
        title: 'WiFi示例',
        name: 'Test_WiFi_' + bill.id,
        merchant_name: '示例商户',
        use_count: Math.floor(Math.random() * 100)
      } : (bill.type === 'goods_sale' ? {
        order_no: bill.bill_no,
        total_amount: bill.amount,
        status: 1,
        created_at: bill.created_at
      } : {
        title: '广告示例',
        space_name: '首页广告位',
        click_count: Math.floor(Math.random() * 1000),
        view_count: Math.floor(Math.random() * 5000)
      }),
      profit_detail: [
        {
          role: '分享者',
          rate: bill.type === 'wifi_share' ? 70 : (bill.type === 'goods_sale' ? 10 : 20),
          amount: bill.share_amount,
          user_info: `${bill.user_name}(${bill.user_phone})`
        },
        {
          role: '平台',
          rate: bill.type === 'wifi_share' ? 30 : (bill.type === 'goods_sale' ? 85 : 70),
          amount: bill.platform_amount,
          user_info: '系统平台'
        }
      ]
    };
    
    logger.info('获取分润账单详情成功');
    return success(res, detailData, '获取分润账单详情成功');
  } catch (err) {
    logger.error('获取分润账单详情失败:', err);
    return error(res, '获取分润账单详情失败');
  }
});

// 结算分润账单
router.post('/bill/settle/:id', verifyToken, adminAuth, (req, res) => {
  try {
    const { id } = req.params;
    const billIndex = billList.findIndex(b => b.id === parseInt(id));
    
    if (billIndex === -1) {
      return error(res, '账单不存在', 404);
    }
    
    // 检查账单状态
    if (billList[billIndex].status !== 1) {
      return error(res, '只有待结算的账单可以进行结算操作', 400);
    }
    
    // 更新账单状态
    billList[billIndex].status = 2;
    billList[billIndex].settle_time = new Date().toISOString().replace('T', ' ').slice(0, 19);
    
    logger.info('结算分润账单成功');
    return success(res, {}, '结算成功');
  } catch (err) {
    logger.error('结算分润账单失败:', err);
    return error(res, '结算分润账单失败');
  }
});

// ==================== 客户端接口 ====================

/**
 * 获取用户收入统计（实时从分润记录计算）
 * GET /api/v1/client/income/stats
 */
router.get('/stats', verifyToken, async (req, res) => {
  try {
    const userId = req.user.id;
    console.log('📊 获取用户收入统计，用户ID:', userId);

    // 获取用户基础信息和余额
    const userQuery = await db.query(
      'SELECT balance FROM user WHERE id = ?',
      [userId]
    );

    const userBalance = userQuery.length > 0 ? userQuery[0].balance || 0 : 0;
    const frozenBalance = 0; // 暂时设为0，因为user表中没有frozen_balance字段

    // 今日收益统计
    const todayStats = await db.query(`
      SELECT
        COALESCE(SUM(amount), 0) as income,
        COALESCE(SUM(CASE WHEN profit_type = 'wifi_share' THEN amount ELSE 0 END), 0) as wifi_income,
        COALESCE(SUM(CASE WHEN profit_type = 'goods_sale' THEN amount ELSE 0 END), 0) as goods_income,
        COALESCE(SUM(CASE WHEN profit_type = 'advertisement' THEN amount ELSE 0 END), 0) as ad_income
      FROM profit_log
      WHERE user_id = ? AND DATE(created_at) = CURDATE()
    `, [userId]);

    // 昨日收益统计
    const yesterdayStats = await db.query(`
      SELECT
        COALESCE(SUM(amount), 0) as income,
        COALESCE(SUM(CASE WHEN profit_type = 'wifi_share' THEN amount ELSE 0 END), 0) as wifi_income,
        COALESCE(SUM(CASE WHEN profit_type = 'goods_sale' THEN amount ELSE 0 END), 0) as goods_income,
        COALESCE(SUM(CASE WHEN profit_type = 'advertisement' THEN amount ELSE 0 END), 0) as ad_income
      FROM profit_log
      WHERE user_id = ? AND DATE(created_at) = DATE_SUB(CURDATE(), INTERVAL 1 DAY)
    `, [userId]);

    // 本月收益统计
    const thisMonthStats = await db.query(`
      SELECT
        COALESCE(SUM(amount), 0) as income,
        COALESCE(SUM(CASE WHEN profit_type = 'wifi_share' THEN amount ELSE 0 END), 0) as wifi_income,
        COALESCE(SUM(CASE WHEN profit_type = 'goods_sale' THEN amount ELSE 0 END), 0) as goods_income,
        COALESCE(SUM(CASE WHEN profit_type = 'advertisement' THEN amount ELSE 0 END), 0) as ad_income
      FROM profit_log
      WHERE user_id = ? AND YEAR(created_at) = YEAR(NOW()) AND MONTH(created_at) = MONTH(NOW())
    `, [userId]);

    // 上月收益统计
    const lastMonthStats = await db.query(`
      SELECT
        COALESCE(SUM(amount), 0) as income,
        COALESCE(SUM(CASE WHEN profit_type = 'wifi_share' THEN amount ELSE 0 END), 0) as wifi_income,
        COALESCE(SUM(CASE WHEN profit_type = 'goods_sale' THEN amount ELSE 0 END), 0) as goods_income,
        COALESCE(SUM(CASE WHEN profit_type = 'advertisement' THEN amount ELSE 0 END), 0) as ad_income
      FROM profit_log
      WHERE user_id = ? AND YEAR(created_at) = YEAR(DATE_SUB(NOW(), INTERVAL 1 MONTH))
        AND MONTH(created_at) = MONTH(DATE_SUB(NOW(), INTERVAL 1 MONTH))
    `, [userId]);

    // 总收益统计
    const totalStats = await db.query(`
      SELECT
        COALESCE(SUM(amount), 0) as income,
        COALESCE(SUM(CASE WHEN profit_type = 'wifi_share' THEN amount ELSE 0 END), 0) as wifi_income,
        COALESCE(SUM(CASE WHEN profit_type = 'goods_sale' THEN amount ELSE 0 END), 0) as goods_income,
        COALESCE(SUM(CASE WHEN profit_type = 'advertisement' THEN amount ELSE 0 END), 0) as ad_income
      FROM profit_log
      WHERE user_id = ?
    `, [userId]);

    // 最近7天收益趋势
    const trendStats = await db.query(`
      SELECT
        DATE(created_at) as date,
        COALESCE(SUM(amount), 0) as income,
        COALESCE(SUM(CASE WHEN profit_type = 'wifi_share' THEN amount ELSE 0 END), 0) as wifi_income,
        COALESCE(SUM(CASE WHEN profit_type = 'goods_sale' THEN amount ELSE 0 END), 0) as goods_income,
        COALESCE(SUM(CASE WHEN profit_type = 'advertisement' THEN amount ELSE 0 END), 0) as ad_income
      FROM profit_log
      WHERE user_id = ? AND created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
      GROUP BY DATE(created_at)
      ORDER BY date DESC
    `, [userId]);

    // 构建7天趋势数组
    const buildTrendArray = (data, field) => {
      const trend = new Array(7).fill(0);
      const today = new Date();

      for (let i = 0; i < 7; i++) {
        const date = new Date(today);
        date.setDate(date.getDate() - i);
        const dateStr = date.toISOString().split('T')[0];

        const dayData = data.find(d => d.date === dateStr);
        trend[6 - i] = dayData ? parseFloat(dayData[field]) : 0;
      }

      return trend;
    };

    // 计算团队收益（推荐奖励等）
    const teamIncomeQuery = await db.query(`
      SELECT COALESCE(SUM(amount), 0) as team_income
      FROM profit_log
      WHERE user_id = ? AND source_type IN ('referral', 'team_bonus')
    `, [userId]);

    const teamIncome = teamIncomeQuery[0]?.team_income || 0;

    const stats = {
      // 前端钱包页面需要的直接字段
      balance: parseFloat(userBalance).toFixed(2),
      wifi_income: parseFloat(totalStats[0].wifi_income).toFixed(2),
      team_income: parseFloat(teamIncome).toFixed(2),
      ad_income: parseFloat(totalStats[0].ad_income).toFixed(2),
      goods_income: parseFloat(totalStats[0].goods_income).toFixed(2),
      total_income: parseFloat(totalStats[0].income).toFixed(2),
      today_income: parseFloat(todayStats[0].income).toFixed(2),
      month_income: parseFloat(thisMonthStats[0].income).toFixed(2),

      // 详细统计数据
      today: {
        income: parseFloat(todayStats[0].income).toFixed(2),
        wifi_income: parseFloat(todayStats[0].wifi_income).toFixed(2),
        goods_income: parseFloat(todayStats[0].goods_income).toFixed(2),
        ad_income: parseFloat(todayStats[0].ad_income).toFixed(2)
      },
      yesterday: {
        income: parseFloat(yesterdayStats[0].income).toFixed(2),
        wifi_income: parseFloat(yesterdayStats[0].wifi_income).toFixed(2),
        goods_income: parseFloat(yesterdayStats[0].goods_income).toFixed(2),
        ad_income: parseFloat(yesterdayStats[0].ad_income).toFixed(2)
      },
      this_month: {
        income: parseFloat(thisMonthStats[0].income).toFixed(2),
        wifi_income: parseFloat(thisMonthStats[0].wifi_income).toFixed(2),
        goods_income: parseFloat(thisMonthStats[0].goods_income).toFixed(2),
        ad_income: parseFloat(thisMonthStats[0].ad_income).toFixed(2)
      },
      last_month: {
        income: parseFloat(lastMonthStats[0].income).toFixed(2),
        wifi_income: parseFloat(lastMonthStats[0].wifi_income).toFixed(2),
        goods_income: parseFloat(lastMonthStats[0].goods_income).toFixed(2),
        ad_income: parseFloat(lastMonthStats[0].ad_income).toFixed(2)
      },
      total: {
        income: parseFloat(totalStats[0].income).toFixed(2),
        wifi_income: parseFloat(totalStats[0].wifi_income).toFixed(2),
        goods_income: parseFloat(totalStats[0].goods_income).toFixed(2),
        ad_income: parseFloat(totalStats[0].ad_income).toFixed(2),
        team_income: parseFloat(teamIncome).toFixed(2),
        balance: parseFloat(userBalance).toFixed(2),
        frozen_balance: parseFloat(frozenBalance).toFixed(2)
      },
      trend: {
        income_trend: buildTrendArray(trendStats, 'income'),
        wifi_trend: buildTrendArray(trendStats, 'wifi_income'),
        goods_trend: buildTrendArray(trendStats, 'goods_income'),
        ad_trend: buildTrendArray(trendStats, 'ad_income')
      }
    };

    console.log('✅ 用户收入统计获取成功:', stats);
    return success(res, stats, '获取收入统计成功');
  } catch (err) {
    console.error('❌ 获取用户收入统计失败:', err);
    console.error('❌ 错误详情:', err.message);
    console.error('❌ 错误堆栈:', err.stack);
    return error(res, '获取收入统计失败: ' + err.message);
  }
});

/**
 * 测试路由 - 确认代码被执行
 * GET /api/v1/client/income/test-details
 */
router.get('/test-details', verifyToken, async (req, res) => {
  try {
    console.log('🎯 测试路由被执行！这证明我的代码是有效的！');
    return success(res, {
      message: "测试路由成功！这证明代码被执行了！",
      timestamp: new Date().toISOString()
    }, '测试路由成功');
  } catch (err) {
    console.error('❌ 测试路由失败:', err);
    return error(res, '测试路由失败');
  }
});

/**
 * 获取用户收入明细
 * GET /api/v1/client/income/details
 */
router.get('/details', verifyToken, async (req, res) => {
  try {
    console.log('🚀🚀🚀 进入收入明细API处理函数！');
    console.log('🔍 用户信息:', req.user);
    const userId = req.user.id;
    const { page = 1, limit = 10, type, start_date, end_date } = req.query;

    console.log('📋 获取用户收入明细，用户ID:', userId, '参数:', { page, limit, type, start_date, end_date });

    // 暂时返回空数据，不进行数据库查询
    console.log('✅ 返回空数据（暂时跳过数据库查询）');
    return success(res, {
      list: [],
      total: 0,
      page: parseInt(page),
      limit: parseInt(limit),
      has_more: false,
      debug_info: {
        message: "数据库查询已跳过，返回空数据",
        timestamp: new Date().toISOString(),
        user_id: userId,
        query_params: { page, limit, type, start_date, end_date }
      }
    }, '获取收入明细成功（空数据）');

    console.log('🔍 开始构建查询条件...');

    // 构建查询条件
    let whereConditions = ['user_id = ?'];
    let params = [userId];

    // 类型筛选
    if (type) {
      whereConditions.push('profit_type = ?');
      params.push(type);
    }

    // 日期筛选
    if (start_date && end_date) {
      whereConditions.push('DATE(created_at) BETWEEN ? AND ?');
      params.push(start_date, end_date);
    }

    const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

    console.log('🔧 查询条件构建完成:');
    console.log('  - whereConditions:', whereConditions);
    console.log('  - whereClause:', whereClause);
    console.log('  - params:', params);

    // 查询总数
    const countSql = `SELECT COUNT(*) as total FROM profit_log ${whereClause}`;
    console.log('🔍 执行计数查询:', countSql, '参数:', params);
    const countResult = await db.query(countSql, params);
    const total = countResult[0].total;
    console.log('📊 查询到总记录数:', total);

    // 分页查询明细
    const pageNum = parseInt(page);
    const pageSize = parseInt(limit);
    const offset = (pageNum - 1) * pageSize;

    const listSql = `
      SELECT
        id,
        amount,
        profit_type as type,
        source_id,
        order_no,
        status,
        remark,
        created_at,
        settle_time
      FROM profit_log
      ${whereClause}
      ORDER BY created_at DESC
      LIMIT ? OFFSET ?
    `;

    const listParams = [...params, pageSize, offset];
    console.log('🔍 执行明细查询:', listSql);
    console.log('🔍 查询参数:', listParams);
    const listResult = await db.query(listSql, listParams);

    // 格式化数据
    const list = listResult.map(item => {
      // 类型名称映射
      const typeNameMap = {
        'wifi_share': 'WiFi分享',
        'goods_sale': '商品销售',
        'advertisement': '广告收益'
      };

      // 状态名称映射
      const statusNameMap = {
        0: '待结算',
        1: '已结算',
        2: '已取消'
      };

      return {
        id: item.id,
        type: item.type,
        type_name: typeNameMap[item.type] || item.type,
        amount: parseFloat(item.amount),
        source_id: item.source_id,
        source_name: item.order_no ? `订单${item.order_no}` : `来源${item.source_id}`,
        status: item.status,
        status_name: statusNameMap[item.status] || '未知',
        remark: item.remark || '分润收益',
        created_at: item.created_at,
        settle_time: item.settle_time
      };
    });

    console.log('✅ 用户收入明细获取成功，共', total, '条记录');
    return success(res, {
      list,
      total,
      page: pageNum,
      limit: pageSize,
      has_more: (pageNum * pageSize) < total,
      debug_info: {
        message: "这是真实的数据库查询版本！",
        timestamp: new Date().toISOString(),
        user_id: userId,
        query_params: { page, limit, type, start_date, end_date }
      }
    }, '获取收入明细成功');
  } catch (err) {
    console.error('❌ 获取用户收入明细失败:', err);
    console.error('❌ 错误详情:', err.message);
    console.error('❌ 错误堆栈:', err.stack);
    return error(res, '获取收入明细失败: ' + err.message);
  }
});

module.exports = router;