const db = require('./src/database');

async function checkTeamApplyTable() {
  try {
    console.log('检查team_apply表...');
    
    // 检查表是否存在
    const [tableCheck] = await db.query(
      "SELECT table_name FROM information_schema.tables WHERE table_schema = 'mall' AND table_name = 'team_apply'"
    );
    
    if (!tableCheck || tableCheck.length === 0) {
      console.log('team_apply表不存在！');
      return;
    }
    
    console.log('team_apply表存在');
    
    // 检查表结构
    const [columns] = await db.query('DESCRIBE team_apply');
    console.log('表结构（第一列）：', columns[0]);
    
    // 尝试查询数据
    const [countResult] = await db.query('SELECT COUNT(*) as total FROM team_apply');
    console.log('查询结果：', JSON.stringify(countResult, null, 2));
    
    // 检查countResult的结构
    let total = 0;
    if (countResult && countResult.length > 0) {
      console.log('countResult[0]:', countResult[0]);
      
      // 查找total字段，不管大小写
      const totalField = Object.keys(countResult[0]).find(key => 
        key.toLowerCase() === 'total' || 
        key === 'COUNT(*)'
      );
      
      if (totalField) {
        total = countResult[0][totalField];
        console.log(`找到数据总数(${totalField}): ${total}`);
      } else {
        console.log('无法找到total字段，countResult[0]的键：', Object.keys(countResult[0]));
      }
    }
    
    // 插入测试数据
    if (total === 0) {
      console.log('插入测试数据...');
      await db.query(
        'INSERT INTO team_apply (user_id, name, phone, area, description, status, created_at) VALUES (?, ?, ?, ?, ?, ?, NOW())',
        [1, '测试联盟', '13800138000', '北京市', '这是一条测试联盟申请', 0]
      );
      console.log('测试数据插入成功');
    } else {
      console.log(`已有 ${total} 条数据，无需插入测试数据`);
    }
    
  } catch (err) {
    console.error('检查过程中出错：', err);
  } finally {
    process.exit(0);
  }
}

checkTeamApplyTable(); 