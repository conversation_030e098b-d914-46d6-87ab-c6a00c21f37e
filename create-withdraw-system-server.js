const mysql = require('mysql2/promise');

async function createWithdrawSystem() {
  let connection;
  
  try {
    // 连接数据库
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: 'wo587129955',
      database: 'mall'
    });
    
    console.log('🔧 开始创建提现系统...\n');
    
    // 1. 创建微信支付账户表
    console.log('1️⃣ 创建微信支付账户表...');
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS wechat_account (
        id int(11) NOT NULL AUTO_INCREMENT COMMENT '微信账户ID',
        user_id int(11) NOT NULL COMMENT '用户ID',
        openid varchar(100) NOT NULL COMMENT '微信openid',
        nickname varchar(100) DEFAULT NULL COMMENT '微信昵称',
        real_name varchar(50) DEFAULT NULL COMMENT '真实姓名',
        is_verified tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否实名认证',
        is_default tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否默认提现方式',
        status tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态',
        created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        updated_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
        PRIMARY KEY (id),
        UNIQUE KEY user_openid (user_id, openid),
        KEY user_id (user_id)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='微信支付账户表'
    `);
    console.log('✅ 微信支付账户表创建成功');
    
    // 2. 重新创建银行卡表
    console.log('\n2️⃣ 重新创建银行卡表...');
    await connection.execute('DROP TABLE IF EXISTS bank_card');
    await connection.execute(`
      CREATE TABLE bank_card (
        id int(11) NOT NULL AUTO_INCREMENT COMMENT '银行卡ID',
        user_id int(11) NOT NULL COMMENT '用户ID',
        bank_name varchar(50) NOT NULL COMMENT '银行名称',
        bank_code varchar(20) DEFAULT NULL COMMENT '银行代码',
        card_number varchar(50) NOT NULL COMMENT '卡号（加密存储）',
        card_number_mask varchar(50) NOT NULL COMMENT '卡号掩码显示',
        card_holder varchar(50) NOT NULL COMMENT '持卡人姓名',
        card_type tinyint(1) NOT NULL DEFAULT '1' COMMENT '卡类型',
        phone varchar(20) DEFAULT NULL COMMENT '预留手机号',
        id_card varchar(50) DEFAULT NULL COMMENT '身份证号（加密存储）',
        is_default tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否默认',
        is_verified tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已验证',
        status tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态',
        created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        updated_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
        PRIMARY KEY (id),
        KEY user_id (user_id),
        KEY status (status)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='银行卡表'
    `);
    console.log('✅ 银行卡表创建成功');
    
    // 3. 重新创建提现申请表
    console.log('\n3️⃣ 重新创建提现申请表...');
    await connection.execute('DROP TABLE IF EXISTS withdraw');
    await connection.execute(`
      CREATE TABLE withdraw (
        id int(11) NOT NULL AUTO_INCREMENT COMMENT '提现ID',
        user_id int(11) NOT NULL COMMENT '用户ID',
        withdraw_no varchar(50) NOT NULL COMMENT '提现单号',
        amount decimal(10,2) NOT NULL COMMENT '提现金额',
        fee decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '手续费',
        actual_amount decimal(10,2) NOT NULL COMMENT '实际到账金额',
        withdraw_type varchar(20) NOT NULL COMMENT '提现方式',
        account_id int(11) NOT NULL COMMENT '账户ID',
        account_info text DEFAULT NULL COMMENT '账户信息快照',
        status tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态',
        apply_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '申请时间',
        audit_user_id int(11) DEFAULT NULL COMMENT '审核人ID',
        audit_time datetime DEFAULT NULL COMMENT '审核时间',
        audit_remark varchar(255) DEFAULT NULL COMMENT '审核备注',
        process_time datetime DEFAULT NULL COMMENT '处理时间',
        complete_time datetime DEFAULT NULL COMMENT '完成时间',
        transaction_id varchar(100) DEFAULT NULL COMMENT '微信支付交易ID',
        bank_transaction_id varchar(100) DEFAULT NULL COMMENT '银行交易流水号',
        failure_reason varchar(255) DEFAULT NULL COMMENT '失败原因',
        remark varchar(255) DEFAULT NULL COMMENT '用户备注',
        created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        updated_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
        PRIMARY KEY (id),
        UNIQUE KEY withdraw_no (withdraw_no),
        KEY user_id (user_id),
        KEY status (status),
        KEY withdraw_type (withdraw_type),
        KEY apply_time (apply_time)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='提现申请表'
    `);
    console.log('✅ 提现申请表创建成功');
    
    // 4. 创建提现配置表
    console.log('\n4️⃣ 创建提现配置表...');
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS withdraw_config (
        id int(11) NOT NULL AUTO_INCREMENT COMMENT '配置ID',
        config_key varchar(50) NOT NULL COMMENT '配置键',
        config_value text NOT NULL COMMENT '配置值',
        config_type varchar(20) NOT NULL DEFAULT 'string' COMMENT '配置类型',
        description varchar(255) DEFAULT NULL COMMENT '配置描述',
        status tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态',
        created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        updated_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
        PRIMARY KEY (id),
        UNIQUE KEY config_key (config_key)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='提现配置表'
    `);
    console.log('✅ 提现配置表创建成功');
    
    // 5. 创建银行信息表
    console.log('\n5️⃣ 创建银行信息表...');
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS bank_info (
        id int(11) NOT NULL AUTO_INCREMENT COMMENT '银行ID',
        bank_code varchar(20) NOT NULL COMMENT '银行代码',
        bank_name varchar(50) NOT NULL COMMENT '银行名称',
        bank_logo varchar(255) DEFAULT NULL COMMENT '银行logo',
        is_support tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否支持',
        min_amount decimal(10,2) NOT NULL DEFAULT '1.00' COMMENT '最小提现金额',
        max_amount decimal(10,2) NOT NULL DEFAULT '50000.00' COMMENT '最大提现金额',
        fee_rate decimal(5,4) NOT NULL DEFAULT '0.0000' COMMENT '手续费率',
        sort_order int(11) NOT NULL DEFAULT '0' COMMENT '排序',
        status tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态',
        created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        updated_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
        PRIMARY KEY (id),
        UNIQUE KEY bank_code (bank_code),
        KEY status (status),
        KEY sort_order (sort_order)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='银行信息表'
    `);
    console.log('✅ 银行信息表创建成功');
    
    // 6. 插入配置数据
    console.log('\n6️⃣ 插入配置数据...');
    
    // 插入提现配置
    const configs = [
      ['min_withdraw_amount', '10', 'number', '最小提现金额（元）'],
      ['max_withdraw_amount', '50000', 'number', '最大提现金额（元）'],
      ['daily_withdraw_limit', '100000', 'number', '每日提现限额（元）'],
      ['wechat_fee_rate', '0.006', 'number', '微信提现手续费率'],
      ['bank_fee_rate', '0.001', 'number', '银行卡提现手续费率'],
      ['wechat_min_fee', '0.1', 'number', '微信提现最小手续费（元）'],
      ['bank_min_fee', '2', 'number', '银行卡提现最小手续费（元）'],
      ['auto_audit_enabled', 'true', 'boolean', '是否启用自动审核'],
      ['auto_audit_limit', '1000', 'number', '自动审核金额限制（元）'],
      ['working_hours', '{"start": "09:00", "end": "18:00"}', 'json', '工作时间配置'],
      ['weekend_process', 'false', 'boolean', '是否周末处理提现'],
      ['notification_enabled', 'true', 'boolean', '是否启用提现通知']
    ];
    
    for (const config of configs) {
      await connection.execute(`
        INSERT INTO withdraw_config (config_key, config_value, config_type, description) VALUES (?, ?, ?, ?)
        ON DUPLICATE KEY UPDATE 
          config_value = VALUES(config_value),
          description = VALUES(description),
          updated_at = CURRENT_TIMESTAMP
      `, config);
    }
    
    // 插入银行信息
    const banks = [
      ['ICBC', '中国工商银行', 10.00, 50000.00, 0.001, 1],
      ['ABC', '中国农业银行', 10.00, 50000.00, 0.001, 2],
      ['BOC', '中国银行', 10.00, 50000.00, 0.001, 3],
      ['CCB', '中国建设银行', 10.00, 50000.00, 0.001, 4],
      ['COMM', '交通银行', 10.00, 50000.00, 0.001, 5],
      ['CMB', '招商银行', 10.00, 50000.00, 0.001, 6],
      ['CITIC', '中信银行', 10.00, 50000.00, 0.001, 7],
      ['CEB', '光大银行', 10.00, 50000.00, 0.001, 8],
      ['CMBC', '中国民生银行', 10.00, 50000.00, 0.001, 9],
      ['PAB', '平安银行', 10.00, 50000.00, 0.001, 10]
    ];
    
    for (const bank of banks) {
      await connection.execute(`
        INSERT INTO bank_info (bank_code, bank_name, min_amount, max_amount, fee_rate, sort_order) VALUES (?, ?, ?, ?, ?, ?)
        ON DUPLICATE KEY UPDATE 
          bank_name = VALUES(bank_name),
          min_amount = VALUES(min_amount),
          max_amount = VALUES(max_amount),
          updated_at = CURRENT_TIMESTAMP
      `, bank);
    }
    
    // 7. 插入测试数据
    console.log('\n7️⃣ 插入测试数据...');
    
    // 插入微信账户
    await connection.execute(`
      INSERT INTO wechat_account (user_id, openid, nickname, real_name, is_verified, is_default) VALUES (?, ?, ?, ?, ?, ?)
      ON DUPLICATE KEY UPDATE 
        nickname = VALUES(nickname),
        updated_at = CURRENT_TIMESTAMP
    `, [1, 'oW20C7mVlW8e3W2AgUGtDTJeAbQU', '润生', '张润生', 1, 1]);
    
    // 插入银行卡
    await connection.execute(`
      INSERT INTO bank_card (user_id, bank_name, bank_code, card_number, card_number_mask, card_holder, phone, is_verified) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
      ON DUPLICATE KEY UPDATE updated_at = CURRENT_TIMESTAMP
    `, [1, '招商银行', 'CMB', 'encrypted_6217000123456789', '**** **** **** 6789', '张润生', '***********', 1]);
    
    console.log('✅ 测试数据插入成功');
    
    // 8. 验证创建结果
    console.log('\n8️⃣ 验证创建结果...');
    const [tables] = await connection.execute(`
      SELECT 
        table_name as '表名',
        table_comment as '表说明'
      FROM information_schema.tables 
      WHERE table_schema = 'mall' 
      AND table_name IN ('bank_card', 'wechat_account', 'withdraw', 'withdraw_config', 'bank_info')
      ORDER BY table_name
    `);
    
    console.table(tables);
    
    // 检查数据
    const [configCount] = await connection.execute('SELECT COUNT(*) as count FROM withdraw_config');
    const [bankCount] = await connection.execute('SELECT COUNT(*) as count FROM bank_info');
    const [wechatCount] = await connection.execute('SELECT COUNT(*) as count FROM wechat_account');
    const [cardCount] = await connection.execute('SELECT COUNT(*) as count FROM bank_card');
    
    console.log('\n📊 数据统计:');
    console.log(`- 提现配置: ${configCount[0].count} 条`);
    console.log(`- 银行信息: ${bankCount[0].count} 条`);
    console.log(`- 微信账户: ${wechatCount[0].count} 条`);
    console.log(`- 银行卡: ${cardCount[0].count} 条`);
    
    console.log('\n🎉 提现系统创建完成！');
    
  } catch (error) {
    console.error('❌ 创建失败:', error.message);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// 运行创建
createWithdrawSystem();
