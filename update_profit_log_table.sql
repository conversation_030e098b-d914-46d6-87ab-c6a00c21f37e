-- 更新 profit_log 表结构，添加缺少的字段
USE mall;

-- 添加缺少的字段（逐个添加，忽略已存在的字段错误）
ALTER TABLE profit_log ADD COLUMN `team_id` int(11) DEFAULT NULL COMMENT '团队ID' AFTER `user_id`;
ALTER TABLE profit_log ADD COLUMN `order_no` varchar(50) DEFAULT NULL COMMENT '关联订单号' AFTER `source_id`;
ALTER TABLE profit_log ADD COLUMN `profit_type` varchar(20) NOT NULL DEFAULT 'wifi_share' COMMENT '分润类型：wifi_share,goods_sale,advertisement' AFTER `order_no`;
ALTER TABLE profit_log ADD COLUMN `role` varchar(20) NOT NULL DEFAULT 'user' COMMENT '角色：leader,user,platform' AFTER `profit_type`;
ALTER TABLE profit_log ADD COLUMN `rate` int(11) NOT NULL DEFAULT 0 COMMENT '分润比例（百分比）' AFTER `role`;
ALTER TABLE profit_log ADD COLUMN `total_amount` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '总金额' AFTER `rate`;
ALTER TABLE profit_log ADD COLUMN `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态：0待结算，1已结算，2已取消' AFTER `total_amount`;
ALTER TABLE profit_log ADD COLUMN `settle_time` datetime DEFAULT NULL COMMENT '结算时间' AFTER `status`;
ALTER TABLE profit_log ADD COLUMN `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间' AFTER `created_at`;

-- 添加索引（忽略已存在的索引错误）
ALTER TABLE profit_log ADD INDEX `team_id` (`team_id`);
ALTER TABLE profit_log ADD INDEX `profit_type` (`profit_type`);
ALTER TABLE profit_log ADD INDEX `status` (`status`);

-- 更新现有数据，设置默认的 profit_type 值
UPDATE profit_log SET 
  profit_type = CASE 
    WHEN source_type = 1 THEN 'wifi_share'
    WHEN source_type = 2 THEN 'goods_sale' 
    WHEN source_type = 3 THEN 'advertisement'
    ELSE 'wifi_share'
  END
WHERE profit_type = 'wifi_share' OR profit_type IS NULL;

-- 显示更新后的表结构
DESCRIBE profit_log;

SELECT '✅ profit_log 表结构更新完成！' as message;
