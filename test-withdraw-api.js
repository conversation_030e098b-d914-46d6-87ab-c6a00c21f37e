const axios = require('axios');
const jwt = require('jsonwebtoken');

async function testWithdrawAPI() {
  try {
    console.log('🧪 测试提现API接口...\n');
    
    // 1. 生成测试token
    const testUser = {
      id: 1,
      openid: 'oW20C7mVlW8e3W2AgUGtDTJeAbQU'
    };
    
    const token = jwt.sign(
      testUser,
      'wifi_share_secret_key',
      { expiresIn: '24h' }
    );
    
    console.log('1️⃣ 生成的测试token:', token.substring(0, 50) + '...\n');
    
    // 2. 测试获取提现方式接口
    console.log('2️⃣ 测试获取提现方式接口...');
    try {
      const methodsResponse = await axios.get('http://localhost:4000/api/v1/client/withdraw/methods', {
        headers: {
          'Authorization': `Bearer ${token}`
        },
        timeout: 10000
      });
      
      console.log('✅ 提现方式接口成功:');
      console.log('状态码:', methodsResponse.status);
      console.log('响应数据:', JSON.stringify(methodsResponse.data, null, 2));
      
    } catch (methodsError) {
      if (methodsError.response) {
        console.log('❌ 提现方式接口失败:');
        console.log('状态码:', methodsError.response.status);
        console.log('错误信息:', JSON.stringify(methodsError.response.data, null, 2));
      } else {
        console.log('❌ 提现方式请求失败:', methodsError.message);
      }
    }
    
    // 3. 测试获取提现配置接口
    console.log('\n3️⃣ 测试获取提现配置接口...');
    try {
      const configResponse = await axios.get('http://localhost:4000/api/v1/client/withdraw/config', {
        headers: {
          'Authorization': `Bearer ${token}`
        },
        timeout: 10000
      });
      
      console.log('✅ 提现配置接口成功:');
      console.log('状态码:', configResponse.status);
      console.log('响应数据:', JSON.stringify(configResponse.data, null, 2));
      
    } catch (configError) {
      if (configError.response) {
        console.log('❌ 提现配置接口失败:');
        console.log('状态码:', configError.response.status);
        console.log('错误信息:', JSON.stringify(configError.response.data, null, 2));
      } else {
        console.log('❌ 提现配置请求失败:', configError.message);
      }
    }
    
    // 4. 测试获取用户提现账户接口
    console.log('\n4️⃣ 测试获取用户提现账户接口...');
    try {
      const accountsResponse = await axios.get('http://localhost:4000/api/v1/client/withdraw/accounts', {
        headers: {
          'Authorization': `Bearer ${token}`
        },
        timeout: 10000
      });
      
      console.log('✅ 用户提现账户接口成功:');
      console.log('状态码:', accountsResponse.status);
      console.log('响应数据:', JSON.stringify(accountsResponse.data, null, 2));
      
    } catch (accountsError) {
      if (accountsError.response) {
        console.log('❌ 用户提现账户接口失败:');
        console.log('状态码:', accountsError.response.status);
        console.log('错误信息:', JSON.stringify(accountsError.response.data, null, 2));
      } else {
        console.log('❌ 用户提现账户请求失败:', accountsError.message);
      }
    }
    
    // 5. 测试计算手续费接口
    console.log('\n5️⃣ 测试计算手续费接口...');
    try {
      const feeResponse = await axios.post('http://localhost:4000/api/v1/client/withdraw/calculate-fee', {
        method_code: 'wechat',
        amount: 100.00
      }, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        timeout: 10000
      });
      
      console.log('✅ 计算手续费接口成功:');
      console.log('状态码:', feeResponse.status);
      console.log('响应数据:', JSON.stringify(feeResponse.data, null, 2));
      
    } catch (feeError) {
      if (feeError.response) {
        console.log('❌ 计算手续费接口失败:');
        console.log('状态码:', feeError.response.status);
        console.log('错误信息:', JSON.stringify(feeError.response.data, null, 2));
      } else {
        console.log('❌ 计算手续费请求失败:', feeError.message);
      }
    }
    
    // 6. 测试获取提现记录接口
    console.log('\n6️⃣ 测试获取提现记录接口...');
    try {
      const recordsResponse = await axios.get('http://localhost:4000/api/v1/client/withdraw/records', {
        headers: {
          'Authorization': `Bearer ${token}`
        },
        params: {
          page: 1,
          limit: 10
        },
        timeout: 10000
      });
      
      console.log('✅ 提现记录接口成功:');
      console.log('状态码:', recordsResponse.status);
      console.log('响应数据:', JSON.stringify(recordsResponse.data, null, 2));
      
    } catch (recordsError) {
      if (recordsError.response) {
        console.log('❌ 提现记录接口失败:');
        console.log('状态码:', recordsError.response.status);
        console.log('错误信息:', JSON.stringify(recordsError.response.data, null, 2));
      } else {
        console.log('❌ 提现记录请求失败:', recordsError.message);
      }
    }
    
    console.log('\n🎉 API测试完成！');
    
  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error.message);
  }
}

// 运行测试
testWithdrawAPI();
