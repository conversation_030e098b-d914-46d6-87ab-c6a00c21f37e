const db = require('../database');
const logger = require('../utils/logger');
const { success, error } = require('../utils/response');

/**
 * 地区管理控制器
 */

/**
 * 获取地区列表
 */
const getRegionList = async (req, res) => {
  try {
    const { parentId, level, status, page = 1, limit = 50 } = req.query;
    
    let whereConditions = [];
    const params = [];
    
    // 父级地区筛选
    if (parentId !== undefined) {
      if (parentId === 'null' || parentId === '') {
        whereConditions.push('parent_id IS NULL');
      } else {
        whereConditions.push('parent_id = ?');
        params.push(parseInt(parentId));
      }
    }
    
    // 地区级别筛选
    if (level) {
      whereConditions.push('level = ?');
      params.push(parseInt(level));
    }
    
    // 状态筛选
    if (status !== undefined) {
      whereConditions.push('status = ?');
      params.push(parseInt(status));
    }
    
    const whereClause = whereConditions.length > 0 ? 
      `WHERE ${whereConditions.join(' AND ')}` : '';
    
    const offset = (parseInt(page) - 1) * parseInt(limit);
    
    // 获取总数
    const countSql = `SELECT COUNT(*) as total FROM region ${whereClause}`;
    const countResult = await db.query(countSql, params);
    const total = countResult[0].total;
    
    // 获取列表
    const listSql = `
      SELECT
        r.id, r.name, r.code, r.parent_id, r.level, r.status, r.sort_order,
        r.created_at, r.updated_at,
        pr.name as parent_name
      FROM region r
      LEFT JOIN region pr ON r.parent_id = pr.id
      ${whereClause}
      ORDER BY r.sort_order ASC, r.id ASC
      LIMIT ${parseInt(limit)} OFFSET ${offset}
    `;

    const list = await db.query(listSql, params);
    
    return success(res, {
      list,
      pagination: {
        total,
        page: parseInt(page),
        limit: parseInt(limit),
        pages: Math.ceil(total / parseInt(limit))
      }
    }, '获取地区列表成功');
  } catch (err) {
    logger.error(`获取地区列表失败: ${err.message}`);
    return error(res, '获取地区列表失败', 500);
  }
};

/**
 * 获取地区详情
 */
const getRegionDetail = async (req, res) => {
  try {
    const { id } = req.params;
    
    const sql = `
      SELECT 
        r.id, r.name, r.code, r.parent_id, r.level, r.status, r.sort_order,
        r.created_at, r.updated_at,
        pr.name as parent_name
      FROM region r
      LEFT JOIN region pr ON r.parent_id = pr.id
      WHERE r.id = ?
    `;
    
    const result = await db.query(sql, [id]);
    
    if (result.length === 0) {
      return error(res, '地区不存在', 404);
    }
    
    return success(res, result[0], '获取地区详情成功');
  } catch (err) {
    logger.error(`获取地区详情失败: ${err.message}`);
    return error(res, '获取地区详情失败', 500);
  }
};

/**
 * 创建地区
 */
const createRegion = async (req, res) => {
  try {
    const { name, code, parentId, level, status = 1, sortOrder = 0 } = req.body;
    
    // 验证必填字段
    if (!name || !code) {
      return error(res, '地区名称和代码不能为空', 400);
    }
    
    // 检查代码是否重复
    const existingRegion = await db.query(
      'SELECT id FROM region WHERE code = ?',
      [code]
    );
    
    if (existingRegion.length > 0) {
      return error(res, '地区代码已存在', 400);
    }
    
    // 创建地区
    const sql = `
      INSERT INTO region (name, code, parent_id, level, status, sort_order, created_at, updated_at)
      VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW())
    `;
    
    const result = await db.query(sql, [
      name, code, parentId || null, level || 1, status, sortOrder
    ]);
    
    return success(res, {
      id: result.insertId,
      name,
      code,
      parentId: parentId || null,
      level: level || 1,
      status,
      sortOrder
    }, '创建地区成功');
  } catch (err) {
    logger.error(`创建地区失败: ${err.message}`);
    return error(res, '创建地区失败', 500);
  }
};

/**
 * 更新地区
 */
const updateRegion = async (req, res) => {
  try {
    const { id } = req.params;
    const { name, code, parentId, level, status, sortOrder } = req.body;
    
    // 检查地区是否存在
    const existingRegion = await db.query(
      'SELECT id FROM region WHERE id = ?',
      [id]
    );
    
    if (existingRegion.length === 0) {
      return error(res, '地区不存在', 404);
    }
    
    // 检查代码是否重复（排除自己）
    if (code) {
      const duplicateRegion = await db.query(
        'SELECT id FROM region WHERE code = ? AND id != ?',
        [code, id]
      );
      
      if (duplicateRegion.length > 0) {
        return error(res, '地区代码已存在', 400);
      }
    }
    
    // 构建更新字段
    const updateFields = [];
    const params = [];
    
    if (name !== undefined) {
      updateFields.push('name = ?');
      params.push(name);
    }
    
    if (code !== undefined) {
      updateFields.push('code = ?');
      params.push(code);
    }
    
    if (parentId !== undefined) {
      updateFields.push('parent_id = ?');
      params.push(parentId || null);
    }
    
    if (level !== undefined) {
      updateFields.push('level = ?');
      params.push(level);
    }
    
    if (status !== undefined) {
      updateFields.push('status = ?');
      params.push(status);
    }
    
    if (sortOrder !== undefined) {
      updateFields.push('sort_order = ?');
      params.push(sortOrder);
    }
    
    if (updateFields.length === 0) {
      return error(res, '没有需要更新的字段', 400);
    }
    
    updateFields.push('updated_at = NOW()');
    params.push(id);
    
    const sql = `UPDATE region SET ${updateFields.join(', ')} WHERE id = ?`;
    await db.query(sql, params);
    
    return success(res, null, '更新地区成功');
  } catch (err) {
    logger.error(`更新地区失败: ${err.message}`);
    return error(res, '更新地区失败', 500);
  }
};

/**
 * 删除地区
 */
const deleteRegion = async (req, res) => {
  try {
    const { id } = req.params;
    
    // 检查地区是否存在
    const existingRegion = await db.query(
      'SELECT id FROM region WHERE id = ?',
      [id]
    );
    
    if (existingRegion.length === 0) {
      return error(res, '地区不存在', 404);
    }
    
    // 检查是否有子地区
    const childRegions = await db.query(
      'SELECT id FROM region WHERE parent_id = ?',
      [id]
    );
    
    if (childRegions.length > 0) {
      return error(res, '该地区下还有子地区，无法删除', 400);
    }
    
    // 检查是否有关联的团队
    const relatedTeams = await db.query(
      'SELECT id FROM team WHERE region_id = ?',
      [id]
    );
    
    if (relatedTeams.length > 0) {
      return error(res, '该地区下还有团队，无法删除', 400);
    }
    
    // 删除地区
    await db.query('DELETE FROM region WHERE id = ?', [id]);
    
    return success(res, null, '删除地区成功');
  } catch (err) {
    logger.error(`删除地区失败: ${err.message}`);
    return error(res, '删除地区失败', 500);
  }
};

/**
 * 获取地区树形结构
 */
const getRegionTree = async (req, res) => {
  try {
    const { status = 1 } = req.query;
    
    // 获取所有地区
    const sql = `
      SELECT id, name, code, parent_id, level, status, sort_order
      FROM region 
      WHERE status = ?
      ORDER BY sort_order ASC, id ASC
    `;
    
    const regions = await db.query(sql, [status]);
    
    // 构建树形结构
    const buildTree = (parentId = null) => {
      return regions
        .filter(region => region.parent_id === parentId)
        .map(region => ({
          ...region,
          children: buildTree(region.id)
        }));
    };
    
    const tree = buildTree();
    
    return success(res, tree, '获取地区树形结构成功');
  } catch (err) {
    logger.error(`获取地区树形结构失败: ${err.message}`);
    return error(res, '获取地区树形结构失败', 500);
  }
};

module.exports = {
  getRegionList,
  getRegionDetail,
  createRegion,
  updateRegion,
  deleteRegion,
  getRegionTree
};
