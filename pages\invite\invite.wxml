<!--pages/invite/invite.wxml-->
<view class="container">
  <!-- 头部 -->
  <view class="header">
    <image class="logo" src="/images/logo.png" mode="aspectFit"></image>
    <text class="title">WiFi共享商城</text>
    <text class="subtitle">邀请您加入团队</text>
  </view>

  <!-- 邀请信息 -->
  <view class="invite-info">
    <view class="team-card">
      <view class="team-header">
        <image class="team-avatar" src="{{teamInfo.avatar || '/images/default-avatar.png'}}" mode="aspectFill"></image>
        <view class="team-details">
          <text class="team-name">{{teamInfo.name || '团队'}}</text>
          <text class="team-leader">团长：{{teamInfo.leaderName || '未知'}}</text>
          <text class="team-members">成员：{{teamInfo.memberCount || 0}}人</text>
        </view>
      </view>
      
      <view class="team-stats">
        <view class="stat-item">
          <text class="stat-value">{{teamInfo.totalIncome || '0.00'}}</text>
          <text class="stat-label">团队总收益</text>
        </view>
        <view class="stat-item">
          <text class="stat-value">{{teamInfo.monthIncome || '0.00'}}</text>
          <text class="stat-label">本月收益</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 邀请码信息 -->
  <view class="invite-code-section">
    <text class="invite-code-label">邀请码</text>
    <view class="invite-code-box">
      <text class="invite-code">{{inviteCode}}</text>
      <button class="copy-btn" bindtap="copyInviteCode">复制</button>
    </view>
  </view>

  <!-- 操作按钮 -->
  <view class="actions">
    <button class="join-btn" bindtap="joinTeam" wx:if="{{!isLoggedIn}}">
      立即加入团队
    </button>
    <button class="login-btn" bindtap="goToLogin" wx:if="{{!isLoggedIn}}">
      登录后加入
    </button>
    <view class="already-member" wx:if="{{isLoggedIn && isMember}}">
      <text>您已经是团队成员了</text>
      <button class="go-team-btn" bindtap="goToTeam">查看团队</button>
    </view>
  </view>

  <!-- 底部说明 -->
  <view class="footer">
    <text class="description">加入团队，一起分享WiFi，赚取收益！</text>
    <text class="tips">• 分享WiFi密码给朋友使用</text>
    <text class="tips">• 朋友使用WiFi时您获得分润</text>
    <text class="tips">• 邀请更多朋友加入团队</text>
  </view>
</view>

<!-- 加载状态 -->
<view class="loading" wx:if="{{loading}}">
  <text>加载中...</text>
</view>
