const jwt = require('jsonwebtoken');
const config = require('./config');

// 生成新的token
const token = jwt.sign(
  { id: 1, openid: 'oW20C7mVlW8e3W2AgUGtDTJeAbQU', role: 'user' },
  config.jwt.secret,
  { expiresIn: config.jwt.expiresIn }
);

console.log('新生成的Token:', token);

// 测试用户信息获取
const axios = require('axios');

async function testUserInfo() {
  try {
    const response = await axios.get('http://localhost:4000/api/v1/client/user/info', {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    console.log('\n✅ 用户信息获取成功:');
    console.log('响应数据:', JSON.stringify(response.data, null, 2));
    
    if (response.data && response.data.data) {
      const userData = response.data.data;
      console.log('\n🖼️ 头像信息分析:');
      console.log('- 用户昵称:', userData.nickname);
      console.log('- 头像URL:', userData.avatar || '(空)');
      
      if (!userData.avatar || userData.avatar === '') {
        console.log('✅ 头像为空，前端将显示默认头像');
      } else if (userData.avatar.includes('thirdwx.qlogo.cn') || 
                 userData.avatar.includes('wx.qlogo.cn') || 
                 userData.avatar.includes('mmopen')) {
        console.log('❌ 仍然包含失效的微信头像URL');
      } else {
        console.log('✅ 头像URL有效');
      }
    }
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    if (error.response) {
      console.error('响应状态:', error.response.status);
      console.error('响应数据:', error.response.data);
    }
  }
}

testUserInfo();
