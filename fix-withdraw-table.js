const mysql = require('mysql2/promise');

async function fixWithdrawTable() {
  let connection;
  
  try {
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: 'wo587129955',
      database: 'mall'
    });
    
    console.log('🔧 修复提现表结构...');
    
    // 修复 withdraw 表的 created_at 和 updated_at 字段
    await connection.execute(`
      ALTER TABLE withdraw 
      MODIFY COLUMN created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP
    `);
    
    await connection.execute(`
      ALTER TABLE withdraw 
      MODIFY COLUMN updated_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    `);
    
    console.log('✅ withdraw 表字段修复完成');
    
    // 验证修复结果
    const [columns] = await connection.execute(`
      SELECT COLUMN_NAME, COLUMN_DEFAULT, IS_NULLABLE, EXTRA 
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = 'mall' 
      AND TABLE_NAME = 'withdraw' 
      AND COLUMN_NAME IN ('created_at', 'updated_at')
    `);
    
    console.log('📊 字段信息验证:');
    console.table(columns);
    
    console.log('🎉 提现表结构修复完成！现在可以正常提现了');
    
  } catch (error) {
    console.error('❌ 修复失败:', error.message);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

fixWithdrawTable();
