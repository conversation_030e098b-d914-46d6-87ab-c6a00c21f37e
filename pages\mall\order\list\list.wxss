/* pages/mall/order/list/list.wxss */
.container {
  height: 100vh;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
}

/* 搜索栏样式 */
.search-bar {
  background: white;
  padding: 20rpx;
  display: flex;
  align-items: center;
  gap: 20rpx;
  border-bottom: 1rpx solid #eee;
}

.search-input-wrapper {
  flex: 1;
  display: flex;
  align-items: center;
  background: #f5f5f5;
  border-radius: 50rpx;
  padding: 0 20rpx;
}

.search-input {
  flex: 1;
  height: 70rpx;
  font-size: 28rpx;
  color: #333;
}

.search-btn {
  padding: 10rpx;
  color: #666;
}

.filter-btn {
  padding: 15rpx 25rpx;
  background: #f0f0f0;
  border-radius: 40rpx;
  font-size: 26rpx;
  color: #666;
}

/* 标签页导航 */
.tabs {
  display: flex;
  background-color: #fff;
  border-bottom: 1rpx solid #eee;
  position: relative;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 30rpx 0;
  font-size: 28rpx;
  color: #666;
  position: relative;
}

.tab-badge {
  position: absolute;
  top: 15rpx;
  right: 20rpx;
  background: #ff4757;
  color: white;
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 20rpx;
  min-width: 32rpx;
  height: 32rpx;
  line-height: 24rpx;
  text-align: center;
}

.search-toggle {
  position: absolute;
  right: 20rpx;
  top: 50%;
  transform: translateY(-50%);
  padding: 10rpx 20rpx;
  background: #f0f0f0;
  border-radius: 30rpx;
  font-size: 24rpx;
  color: #666;
}

.tab-item.active {
  color: #07c160;
  font-weight: 500;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 4rpx;
  background-color: #07c160;
  border-radius: 2rpx;
}

/* 订单列表 */
.order-list {
  flex: 1;
  padding: 20rpx;
}

.order-item {
  background-color: #fff;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
}

/* 订单头部 */
.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.order-info {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.order-no {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.order-time {
  font-size: 24rpx;
  color: #999;
}

.order-status {
  font-size: 26rpx;
  color: #07c160;
  font-weight: 500;
}

.order-status.pending {
  color: #ff6b35;
}

.order-status.cancelled {
  color: #999;
}

/* 商品列表 */
.goods-list {
  padding: 0 24rpx;
}

.goods-item {
  display: flex;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.goods-item:last-child {
  border-bottom: none;
}

.goods-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 12rpx;
  margin-right: 24rpx;
  background-color: #f5f5f5;
}

.goods-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.goods-name {
  font-size: 28rpx;
  color: #333;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.goods-spec {
  font-size: 24rpx;
  color: #999;
  margin: 8rpx 0;
}

.goods-price-qty {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.goods-price {
  font-size: 28rpx;
  color: #ff6b35;
  font-weight: 500;
}

.goods-qty {
  font-size: 24rpx;
  color: #999;
}

/* 订单金额 */
.order-amount {
  padding: 24rpx;
  text-align: right;
  border-bottom: 1rpx solid #f0f0f0;
  font-size: 26rpx;
  color: #666;
}

.total-amount {
  color: #ff6b35;
  font-weight: 500;
  font-size: 28rpx;
}

/* 操作按钮 */
.order-actions {
  display: flex;
  justify-content: flex-end;
  gap: 20rpx;
  padding: 24rpx;
}

.action-btn {
  padding: 16rpx 32rpx;
  border-radius: 40rpx;
  font-size: 26rpx;
  border: none;
  min-width: 120rpx;
}

.action-btn.primary {
  background-color: #07c160;
  color: #fff;
}

.action-btn.danger {
  background-color: #ff4757;
  color: #fff;
  border: 1rpx solid #ff4757;
}

.action-btn.danger:active {
  background-color: #ff3838;
}

.action-btn.secondary {
  background-color: #fff;
  color: #666;
  border: 1rpx solid #ddd;
}

/* 加载状态 */
.loading-more {
  text-align: center;
  padding: 40rpx;
  font-size: 26rpx;
  color: #999;
}

.no-more {
  text-align: center;
  padding: 40rpx;
  font-size: 26rpx;
  color: #999;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 40rpx;
  opacity: 0.6;
  line-height: 1;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 40rpx;
}

.empty-btn {
  background-color: #07c160;
  color: #fff;
  border-radius: 40rpx;
  padding: 20rpx 60rpx;
  font-size: 28rpx;
  border: none;
}

/* 筛选弹窗样式 */
.filter-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.filter-popup.show {
  opacity: 1;
  visibility: visible;
}

.filter-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
}

.filter-content {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  border-radius: 40rpx 40rpx 0 0;
  padding: 40rpx;
  max-height: 80vh;
  transform: translateY(100%);
  transition: transform 0.3s ease;
}

.filter-popup.show .filter-content {
  transform: translateY(0);
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 40rpx;
}

.filter-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.filter-close {
  font-size: 40rpx;
  color: #999;
  padding: 10rpx;
}

.filter-section {
  margin-bottom: 40rpx;
}

.filter-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 20rpx;
  font-weight: 500;
}

.filter-options {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.filter-option {
  padding: 16rpx 32rpx;
  background: #f5f5f5;
  border-radius: 40rpx;
  font-size: 26rpx;
  color: #666;
  border: 2rpx solid transparent;
  transition: all 0.2s ease;
}

.filter-option.active {
  background: #e8f5e8;
  color: #07c160;
  border-color: #07c160;
}

.filter-actions {
  display: flex;
  gap: 20rpx;
  margin-top: 40rpx;
}

.filter-reset {
  flex: 1;
  padding: 24rpx;
  background: #f5f5f5;
  color: #666;
  border-radius: 40rpx;
  font-size: 28rpx;
  border: none;
}

.filter-confirm {
  flex: 2;
  padding: 24rpx;
  background: #07c160;
  color: white;
  border-radius: 40rpx;
  font-size: 28rpx;
  border: none;
}