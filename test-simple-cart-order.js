const axios = require('axios');
const jwt = require('jsonwebtoken');

async function testSimpleCartOrder() {
  try {
    console.log('🔧 测试购物车订单创建...');
    
    // 生成JWT token
    const config = require('./config');
    const userId = 2;
    
    const token = jwt.sign(
      { id: userId, openid: 'test_openid_13800138000' },
      config.jwt.secret,
      { expiresIn: config.jwt.expiresIn }
    );
    
    console.log('🔑 Token生成成功');
    
    // 测试从购物车创建订单
    console.log('\n📝 测试从购物车创建订单...');
    const orderData = {
      addressId: 1,
      goods: [], // 空数组，应该从购物车获取
      remark: '测试从购物车创建订单',
      paymentMethod: 'wechat',
      couponId: 0,
      fromCart: true // 关键参数
    };
    
    console.log('订单数据:', orderData);
    
    const createOrderResponse = await axios.post('http://localhost:4000/api/v1/client/order/create', orderData, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      timeout: 10000
    });
    
    console.log('✅ 订单创建响应:', createOrderResponse.data);
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    if (error.response) {
      console.error('响应状态:', error.response.status);
      console.error('响应数据:', error.response.data);
    } else {
      console.error('其他错误:', error.code || error.message);
    }
  }
}

// 运行测试
testSimpleCartOrder();
