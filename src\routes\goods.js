const express = require('express');
const router = express.Router();
const { verifyToken, optionalAuth } = require('../middlewares/auth');
const { success } = require('../utils/response');
const goodsController = require('../controllers/goods');

/**
 * @route   GET /api/v1/client/goods/list
 * @desc    获取商品列表，支持分页、搜索、筛选
 * @access  Public
 */
router.get('/client/list', goodsController.getGoodsList);

/**
 * @route   GET /api/v1/client/goods/detail/:id
 * @desc    获取商品详情
 * @access  Public
 */
router.get('/client/detail/:id', goodsController.getGoodsDetail);

/**
 * @route   GET /api/v1/client/goods/categories
 * @desc    获取商品分类
 * @access  Public
 */
router.get('/client/categories', (req, res) => {
  // 调用商品分类控制器
  goodsController.getCategories(req, res);
});

/**
 * 管理端API路由
 */

// 处理图片URL，确保返回正确的URL
function processImageUrl(url) {
  if (!url) {
    return '/uploads/default-ad.jpg';
  }
  
  // 如果已经是完整URL，直接返回
  if (url.startsWith('http://') || url.startsWith('https://')) {
    return url;
  }
  
  // 如果是相对路径但没有/uploads前缀，添加前缀
  if (!url.startsWith('/uploads/') && !url.startsWith('/')) {
    return `/uploads/${url}`;
  } else if (!url.startsWith('/')) {
    // 确保有前导斜杠
    return `/${url}`;
  }
  
  // 其他情况直接返回
  return url;
}

// 处理商品数据中的图片URL
function processGoodsData(goods) {
  if (!goods) return goods;
  
  // 如果是数组，处理每个商品
  if (Array.isArray(goods)) {
    return goods.map(item => {
      if (item.cover) {
        item.cover = processImageUrl(item.cover);
      }
      return item;
    });
  }
  
  // 处理单个商品
  if (goods.cover) {
    goods.cover = processImageUrl(goods.cover);
  }
  
  return goods;
}

/**
 * 获取商品列表（管理端）
 * GET /api/v1/admin/goods/list
 */
router.get('/list', verifyToken, goodsController.getGoodsList);

/**
 * 获取商品详情（管理端）
 * GET /api/v1/admin/goods/detail/:id
 */
router.get('/detail/:id', verifyToken, goodsController.getGoodsDetail);

/**
 * 创建商品（管理端）
 * POST /api/v1/admin/goods/create
 */
router.post('/create', verifyToken, goodsController.createGoods);

/**
 * 更新商品（管理端）
 * PUT /api/v1/admin/goods/update/:id
 */
router.put('/update/:id', verifyToken, goodsController.updateGoods);

/**
 * 删除商品（管理端）
 * DELETE /api/v1/admin/goods/delete/:id
 */
router.delete('/delete/:id', verifyToken, (req, res, next) => {
  console.log(`收到删除商品请求，ID: ${req.params.id}`);
  console.log(`请求路径: ${req.originalUrl}`);
  console.log(`请求方法: ${req.method}`);
  console.log(`请求头:`, req.headers);
  
  // 将请求传递给控制器
  goodsController.deleteGoods(req, res, next);
});

/**
 * 更新商品状态（管理端）
 * PUT /api/v1/admin/goods/status/:id
 */
router.put('/status/:id', verifyToken, goodsController.updateGoodsStatus);

// 路由占位
router.get('/', (req, res) => {
  return success(res, [], '商品路由已设置');
});

module.exports = router; 