const logger = require('../utils/logger');

/**
 * 日志中间件
 * 记录请求和响应信息
 */
function loggerMiddleware(req, res, next) {
  // 记录请求开始时间
  const start = new Date();
  
  // 生成唯一请求ID
  const requestId = `req-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  req.requestId = requestId;
  
  // 在请求处理完成后记录信息
  res.on('finish', () => {
    const duration = new Date() - start;
    
    // 记录基本请求信息
    const logInfo = {
      requestId,
      method: req.method,
      url: req.originalUrl || req.url,
      status: res.statusCode,
      duration: `${duration}ms`,
      userAgent: req.headers['user-agent'],
      ip: req.ip || req.headers['x-forwarded-for'] || req.connection.remoteAddress
    };
    
    // 根据状态码决定日志级别
    if (res.statusCode >= 500) {
      logger.error(`[${requestId}] 请求失败`, logInfo);
    } else if (res.statusCode >= 400) {
      logger.warn(`[${requestId}] 客户端错误`, logInfo);
    } else {
      logger.info(`[${requestId}] 请求成功`, logInfo);
    }
  });
  
  // 继续处理请求
  next();
}

module.exports = loggerMiddleware; 