const COS = require('cos-nodejs-sdk-v5');
const path = require('path');
const fs = require('fs');

// 创建腾讯云COS实例
const cos = new COS({
  SecretId: process.env.COS_SECRET_ID,
  SecretKey: process.env.COS_SECRET_KEY
});

const Bucket = process.env.COS_BUCKET || 'wifi-share-1234567890';
const Region = process.env.COS_REGION || 'ap-guangzhou';

module.exports = {
  // 上传文件到腾讯云COS
  uploadFile: (localFilePath, remoteFilePath) => {
    return new Promise((resolve, reject) => {
      cos.putObject({
        Bucket,
        Region,
        Key: remoteFilePath,
        Body: fs.createReadStream(localFilePath),
        ContentLength: fs.statSync(localFilePath).size
      }, (err, data) => {
        if (err) {
          reject(err);
          return;
        }
        resolve({
          url: `https://${Bucket}.cos.${Region}.myqcloud.com/${remoteFilePath}`,
          ...data
        });
      });
    });
  },
  
  // 从腾讯云COS删除文件
  deleteFile: (remoteFilePath) => {
    return new Promise((resolve, reject) => {
      cos.deleteObject({
        Bucket,
        Region,
        Key: remoteFilePath
      }, (err, data) => {
        if (err) {
          reject(err);
          return;
        }
        resolve(data);
      });
    });
  },
  
  // 生成临时预签名URL
  getSignedUrl: (remoteFilePath, expires = 3600) => {
    return cos.getObjectUrl({
      Bucket,
      Region,
      Key: remoteFilePath,
      Sign: true,
      Expires: expires
    });
  }
}; 