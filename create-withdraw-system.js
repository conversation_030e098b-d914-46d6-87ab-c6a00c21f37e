const mysql = require('mysql2/promise');

async function createWithdrawSystem() {
  let connection;
  
  try {
    // 连接数据库
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: 'wo587129955',
      database: 'mall'
    });
    
    console.log('🔧 创建完整的提现系统...\n');
    
    // 1. 创建提现方式配置表
    console.log('1️⃣ 创建提现方式配置表...');
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS withdraw_method (
        id int(11) NOT NULL AUTO_INCREMENT COMMENT '提现方式ID',
        method_code varchar(20) NOT NULL COMMENT '方式代码',
        method_name varchar(50) NOT NULL COMMENT '方式名称',
        icon varchar(255) DEFAULT NULL COMMENT '图标',
        min_amount decimal(10,2) NOT NULL DEFAULT '1.00' COMMENT '最小提现金额',
        max_amount decimal(10,2) NOT NULL DEFAULT '50000.00' COMMENT '最大提现金额',
        fee_type tinyint(1) NOT NULL DEFAULT '1' COMMENT '手续费类型：1按比例，2固定金额',
        fee_rate decimal(5,2) NOT NULL DEFAULT '0.00' COMMENT '手续费比例（百分比）',
        fee_amount decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '固定手续费金额',
        min_fee decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '最小手续费',
        max_fee decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '最大手续费',
        process_time varchar(50) DEFAULT NULL COMMENT '到账时间说明',
        is_enabled tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用',
        sort_order int(11) NOT NULL DEFAULT '0' COMMENT '排序',
        created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        updated_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
        PRIMARY KEY (id),
        UNIQUE KEY method_code (method_code)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='提现方式配置表'
    `);
    console.log('✅ 提现方式配置表创建成功');
    
    // 2. 创建用户提现账户表
    console.log('\n2️⃣ 创建用户提现账户表...');
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS user_withdraw_account (
        id int(11) NOT NULL AUTO_INCREMENT COMMENT '账户ID',
        user_id int(11) NOT NULL COMMENT '用户ID',
        method_code varchar(20) NOT NULL COMMENT '提现方式代码',
        account_name varchar(100) NOT NULL COMMENT '账户名称',
        account_number varchar(100) NOT NULL COMMENT '账户号码',
        account_holder varchar(50) NOT NULL COMMENT '账户持有人',
        bank_name varchar(100) DEFAULT NULL COMMENT '银行名称（银行卡专用）',
        bank_branch varchar(100) DEFAULT NULL COMMENT '开户行（银行卡专用）',
        id_card varchar(20) DEFAULT NULL COMMENT '身份证号',
        phone varchar(20) DEFAULT NULL COMMENT '手机号',
        is_default tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否默认账户',
        is_verified tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已验证',
        status tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0禁用，1启用',
        created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        updated_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
        PRIMARY KEY (id),
        KEY user_id (user_id),
        KEY method_code (method_code)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户提现账户表'
    `);
    console.log('✅ 用户提现账户表创建成功');
    
    // 3. 创建提现申请表（增强版）
    console.log('\n3️⃣ 创建增强版提现申请表...');
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS withdraw_application (
        id int(11) NOT NULL AUTO_INCREMENT COMMENT '申请ID',
        user_id int(11) NOT NULL COMMENT '用户ID',
        withdraw_no varchar(50) NOT NULL COMMENT '提现单号',
        method_code varchar(20) NOT NULL COMMENT '提现方式',
        account_id int(11) NOT NULL COMMENT '提现账户ID',
        amount decimal(10,2) NOT NULL COMMENT '申请金额',
        fee decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '手续费',
        actual_amount decimal(10,2) NOT NULL COMMENT '实际到账金额',
        balance_before decimal(10,2) NOT NULL COMMENT '提现前余额',
        balance_after decimal(10,2) NOT NULL COMMENT '提现后余额',
        status tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态：0待审核，1审核通过，2审核拒绝，3处理中，4已完成，5已取消',
        apply_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '申请时间',
        audit_user_id int(11) DEFAULT NULL COMMENT '审核人ID',
        audit_time datetime DEFAULT NULL COMMENT '审核时间',
        audit_remark varchar(255) DEFAULT NULL COMMENT '审核备注',
        process_time datetime DEFAULT NULL COMMENT '处理时间',
        complete_time datetime DEFAULT NULL COMMENT '完成时间',
        transaction_no varchar(100) DEFAULT NULL COMMENT '第三方交易号',
        failure_reason varchar(255) DEFAULT NULL COMMENT '失败原因',
        remark varchar(255) DEFAULT NULL COMMENT '申请备注',
        created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        updated_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
        PRIMARY KEY (id),
        UNIQUE KEY withdraw_no (withdraw_no),
        KEY user_id (user_id),
        KEY method_code (method_code),
        KEY status (status),
        KEY apply_time (apply_time)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='提现申请表'
    `);
    console.log('✅ 增强版提现申请表创建成功');
    
    // 4. 插入提现方式配置数据
    console.log('\n4️⃣ 插入提现方式配置数据...');
    await connection.execute(`
      INSERT INTO withdraw_method (method_code, method_name, icon, min_amount, max_amount, fee_type, fee_rate, fee_amount, min_fee, max_fee, process_time, is_enabled, sort_order) VALUES
      ('wechat', '微信零钱', '/assets/icons/wechat.png', 1.00, 20000.00, 1, 0.60, 0.00, 0.60, 25.00, '实时到账', 1, 1),
      ('alipay', '支付宝', '/assets/icons/alipay.png', 1.00, 50000.00, 1, 0.10, 0.00, 0.10, 25.00, '2小时内到账', 1, 2),
      ('bank_card', '银行卡', '/assets/icons/bank.png', 100.00, 50000.00, 2, 0.00, 2.00, 2.00, 25.00, '1-3个工作日', 1, 3)
      ON DUPLICATE KEY UPDATE 
        method_name = VALUES(method_name),
        min_amount = VALUES(min_amount),
        max_amount = VALUES(max_amount),
        fee_type = VALUES(fee_type),
        fee_rate = VALUES(fee_rate),
        fee_amount = VALUES(fee_amount),
        process_time = VALUES(process_time),
        updated_at = CURRENT_TIMESTAMP
    `);
    console.log('✅ 提现方式配置数据插入成功');
    
    // 5. 为测试用户添加提现账户
    console.log('\n5️⃣ 为测试用户添加提现账户...');
    await connection.execute(`
      INSERT INTO user_withdraw_account (user_id, method_code, account_name, account_number, account_holder, is_default, is_verified) VALUES
      (1, 'wechat', '微信零钱', 'oW20C7mVlW8e3W2AgUGtDTJeAbQU', '润生', 1, 1),
      (1, 'alipay', '支付宝账户', '138****8888', '润生', 0, 0),
      (1, 'bank_card', '中国银行储蓄卡', '6217****1234', '润生', 0, 0)
      ON DUPLICATE KEY UPDATE updated_at = CURRENT_TIMESTAMP
    `);
    console.log('✅ 测试用户提现账户添加成功');
    
    // 6. 创建提现限额控制表
    console.log('\n6️⃣ 创建提现限额控制表...');
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS withdraw_limit (
        id int(11) NOT NULL AUTO_INCREMENT COMMENT '限额ID',
        user_id int(11) NOT NULL COMMENT '用户ID',
        limit_date date NOT NULL COMMENT '限额日期',
        daily_used decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '当日已用额度',
        daily_limit decimal(10,2) NOT NULL DEFAULT '10000.00' COMMENT '当日限额',
        monthly_used decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '当月已用额度',
        monthly_limit decimal(10,2) NOT NULL DEFAULT '100000.00' COMMENT '当月限额',
        withdraw_count int(11) NOT NULL DEFAULT '0' COMMENT '当日提现次数',
        max_daily_count int(11) NOT NULL DEFAULT '3' COMMENT '每日最大提现次数',
        created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        updated_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
        PRIMARY KEY (id),
        UNIQUE KEY user_date (user_id, limit_date),
        KEY user_id (user_id)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='提现限额控制表'
    `);
    console.log('✅ 提现限额控制表创建成功');
    
    // 7. 插入测试提现申请
    console.log('\n7️⃣ 插入测试提现申请...');
    const withdrawNo = 'WD' + Date.now();
    await connection.execute(`
      INSERT INTO withdraw_application (user_id, withdraw_no, method_code, account_id, amount, fee, actual_amount, balance_before, balance_after, status, remark) VALUES
      (1, ?, 'wechat', 1, 50.00, 0.60, 49.40, 66.80, 16.80, 0, '测试提现申请')
    `, [withdrawNo]);
    console.log('✅ 测试提现申请插入成功');
    
    // 8. 验证创建结果
    console.log('\n8️⃣ 验证创建结果...');
    
    // 检查提现方式
    const [methods] = await connection.execute(`
      SELECT method_code, method_name, min_amount, max_amount, fee_rate, process_time, is_enabled 
      FROM withdraw_method 
      ORDER BY sort_order
    `);
    console.log('\n📋 提现方式配置:');
    console.table(methods);
    
    // 检查用户账户
    const [accounts] = await connection.execute(`
      SELECT id, method_code, account_name, account_number, account_holder, is_default, is_verified 
      FROM user_withdraw_account 
      WHERE user_id = 1
    `);
    console.log('\n💳 用户提现账户:');
    console.table(accounts);
    
    // 检查提现申请
    const [applications] = await connection.execute(`
      SELECT id, withdraw_no, method_code, amount, fee, actual_amount, status, apply_time 
      FROM withdraw_application 
      WHERE user_id = 1
    `);
    console.log('\n📝 提现申请记录:');
    console.table(applications);
    
    console.log('\n🎉 完整的提现系统创建完成！');
    console.log('\n✅ 功能特性:');
    console.log('- 支持微信、支付宝、银行卡三种提现方式');
    console.log('- 灵活的手续费配置（按比例或固定金额）');
    console.log('- 完整的审核流程管理');
    console.log('- 提现限额控制');
    console.log('- 用户多账户管理');
    console.log('- 详细的状态跟踪');
    
  } catch (error) {
    console.error('❌ 创建失败:', error.message);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// 运行创建
createWithdrawSystem();
