/**
 * 文件上传测试脚本
 * 
 * 使用方法:
 * 1. 确保服务器已启动
 * 2. 在终端中执行 node test-upload.js
 * 
 * 注意: 此脚本需要安装 axios 和 form-data 依赖
 */

const axios = require('axios');
const fs = require('fs');
const path = require('path');
const FormData = require('form-data');

// 测试配置
const config = {
  baseUrl: 'http://localhost:4000/api/v1',
  testImagePath: path.join(__dirname, 'test-image.jpg'),
  admin: {
    username: 'admin',
    password: 'wo587129955'  // 用户提供的密码
  }
};

// 创建测试图片
function createTestImage() {
  // 这个函数只是将一个示例图片数据写入文件
  // 在实际使用时，你可以使用一个真实的测试图片文件
  
  console.log('创建测试图片...');
  
  // 简单的JPG文件头部(不是有效的图片，仅用于测试)
  const jpgHeader = Buffer.from([
    0xFF, 0xD8, 0xFF, 0xE0, 0x00, 0x10, 0x4A, 0x46,
    0x49, 0x46, 0x00, 0x01, 0x01, 0x01, 0x00, 0x48,
    0x00, 0x48, 0x00, 0x00, 0xFF, 0xDB, 0x00, 0x43,
    0x00, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF
  ]);
  
  try {
    fs.writeFileSync(config.testImagePath, jpgHeader);
    console.log(`测试图片已创建: ${config.testImagePath}`);
    return true;
  } catch (error) {
    console.error('创建测试图片失败:', error.message);
    return false;
  }
}

// 登录并获取令牌
async function login() {
  try {
    console.log('正在登录...');
    const response = await axios.post(`${config.baseUrl}/admin/auth/login`, {
      username: config.admin.username,
      password: config.admin.password
    });
    
    if (response.data && (response.data.code === 200 || response.data.status === 'success')) {
      const token = response.data.data && response.data.data.token;
      if (token) {
        console.log('登录成功，获取到令牌');
        return token;
      }
    }
    
    console.error('登录失败:', response.data);
    return null;
  } catch (error) {
    console.error('登录请求出错:', error.message);
    if (error.response) {
      console.error('错误详情:', error.response.data);
    }
    return null;
  }
}

// 测试单文件上传
async function testSingleUpload(token) {
  try {
    console.log('\n测试单文件上传...');
    
    // 确保测试图片存在
    if (!fs.existsSync(config.testImagePath)) {
      if (!createTestImage()) {
        return false;
      }
    }
    
    // 创建表单数据
    const formData = new FormData();
    formData.append('file', fs.createReadStream(config.testImagePath));
    
    // 发送请求
    const response = await axios.post(
      `${config.baseUrl}/admin/upload`,
      formData,
      {
        headers: {
          ...formData.getHeaders(),
          'Authorization': `Bearer ${token}`
        }
      }
    );
    
    // 检查响应
    if (response.data && (response.data.code === 200 || response.data.status === 'success')) {
      console.log('单文件上传成功!');
      console.log('上传文件URL:', response.data.data.url);
      return response.data.data;
    } else {
      console.error('单文件上传失败:', response.data);
      return null;
    }
  } catch (error) {
    console.error('单文件上传请求出错:', error.message);
    if (error.response) {
      console.error('错误详情:', error.response.data);
    }
    return null;
  }
}

// 测试删除文件
async function testDeleteFile(token, filename) {
  try {
    console.log('\n测试删除文件...');
    
    if (!filename) {
      console.error('未提供文件名，无法测试删除');
      return false;
    }
    
    // 发送请求
    const response = await axios.delete(
      `${config.baseUrl}/admin/upload/${filename}`,
      {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      }
    );
    
    // 检查响应
    if (response.data && (response.data.code === 200 || response.data.status === 'success')) {
      console.log('文件删除成功!');
      return true;
    } else {
      console.error('文件删除失败:', response.data);
      return false;
    }
  } catch (error) {
    console.error('删除文件请求出错:', error.message);
    if (error.response) {
      console.error('错误详情:', error.response.data);
    }
    return false;
  }
}

// 运行所有测试
async function runTests() {
  console.log('===== 开始上传功能测试 =====');
  
  // 登录
  const token = await login();
  if (!token) {
    console.error('登录失败，终止测试');
    return;
  }
  
  // 单文件上传测试
  const uploadResult = await testSingleUpload(token);
  if (!uploadResult) {
    console.error('单文件上传测试失败，终止后续测试');
    return;
  }
  
  // 删除文件测试
  await testDeleteFile(token, uploadResult.filename);
  
  console.log('\n===== 测试完成 =====');
}

// 执行测试
runTests(); 