# 商品分类图标说明

## 图标文件列表

| 图标文件 | 对应分类 | Element UI图标名 |
|---------|----------|------------------|
| mobile-phone.png | 数码产品 | el-icon-mobile-phone |
| house.png | 家居用品 | el-icon-house |
| shopping-bag.png | 服装鞋帽 | el-icon-shopping-bag-1 |
| coffee-cup.png | 食品饮料 | el-icon-coffee-cup |
| reading.png | 图书文具 | el-icon-reading |
| bicycle.png | 运动户外 | el-icon-bicycle |
| default.png | 默认图标 | 通用默认图标 |

## 图标规格

- **尺寸**: 64x64px
- **格式**: PNG
- **背景**: 透明
- **风格**: 简约线条图标

## 使用方法

```javascript
// 在页面中引入图标映射工具
const { getIconPath, processCategoryIcons } = require('../../utils/iconMapping');

// 处理分类数据
const processedCategories = processCategoryIcons(categories);

// 获取单个图标路径
const iconPath = getIconPath('el-icon-mobile-phone');
```

## 注意事项

1. 所有图标都使用本地路径，避免HTTP协议问题
2. 图标文件需要放在 `/assets/icons/category/` 目录下
3. 如果缺少对应图标文件，会自动使用默认图标
4. 支持HTTP到HTTPS的自动转换

## 临时解决方案

如果图标文件暂时缺失，可以：
1. 使用默认图标占位
2. 使用文字标识代替图标
3. 从图标库下载对应图标

## 图标来源建议

推荐使用以下图标库：
- Iconfont (阿里巴巴矢量图标库)
- Feather Icons
- Heroicons
- Tabler Icons
