const mysql = require('mysql2/promise');

// 数据库配置
const dbConfig = {
  host: 'localhost',
  user: 'root',
  password: 'wo587129955',
  database: 'wifi_share_system'
};

async function checkAndInitData() {
  let connection;
  
  try {
    console.log('🔗 连接数据库...');
    connection = await mysql.createConnection(dbConfig);
    
    // 检查商品数据
    console.log('\n📦 检查商品数据...');
    const [goods] = await connection.execute('SELECT COUNT(*) as count FROM goods');
    console.log(`商品数量: ${goods[0].count}`);
    
    if (goods[0].count === 0) {
      console.log('📝 插入测试商品数据...');
      await initGoodsData(connection);
    } else {
      console.log('✅ 商品数据已存在');
      // 显示现有商品
      const [goodsList] = await connection.execute(
        'SELECT id, name, price, stock FROM goods LIMIT 10'
      );
      console.log('现有商品:');
      goodsList.forEach(item => {
        console.log(`  ID: ${item.id}, 名称: ${item.name}, 价格: ¥${item.price}, 库存: ${item.stock}`);
      });
    }
    
    // 检查地址数据
    console.log('\n🏠 检查地址数据...');
    const [addresses] = await connection.execute('SELECT COUNT(*) as count FROM user_address');
    console.log(`地址数量: ${addresses[0].count}`);
    
    if (addresses[0].count === 0) {
      console.log('📝 插入测试地址数据...');
      await initAddressData(connection);
    } else {
      console.log('✅ 地址数据已存在');
      // 显示现有地址
      const [addressList] = await connection.execute(
        'SELECT id, user_id, name, phone, province, city, district FROM user_address LIMIT 5'
      );
      console.log('现有地址:');
      addressList.forEach(addr => {
        console.log(`  ID: ${addr.id}, 用户: ${addr.user_id}, 姓名: ${addr.name}, 地址: ${addr.province}${addr.city}${addr.district}`);
      });
    }
    
    // 检查用户数据
    console.log('\n👤 检查用户数据...');
    const [users] = await connection.execute('SELECT COUNT(*) as count FROM user');
    console.log(`用户数量: ${users[0].count}`);
    
    if (users[0].count === 0) {
      console.log('📝 插入测试用户数据...');
      await initUserData(connection);
    } else {
      console.log('✅ 用户数据已存在');
    }
    
    console.log('\n🎉 数据检查完成！');
    
  } catch (error) {
    console.error('❌ 检查数据失败:', error);
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔚 数据库连接已关闭');
    }
  }
}

async function initGoodsData(connection) {
  const testGoods = [
    {
      name: '苹果iPhone 15',
      description: '最新款苹果手机，性能强劲',
      price: 5999.00,
      original_price: 6999.00,
      stock: 100,
      category_id: 1,
      cover: '/assets/images/iphone15.jpg',
      images: JSON.stringify(['/assets/images/iphone15-1.jpg', '/assets/images/iphone15-2.jpg']),
      status: 1
    },
    {
      name: '华为Mate 60',
      description: '华为旗舰手机，拍照出色',
      price: 4999.00,
      original_price: 5999.00,
      stock: 80,
      category_id: 1,
      cover: '/assets/images/mate60.jpg',
      images: JSON.stringify(['/assets/images/mate60-1.jpg']),
      status: 1
    },
    {
      name: '小米14',
      description: '小米高性能手机，性价比之选',
      price: 3999.00,
      original_price: 4499.00,
      stock: 120,
      category_id: 1,
      cover: '/assets/images/mi14.jpg',
      images: JSON.stringify(['/assets/images/mi14-1.jpg']),
      status: 1
    },
    {
      name: 'MacBook Pro',
      description: '苹果笔记本电脑，专业办公',
      price: 12999.00,
      original_price: 14999.00,
      stock: 50,
      category_id: 2,
      cover: '/assets/images/macbook.jpg',
      images: JSON.stringify(['/assets/images/macbook-1.jpg']),
      status: 1
    }
  ];
  
  for (const goods of testGoods) {
    await connection.execute(
      `INSERT INTO goods (name, description, price, original_price, stock, category_id, cover, images, status, created_at, updated_at)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())`,
      [
        goods.name,
        goods.description,
        goods.price,
        goods.original_price,
        goods.stock,
        goods.category_id,
        goods.cover,
        goods.images,
        goods.status
      ]
    );
    console.log(`✅ 插入商品: ${goods.name} - ¥${goods.price}`);
  }
}

async function initAddressData(connection) {
  const testAddresses = [
    {
      user_id: 1,
      name: '张三',
      phone: '13800138000',
      province: '广东省',
      city: '深圳市',
      district: '南山区',
      address: '科技园南区深南大道10000号',
      is_default: 1
    },
    {
      user_id: 1,
      name: '张三',
      phone: '13800138000',
      province: '广东省',
      city: '深圳市',
      district: '福田区',
      address: '华强北路1000号',
      is_default: 0
    }
  ];
  
  for (const address of testAddresses) {
    await connection.execute(
      `INSERT INTO user_address (user_id, name, phone, province, city, district, address, is_default, created_at, updated_at)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())`,
      [
        address.user_id,
        address.name,
        address.phone,
        address.province,
        address.city,
        address.district,
        address.address,
        address.is_default
      ]
    );
    console.log(`✅ 插入地址: ${address.name} - ${address.province}${address.city}${address.district}`);
  }
}

async function initUserData(connection) {
  const testUsers = [
    {
      id: 1,
      username: 'testuser',
      phone: '13800138000',
      nickname: '测试用户',
      avatar: '/assets/images/avatar.jpg',
      status: 1
    }
  ];
  
  for (const user of testUsers) {
    await connection.execute(
      `INSERT INTO user (id, username, phone, nickname, avatar, status, created_at, updated_at)
       VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW())
       ON DUPLICATE KEY UPDATE updated_at = NOW()`,
      [
        user.id,
        user.username,
        user.phone,
        user.nickname,
        user.avatar,
        user.status
      ]
    );
    console.log(`✅ 插入用户: ${user.nickname} (${user.phone})`);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  checkAndInitData();
}

module.exports = { checkAndInitData };
