// 添加测试收入数据
const mysql = require('mysql2/promise');

async function addTestIncomeData() {
  let connection;
  
  try {
    console.log('🔧 开始添加测试收入数据...');
    
    // 创建数据库连接
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: 'root',
      database: 'wifi_share'
    });
    
    console.log('✅ 数据库连接成功');
    
    // 1. 检查用户是否存在
    const [users] = await connection.execute('SELECT id, balance FROM user WHERE id = 1');
    
    if (users.length === 0) {
      console.log('❌ 用户ID=1不存在，先创建用户...');
      
      // 创建测试用户
      await connection.execute(`
        INSERT INTO user (id, openid, nickname, avatar, phone, balance, created_at, updated_at)
        VALUES (1, 'test_openid_001', '测试用户', '', '13800138000', 66.80, NOW(), NOW())
      `);
      
      console.log('✅ 测试用户创建成功');
    } else {
      console.log('✅ 用户ID=1已存在，余额:', users[0].balance);
      
      // 更新用户余额
      await connection.execute('UPDATE user SET balance = 66.80 WHERE id = 1');
      console.log('✅ 用户余额已更新为66.80');
    }
    
    // 2. 清除旧的测试数据
    await connection.execute('DELETE FROM profit_log WHERE user_id = 1');
    console.log('✅ 清除旧的收入记录');
    
    // 3. 添加WiFi分享收入记录
    const wifiIncomeRecords = [
      {
        amount: 15.50,
        title: 'WiFi分享收入',
        description: '用户使用WiFi产生的分润收入',
        created_at: '2025-01-29 10:30:00'
      },
      {
        amount: 12.80,
        title: 'WiFi分享收入',
        description: '用户使用WiFi产生的分润收入',
        created_at: '2025-01-29 14:20:00'
      }
    ];
    
    for (const record of wifiIncomeRecords) {
      await connection.execute(`
        INSERT INTO profit_log (
          user_id, amount, source_type, source_id, title, description, 
          status, created_at, updated_at
        ) VALUES (1, ?, 'wifi_share', 1, ?, ?, 1, ?, NOW())
      `, [record.amount, record.title, record.description, record.created_at]);
    }
    
    console.log('✅ WiFi分享收入记录添加完成');
    
    // 4. 添加团队收入记录
    const teamIncomeRecords = [
      {
        amount: 25.00,
        title: '推荐奖励',
        description: '推荐新用户获得的奖励',
        created_at: '2025-01-28 16:45:00'
      }
    ];
    
    for (const record of teamIncomeRecords) {
      await connection.execute(`
        INSERT INTO profit_log (
          user_id, amount, source_type, source_id, title, description,
          status, created_at, updated_at
        ) VALUES (1, ?, 'referral', 2, ?, ?, 1, ?, NOW())
      `, [record.amount, record.title, record.description, record.created_at]);
    }
    
    console.log('✅ 团队收入记录添加完成');
    
    // 5. 添加广告收入记录
    const adIncomeRecords = [
      {
        amount: 8.30,
        title: '广告点击收入',
        description: '用户点击广告产生的收入',
        created_at: '2025-01-29 11:15:00'
      },
      {
        amount: 5.20,
        title: '广告点击收入',
        description: '用户点击广告产生的收入',
        created_at: '2025-01-29 15:30:00'
      }
    ];
    
    for (const record of adIncomeRecords) {
      await connection.execute(`
        INSERT INTO profit_log (
          user_id, amount, source_type, source_id, title, description,
          status, created_at, updated_at
        ) VALUES (1, ?, 'advertisement', 3, ?, ?, 1, ?, NOW())
      `, [record.amount, record.title, record.description, record.created_at]);
    }
    
    console.log('✅ 广告收入记录添加完成');
    
    // 6. 验证数据
    const [totalStats] = await connection.execute(`
      SELECT
        COUNT(*) as record_count,
        SUM(amount) as total_amount,
        SUM(CASE WHEN source_type = 'wifi_share' THEN amount ELSE 0 END) as wifi_income,
        SUM(CASE WHEN source_type = 'referral' THEN amount ELSE 0 END) as team_income,
        SUM(CASE WHEN source_type = 'advertisement' THEN amount ELSE 0 END) as ad_income,
        SUM(CASE WHEN source_type = 'goods_sale' THEN amount ELSE 0 END) as goods_income
      FROM profit_log
      WHERE user_id = 1
    `);
    
    console.log('\n📊 数据验证结果:');
    console.log('- 记录总数:', totalStats[0].record_count);
    console.log('- 总收入:', totalStats[0].total_amount);
    console.log('- WiFi收入:', totalStats[0].wifi_income);
    console.log('- 团队收入:', totalStats[0].team_income);
    console.log('- 广告收入:', totalStats[0].ad_income);
    console.log('- 商品收入:', totalStats[0].goods_income);
    
    // 7. 检查今日收入
    const [todayStats] = await connection.execute(`
      SELECT
        SUM(amount) as today_income,
        SUM(CASE WHEN source_type = 'wifi_share' THEN amount ELSE 0 END) as today_wifi,
        SUM(CASE WHEN source_type = 'advertisement' THEN amount ELSE 0 END) as today_ad
      FROM profit_log
      WHERE user_id = 1 AND DATE(created_at) = CURDATE()
    `);
    
    console.log('\n📅 今日收入统计:');
    console.log('- 今日总收入:', todayStats[0].today_income);
    console.log('- 今日WiFi收入:', todayStats[0].today_wifi);
    console.log('- 今日广告收入:', todayStats[0].today_ad);
    
    console.log('\n🎉 测试数据添加完成！');
    console.log('\n现在前端钱包页面应该显示:');
    console.log('- 账户余额: ¥66.80');
    console.log('- WiFi分享收益: ¥28.30');
    console.log('- 团队收益: ¥25.00');
    console.log('- 广告流量收益: ¥13.50');
    console.log('- 商城订单收益: ¥0.00');
    
  } catch (error) {
    console.error('❌ 添加测试数据失败:', error);
  } finally {
    if (connection) {
      await connection.end();
      console.log('✅ 数据库连接已关闭');
    }
  }
}

// 运行脚本
addTestIncomeData();
