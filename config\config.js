// config/config.js
// 全局配置

module.exports = {
  // 环境配置
  isDev: false, // 关闭模拟数据，使用真实登录
  // API配置
  api: {
    // 直接指向后端服务，不包含/api/v1/client前缀
    baseUrl: 'http://localhost:4000',
    // 客户端API基础路径
    clientPath: '/api/v1/client',
    timeout: 10000
  },

  // API基础URL，用于所有接口
  apiBaseUrl: 'http://localhost:4000',

  // 图片服务器配置
  imageServer: {
    // 图片服务器基础URL，用于访问上传的图片
    baseUrl: 'http://localhost:4000',
    // 默认图片前缀，用于处理相对路径
    prefix: ''
  },
  
  // 存储键名配置
  storageKeys: {
    token: 'token',
    userInfo: 'userInfo',
    cartItems: 'cartItems',
    searchHistory: 'searchHistory'
  },
  
  // 分页配置
  pagination: {
    pageSize: 10,
    maxPageSize: 50
  },
  
  // 图片配置
  image: {
    defaultAvatar: 'https://thirdwx.qlogo.cn/mmopen/vi_32/POgEwh4mIHO4nibH0KlMECNjjGxQUq24ZEaGT4poC6icRiccVGKSyXwibcPq4BWmiaIGuG1icwxaQX6grC9VemZoJ8rg/132',
    placeholder: '/assets/images/goods-placeholder.jpg',
    uploadMaxSize: 5 * 1024 * 1024, // 5MB
    allowedTypes: ['jpg', 'jpeg', 'png', 'gif']
  },
  
  // 订单状态配置
  orderStatus: {
    pending: 1,     // 待付款
    paid: 2,        // 已付款
    shipped: 3,     // 已发货
    received: 4,    // 已收货
    completed: 5,   // 已完成
    cancelled: 0    // 已取消
  },
  
  // 订单状态文本
  orderStatusText: {
    0: '已取消',
    1: '待付款',
    2: '待发货',
    3: '待收货',
    4: '已收货',
    5: '已完成'
  },
  
  // 支付方式配置
  paymentMethods: {
    wechat: 'wechat',
    alipay: 'alipay',
    balance: 'balance'
  },
  
  // 支付方式文本
  paymentMethodText: {
    wechat: '微信支付',
    alipay: '支付宝',
    balance: '余额支付'
  },
  
  // WiFi加密类型
  wifiSecurityTypes: {
    none: 'nopass',
    wep: 'WEP',
    wpa: 'WPA'
  },
  
  // 广告位类型
  adTypes: {
    banner: 'banner',     // 横幅广告
    card: 'card',         // 卡片广告
    popup: 'popup'        // 弹窗广告
  }
} 