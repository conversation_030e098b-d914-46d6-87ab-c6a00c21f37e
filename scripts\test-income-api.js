// 测试收入统计API
const axios = require('axios');
const jwt = require('jsonwebtoken');

async function testIncomeAPI() {
  try {
    console.log('🧪 测试收入统计API...\n');
    
    // 1. 生成测试token
    const testUser = {
      id: 1,
      openid: 'test_openid_001',
      role: 'user'
    };
    
    const token = jwt.sign(
      testUser,
      'wifi_share_secret_key',
      { expiresIn: '24h' }
    );
    
    console.log('1️⃣ 生成的测试token:', token.substring(0, 50) + '...\n');
    
    // 2. 测试收入统计接口
    console.log('2️⃣ 测试收入统计接口...');
    try {
      const response = await axios.get('http://localhost:4000/api/v1/client/income/stats', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        timeout: 10000
      });
      
      console.log('✅ API调用成功:');
      console.log('状态码:', response.status);
      console.log('响应数据:', JSON.stringify(response.data, null, 2));
      
      // 分析响应数据
      const res = response.data;
      if (res.success && res.data) {
        const data = res.data;
        
        console.log('\n📊 前端钱包页面数据映射:');
        console.log('='.repeat(50));
        
        // 直接字段映射
        console.log('💰 直接字段:');
        console.log('- 账户余额 (balance):', data.balance || '未设置');
        console.log('- WiFi收入 (wifi_income):', data.wifi_income || '未设置');
        console.log('- 团队收入 (team_income):', data.team_income || '未设置');
        console.log('- 广告收入 (ad_income):', data.ad_income || '未设置');
        console.log('- 商品收入 (goods_income):', data.goods_income || '未设置');
        console.log('- 总收入 (total_income):', data.total_income || '未设置');
        console.log('- 今日收入 (today_income):', data.today_income || '未设置');
        console.log('- 本月收入 (month_income):', data.month_income || '未设置');
        
        // 嵌套字段映射
        if (data.total) {
          console.log('\n📦 嵌套total字段:');
          console.log('- total.balance:', data.total.balance || '未设置');
          console.log('- total.wifi_income:', data.total.wifi_income || '未设置');
          console.log('- total.team_income:', data.total.team_income || '未设置');
          console.log('- total.ad_income:', data.total.ad_income || '未设置');
          console.log('- total.goods_income:', data.total.goods_income || '未设置');
        }
        
        // 前端数据映射验证
        console.log('\n🎯 前端钱包页面最终显示:');
        console.log('='.repeat(50));
        
        const frontendData = {
          balance: data.balance || data.total?.balance || '0.00',
          incomeStats: {
            wifi: data.wifi_income || data.total?.wifi_income || '0.00',
            team: data.team_income || data.total?.team_income || '0.00',
            ads: data.ad_income || data.total?.ad_income || '0.00',
            mall: data.goods_income || data.total?.goods_income || '0.00'
          }
        };
        
        console.log('- 账户余额: ¥' + frontendData.balance);
        console.log('- WiFi分享收益: ¥' + frontendData.incomeStats.wifi + ' (📶)');
        console.log('- 团队收益: ¥' + frontendData.incomeStats.team + ' (👥)');
        console.log('- 广告流量收益: ¥' + frontendData.incomeStats.ads + ' (📺)');
        console.log('- 商城订单收益: ¥' + frontendData.incomeStats.mall + ' (🛒)');
        
        // 验证数据是否为真实数据
        const hasRealData = parseFloat(frontendData.balance) > 0 || 
                           Object.values(frontendData.incomeStats).some(val => parseFloat(val) > 0);
        
        if (hasRealData) {
          console.log('\n✅ 检测到真实数据！前端应该能正常显示');
        } else {
          console.log('\n⚠️ 所有数据都为0，可能需要添加测试数据');
        }
        
        // 生成前端调试代码
        console.log('\n💻 前端调试代码:');
        console.log('```javascript');
        console.log('// 在钱包页面控制台中执行:');
        console.log('console.log("API返回数据:", ' + JSON.stringify(frontendData, null, 2) + ');');
        console.log('```');
        
      } else {
        console.log('❌ API响应格式不正确:', res);
      }
      
    } catch (apiError) {
      console.log('❌ API调用失败:');
      if (apiError.response) {
        console.log('状态码:', apiError.response.status);
        console.log('错误信息:', JSON.stringify(apiError.response.data, null, 2));
        
        if (apiError.response.status === 401) {
          console.log('🔐 认证失败，可能是token无效');
        } else if (apiError.response.status === 404) {
          console.log('🔍 接口不存在，请检查API路径');
        } else if (apiError.response.status === 500) {
          console.log('💥 服务器内部错误，请检查后端日志');
        }
      } else if (apiError.request) {
        console.log('🌐 网络请求失败，后端服务可能未启动');
        console.log('请确保后端服务运行在 http://localhost:4000');
      } else {
        console.log('⚙️ 请求配置错误:', apiError.message);
      }
    }
    
    // 3. 测试后端服务状态
    console.log('\n3️⃣ 测试后端服务状态...');
    try {
      const healthResponse = await axios.get('http://localhost:4000/health', {
        timeout: 5000
      });
      
      console.log('✅ 后端服务正常运行');
      console.log('健康检查响应:', healthResponse.data);
      
    } catch (healthError) {
      console.log('❌ 后端服务可能未启动');
      console.log('请运行: npm run dev');
    }
    
    // 4. 生成修复建议
    console.log('\n🔧 修复建议:');
    console.log('1. 确保后端服务已启动: npm run dev');
    console.log('2. 运行测试数据脚本: node scripts/add-test-income-data.js');
    console.log('3. 检查数据库连接和数据');
    console.log('4. 验证前端API配置');
    console.log('5. 检查用户登录状态和token');
    
  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error.message);
  }
}

// 运行测试
testIncomeAPI();
