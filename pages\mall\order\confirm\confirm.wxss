/* pages/mall/order/confirm/confirm.wxss */
.confirm-container {
  padding-bottom: 160rpx;
  background-color: #f5f5f5;
}

/* 收货地址 */
.address-section {
  background-color: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;
  position: relative;
  display: flex;
  align-items: center;
}

.address-info {
  flex: 1;
}

.address-contact {
  margin-bottom: 10rpx;
}

.address-name {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  margin-right: 20rpx;
}

.address-phone {
  font-size: 28rpx;
  color: #666;
}

.address-detail {
  font-size: 28rpx;
  color: #666;
  line-height: 1.4;
}

.address-empty {
  color: #999;
  font-size: 28rpx;
  padding: 20rpx 0;
}

.address-arrow {
  color: #999;
  font-size: 28rpx;
  margin-left: 20rpx;
}

/* 商品清单 */
.goods-section {
  background-color: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 20rpx;
}

.goods-list {
  
}

.goods-item {
  display: flex;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #eee;
}

.goods-item:last-child {
  border-bottom: none;
}

.goods-image {
  width: 160rpx;
  height: 160rpx;
  border-radius: 8rpx;
  background-color: #f5f5f5;
  margin-right: 20rpx;
}

.goods-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.goods-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.goods-spec {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 10rpx;
}

.goods-price-quantity {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.goods-price {
  font-size: 28rpx;
  color: #ff4d4f;
  font-weight: 500;
}

.goods-quantity {
  font-size: 26rpx;
  color: #999;
}

/* 配送方式 */
.delivery-section {
  background-color: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.delivery-options {

}

.delivery-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  position: relative;
  min-height: 80rpx;
  cursor: pointer;
  transition: background-color 0.2s;
}

.delivery-option:last-child {
  border-bottom: none;
}

.delivery-option:active {
  background-color: #f5f5f5;
}

.delivery-option.active {
  color: #07c160;
}

.delivery-info {
  flex: 1;
}

.delivery-name {
  font-size: 28rpx;
  display: block;
}

.delivery-desc {
  font-size: 24rpx;
  color: #999;
  display: block;
  margin-top: 8rpx;
}

.delivery-threshold {
  font-size: 22rpx;
  color: #ff4d4f;
  display: block;
  margin-top: 4rpx;
}

.delivery-price {
  display: flex;
  align-items: center;
  position: relative;
}

.delivery-fee {
  font-size: 28rpx;
  color: #ff4d4f;
  margin-right: 20rpx;
}

.delivery-check {
  color: #07c160;
  font-weight: bold;
  font-size: 32rpx;
}

/* 支付方式 */
.payment-section {
  background-color: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.payment-options {
  
}

.payment-option {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  position: relative;
  min-height: 80rpx;
  cursor: pointer;
  transition: background-color 0.2s;
}

.payment-option:active {
  background-color: #f5f5f5;
}

.payment-option.active {
  color: #07c160;
}

.payment-icon {
  margin-right: 20rpx;
  font-size: 32rpx;
}

.payment-name {
  font-size: 28rpx;
}

.payment-check {
  position: absolute;
  right: 0;
  color: #07c160;
  font-weight: bold;
}

/* 优惠券 */
.coupon-section {
  background-color: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.coupon-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.coupon-text {
  font-size: 28rpx;
  color: #333;
}

.coupon-arrow {
  color: #999;
  font-size: 28rpx;
}

/* 订单备注 */
.remark-section {
  background-color: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.remark-input-box {
  background-color: #f5f5f5;
  border-radius: 8rpx;
  padding: 20rpx;
}

.remark-input {
  width: 100%;
  height: 120rpx;
  font-size: 28rpx;
  color: #333;
}

/* 广告容器 */
.ad-container {
  margin: 20rpx;
  border-radius: 12rpx;
  overflow: hidden;
}

/* 底部结算栏 */
.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
  padding: 20rpx 30rpx;
}

.price-info {
  margin-bottom: 20rpx;
}

.price-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10rpx;
  font-size: 26rpx;
  color: #666;
}

.price-item.total {
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
}

.price-value {
  color: #ff4d4f;
}

.submit-btn {
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  background-color: #07c160;
  color: #fff;
  border-radius: 40rpx;
  font-size: 30rpx;
}

.submit-btn.disabled {
  background-color: #ccc;
}

/* 加载状态 */
.loading-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999;
}

.loading-text {
  color: #999;
  font-size: 28rpx;
} 