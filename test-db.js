console.log('开始测试数据库连接...');

try {
  const mysql = require('mysql2');
  console.log('mysql2 模块加载成功');
  
  const connection = mysql.createConnection({
    host: 'localhost',
    user: 'root',
    password: 'wo587129955',
    database: 'mall'
  });
  
  console.log('数据库连接对象创建成功');
  
  connection.query('SELECT COUNT(*) as count FROM orders', (error, results) => {
    if (error) {
      console.error('查询失败:', error);
    } else {
      console.log('查询成功，订单总数:', results[0].count);
      
      // 查询最近的订单
      connection.query(
        'SELECT id, order_no, user_id, status, total_amount, created_at FROM orders ORDER BY created_at DESC LIMIT 5',
        (error, results) => {
          if (error) {
            console.error('查询订单失败:', error);
          } else {
            console.log('最近的订单:', results);
            
            // 查找测试订单
            connection.query(
              'SELECT id, order_no, user_id, status, total_amount FROM orders WHERE total_amount = 1065.00',
              (error, results) => {
                if (error) {
                  console.error('查询测试订单失败:', error);
                } else {
                  console.log('找到的测试订单:', results);
                  
                  if (results.length > 0) {
                    const testOrder = results[0];
                    console.log(`准备删除测试订单: ID=${testOrder.id}, 订单号=${testOrder.order_no}`);
                    
                    // 删除订单商品
                    connection.query(
                      'DELETE FROM order_goods WHERE order_id = ?',
                      [testOrder.id],
                      (error) => {
                        if (error) {
                          console.error('删除订单商品失败:', error);
                        } else {
                          console.log('订单商品删除成功');
                          
                          // 删除订单
                          connection.query(
                            'DELETE FROM orders WHERE id = ?',
                            [testOrder.id],
                            (error) => {
                              if (error) {
                                console.error('删除订单失败:', error);
                              } else {
                                console.log('测试订单删除成功！');
                              }
                              connection.end();
                            }
                          );
                        }
                      }
                    );
                  } else {
                    console.log('未找到金额为1065.00的测试订单');
                    connection.end();
                  }
                }
              }
            );
          }
        }
      );
    }
  });
  
} catch (error) {
  console.error('脚本执行失败:', error);
}
