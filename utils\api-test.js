// utils/api-test.js
// API连接测试工具

const request = require('./request.js')
const config = require('../config/config.js')

/**
 * 测试后端服务连接
 */
const testBackendConnection = async () => {
  try {
    console.log('🔍 测试后端服务连接...')
    console.log('📍 服务地址:', config.api.baseUrl)
    
    // 尝试访问后端服务的基础路径
    const response = await request.get('/health', {}, {
      showLoading: false,
      showError: false
    })
    
    console.log('✅ 后端服务连接成功:', response)
    return true
  } catch (error) {
    console.error('❌ 后端服务连接失败:', error.message)
    
    // 提供详细的错误信息和解决建议
    let errorMsg = '后端服务连接失败'
    let suggestions = []
    
    if (error.message.includes('网络连接失败')) {
      errorMsg = '无法连接到后端服务'
      suggestions = [
        '1. 检查后端服务是否已启动（npm start 或 node app.js）',
        '2. 检查服务端口是否为4000',
        '3. 检查网络连接是否正常',
        '4. 检查防火墙设置'
      ]
    } else if (error.message.includes('timeout')) {
      errorMsg = '连接后端服务超时'
      suggestions = [
        '1. 检查网络连接速度',
        '2. 检查后端服务是否响应缓慢',
        '3. 尝试重启后端服务'
      ]
    }
    
    console.log('💡 解决建议:')
    suggestions.forEach(suggestion => {
      console.log('   ', suggestion)
    })
    
    return false
  }
}

/**
 * 测试具体的API接口
 */
const testSpecificAPI = async (apiPath, method = 'GET', data = {}) => {
  try {
    console.log(`🔍 测试API接口: ${method} ${apiPath}`)
    
    let response
    switch (method.toUpperCase()) {
      case 'GET':
        response = await request.get(apiPath, data, {
          showLoading: false,
          showError: false
        })
        break
      case 'POST':
        response = await request.post(apiPath, data, {
          showLoading: false,
          showError: false
        })
        break
      default:
        throw new Error(`不支持的请求方法: ${method}`)
    }
    
    console.log(`✅ API接口 ${apiPath} 测试成功:`, response)
    return response
  } catch (error) {
    console.error(`❌ API接口 ${apiPath} 测试失败:`, error.message)
    return null
  }
}

/**
 * 运行完整的连接测试
 */
const runFullTest = async () => {
  console.log('🚀 开始API连接测试...')
  
  // 1. 测试基础连接
  const basicConnection = await testBackendConnection()
  
  if (!basicConnection) {
    console.log('❌ 基础连接失败，跳过其他测试')
    return false
  }
  
  // 2. 测试商品列表API
  console.log('\n📦 测试商品相关API...')
  await testSpecificAPI('/goods/list', 'GET', { page: 1, limit: 5 })
  
  // 3. 测试购物车API（可能需要登录）
  console.log('\n🛒 测试购物车API...')
  await testSpecificAPI('/cart/list', 'GET')
  
  // 4. 测试广告API
  console.log('\n📺 测试广告API...')
  await testSpecificAPI('/ads/click', 'POST', {
    ad_type: 'test',
    page: 'test',
    timestamp: Date.now()
  })
  
  console.log('\n✅ API连接测试完成')
  return true
}

module.exports = {
  testBackendConnection,
  testSpecificAPI,
  runFullTest
} 