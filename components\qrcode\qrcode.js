// components/qrcode/qrcode.js
// 二维码生成组件

// 导入weapp-qrcode库
const qrcode = require('../../utils/weapp-qrcode.js');

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    ssid: {
      type: String,
      value: ''
    },
    password: {
      type: String,
      value: ''
    },
    size: {
      type: Number,
      value: 400
    },
    merchantName: {
      type: String,
      value: ''
    },
    foreground: {
      type: String,
      value: '#000000'
    },
    background: {
      type: String,
      value: '#ffffff'
    },
    adEnabled: {
      type: Boolean,
      value: false
    },
    adType: {
      type: String,
      value: 'video'
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    qrCodeData: '',
    loading: true,
    qrCodeImageUrl: '',
    fallbackInfo: null,
    _forceUpdate: 0, // 用于强制刷新DOM
    canvasId: '' // 动态Canvas ID
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached: function() {
      // 生成唯一的Canvas ID
      const canvasId = 'qrcode-canvas-' + Date.now() + '-' + Math.random().toString(36).substring(2, 11);
      this.setData({
        canvasId: canvasId
      });

      this.generateQRCode();
    },
    ready: function() {
      // 组件完全渲染后，如果有外部API调用，先尝试使用
      if (this.properties.ssid && this.properties.password) {
        this.tryServerQRCode();
      }
    }
  },

  /**
   * 监听属性变化
   */
  observers: {
    'ssid, password': function(ssid, password) {
      if (ssid && password) {
        this.generateQRCode();
      }
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 生成WiFi二维码数据
     */
    generateQRCode: function() {
      const { ssid, password, adEnabled, adType } = this.properties;

      if (!ssid || !password) {
        console.log('SSID或密码为空，无法生成二维码');
        return;
      }

      console.log('生成WiFi二维码，SSID:', ssid, '密码长度:', password.length);

      let qrCodeData;

      if (adEnabled) {
        qrCodeData = this.generateAdUrl(ssid, password, adType);
      } else {
        qrCodeData = `WIFI:T:WPA;S:${ssid};P:${password};H:false;;`;
      }

      console.log('二维码数据:', qrCodeData);

      // 设置数据
      this.setData({
        qrCodeData: qrCodeData,
        loading: true,
        qrCodeImageUrl: null
      });

      // 不立即绘制，等待ready生命周期中的服务器API调用
    },

    /**
     * 生成广告链接URL
     */
    generateAdUrl: function(ssid, password, adType) {
      const encodedSsid = encodeURIComponent(ssid);
      const encodedPassword = encodeURIComponent(password);
      return `/pages/wifi/ad-view/ad-view?ssid=${encodedSsid}&pwd=${encodedPassword}&type=${adType}`;
    },

    /**
     * 尝试使用服务器API生成二维码
     */
    tryServerQRCode: function() {
      const { ssid, password, adEnabled } = this.properties;

      if (!ssid || !password) {
        return;
      }

      console.log('尝试使用服务器API生成二维码');

      // 调用后端API
      wx.request({
        url: 'http://localhost:4000/api/v1/client/wifi-qrcode',
        method: 'GET',
        data: {
          ssid: ssid,
          password: password,
          encryption: 'WPA',
          hidden: 'false',
          adEnabled: adEnabled ? 'true' : 'false'
        },
        success: (res) => {
          console.log('服务器API响应:', res);
          if (res.data && res.data.status === 'success' && res.data.data && res.data.data.qrcode_url) {
            const qrcodeUrl = res.data.data.qrcode_url;
            console.log('获取到WiFi二维码URL:', qrcodeUrl);

            // 检查是否是外部URL
            if (qrcodeUrl.startsWith('https://api.qrserver.com') || qrcodeUrl.startsWith('http://api.qrserver.com')) {
              console.log('检测到外部二维码URL，回退到Canvas绘制');
              // 外部URL无法加载，直接使用Canvas绘制
              this.drawQRCode();
            } else {
              // 本地URL，可以直接使用
              this.setData({
                qrCodeImageUrl: qrcodeUrl,
                loading: false
              });
            }
          } else {
            console.log('服务器API返回异常，使用Canvas绘制');
            this.drawQRCode();
          }
        },
        fail: (err) => {
          console.error('服务器API调用失败:', err);
          this.drawQRCode();
        }
      });
    },

    /**
     * 绘制二维码到Canvas
     */
    drawQRCode: function() {
      if (!this.data.qrCodeData) {
        console.error('没有二维码数据可绘制');
        return;
      }

      console.log('开始Canvas绘制二维码');
      console.log('当前数据状态:', {
        qrCodeImageUrl: this.data.qrCodeImageUrl,
        loading: this.data.loading,
        qrCodeData: this.data.qrCodeData ? '已设置' : '未设置'
      });

      // 确保Canvas显示条件满足
      this.setData({
        qrCodeImageUrl: '', // 使用空字符串而不是null
        loading: true
      }, () => {
        console.log('已设置qrCodeImageUrl为空字符串，Canvas应该显示');

        // 强制触发页面更新
        this.setData({
          _forceUpdate: Date.now()
        }, () => {
          // 延迟查询确保DOM渲染完成
          setTimeout(() => {
            this.queryCanvas();
          }, 1000); // 增加延迟时间到1秒
        });
      });
    },

    /**
     * 查询Canvas节点
     */
    queryCanvas: function(retryCount = 0) {
      console.log('查询Canvas节点...（第' + (retryCount + 1) + '次尝试）');
      console.log('当前组件数据状态:', {
        qrCodeImageUrl: this.data.qrCodeImageUrl,
        loading: this.data.loading,
        canvasId: this.data.canvasId
      });

      const canvasSelector = '#' + this.data.canvasId;
      console.log('Canvas选择器:', canvasSelector);

      const query = this.createSelectorQuery();
      query.select(canvasSelector).fields({
        node: true,
        size: true
      }).exec((res) => {
        console.log('Canvas查询结果:', res);

        if (!res || !res[0] || !res[0].node) {
          if (retryCount < 3) {
            console.log('Canvas节点未找到，' + (1000 * (retryCount + 1)) + 'ms后重试...');
            setTimeout(() => {
              this.queryCanvas(retryCount + 1);
            }, 1000 * (retryCount + 1)); // 递增延迟：1s, 2s, 3s
          } else {
            console.error('多次重试后仍未找到Canvas节点，使用备用方案');
            this.showFallbackQRCode();
          }
          return;
        }

        console.log('Canvas节点查询成功！');
        this.performCanvasDraw(res[0].node);
      });
    },

    /**
     * 执行Canvas绘制
     */
    performCanvasDraw: function(canvas) {
      console.log('开始执行Canvas绘制');

      try {
        const ctx = canvas.getContext('2d');
        const dpr = wx.getSystemInfoSync().pixelRatio;
        const canvasSize = this.properties.size / 750 * wx.getSystemInfoSync().windowWidth;

        canvas.width = canvasSize * dpr;
        canvas.height = canvasSize * dpr;
        ctx.scale(dpr, dpr);

        // 清空画布
        ctx.fillStyle = this.properties.background;
        ctx.fillRect(0, 0, canvasSize, canvasSize);

        // 使用weapp-qrcode库生成二维码
        const qr = qrcode(this.data.canvasId, {
          text: this.data.qrCodeData,
          width: canvasSize,
          height: canvasSize,
          colorDark: this.properties.foreground,
          colorLight: this.properties.background,
          correctLevel: qrcode.CorrectLevel.H
        });

        console.log('二维码对象创建成功，开始绘制');
        qr.makeCode(this.data.qrCodeData);
        console.log('真实WiFi二维码绘制完成');

        // 设置完成状态
        this.setData({
          loading: false
        });

        // 触发事件
        this.triggerEvent('generated', {
          data: this.data.qrCodeData,
          imageUrl: null,
          adEnabled: this.properties.adEnabled
        });

      } catch (error) {
        console.error('Canvas绘制失败:', error);
        this.showFallbackQRCode();
      }
    },

    /**
     * 显示备用信息
     */
    showFallbackQRCode: function() {
      console.log('显示备用二维码信息');

      const fallbackInfo = {
        ssid: this.properties.ssid,
        password: this.properties.password,
        merchantName: this.properties.merchantName,
        adEnabled: this.properties.adEnabled
      };

      this.setData({
        qrCodeImageUrl: null,
        fallbackInfo: fallbackInfo,
        loading: false
      });

      this.triggerEvent('generated', {
        data: this.data.qrCodeData,
        imageUrl: null,
        fallbackInfo: fallbackInfo,
        adEnabled: this.properties.adEnabled
      });
    },

    /**
     * 图片加载失败处理
     */
    onImageLoadError: function(e) {
      console.error('二维码图片加载失败:', e.detail);
      this.showFallbackQRCode();
    },

    /**
     * 二维码点击事件
     */
    onQRCodeTap: function() {
      this.triggerEvent('tap', {
        ssid: this.properties.ssid,
        password: this.properties.password,
        imageUrl: this.data.qrCodeImageUrl,
        adEnabled: this.properties.adEnabled
      });
    },

    /**
     * 二维码长按事件
     */
    onQRCodeLongPress: function() {
      this.triggerEvent('longpress', {
        ssid: this.properties.ssid,
        password: this.properties.password,
        imageUrl: this.data.qrCodeImageUrl,
        adEnabled: this.properties.adEnabled
      });
    }
  }
});