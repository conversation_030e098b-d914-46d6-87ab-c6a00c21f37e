<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>钱包管理</span>
        <el-button
          style="float: right; padding: 3px 0"
          type="text"
          icon="el-icon-data-line"
          @click="showStats"
        >
          统计概览
        </el-button>
      </div>
      
      <!-- 搜索区域 -->
      <el-form :inline="true" :model="queryParams" class="demo-form-inline" size="small">
        <el-form-item label="用户昵称">
          <el-input
            v-model="queryParams.nickname"
            placeholder="请输入用户昵称"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="手机号">
          <el-input
            v-model="queryParams.phone"
            placeholder="请输入手机号"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="余额范围">
          <el-input
            v-model="queryParams.min_balance"
            placeholder="最小余额"
            style="width: 100px"
            clearable
          />
          <span style="margin: 0 10px">-</span>
          <el-input
            v-model="queryParams.max_balance"
            placeholder="最大余额"
            style="width: 100px"
            clearable
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="handleQuery">查询</el-button>
          <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
      
      <!-- 表格区域 -->
      <el-table
        v-loading="loading"
        :data="walletList"
        border
        style="width: 100%"
      >
        <el-table-column prop="id" label="用户ID" width="80" />
        <el-table-column label="用户信息" min-width="200">
          <template slot-scope="scope">
            <div class="user-info">
              <el-avatar :src="scope.row.avatar" size="small">
                {{ scope.row.nickname ? scope.row.nickname.charAt(0) : 'U' }}
              </el-avatar>
              <div class="user-details">
                <div class="nickname">{{ scope.row.nickname || '未设置' }}</div>
                <div class="phone">{{ scope.row.phone || '未绑定' }}</div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="balance" label="当前余额" width="120">
          <template slot-scope="scope">
            <span class="balance-amount">¥{{ formatNumber(scope.row.balance) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="total_income" label="累计收益" width="120">
          <template slot-scope="scope">
            <span class="income-amount">¥{{ formatNumber(scope.row.total_income) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="transaction_count" label="交易次数" width="100" />
        <el-table-column prop="role" label="角色" width="100">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.role === 'leader'" type="warning">团长</el-tag>
            <el-tag v-else-if="scope.row.role === 'admin'" type="danger">管理员</el-tag>
            <el-tag v-else type="info">普通用户</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.status === 1" type="success">正常</el-tag>
            <el-tag v-else type="danger">禁用</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="注册时间" width="160">
          <template slot-scope="scope">
            <span>{{ scope.row.created_at }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-view"
              @click="handleDetail(scope.row)"
            >
              详情
            </el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit-outline"
              @click="handleAdjust(scope.row)"
            >
              调整余额
            </el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-document"
              @click="handleTransactions(scope.row)"
            >
              交易记录
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页区域 -->
      <div class="pagination-container">
        <el-pagination
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="queryParams.page"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="queryParams.limit"
          layout="total, sizes, prev, pager, next, jumper"
          :total="safeTotal"
        />
      </div>
    </el-card>
    
    <!-- 余额调整对话框 -->
    <el-dialog title="调整余额" :visible.sync="adjustDialogVisible" width="500px" @close="resetAdjustForm">
      <el-form
        ref="adjustForm"
        :model="adjustForm"
        :rules="adjustRules"
        label-width="100px"
        size="small"
      >
        <el-form-item label="用户信息">
          <div class="user-info">
            <el-avatar :src="adjustForm.user.avatar" size="small">
              {{ adjustForm.user.nickname ? adjustForm.user.nickname.charAt(0) : 'U' }}
            </el-avatar>
            <div class="user-details">
              <div class="nickname">{{ adjustForm.user.nickname || '未设置' }}</div>
              <div class="phone">{{ adjustForm.user.phone || '未绑定' }}</div>
            </div>
          </div>
        </el-form-item>
        <el-form-item label="当前余额">
          <span class="balance-amount">¥{{ formatNumber(adjustForm.user.balance) }}</span>
        </el-form-item>
        <el-form-item label="调整类型" prop="type">
          <el-radio-group v-model="adjustForm.type">
            <el-radio label="increase">增加余额</el-radio>
            <el-radio label="decrease">减少余额</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="调整金额" prop="amount">
          <el-input
            v-model="adjustForm.amount"
            placeholder="请输入调整金额"
            type="number"
            step="0.01"
            min="0"
          >
            <template slot="prepend">¥</template>
          </el-input>
        </el-form-item>
        <el-form-item label="调整说明" prop="description">
          <el-input
            v-model="adjustForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入调整说明"
          />
        </el-form-item>
        <el-form-item label="调整后余额">
          <span class="preview-balance">¥{{ previewBalance }}</span>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="adjustDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitAdjust" :loading="adjusting">确 定</el-button>
      </div>
    </el-dialog>
    
    <!-- 统计概览对话框 -->
    <el-dialog title="钱包统计概览" :visible.sync="statsDialogVisible" width="800px">
      <div v-loading="statsLoading">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-card shadow="hover">
              <div class="stat-item">
                <div class="stat-title">用户总数</div>
                <div class="stat-value">{{ stats.balance.total_users || 0 }}</div>
                <div class="stat-desc">有余额用户: {{ stats.balance.users_with_balance || 0 }}</div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="8">
            <el-card shadow="hover">
              <div class="stat-item">
                <div class="stat-title">总余额</div>
                <div class="stat-value">¥{{ formatNumber(stats.balance.total_balance || 0) }}</div>
                <div class="stat-desc">平均余额: ¥{{ formatNumber(stats.balance.avg_balance || 0) }}</div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="8">
            <el-card shadow="hover">
              <div class="stat-item">
                <div class="stat-title">交易总数</div>
                <div class="stat-value">{{ stats.transactions.total_transactions || 0 }}</div>
                <div class="stat-desc">今日交易: {{ stats.transactions.today_transactions || 0 }}</div>
              </div>
            </el-card>
          </el-col>
        </el-row>
        
        <el-row :gutter="20" style="margin-top: 20px">
          <el-col :span="12">
            <el-card shadow="hover">
              <div class="stat-item">
                <div class="stat-title">总收入</div>
                <div class="stat-value income">¥{{ formatNumber(stats.transactions.total_income || 0) }}</div>
                <div class="stat-desc">收入笔数: {{ stats.transactions.income_count || 0 }}</div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="12">
            <el-card shadow="hover">
              <div class="stat-item">
                <div class="stat-title">总支出</div>
                <div class="stat-value expense">¥{{ formatNumber(stats.transactions.total_expense || 0) }}</div>
                <div class="stat-desc">支出笔数: {{ stats.transactions.expense_count || 0 }}</div>
              </div>
            </el-card>
          </el-col>
        </el-row>
        
        <el-card style="margin-top: 20px">
          <div slot="header">业务类型统计</div>
          <el-table :data="stats.business_types || []" border>
            <el-table-column prop="business_type" label="业务类型" />
            <el-table-column prop="transaction_count" label="交易次数" />
            <el-table-column prop="total_amount" label="总金额">
              <template slot-scope="scope">
                ¥{{ formatNumber(scope.row.total_amount) }}
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getWalletList, adjustBalance, getWalletStats } from '@/api/wallet'

export default {
  name: 'WalletList',
  data() {
    return {
      // 查询参数
      queryParams: {
        page: 1,
        limit: 20,
        nickname: '',
        phone: '',
        min_balance: '',
        max_balance: ''
      },
      // 钱包列表
      walletList: [],
      // 总数
      total: 0,
      // 加载状态
      loading: false,
      
      // 余额调整对话框
      adjustDialogVisible: false,
      adjusting: false,
      adjustForm: {
        user: {},
        type: 'increase',
        amount: '',
        description: ''
      },
      adjustRules: {
        type: [
          { required: true, message: '请选择调整类型', trigger: 'change' }
        ],
        amount: [
          { required: true, message: '请输入调整金额', trigger: 'blur' },
          { pattern: /^\d+(\.\d{1,2})?$/, message: '请输入有效的金额', trigger: 'blur' }
        ]
      },
      
      // 统计对话框
      statsDialogVisible: false,
      statsLoading: false,
      stats: {
        balance: {},
        transactions: {},
        business_types: []
      }
    }
  },
  computed: {
    previewBalance() {
      const currentBalance = parseFloat(this.adjustForm.user.balance || 0)
      const adjustAmount = parseFloat(this.adjustForm.amount || 0)
      
      if (this.adjustForm.type === 'increase') {
        return this.formatNumber(currentBalance + adjustAmount)
      } else {
        return this.formatNumber(Math.max(0, currentBalance - adjustAmount))
      }
    }
  },
  computed: {
    // 确保total始终是数字类型，避免分页组件报错
    safeTotal() {
      // 多重保护确保返回有效数字
      if (this.total === null || this.total === undefined) {
        return 0
      }
      if (typeof this.total === 'string') {
        const parsed = parseInt(this.total, 10)
        return isNaN(parsed) ? 0 : parsed
      }
      if (typeof this.total === 'number' && !isNaN(this.total)) {
        return Math.max(0, Math.floor(this.total))
      }
      return 0
    }
  },
  created() {
    this.getList()
  },
  methods: {
    // 获取钱包列表
    async getList() {
      this.loading = true
      try {
        const { data } = await getWalletList(this.queryParams)
        this.walletList = data.list || []
        this.total = data.total || 0
      } catch (error) {
        console.error('获取钱包列表失败:', error)
        this.walletList = []
        this.total = 0
        this.$message.error('获取钱包列表失败')
      } finally {
        this.loading = false
      }
    },
    // 查询按钮
    handleQuery() {
      this.queryParams.page = 1
      this.getList()
    },
    // 重置按钮
    resetQuery() {
      this.queryParams = {
        page: 1,
        limit: 20,
        nickname: '',
        phone: '',
        min_balance: '',
        max_balance: ''
      }
      this.getList()
    },
    // 每页条数改变
    handleSizeChange(val) {
      this.queryParams.limit = val
      this.getList()
    },
    // 当前页改变
    handleCurrentChange(val) {
      this.queryParams.page = val
      this.getList()
    },
    // 查看详情
    handleDetail(row) {
      this.$router.push({ path: `/wallet/detail/${row.id}` })
    },
    // 调整余额
    handleAdjust(row) {
      this.adjustDialogVisible = true
      this.adjustForm = {
        user: { ...row },
        type: 'increase',
        amount: '',
        description: ''
      }
    },
    // 查看交易记录
    handleTransactions(row) {
      this.$router.push({ path: `/wallet/transactions/${row.id}` })
    },
    // 显示统计
    async showStats() {
      this.statsDialogVisible = true
      this.statsLoading = true
      try {
        const { data } = await getWalletStats()
        this.stats = data
      } catch (error) {
        console.error('获取统计数据失败:', error)
      } finally {
        this.statsLoading = false
      }
    },
    // 重置调整表单
    resetAdjustForm() {
      this.$refs.adjustForm && this.$refs.adjustForm.resetFields()
    },
    // 提交余额调整
    submitAdjust() {
      this.$refs.adjustForm.validate(async valid => {
        if (!valid) return
        
        this.adjusting = true
        try {
          await adjustBalance(this.adjustForm.user.id, {
            type: this.adjustForm.type,
            amount: this.adjustForm.amount,
            description: this.adjustForm.description
          })
          this.$message.success('余额调整成功')
          this.adjustDialogVisible = false
          this.getList()
        } catch (error) {
          this.$message.error('余额调整失败')
        } finally {
          this.adjusting = false
        }
      })
    },
    // 格式化数字
    formatNumber(num) {
      return parseFloat(num || 0).toFixed(2)
    }
  }
}
</script>

<style scoped>
.user-info {
  display: flex;
  align-items: center;
}

.user-details {
  margin-left: 10px;
}

.nickname {
  font-weight: bold;
  color: #303133;
}

.phone {
  font-size: 12px;
  color: #909399;
}

.balance-amount {
  color: #67C23A;
  font-weight: bold;
}

.income-amount {
  color: #409EFF;
  font-weight: bold;
}

.preview-balance {
  color: #E6A23C;
  font-weight: bold;
  font-size: 16px;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.stat-item {
  text-align: center;
  padding: 20px;
}

.stat-title {
  font-size: 14px;
  color: #909399;
  margin-bottom: 10px;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 5px;
}

.stat-value.income {
  color: #67C23A;
}

.stat-value.expense {
  color: #F56C6C;
}

.stat-desc {
  font-size: 12px;
  color: #909399;
}
</style>
