
// 独立的WiFi详情API处理函数
const express = require('express');
const router = express.Router();
const db = require('../../config/database');
const { success, error } = require('../utils/response');

// 管理端WiFi详情API
router.get('/detail/:id', async (req, res) => {
  console.log('处理WiFi详情请求，ID:', req.params.id);
  try {
    const { id } = req.params;
    
    if (!id || isNaN(parseInt(id))) {
      return error(res, '无效的ID参数', 400);
    }
    
    // 查询数据库
    const [rows] = await db.query('SELECT * FROM wifi WHERE id = ?', [parseInt(id)]);
    
    if (!rows || rows.length === 0) {
      return error(res, 'WiFi码不存在', 404);
    }
    
    // 获取WiFi记录
    const wifi = rows[0];
    
    // 增强记录，添加广告相关字段
    const enhancedWifi = {
      ...wifi,
      ad_enabled: wifi.ad_enabled !== undefined ? wifi.ad_enabled : (wifi.ad_required === 1),
      ad_type: wifi.ad_type || 'video',
      ad_content: wifi.ad_content || wifi.ad_url || '',
      ad_duration: wifi.ad_duration || 5,
      ad_title: wifi.ad_title || '推荐内容',
      ad_link: wifi.ad_link || '',
      ad_view_count: wifi.ad_view_count || 0,
      ad_click_count: wifi.ad_click_count || 0
    };
    
    return success(res, enhancedWifi, '获取WiFi码详情成功');
  } catch (err) {
    console.error('获取WiFi详情失败:', err);
    return error(res, '获取WiFi详情失败: ' + err.message, 500);
  }
});

module.exports = router;
