/**
 * 分润管理服务
 */
const db = require('../../config/database');
const logger = require('../utils/logger');
const ProfitCalculator = require('../utils/profit-calculator');

class ProfitService {
  /**
   * 处理WiFi分享分润
   * @param {object} data WiFi分享数据
   * @returns {Promise<object>} 分润结果
   */
  static async processWifiShareProfit(data) {
    try {
      const { wifiId, userId, teamId, leaderId, amount = 1.0 } = data;
      
      const participants = {
        userId,
        leaderId,
        teamId
      };
      
      // 计算分润
      const profitResult = await ProfitCalculator.calculateProfit(
        'wifi_share',
        amount,
        participants
      );
      
      if (!profitResult.success) {
        return profitResult;
      }
      
      // 执行分润分配
      const distributionResult = await ProfitCalculator.distributeProfits(
        profitResult,
        wifiId,
        null
      );
      
      // 更新WiFi使用次数
      await db.query(
        'UPDATE wifi SET use_count = use_count + 1 WHERE id = ?',
        [wifiId]
      );
      
      // 更新团队收益
      if (teamId && distributionResult.success) {
        const teamProfit = distributionResult.records
          .filter(r => r.role === 'leader')
          .reduce((sum, r) => sum + r.amount, 0);
          
        await db.query(
          'UPDATE team SET total_profit = total_profit + ? WHERE id = ?',
          [teamProfit, teamId]
        );
      }
      
      return {
        success: true,
        type: 'wifi_share',
        profitResult,
        distributionResult
      };
    } catch (error) {
      logger.error(`处理WiFi分享分润失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 处理商品销售分润
   * @param {object} data 订单数据
   * @returns {Promise<object>} 分润结果
   */
  static async processGoodsSaleProfit(data) {
    try {
      const { orderId, orderNo, userId, teamId, leaderId, goodsAmount } = data;
      
      const participants = {
        userId,
        leaderId,
        teamId
      };
      
      // 计算分润
      const profitResult = await ProfitCalculator.calculateProfit(
        'goods_sale',
        goodsAmount,
        participants
      );
      
      if (!profitResult.success) {
        return profitResult;
      }
      
      // 执行分润分配
      const distributionResult = await ProfitCalculator.distributeProfits(
        profitResult,
        orderId,
        orderNo
      );
      
      // 更新团队收益
      if (teamId && distributionResult.success) {
        const teamProfit = distributionResult.records
          .filter(r => r.role === 'leader')
          .reduce((sum, r) => sum + r.amount, 0);
          
        await db.query(
          'UPDATE team SET total_profit = total_profit + ? WHERE id = ?',
          [teamProfit, teamId]
        );
      }
      
      return {
        success: true,
        type: 'goods_sale',
        profitResult,
        distributionResult
      };
    } catch (error) {
      logger.error(`处理商品销售分润失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 处理广告点击分润
   * @param {object} data 广告数据
   * @returns {Promise<object>} 分润结果
   */
  static async processAdvertisementProfit(data) {
    try {
      const { adId, userId, teamId, leaderId, clickAmount = 0.5 } = data;
      
      const participants = {
        userId,
        leaderId,
        teamId
      };
      
      // 计算分润
      const profitResult = await ProfitCalculator.calculateProfit(
        'advertisement',
        clickAmount,
        participants
      );
      
      if (!profitResult.success) {
        return profitResult;
      }
      
      // 执行分润分配
      const distributionResult = await ProfitCalculator.distributeProfits(
        profitResult,
        adId,
        null
      );
      
      // 更新广告点击次数
      await db.query(
        'UPDATE advertisement SET click_count = click_count + 1 WHERE id = ?',
        [adId]
      );
      
      // 更新团队收益
      if (teamId && distributionResult.success) {
        const teamProfit = distributionResult.records
          .filter(r => r.role === 'leader')
          .reduce((sum, r) => sum + r.amount, 0);
          
        await db.query(
          'UPDATE team SET total_profit = total_profit + ? WHERE id = ?',
          [teamProfit, teamId]
        );
      }
      
      return {
        success: true,
        type: 'advertisement',
        profitResult,
        distributionResult
      };
    } catch (error) {
      logger.error(`处理广告点击分润失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 结算分润记录
   * @param {Array} recordIds 分润记录ID列表
   * @returns {Promise<object>} 结算结果
   */
  static async settleProfitRecords(recordIds) {
    try {
      return await db.transaction(async (connection) => {
        const results = [];
        
        for (const recordId of recordIds) {
          // 获取分润记录
          const records = await connection.execute(
            'SELECT * FROM profit_log WHERE id = ? AND status = 0',
            [recordId]
          );
          
          if (records[0].length === 0) {
            results.push({
              recordId,
              success: false,
              message: '记录不存在或已结算'
            });
            continue;
          }
          
          const record = records[0][0];
          
          // 更新用户余额
          await connection.execute(
            'UPDATE user SET balance = balance + ? WHERE id = ?',
            [record.amount, record.user_id]
          );
          
          // 更新分润记录状态
          await connection.execute(
            'UPDATE profit_log SET status = 1, settle_time = NOW() WHERE id = ?',
            [recordId]
          );
          
          results.push({
            recordId,
            success: true,
            userId: record.user_id,
            amount: record.amount
          });
        }
        
        return {
          success: true,
          totalRecords: recordIds.length,
          settledRecords: results.filter(r => r.success).length,
          failedRecords: results.filter(r => !r.success).length,
          results
        };
      });
    } catch (error) {
      logger.error(`结算分润记录失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 获取用户分润统计
   * @param {number} userId 用户ID
   * @param {string} timeRange 时间范围
   * @returns {Promise<object>} 分润统计
   */
  static async getUserProfitStats(userId, timeRange = 'month') {
    try {
      let timeCondition = '';
      const now = new Date();
      
      switch (timeRange) {
        case 'today':
          timeCondition = 'AND DATE(created_at) = CURDATE()';
          break;
        case 'week':
          timeCondition = 'AND created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)';
          break;
        case 'month':
          timeCondition = 'AND created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)';
          break;
        case 'year':
          timeCondition = 'AND YEAR(created_at) = YEAR(NOW())';
          break;
      }
      
      const sql = `
        SELECT 
          profit_type,
          role,
          COUNT(*) as count,
          SUM(amount) as total_amount,
          SUM(CASE WHEN status = 1 THEN amount ELSE 0 END) as settled_amount,
          SUM(CASE WHEN status = 0 THEN amount ELSE 0 END) as pending_amount
        FROM profit_log 
        WHERE user_id = ? ${timeCondition}
        GROUP BY profit_type, role
        ORDER BY profit_type, role
      `;
      
      const result = await db.query(sql, [userId]);
      
      // 计算总计
      const totalStats = {
        totalAmount: 0,
        settledAmount: 0,
        pendingAmount: 0,
        totalCount: 0
      };
      
      result.forEach(row => {
        totalStats.totalAmount += parseFloat(row.total_amount);
        totalStats.settledAmount += parseFloat(row.settled_amount);
        totalStats.pendingAmount += parseFloat(row.pending_amount);
        totalStats.totalCount += parseInt(row.count);
      });
      
      return {
        timeRange,
        details: result,
        summary: totalStats
      };
    } catch (error) {
      logger.error(`获取用户分润统计失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 自动结算到期分润
   * @returns {Promise<object>} 结算结果
   */
  static async autoSettleProfits() {
    try {
      // 获取配置的结算延迟时间
      const configResult = await db.query(
        'SELECT config_value FROM profit_config WHERE config_key = "profit_settle_delay"'
      );
      const delayHours = configResult.length > 0 ? parseInt(configResult[0].config_value) : 24;
      
      // 查找到期的分润记录
      const sql = `
        SELECT id FROM profit_log 
        WHERE status = 0 
        AND created_at <= DATE_SUB(NOW(), INTERVAL ? HOUR)
        LIMIT 100
      `;
      
      const records = await db.query(sql, [delayHours]);
      
      if (records.length === 0) {
        return {
          success: true,
          message: '没有需要结算的分润记录',
          settledCount: 0
        };
      }
      
      const recordIds = records.map(r => r.id);
      const settleResult = await this.settleProfitRecords(recordIds);
      
      logger.info(`自动结算完成: ${settleResult.settledRecords}/${settleResult.totalRecords}`);
      
      return settleResult;
    } catch (error) {
      logger.error(`自动结算分润失败: ${error.message}`);
      throw error;
    }
  }
}

module.exports = ProfitService;
