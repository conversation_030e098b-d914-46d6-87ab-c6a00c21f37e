// services/goods.js
// 商品相关服务

const { get, post, put, delete: del } = require('../utils/request.js')
const API = require('../config/api.js')
const config = require('../config/config.js')

/**
 * 获取商品列表
 * @param {Object} params 查询参数
 * @returns {Promise} 商品列表
 */
const getGoodsList = (params = {}) => {
  return get(API.goods.list, params)
}

/**
 * 获取商品详情
 * @param {String} id 商品ID
 * @returns {Promise} 商品详情
 */
const getGoodsDetail = (id) => {
  return get(`${API.goods.detail}/${id}`)
}

/**
 * 获取商品分类
 * @returns {Promise} 分类列表
 */
const getCategories = () => {
  return get(API.goods.categories)
}

/**
 * 搜索商品
 * @param {String} keyword 搜索关键词
 * @param {Object} params 其他参数
 * @returns {Promise} 搜索结果
 */
const searchGoods = (keyword, params = {}) => {
  return get(API.goods.list, {
    keyword,
    ...params
  })
}

/**
 * 处理商品数据中的图片URL
 * @param {Object} goodsData 商品数据
 * @returns {Object} 处理后的商品数据
 */
const processGoodsImageUrls = (goodsData) => {
  if (!goodsData) return goodsData
  
  const baseUrl = config.imageServer.baseUrl || config.api.baseUrl || 'http://localhost:4000'
  
  // 处理商品封面图
  if (goodsData.cover && !goodsData.cover.startsWith('http')) {
    goodsData.cover = baseUrl + goodsData.cover
  }
  
  // 处理商品图片列表
  if (goodsData.images && Array.isArray(goodsData.images)) {
    goodsData.images = goodsData.images.map(img => {
      if (img && !img.startsWith('http')) {
        return baseUrl + img
      }
      return img
    })
  }
  
  return goodsData
}

module.exports = {
  getGoodsList,
  getGoodsDetail,
  getCategories,
  searchGoods,
  processGoodsImageUrls
} 