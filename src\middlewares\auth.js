const jwt = require('jsonwebtoken');
const config = require('../../config');
const { UnauthorizedError, ForbiddenError } = require('../utils/errors');
const userService = require('../services/user');
const logger = require('../utils/logger');

/**
 * 验证JWT令牌并将用户信息附加到请求对象
 */
async function verifyToken(req, res, next) {
  try {
    // 从请求头中获取令牌
    const authHeader = req.headers.authorization;
    
    if (!authHeader) {
      logger.warn('verifyToken: 未提供Authorization头');
      return next(new UnauthorizedError('未提供访问令牌'));
    }
    
    // 检查令牌格式
    const parts = authHeader.split(' ');
    if (parts.length !== 2 || parts[0] !== 'Bearer') {
      logger.warn(`verifyToken: 令牌格式错误: ${authHeader}`);
      return next(new UnauthorizedError('令牌格式错误'));
    }
    
    const token = parts[1];
    logger.info(`verifyToken: 收到令牌: ${token.substring(0, 10)}...`);
    
    // 开发环境测试模式 - 允许test-token作为有效token
    if (process.env.NODE_ENV === 'development' && (token === 'test-token' || token.startsWith('mock_token_'))) {
      logger.warn('verifyToken: 检测到测试token，跳过验证，使用模拟用户数据');
      req.user = {
        id: 1,
        nickname: '测试用户',
        avatar: 'https://thirdwx.qlogo.cn/mmopen/vi_32/POgEwh4mIHO4nibH0KlMECNjjGxQUq24ZEaGT4poC6icRiccVGKSyXwibcPq4BWmiaIGuG1icwxaQX6grC9VemZoJ8rg/132',
        role: 'admin', // 给予管理员权限
        status: 1,
        openid: 'test_openid_' + Date.now()
      };
      logger.info(`verifyToken: 设置测试用户: ${JSON.stringify(req.user)}`);
      return next();
    }
    
    // 检查是否是模拟token（开发环境）
    if (process.env.NODE_ENV === 'development' && token.startsWith('mock_token_')) {
      logger.warn('verifyToken: 检测到模拟token，使用模拟用户数据');
      req.user = {
        id: 1,
        nickname: '模拟用户',
        avatar: 'https://thirdwx.qlogo.cn/mmopen/vi_32/POgEwh4mIHO4nibH0KlMECNjjGxQUq24ZEaGT4poC6icRiccVGKSyXwibcPq4BWmiaIGuG1icwxaQX6grC9VemZoJ8rg/132',
        role: 'user',
        status: 1,
        openid: 'mock_openid_' + Date.now()
      };
      logger.info(`verifyToken: 设置模拟用户: ${JSON.stringify(req.user)}`);
      return next();
    }
    
    // 验证令牌
    try {
    const decoded = jwt.verify(token, config.jwt.secret);
      logger.info(`verifyToken: 令牌验证成功，用户ID: ${decoded.id}, 角色: ${decoded.role}`);
    
    // 根据角色查找用户
    let user;
    if (decoded.role === 'admin') {
      // 查找管理员用户
      const db = require('../database');
      const adminUser = await db.getOne('SELECT * FROM admin_user WHERE id = ?', [decoded.id]);
      if (!adminUser) {
          logger.warn(`verifyToken: 管理员不存在，ID: ${decoded.id}`);
        return next(new UnauthorizedError('管理员不存在'));
      }
      if (adminUser.status !== 1) {
          logger.warn(`verifyToken: 管理员已被禁用，ID: ${decoded.id}`);
        return next(new UnauthorizedError('管理员已被禁用'));
      }
      // 转换为统一的用户格式
      user = {
        id: adminUser.id,
        username: adminUser.username,
        nickname: adminUser.real_name || adminUser.username,
        role: 'admin',
        status: adminUser.status
      };
      } else {
        // 对于微信小程序登录，先尝试使用JWT中的信息
        if (decoded.openid) {
          user = {
            id: decoded.id,
            openid: decoded.openid,
            role: decoded.role || 'user',
            session_key: decoded.session_key
          };
          logger.info(`verifyToken: 使用JWT中的用户信息: ID=${user.id}, openid=${user.openid}`);
    } else {
      // 查找普通用户
          try {
            // 确保使用正确的表名
            const db = require('../database');
            let tableName;
            
            try {
              // 检查数据库中是否存在users表或user表
              const tableCheck = await db.getOne(`
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = DATABASE() AND table_name IN ('users', 'user')
              `);
              
              if (!tableCheck) {
                logger.warn('verifyToken: 未找到用户表，使用默认表名"user"');
                tableName = 'user';
              } else {
                tableName = tableCheck.table_name;
                logger.info(`verifyToken: 使用表名 ${tableName} 查询用户`);
              }
            } catch (tableError) {
              logger.error(`verifyToken: 检查用户表失败: ${tableError.message}`);
              // 默认使用user表
              tableName = 'user';
            }
            
            // 确保表名有值
            if (!tableName) {
              logger.warn('verifyToken: 表名未定义，使用默认表名"user"');
              tableName = 'user';
            }
            
            const userSql = `SELECT * FROM ${tableName} WHERE id = ?`;
            logger.info(`verifyToken: 执行SQL查询: ${userSql}, 参数: [${decoded.id}]`);
            
            user = await db.getOne(userSql, [decoded.id]);
            
    if (!user) {
              logger.warn(`verifyToken: 用户不存在，ID: ${decoded.id}`);
              
              // 在开发环境中，如果用户不存在，使用模拟用户
              if (process.env.NODE_ENV === 'development') {
                logger.info('verifyToken: 开发环境下使用模拟用户数据');
                user = {
                  id: decoded.id,
                  nickname: '开发环境用户',
                  avatar: 'https://thirdwx.qlogo.cn/mmopen/vi_32/POgEwh4mIHO4nibH0KlMECNjjGxQUq24ZEaGT4poC6icRiccVGKSyXwibcPq4BWmiaIGuG1icwxaQX6grC9VemZoJ8rg/132',
                  role: 'user',
                  status: 1,
                  openid: 'dev_openid_' + Date.now()
                };
              } else {
      return next(new UnauthorizedError('用户不存在'));
    }
            }
            
            if (user.status !== 1 && process.env.NODE_ENV !== 'development') {
              logger.warn(`verifyToken: 用户已被禁用，ID: ${decoded.id}`);
      return next(new UnauthorizedError('用户已被禁用'));
      }
            
            logger.info(`verifyToken: 从数据库获取用户信息成功: ID=${user.id}`);
          } catch (dbError) {
            logger.error(`verifyToken: 数据库查询失败: ${dbError.message}`);
            
            // 在开发环境中，如果数据库查询失败，使用模拟用户
            if (process.env.NODE_ENV === 'development') {
              logger.info('verifyToken: 数据库查询失败，开发环境下使用模拟用户数据');
              user = {
                id: decoded.id,
                nickname: '开发环境用户',
                avatar: 'https://thirdwx.qlogo.cn/mmopen/vi_32/POgEwh4mIHO4nibH0KlMECNjjGxQUq24ZEaGT4poC6icRiccVGKSyXwibcPq4BWmiaIGuG1icwxaQX6grC9VemZoJ8rg/132',
                role: 'user',
                status: 1,
                openid: 'dev_openid_' + Date.now()
              };
            } else {
              return next(new UnauthorizedError('数据库查询失败'));
            }
          }
        }
    }
    
    // 将用户信息附加到请求对象
    req.user = user;
      logger.info(`verifyToken: 成功设置req.user: ${JSON.stringify(req.user)}`);
    
    next();
    } catch (jwtError) {
      logger.error(`verifyToken: JWT验证失败: ${jwtError.message}`);
      if (jwtError.name === 'JsonWebTokenError') {
      return next(new UnauthorizedError('无效的访问令牌'));
    }
      if (jwtError.name === 'TokenExpiredError') {
      return next(new UnauthorizedError('访问令牌已过期'));
      }
      throw jwtError;
    }
  } catch (error) {
    logger.error(`verifyToken: 令牌验证失败: ${error.message}`, error);
    next(error);
  }
}

/**
 * 角色验证中间件
 * @param {string|string[]} roles - 允许访问的角色
 */
function checkRole(roles) {
  return (req, res, next) => {
    if (!req.user) {
      return next(new UnauthorizedError('未授权访问'));
    }
    
    const userRole = req.user.role;
    
    // 检查用户角色是否在允许的角色列表中
    const allowedRoles = Array.isArray(roles) ? roles : [roles];
    if (allowedRoles.includes(userRole) || userRole === 'admin') {
      next();
    } else {
      next(new ForbiddenError('您没有权限访问此资源'));
    }
  };
}

/**
 * 可选的令牌验证
 * 如果提供了令牌，则验证并附加用户信息，否则继续处理请求
 */
async function optionalAuth(req, res, next) {
  try {
    const authHeader = req.headers.authorization;
    if (!authHeader) {
      // 没有提供令牌，继续处理请求
      console.log('optionalAuth: 未提供令牌，继续处理请求');
      return next();
    }
    
    const parts = authHeader.split(' ');
    if (parts.length !== 2 || parts[0] !== 'Bearer') {
      // 令牌格式错误，继续处理请求
      console.log('optionalAuth: 令牌格式错误，继续处理请求');
      return next();
    }
    
    const token = parts[1];
    
    // 检查是否是模拟token（开发环境）
    if (process.env.NODE_ENV === 'development' && token.startsWith('mock_token_')) {
      console.log('optionalAuth: 检测到模拟token，使用模拟用户数据');
      req.user = {
        id: 1,
        nickname: '模拟用户',
        avatar: 'https://thirdwx.qlogo.cn/mmopen/vi_32/POgEwh4mIHO4nibH0KlMECNjjGxQUq24ZEaGT4poC6icRiccVGKSyXwibcPq4BWmiaIGuG1icwxaQX6grC9VemZoJ8rg/132',
        role: 'user',
        status: 1,
        openid: 'mock_openid_' + Date.now()
      };
      return next();
    }
    
    try {
    const decoded = jwt.verify(token, config.jwt.secret);
    
      // 根据角色查找用户
      if (decoded.role === 'admin') {
        // 查找管理员用户
        const db = require('../database');
        const adminUser = await db.getOne('SELECT * FROM admin_user WHERE id = ?', [decoded.id]);
        if (adminUser && adminUser.status === 1) {
          req.user = {
            id: adminUser.id,
            username: adminUser.username,
            nickname: adminUser.real_name || adminUser.username,
            role: 'admin',
            status: adminUser.status
          };
        }
      } else if (decoded.openid && decoded.session_key) {
        // 对于微信小程序登录，直接使用JWT中的信息
        req.user = {
          id: decoded.id,
          openid: decoded.openid,
          role: decoded.role,
          session_key: decoded.session_key
        };
      } else {
        // 查找普通用户
    const user = await userService.findUserById(decoded.id);
    if (user && user.status === 1) {
      req.user = user;
        }
      }
    } catch (tokenError) {
      // 令牌验证失败，但仍继续处理请求
      console.log('optionalAuth: 令牌验证失败，继续处理请求', tokenError.message);
    }
    
    next();
  } catch (error) {
    // 忽略错误，不影响请求处理
    console.log('optionalAuth: 处理错误，继续处理请求', error.message);
    next();
  }
}

/**
 * 管理员验证中间件
 * 验证用户是否为管理员
 */
async function adminAuth(req, res, next) {
  try {
    // 从请求头中获取令牌
    const authHeader = req.headers.authorization;
    
    if (!authHeader) {
      return next(new UnauthorizedError('未提供访问令牌'));
    }
    
    // 检查令牌格式
    const parts = authHeader.split(' ');
    if (parts.length !== 2 || parts[0] !== 'Bearer') {
      return next(new UnauthorizedError('令牌格式错误'));
    }
    
    const token = parts[1];
    
    // 验证令牌
    const decoded = jwt.verify(token, config.jwt.secret);
    
    // 检查是否是管理员
    if (decoded.role !== 'admin') {
      return next(new ForbiddenError('只有管理员可以访问此资源'));
    }
    
    // 查找管理员用户
    const db = require('../database');
    const adminUser = await db.getOne('SELECT * FROM admin_user WHERE id = ?', [decoded.id]);
    if (!adminUser) {
      return next(new UnauthorizedError('管理员不存在'));
    }
    if (adminUser.status !== 1) {
      return next(new UnauthorizedError('管理员已被禁用'));
    }
    
    // 将用户信息附加到请求对象
    req.user = {
      id: adminUser.id,
      username: adminUser.username,
      nickname: adminUser.real_name || adminUser.username,
      role: 'admin',
      status: adminUser.status
    };
    
    next();
  } catch (error) {
    if (error.name === 'JsonWebTokenError') {
      return next(new UnauthorizedError('无效的访问令牌'));
    }
    
    if (error.name === 'TokenExpiredError') {
      return next(new UnauthorizedError('访问令牌已过期'));
    }
    
    logger.error('令牌验证失败:', error);
    next(error);
  }
}

module.exports = {
  verifyToken,
  checkRole,
  optionalAuth,
  adminAuth
}; 