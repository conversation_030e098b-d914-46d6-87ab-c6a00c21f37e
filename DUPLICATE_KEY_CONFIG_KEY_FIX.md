# 数据库重复键名'config_key'错误修复说明

## 错误描述
在执行数据库建表语句时遇到错误：
```
Duplicate key name 'config_key'
```

## 问题分析

### 1. 错误原因
在 `profit_config` 表的定义中存在重复的唯一键定义：

**第402行：**
```sql
`config_key` varchar(50) NOT NULL UNIQUE COMMENT '配置键',
```

**第410行：**
```sql
UNIQUE KEY `config_key` (`config_key`)
```

这导致了同一个字段上有两个相同名称的唯一键约束，MySQL报错 `Duplicate key name 'config_key'`。

### 2. 表结构问题
```sql
-- 问题代码（修复前）
CREATE TABLE IF NOT EXISTS `profit_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `config_key` varchar(50) NOT NULL UNIQUE COMMENT '配置键',  -- 这里已经定义了UNIQUE
  `config_value` text NOT NULL COMMENT '配置值',
  `config_type` varchar(20) NOT NULL DEFAULT 'string' COMMENT '配置类型：string,number,json,boolean',
  `description` varchar(255) DEFAULT NULL COMMENT '配置描述',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0禁用，1启用',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `config_key` (`config_key`)  -- 这里又定义了一次UNIQUE KEY
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分润配置表';
```

## 解决方案

### 修复方法
删除字段定义中的 `UNIQUE` 关键字，保留显式的唯一键约束定义：

```sql
-- 修复后的代码
CREATE TABLE IF NOT EXISTS `profit_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `config_key` varchar(50) NOT NULL COMMENT '配置键',  -- 移除了UNIQUE
  `config_value` text NOT NULL COMMENT '配置值',
  `config_type` varchar(20) NOT NULL DEFAULT 'string' COMMENT '配置类型：string,number,json,boolean',
  `description` varchar(255) DEFAULT NULL COMMENT '配置描述',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0禁用，1启用',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `config_key` (`config_key`)  -- 保留显式的唯一键约束
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分润配置表';
```

### 修复步骤
1. **编辑建表语句文件**：删除第402行中的 `UNIQUE` 关键字
2. **重新创建表**：删除现有表并重新创建
3. **插入默认数据**：插入系统默认配置数据

## 修复验证

### 1. 表创建成功
```sql
-- 验证表结构
DESCRIBE profit_config;
```

### 2. 数据插入成功
```sql
-- 验证插入的配置数据
SELECT * FROM profit_config;
```

**插入结果：**
```
+----+----------------------+--------------+-------------+--------------------------+--------+---------------------+---------------------+
| id | config_key           | config_value | config_type | description              | status | created_at          | updated_at          |
+----+----------------------+--------------+-------------+--------------------------+--------+---------------------+---------------------+
|  1 | min_withdraw_amount  | 50           | number      | 最小提现金额             |      1 | 2025-07-29 20:02:35 | 2025-07-29 20:02:35 |
|  2 | withdraw_fee_rate    | 1            | number      | 提现手续费比例（百分比） |      1 | 2025-07-29 20:02:45 | 2025-07-29 20:02:45 |
|  3 | max_daily_withdraw   | 10000        | number      | 每日最大提现金额         |      1 | 2025-07-29 20:02:45 | 2025-07-29 20:02:45 |
|  4 | profit_settle_delay  | 24           | number      | 分润结算延迟时间（小时） |      1 | 2025-07-29 20:02:45 | 2025-07-29 20:02:45 |
|  5 | auto_settle_enabled  | true         | boolean     | 是否启用自动结算         |      1 | 2025-07-29 20:02:45 | 2025-07-29 20:02:45 |
|  6 | profit_audit_enabled | false        | boolean     | 是否启用分润审核         |      1 | 2025-07-29 20:02:45 | 2025-07-29 20:02:45 |
+----+----------------------+--------------+-------------+--------------------------+--------+---------------------+---------------------+
```

## 配置说明

### 插入的默认配置项
1. **min_withdraw_amount**: 最小提现金额 (50元)
2. **withdraw_fee_rate**: 提现手续费比例 (1%)
3. **max_daily_withdraw**: 每日最大提现金额 (10000元)
4. **profit_settle_delay**: 分润结算延迟时间 (24小时)
5. **auto_settle_enabled**: 是否启用自动结算 (true)
6. **profit_audit_enabled**: 是否启用分润审核 (false)

### 配置类型说明
- **number**: 数值类型配置
- **boolean**: 布尔类型配置 (true/false)
- **json**: JSON格式配置 (用于复杂数据结构)
- **string**: 字符串类型配置

## 最佳实践

### 1. 避免重复约束定义
```sql
-- 推荐：使用显式约束定义
CREATE TABLE example (
  id int PRIMARY KEY,
  unique_field varchar(50) NOT NULL,
  UNIQUE KEY unique_field_idx (unique_field)
);

-- 不推荐：字段定义和约束定义重复
CREATE TABLE example (
  id int PRIMARY KEY,
  unique_field varchar(50) NOT NULL UNIQUE,  -- 避免这样
  UNIQUE KEY unique_field_idx (unique_field)  -- 与上面重复
);
```

### 2. 约束命名规范
```sql
-- 推荐的约束命名
UNIQUE KEY uk_table_field (field_name)
INDEX idx_table_field (field_name)
FOREIGN KEY fk_table_ref_table (field_name)
```

### 3. 建表语句检查清单
- [ ] 检查是否有重复的约束定义
- [ ] 验证字段类型和长度
- [ ] 确认默认值设置正确
- [ ] 检查外键关系
- [ ] 验证字符集和排序规则

## 相关文件

- ✅ `数据库建表语句.sql` - 修复了重复键定义
- ✅ `insert_profit_config.sql` - 配置数据插入脚本
- ✅ `DUPLICATE_KEY_CONFIG_KEY_FIX.md` - 本修复说明文档

## 总结

**问题根源**：表定义中存在重复的唯一键约束定义
**解决方案**：删除字段定义中的UNIQUE关键字，保留显式的唯一键约束
**修复效果**：表创建成功，默认配置数据插入完成

**重要提醒**：在编写建表语句时，避免在字段定义和约束定义中重复定义相同的约束。

修复完成时间：2025-01-29
