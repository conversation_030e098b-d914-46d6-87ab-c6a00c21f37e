-- 插入测试分润数据
USE wifi_share;

-- 清空现有分润数据（可选）
-- DELETE FROM profit_log;

-- 插入WiFi分享分润数据
INSERT INTO profit_log (
  user_id, team_id, amount, source_type, source_id, order_no, 
  profit_type, role, rate, total_amount, status, settle_time, remark, created_at
) VALUES
-- 用户1的分润记录
(1, 1, 12.50, 1, 1, NULL, 'wifi_share', 'user', 20, 62.50, 1, NOW(), 'WiFi分享收益', '2025-01-15 10:30:00'),
(1, 1, 8.80, 2, 1, 'ORD20250115001', 'goods_sale', 'user', 20, 44.00, 1, NOW(), '商品销售分润', '2025-01-15 14:20:00'),
(1, 1, 3.20, 3, 1, NULL, 'advertisement', 'user', 10, 32.00, 1, NOW(), '广告点击收益', '2025-01-15 16:45:00'),

-- 今日收益
(1, 1, 15.60, 1, 2, NULL, 'wifi_share', 'user', 20, 78.00, 1, NOW(), 'WiFi分享收益', NOW()),
(1, 1, 22.40, 2, 2, 'ORD20250130001', 'goods_sale', 'user', 20, 112.00, 1, NOW(), '商品销售分润', NOW()),
(1, 1, 4.80, 3, 2, NULL, 'advertisement', 'user', 10, 48.00, 0, NULL, '广告点击收益', NOW()),

-- 本月其他收益
(1, 1, 18.90, 1, 3, NULL, 'wifi_share', 'user', 20, 94.50, 1, NOW(), 'WiFi分享收益', '2025-01-20 09:15:00'),
(1, 1, 31.20, 2, 3, 'ORD20250120001', 'goods_sale', 'user', 20, 156.00, 1, NOW(), '商品销售分润', '2025-01-20 11:30:00'),
(1, 1, 6.40, 3, 3, NULL, 'advertisement', 'user', 10, 64.00, 1, NOW(), '广告点击收益', '2025-01-20 15:20:00'),

-- 上月收益
(1, 1, 25.30, 1, 4, NULL, 'wifi_share', 'user', 20, 126.50, 1, NOW(), 'WiFi分享收益', '2024-12-15 10:00:00'),
(1, 1, 42.60, 2, 4, 'ORD20241215001', 'goods_sale', 'user', 20, 213.00, 1, NOW(), '商品销售分润', '2024-12-15 14:30:00'),
(1, 1, 8.10, 3, 4, NULL, 'advertisement', 'user', 10, 81.00, 1, NOW(), '广告点击收益', '2024-12-15 16:45:00'),

-- 用户2的分润记录（团长）
(2, 1, 25.00, 1, 1, NULL, 'wifi_share', 'leader', 40, 62.50, 1, NOW(), 'WiFi分享团长分润', '2025-01-15 10:30:00'),
(2, 1, 13.20, 2, 1, 'ORD20250115001', 'goods_sale', 'leader', 30, 44.00, 1, NOW(), '商品销售团长分润', '2025-01-15 14:20:00'),
(2, 1, 9.60, 3, 1, NULL, 'advertisement', 'leader', 30, 32.00, 1, NOW(), '广告点击团长分润', '2025-01-15 16:45:00'),

-- 今日团长收益
(2, 1, 31.20, 1, 2, NULL, 'wifi_share', 'leader', 40, 78.00, 1, NOW(), 'WiFi分享团长分润', NOW()),
(2, 1, 33.60, 2, 2, 'ORD20250130001', 'goods_sale', 'leader', 30, 112.00, 1, NOW(), '商品销售团长分润', NOW()),
(2, 1, 14.40, 3, 2, NULL, 'advertisement', 'leader', 30, 48.00, 0, NULL, '广告点击团长分润', NOW());

-- 更新用户余额
UPDATE users SET balance = balance + 200.00, total_income = total_income + 200.00 WHERE id = 1;
UPDATE users SET balance = balance + 300.00, total_income = total_income + 300.00 WHERE id = 2;

-- 更新团队统计
UPDATE teams SET 
  total_income = total_income + 500.00,
  month_income = month_income + 300.00,
  today_income = today_income + 100.00
WHERE id = 1;

SELECT '✅ 测试分润数据插入完成！' as message;
