const axios = require('axios');
const jwt = require('jsonwebtoken');
const mysql = require('mysql2/promise');

async function testAddressAdd() {
  let connection;
  try {
    console.log('开始测试地址添加功能...');
    
    // 连接数据库检查数据
    console.log('1. 连接数据库检查数据...');
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: 'wo587129955',
      database: 'mall'
    });
    
    // 检查用户是否存在
    const [users] = await connection.execute('SELECT id, openid, nickname FROM user WHERE id = 2');
    console.log('用户信息:', users);
    
    if (users.length === 0) {
      console.error('用户ID 2 不存在');
      return;
    }
    
    // 检查user_address表结构
    const [tableInfo] = await connection.execute('DESCRIBE user_address');
    console.log('user_address表结构:', tableInfo);
    
    // 直接生成JWT token
    console.log('\n2. 生成测试token...');
    const config = require('./config');
    const userId = 2;
    
    const token = jwt.sign(
      { id: userId, openid: users[0].openid },
      config.jwt.secret,
      { expiresIn: config.jwt.expiresIn }
    );
    
    console.log('生成的token:', token);
    
    // 测试添加地址
    console.log('\n3. 测试添加地址...');
    const addressData = {
      name: "xinxin",
      phone: "18720759701",
      province: "广东省",
      city: "韶关市",
      district: "武江区",
      address: "测试详细地址123号",
      is_default: 0
    };
    
    console.log('地址数据:', addressData);
    
    const addAddressResponse = await axios.post('http://localhost:4000/api/v1/client/user/address/add', addressData, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      timeout: 10000
    });
    
    console.log('地址添加响应:', addAddressResponse.data);
    
    // 检查地址数据
    console.log('\n4. 检查地址数据...');
    const [addressItems] = await connection.execute('SELECT * FROM user_address WHERE user_id = 2');
    console.log('地址数据:', addressItems);
    
  } catch (error) {
    console.error('测试失败:', error.message);
    if (error.response) {
      console.error('响应状态:', error.response.status);
      console.error('响应数据:', error.response.data);
    } else if (error.code === 'ECONNREFUSED') {
      console.error('连接被拒绝，请确保服务器正在运行');
    } else {
      console.error('其他错误:', error.code || error.message);
      console.error('错误堆栈:', error.stack);
    }
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// 运行测试
testAddressAdd();
