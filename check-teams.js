const db = require('./src/database');

async function checkTeams() {
  try {
    console.log('🔍 检查团队数据...\n');
    
    // 查询所有团队
    const teams = await db.query(`
      SELECT t.*, u.nickname as leader_name, u.phone as leader_phone
      FROM team t
      LEFT JOIN user u ON t.leader_id = u.id
      ORDER BY t.id DESC
    `);
    
    console.log(`📊 总共有 ${teams.length} 个团队:`);
    console.table(teams);
    
    // 查询团队成员
    console.log('\n👥 团队成员信息:');
    const members = await db.query(`
      SELECT tm.*, t.name as team_name, u.nickname as user_name
      FROM team_member tm
      LEFT JOIN team t ON tm.team_id = t.id
      LEFT JOIN user u ON tm.user_id = u.id
      ORDER BY tm.team_id, tm.id
    `);
    
    console.table(members);
    
    // 查询最近通过的联盟申请
    console.log('\n📋 最近通过的联盟申请:');
    const approvedApplications = await db.query(`
      SELECT ta.*, u.nickname, u.phone
      FROM team_apply ta
      LEFT JOIN user u ON ta.user_id = u.id
      WHERE ta.status = 1
      ORDER BY ta.updated_at DESC
      LIMIT 5
    `);
    
    console.table(approvedApplications);
    
    // 检查用户的团长状态
    console.log('\n👑 团长用户状态:');
    const leaders = await db.query(`
      SELECT id, nickname, phone, is_leader, team_id
      FROM user
      WHERE is_leader = 1 OR team_id IS NOT NULL
      ORDER BY id
    `);
    
    console.table(leaders);
    
    process.exit(0);
  } catch (error) {
    console.error('❌ 检查失败:', error);
    process.exit(1);
  }
}

checkTeams();
