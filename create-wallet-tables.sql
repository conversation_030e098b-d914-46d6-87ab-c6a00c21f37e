-- 创建钱包相关表
USE mall;

-- 余额交易记录表
CREATE TABLE IF NOT EXISTS `balance_transactions` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '交易记录ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `type` enum('income','expense') NOT NULL COMMENT '交易类型：income收入，expense支出',
  `amount` decimal(10,2) NOT NULL COMMENT '交易金额',
  `balance_before` decimal(10,2) NOT NULL COMMENT '交易前余额',
  `balance_after` decimal(10,2) NOT NULL COMMENT '交易后余额',
  `source_type` varchar(50) DEFAULT NULL COMMENT '来源类型：wifi_share,goods_sale,advertisement,withdraw,recharge',
  `source_id` int(11) DEFAULT NULL COMMENT '来源ID',
  `order_no` varchar(50) DEFAULT NULL COMMENT '关联订单号',
  `description` varchar(255) DEFAULT NULL COMMENT '交易描述',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0失败，1成功',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `type` (`type`),
  KEY `source_type` (`source_type`),
  KEY `created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='余额交易记录表';

-- 检查users表是否存在total_income字段，如果不存在则添加
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE TABLE_SCHEMA = 'mall' AND TABLE_NAME = 'user' AND COLUMN_NAME = 'total_income') = 0,
    'ALTER TABLE user ADD COLUMN total_income DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT "总收入"',
    'SELECT "total_income字段已存在" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查users表是否存在role字段，如果不存在则添加
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE TABLE_SCHEMA = 'mall' AND TABLE_NAME = 'user' AND COLUMN_NAME = 'role') = 0,
    'ALTER TABLE user ADD COLUMN role VARCHAR(20) DEFAULT "user" COMMENT "用户角色：user普通用户，leader团长，admin管理员"',
    'SELECT "role字段已存在" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 为现有用户设置默认值
UPDATE user SET total_income = 0.00 WHERE total_income IS NULL;
UPDATE user SET role = 'user' WHERE role IS NULL OR role = '';

-- 插入一些测试数据（如果用户表为空）
INSERT INTO user (openid, nickname, phone, balance, total_income, role, status)
SELECT 'test_openid_001', '测试用户1', '13800138001', 100.00, 50.00, 'user', 1
WHERE NOT EXISTS (SELECT 1 FROM user WHERE openid = 'test_openid_001');

INSERT INTO user (openid, nickname, phone, balance, total_income, role, status)
SELECT 'test_openid_002', '测试用户2', '13800138002', 200.00, 150.00, 'leader', 1
WHERE NOT EXISTS (SELECT 1 FROM user WHERE openid = 'test_openid_002');

INSERT INTO user (openid, nickname, phone, balance, total_income, role, status)
SELECT 'test_openid_003', '测试用户3', '13800138003', 50.00, 25.00, 'user', 1
WHERE NOT EXISTS (SELECT 1 FROM user WHERE openid = 'test_openid_003');

-- 为测试用户插入一些交易记录
INSERT INTO balance_transactions (user_id, type, amount, balance_before, balance_after, source_type, description)
SELECT 1, 'income', 50.00, 50.00, 100.00, 'wifi_share', 'WiFi分享收益'
WHERE EXISTS (SELECT 1 FROM user WHERE id = 1)
AND NOT EXISTS (SELECT 1 FROM balance_transactions WHERE user_id = 1 AND description = 'WiFi分享收益');

INSERT INTO balance_transactions (user_id, type, amount, balance_before, balance_after, source_type, description)
SELECT 2, 'income', 100.00, 100.00, 200.00, 'goods_sale', '商品销售分润'
WHERE EXISTS (SELECT 1 FROM user WHERE id = 2)
AND NOT EXISTS (SELECT 1 FROM balance_transactions WHERE user_id = 2 AND description = '商品销售分润');

INSERT INTO balance_transactions (user_id, type, amount, balance_before, balance_after, source_type, description)
SELECT 2, 'income', 50.00, 200.00, 250.00, 'advertisement', '广告点击收益'
WHERE EXISTS (SELECT 1 FROM user WHERE id = 2)
AND NOT EXISTS (SELECT 1 FROM balance_transactions WHERE user_id = 2 AND description = '广告点击收益');

INSERT INTO balance_transactions (user_id, type, amount, balance_before, balance_after, source_type, description)
SELECT 3, 'income', 25.00, 25.00, 50.00, 'wifi_share', 'WiFi分享收益'
WHERE EXISTS (SELECT 1 FROM user WHERE id = 3)
AND NOT EXISTS (SELECT 1 FROM balance_transactions WHERE user_id = 3 AND description = 'WiFi分享收益');

-- 验证创建结果
SELECT 'balance_transactions表创建完成' as message;
SELECT COUNT(*) as user_count FROM user;
SELECT COUNT(*) as transaction_count FROM balance_transactions;

-- 显示用户钱包信息
SELECT 
  u.id, u.nickname, u.phone, u.balance, u.total_income, u.role, u.status,
  COUNT(bt.id) as transaction_count,
  SUM(CASE WHEN bt.type = 'income' THEN bt.amount ELSE 0 END) as total_income_amount,
  SUM(CASE WHEN bt.type = 'expense' THEN bt.amount ELSE 0 END) as total_expense_amount
FROM user u
LEFT JOIN balance_transactions bt ON u.id = bt.user_id
GROUP BY u.id
ORDER BY u.balance DESC
LIMIT 10;
