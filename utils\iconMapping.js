/**
 * 图标映射工具
 * 将Element UI图标名称映射到本地图标路径
 */

// Element UI图标到本地图标的映射
// 临时使用现有图标作为占位符，避免404错误
const iconMapping = {
  // 数码产品 - 临时使用wifi图标
  'el-icon-mobile-phone': '/assets/icons/wifi.png',

  // 家居用品 - 临时使用home图标
  'el-icon-house': '/assets/icons/home.png',

  // 服装鞋帽 - 临时使用购物车图标
  'el-icon-shopping-bag-1': '/assets/icons/cart.png',

  // 食品饮料 - 临时使用用户图标
  'el-icon-coffee-cup': '/assets/icons/user.png',

  // 图书文具 - 临时使用二维码图标
  'el-icon-reading': '/assets/icons/qrcode.png',

  // 运动户外 - 临时使用团队图标
  'el-icon-bicycle': '/assets/icons/team-leader.png',

  // 默认图标
  'default': '/assets/icons/cart.png'
};

/**
 * 获取图标路径
 * @param {string} iconName - Element UI图标名称
 * @returns {string} 本地图标路径
 */
function getIconPath(iconName) {
  // 如果已经是完整路径，直接返回
  if (iconName && (iconName.startsWith('/') || iconName.startsWith('http'))) {
    return iconName;
  }
  
  // 映射Element UI图标名称
  return iconMapping[iconName] || iconMapping['default'];
}

/**
 * 处理商品分类数据，转换图标路径
 * @param {Array} categories - 分类数据数组
 * @returns {Array} 处理后的分类数据
 */
function processCategoryIcons(categories) {
  if (!Array.isArray(categories)) {
    return [];
  }
  
  return categories.map(category => ({
    ...category,
    icon: getIconPath(category.icon)
  }));
}

/**
 * 检查图标是否为HTTP协议
 * @param {string} iconPath - 图标路径
 * @returns {boolean} 是否为HTTP协议
 */
function isHttpIcon(iconPath) {
  return iconPath && iconPath.startsWith('http://');
}

/**
 * 将HTTP图标转换为HTTPS或本地路径
 * @param {string} iconPath - 图标路径
 * @returns {string} 转换后的路径
 */
function convertHttpIcon(iconPath) {
  if (!iconPath) {
    return iconMapping['default'];
  }
  
  // 如果是HTTP协议，转换为HTTPS
  if (iconPath.startsWith('http://')) {
    return iconPath.replace('http://', 'https://');
  }
  
  // 如果是相对路径，保持不变
  if (iconPath.startsWith('/')) {
    return iconPath;
  }
  
  // 其他情况返回默认图标
  return iconMapping['default'];
}

module.exports = {
  iconMapping,
  getIconPath,
  processCategoryIcons,
  isHttpIcon,
  convertHttpIcon
};
