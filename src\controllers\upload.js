const path = require('path');
const fs = require('fs');
const multer = require('multer');
const { v4: uuidv4 } = require('uuid');
const config = require('../../config');

// 配置存储
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    // 根据路径确定上传目录
    let uploadDir;
    
    if (req.path.includes('/upload/image')) {
      uploadDir = path.join(process.cwd(), 'public', 'uploads', 'images');
    } else if (req.path.includes('/qrcodes')) {
      uploadDir = path.join(process.cwd(), 'public', 'uploads', 'qrcodes');
    } else {
      uploadDir = path.join(process.cwd(), 'public', 'uploads', 'images');
    }
    
    console.log('上传目标目录:', uploadDir);
    
    // 确保目录存在
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    
    cb(null, uploadDir);
  },
  filename: function (req, file, cb) {
    // 生成唯一文件名
    const timestamp = Date.now();
    const uniqueId = uuidv4().substring(0, 8);
    const ext = path.extname(file.originalname);
    cb(null, `${timestamp}_${uniqueId}${ext}`);
  }
});

// 文件过滤器
const fileFilter = (req, file, cb) => {
  // 接受的文件类型
  const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
  
  if (allowedTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new Error('不支持的文件类型! 仅支持JPG, PNG, GIF和WEBP格式'), false);
  }
};

// 设置上传配置
const upload = multer({ 
  storage: storage,
  fileFilter: fileFilter,
  limits: {
    fileSize: 5 * 1024 * 1024 // 限制5MB
  }
});

// 处理Base64编码的图片数据
const saveBase64Image = (base64String, wifiId = null) => {
  return new Promise((resolve, reject) => {
    try {
      // 检查是否为base64字符串
      if (!base64String || !base64String.startsWith('data:image')) {
        return reject(new Error('无效的Base64图片数据'));
      }
      
      // 解析出mime类型和数据部分
      const matches = base64String.match(/^data:([A-Za-z-+\/]+);base64,(.+)$/);
      if (!matches || matches.length !== 3) {
        return reject(new Error('无效的Base64图片格式'));
      }
      
      const mimeType = matches[1];
      const base64Data = matches[2];
      
      // 验证mime类型
      if (!['image/jpeg', 'image/png', 'image/gif', 'image/webp'].includes(mimeType)) {
        return reject(new Error('不支持的图片类型'));
      }
      
      // 生成文件扩展名
      let extension = '.jpg'; // 默认
      if (mimeType === 'image/png') extension = '.png';
      else if (mimeType === 'image/gif') extension = '.gif';
      else if (mimeType === 'image/webp') extension = '.webp';
      
      // 生成唯一文件名
      const timestamp = Date.now();
      const uniqueId = uuidv4().substring(0, 8);
      const fileName = `${timestamp}_${uniqueId}${extension}`;
      
      // 确定保存目录
      let uploadDir = path.join(process.cwd(), 'public', 'uploads', 'images');
      
      // 如果是WiFi二维码，使用特定目录
      if (wifiId) {
        uploadDir = path.join(process.cwd(), 'public', 'uploads', 'qrcodes');
      }
      
      // 确保目录存在
      if (!fs.existsSync(uploadDir)) {
        fs.mkdirSync(uploadDir, { recursive: true });
      }
      
      // 完整文件路径
      const filePath = path.join(uploadDir, fileName);
      
      // 将base64数据转换为文件
      const fileBuffer = Buffer.from(base64Data, 'base64');
      fs.writeFileSync(filePath, fileBuffer);
      
      // 构建访问URL - 使用相对路径
      const fileUrl = wifiId 
        ? `/uploads/qrcodes/${fileName}`
        : `/uploads/images/${fileName}`;
      
      console.log('Base64图片保存成功，路径:', fileUrl);
      
      resolve({
        url: fileUrl,
        filename: fileName,
        mimetype: mimeType,
        size: fileBuffer.length
      });
    } catch (error) {
      console.error('保存Base64图片失败:', error);
      reject(error);
    }
  });
};

// 单文件上传处理器
exports.uploadSingle = (req, res) => {
  try {
    console.log('处理文件上传请求，路径:', req.path);
    
    // 检查是否是Base64上传模式
    if (req.body && req.body.imageData && !req.file) {
      console.log('检测到Base64图片上传');
      
      // 如果包含wifiId参数，说明是二维码
      const wifiId = req.body.wifiId || null;
      
      saveBase64Image(req.body.imageData, wifiId)
        .then(fileInfo => {
          console.log('Base64图片上传成功:', fileInfo);
          return res.json({
            code: 200,
            success: true,
            data: fileInfo,
            message: '文件上传成功'
          });
        })
        .catch(error => {
          console.error('Base64图片上传失败:', error);
          return res.status(400).json({
            code: 400,
            success: false,
            message: '文件上传失败: ' + error.message
          });
        });
      return;
    }
    
    // 常规文件上传模式
    if (!req.file) {
      return res.status(400).json({
        code: 400,
        success: false,
        message: '没有文件被上传'
      });
    }

    console.log('上传文件信息:', req.file);

    // 构建访问URL - 使用相对路径
    let fileUrl;
    if (req.path.includes('/qrcodes')) {
      fileUrl = `/uploads/qrcodes/${req.file.filename}`;
    } else {
      fileUrl = `/uploads/images/${req.file.filename}`;
    }
    
    console.log('文件上传成功，相对路径:', fileUrl);
    
    // 返回成功响应
    return res.json({
      code: 200,
      success: true,
      data: {
        url: fileUrl,
        filename: req.file.filename,
        originalname: req.file.originalname,
        size: req.file.size,
        mimetype: req.file.mimetype
      },
      message: '文件上传成功'
    });
  } catch (error) {
    console.error('上传文件时出错:', error);
    return res.status(500).json({
      code: 500,
      success: false,
      message: '文件上传失败: ' + error.message
    });
  }
};

// 多文件上传处理器
exports.uploadMultiple = (req, res) => {
  try {
    if (!req.files || req.files.length === 0) {
      return res.status(400).json({
        code: 400,
        success: false,
        message: '没有文件被上传'
      });
    }
    
    const uploadedFiles = req.files.map(file => {
      const fileUrl = `/uploads/images/${file.filename}`;
      return {
        url: fileUrl,
        filename: file.filename,
        originalname: file.originalname,
        size: file.size,
        mimetype: file.mimetype
      };
    });
    
    console.log('多文件上传成功，文件数量:', uploadedFiles.length);

    return res.json({
      code: 200,
      success: true,
      data: uploadedFiles,
      message: '文件上传成功'
    });
  } catch (error) {
    console.error('上传多个文件时出错:', error);
    return res.status(500).json({
      code: 500,
      success: false,
      message: '文件上传失败: ' + error.message
    });
  }
};

// 删除文件处理器
exports.deleteFile = (req, res) => {
  try {
    const { filename } = req.params;
    
    // 验证文件名
    if (!filename || filename.includes('..')) {
      return res.status(400).json({
        code: 400,
        success: false,
        message: '无效的文件名'
      });
    }
    
    const filePath = path.join(process.cwd(), 'public', 'uploads', 'images', filename);
    
    // 检查文件是否存在
    if (!fs.existsSync(filePath)) {
      return res.status(404).json({
        code: 404,
        success: false,
        message: '文件不存在'
      });
    }
    
    // 删除文件
    fs.unlinkSync(filePath);
    
    return res.json({
      code: 200,
      success: true,
      message: '文件删除成功'
    });
  } catch (error) {
    console.error('删除文件时出错:', error);
    return res.status(500).json({
      code: 500,
      success: false,
      message: '删除文件失败: ' + error.message
    });
  }
};

// 导出中间件
exports.uploadMiddleware = upload.single('file');
exports.uploadMultipleMiddleware = upload.array('files', 10); 