/**
 * 基础错误类
 */
class AppError extends Error {
  constructor(message, statusCode) {
    super(message);
    this.name = this.constructor.name;
    this.statusCode = statusCode;
    Error.captureStackTrace(this, this.constructor);
  }
}

/**
 * 400错误 - 错误请求
 */
class BadRequestError extends AppError {
  constructor(message = '请求参数错误') {
    super(message, 400);
  }
}

/**
 * 401错误 - 未授权
 */
class UnauthorizedError extends AppError {
  constructor(message = '未授权，请登录') {
    super(message, 401);
  }
}

/**
 * 403错误 - 禁止访问
 */
class ForbiddenError extends AppError {
  constructor(message = '您没有权限执行此操作') {
    super(message, 403);
  }
}

/**
 * 404错误 - 资源未找到
 */
class NotFoundError extends AppError {
  constructor(message = '请求的资源不存在') {
    super(message, 404);
  }
}

/**
 * 409错误 - 资源冲突
 */
class ConflictError extends AppError {
  constructor(message = '资源冲突') {
    super(message, 409);
  }
}

/**
 * 500错误 - 服务器内部错误
 */
class ServerError extends AppError {
  constructor(message = '服务器内部错误') {
    super(message, 500);
  }
}

module.exports = {
  AppError,
  BadRequestError,
  UnauthorizedError,
  ForbiddenError,
  NotFoundError,
  ConflictError,
  ServerError
}; 