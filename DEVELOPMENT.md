# 开发指南

## 快速开始

### 1. 环境准备

1. **下载微信开发者工具**
   - 访问 [微信公众平台](https://developers.weixin.qq.com/miniprogram/dev/devtools/download.html)
   - 下载并安装微信开发者工具
   - 使用微信扫码登录

2. **后端服务**
   - 确保后端服务运行在 `http://localhost:4000`
   - 数据库服务正常运行
   - API接口可正常访问

### 2. 项目导入

1. 打开微信开发者工具
2. 选择"导入项目"
3. 项目目录选择 `wifi-share-miniapp` 文件夹
4. AppID填入测试AppID或选择"测试号"
5. 点击"导入"按钮

### 3. 项目配置

#### 修改AppID（可选）
```json
// project.config.json
{
  "appid": "your-real-appid"
}
```

#### 修改API地址（如果需要）
```javascript
// config/config.js
api: {
  baseUrl: 'https://your-api-domain.com/api/v1/client'
}
```

### 4. 启动开发

1. 在微信开发者工具中点击"编译"按钮
2. 查看控制台确认无报错
3. 在模拟器中预览效果
4. 使用"真机调试"测试真实设备效果

## 开发规范

### 代码规范

1. **文件命名**
   - 页面文件：使用描述性名称，如 `create.js`、`list.js`
   - 组件文件：使用PascalCase，如 `UserCard.js`
   - 工具文件：使用camelCase，如 `util.js`、`request.js`

2. **代码格式**
   - 使用2个空格缩进
   - 函数和变量使用camelCase
   - 常量使用UPPER_CASE
   - 及时添加注释说明

3. **目录结构**
   ```
   pages/
     ├── feature/          # 功能模块
     │   ├── subpage/     # 子页面
     │   │   ├── subpage.js
     │   │   ├── subpage.wxml
     │   │   ├── subpage.wxss
     │   │   └── subpage.json
   ```

### 样式规范

1. **单位使用**
   - 使用rpx作为主要单位（自适应不同屏幕）
   - 1px边框使用1rpx
   - 字体大小建议使用偶数值

2. **颜色规范**
   ```css
   /* 主色调 */
   --primary-color: #07c160;
   --secondary-color: #1890ff;
   
   /* 文字颜色 */
   --text-primary: #333333;
   --text-secondary: #666666;
   --text-muted: #999999;
   
   /* 背景颜色 */
   --bg-primary: #ffffff;
   --bg-secondary: #f5f5f5;
   ```

3. **布局规范**
   - 优先使用flex布局
   - 合理使用grid布局
   - 保持一致的间距规范

### API调用规范

1. **使用封装的request**
   ```javascript
   const request = require('../../utils/request.js')
   
   // GET请求
   request.get('/api/goods/list', { page: 1 })
   
   // POST请求
   request.post('/api/wifi/create', wifiData)
   ```

2. **错误处理**
   ```javascript
   try {
     const result = await request.get('/api/data')
     // 处理成功结果
   } catch (error) {
     // 处理错误
     console.error('请求失败：', error.message)
   }
   ```

3. **Loading状态**
   - 重要操作显示loading
   - 避免重复提交
   - 超时处理

## 调试技巧

### 1. 控制台调试
- 使用 `console.log()` 输出调试信息
- 使用 `console.error()` 输出错误信息
- 查看Network面板检查网络请求

### 2. 真机调试
- 使用"真机调试"功能
- 检查在真实设备上的表现
- 测试不同机型的兼容性

### 3. 性能优化
- 使用Performance面板分析性能
- 检查内存使用情况
- 优化图片和资源加载

## 常见问题

### 1. 页面跳转失败
```javascript
// 错误写法
wx.navigateTo({
  url: '/pages/detail/detail?id=' + id
})

// 正确写法
wx.navigateTo({
  url: `/pages/detail/detail?id=${id}`
})
```

### 2. 数据更新不及时
```javascript
// 确保使用setData更新数据
this.setData({
  userInfo: newUserInfo
})
```

### 3. 图片路径问题
```javascript
// 使用绝对路径
src="/assets/images/banner.jpg"

// 或相对路径
src="../../assets/images/banner.jpg"
```

### 4. API请求跨域
- 在后端配置CORS
- 在小程序后台配置合法域名

## 发布流程

### 1. 代码检查
- 检查代码质量
- 确认功能完整性
- 测试各种场景

### 2. 版本管理
- 更新版本号
- 添加更新日志
- 提交代码到版本库

### 3. 小程序发布
1. 在微信开发者工具中点击"上传"
2. 填写版本号和项目备注
3. 上传成功后在微信公众平台提交审核
4. 审核通过后发布上线

## 性能优化建议

### 1. 代码优化
- 避免在onShow中执行耗时操作
- 合理使用lazy loading
- 减少不必要的数据绑定

### 2. 资源优化
- 压缩图片资源
- 使用适当的图片格式
- 启用图片懒加载

### 3. 网络优化
- 减少API请求次数
- 使用数据缓存
- 优化请求参数

### 4. 内存优化
- 及时清理不用的数据
- 避免内存泄漏
- 合理使用本地存储

---

有问题请查看官方文档或联系开发团队。 