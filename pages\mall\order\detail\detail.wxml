<!--pages/mall/order/detail/detail.wxml-->
<view class="container" wx:if="{{!loading && orderInfo}}">
  <!-- 订单状态 -->
  <view class="status-section">
    <view class="status-icon">
      <text class="status-emoji">
        {{orderInfo.status === 0 ? '💰' : orderInfo.status === 1 ? '📦' : orderInfo.status === 2 ? '🚚' : orderInfo.status === 3 ? '✅' : '❌'}}
      </text>
    </view>
    <view class="status-info">
      <text class="status-text">{{orderInfo.status_name}}</text>
      <text class="status-desc" wx:if="{{orderInfo.status === 0}}">请在24小时内完成支付</text>
      <text class="status-desc" wx:if="{{orderInfo.status === 1}}">商家正在准备您的商品</text>
      <text class="status-desc" wx:if="{{orderInfo.status === 2}}">商品正在配送中，请耐心等待</text>
      <text class="status-desc" wx:if="{{orderInfo.status === 3}}">感谢您的购买</text>
    </view>
  </view>

  <!-- 收货地址 -->
  <view class="address-section">
    <view class="section-title">
      <text class="title-icon">📍</text>
      <text>收货地址</text>
    </view>
    <view class="address-info">
      <view class="receiver-info">
        <text class="receiver-name">{{orderInfo.receiver_name}}</text>
        <text class="receiver-phone">{{orderInfo.receiver_phone}}</text>
      </view>
      <text class="receiver-address">{{orderInfo.receiver_address}}</text>
    </view>
  </view>

  <!-- 商品信息 -->
  <view class="goods-section">
    <view class="section-title">
      <text class="title-icon">🛍️</text>
      <text>商品信息</text>
    </view>
    <view class="goods-list">
      <view class="goods-item" wx:for="{{orderInfo.goods_list}}" wx:key="id">
        <image class="goods-image" src="{{item.image}}" mode="aspectFill"></image>
        <view class="goods-info">
          <text class="goods-name">{{item.name}}</text>
          <text class="goods-spec">{{item.spec}}</text>
          <view class="goods-price-qty">
            <text class="goods-price">¥{{item.price}}</text>
            <text class="goods-qty">x{{item.quantity}}</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 订单信息 -->
  <view class="order-section">
    <view class="section-title">
      <text class="title-icon">📋</text>
      <text>订单信息</text>
    </view>
    <view class="order-info">
      <view class="info-row">
        <text class="info-label">订单编号</text>
        <view class="info-value">
          <text>{{orderInfo.order_no}}</text>
          <text class="copy-btn" bindtap="onCopyOrderNo">复制</text>
        </view>
      </view>
      <view class="info-row">
        <text class="info-label">下单时间</text>
        <text class="info-value">{{orderInfo.created_at}}</text>
      </view>
      <view class="info-row" wx:if="{{orderInfo.payment_time}}">
        <text class="info-label">支付时间</text>
        <text class="info-value">{{orderInfo.payment_time}}</text>
      </view>
      <view class="info-row" wx:if="{{orderInfo.delivery_time}}">
        <text class="info-label">发货时间</text>
        <text class="info-value">{{orderInfo.delivery_time}}</text>
      </view>
      <view class="info-row" wx:if="{{orderInfo.finish_time}}">
        <text class="info-label">完成时间</text>
        <text class="info-value">{{orderInfo.finish_time}}</text>
      </view>
      <view class="info-row">
        <text class="info-label">支付方式</text>
        <text class="info-value">{{orderInfo.payment_method_name}}</text>
      </view>
      <view class="info-row" wx:if="{{orderInfo.remark}}">
        <text class="info-label">订单备注</text>
        <text class="info-value">{{orderInfo.remark}}</text>
      </view>
    </view>
  </view>

  <!-- 费用明细 -->
  <view class="amount-section">
    <view class="section-title">
      <text class="title-icon">💰</text>
      <text>费用明细</text>
    </view>
    <view class="amount-info">
      <view class="amount-row">
        <text class="amount-label">商品总价</text>
        <text class="amount-value">¥{{orderInfo.goods_amount}}</text>
      </view>
      <view class="amount-row" wx:if="{{orderInfo.delivery_fee > 0}}">
        <text class="amount-label">配送费</text>
        <text class="amount-value">¥{{orderInfo.delivery_fee}}</text>
      </view>
      <view class="amount-row" wx:if="{{orderInfo.coupon_amount > 0}}">
        <text class="amount-label">优惠券</text>
        <text class="amount-value discount">-¥{{orderInfo.coupon_amount}}</text>
      </view>
      <view class="amount-row total">
        <text class="amount-label">实付金额</text>
        <text class="amount-value">¥{{orderInfo.total_amount}}</text>
      </view>
    </view>
  </view>

  <!-- 操作按钮 -->
  <view class="action-section">
    <button class="action-btn secondary" bindtap="onContactService">联系客服</button>
    
    <!-- 待付款状态 -->
    <block wx:if="{{orderInfo.status === 0}}">
      <button class="action-btn secondary" bindtap="onCancelOrder">取消订单</button>
      <button class="action-btn primary" bindtap="onPayNow">立即支付</button>
    </block>
    
    <!-- 待收货状态 -->
    <block wx:if="{{orderInfo.status === 2}}">
      <button class="action-btn secondary" bindtap="onViewLogistics">查看物流</button>
      <button class="action-btn primary" bindtap="onConfirmReceive">确认收货</button>
    </block>
    
    <!-- 待发货状态 -->
    <block wx:if="{{orderInfo.status === 1}}">
      <button class="action-btn secondary" bindtap="onViewLogistics">查看物流</button>
    </block>
  </view>
</view>

<!-- 加载状态 -->
<view class="loading-container" wx:if="{{loading}}">
  <text>加载中...</text>
</view>

<!-- 错误状态 -->
<view class="error-container" wx:if="{{!loading && !orderInfo}}">
  <text class="error-icon">❌</text>
  <text class="error-text">订单不存在或已被删除</text>
  <button class="error-btn" bindtap="onGoBack">返回</button>
</view>
