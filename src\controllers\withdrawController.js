/**
 * 提现控制器
 * 处理用户提现相关的所有请求
 */

const withdrawService = require('../services/withdrawService');
const { validationResult } = require('express-validator');

class WithdrawController {
  
  /**
   * 获取提现配置
   * GET /api/v1/client/withdraw/config
   */
  async getConfig(req, res) {
    try {
      const userId = req.user.id;
      
      // 获取提现方式配置
      const methods = await withdrawService.getWithdrawMethods();
      
      // 获取用户余额
      const userBalance = await withdrawService.getUserBalance(userId);
      
      // 获取提现限制
      const limits = await withdrawService.getWithdrawLimits(userId);
      
      res.json({
        success: true,
        message: '获取提现配置成功',
        data: {
          methods,
          user_balance: userBalance,
          daily_limit: limits.daily_limit,
          monthly_limit: limits.monthly_limit,
          min_withdraw_amount: limits.min_withdraw_amount
        }
      });
      
    } catch (error) {
      console.error('获取提现配置失败:', error);
      res.status(500).json({
        success: false,
        message: '获取提现配置失败',
        error: error.message
      });
    }
  }
  
  /**
   * 获取用户提现账户
   * GET /api/v1/client/withdraw/accounts
   */
  async getAccounts(req, res) {
    try {
      const userId = req.user.id;
      const { method_code } = req.query;
      
      const accounts = await withdrawService.getUserWithdrawAccounts(userId, method_code);
      
      res.json({
        success: true,
        message: '获取提现账户成功',
        data: accounts
      });
      
    } catch (error) {
      console.error('获取提现账户失败:', error);
      res.status(500).json({
        success: false,
        message: '获取提现账户失败',
        error: error.message
      });
    }
  }
  
  /**
   * 计算提现手续费
   * POST /api/v1/client/withdraw/calculate-fee
   */
  async calculateFee(req, res) {
    try {
      // 验证请求参数
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: '参数验证失败',
          errors: errors.array()
        });
      }
      
      const { method_code, amount } = req.body;
      
      // 计算手续费
      const feeInfo = await withdrawService.calculateWithdrawFee(method_code, amount);
      
      res.json({
        success: true,
        message: '计算手续费成功',
        data: feeInfo
      });
      
    } catch (error) {
      console.error('计算手续费失败:', error);
      res.status(500).json({
        success: false,
        message: '计算手续费失败',
        error: error.message
      });
    }
  }
  
  /**
   * 提交提现申请
   * POST /api/v1/client/withdraw/apply
   */
  async apply(req, res) {
    try {
      // 验证请求参数
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: '参数验证失败',
          errors: errors.array()
        });
      }
      
      const userId = req.user.id;
      const { method_code, amount, account_id, remark } = req.body;
      
      // 提交提现申请
      const result = await withdrawService.submitWithdrawApplication({
        userId,
        methodCode: method_code,
        amount,
        accountId: account_id,
        remark,
        ipAddress: req.ip,
        userAgent: req.get('User-Agent')
      });
      
      res.json({
        success: true,
        message: '提现申请提交成功',
        data: result
      });
      
    } catch (error) {
      console.error('提现申请失败:', error);
      
      // 根据错误类型返回不同的状态码
      let statusCode = 500;
      if (error.message.includes('余额不足')) {
        statusCode = 400;
      } else if (error.message.includes('超出限额')) {
        statusCode = 400;
      } else if (error.message.includes('账户不存在')) {
        statusCode = 404;
      }
      
      res.status(statusCode).json({
        success: false,
        message: error.message || '提现申请失败',
        error: error.message
      });
    }
  }
  
  /**
   * 获取提现记录
   * GET /api/v1/client/withdraw/records
   */
  async getRecords(req, res) {
    try {
      const userId = req.user.id;
      const { page = 1, limit = 10, status, method_code } = req.query;
      
      const result = await withdrawService.getWithdrawRecords({
        userId,
        page: parseInt(page),
        limit: parseInt(limit),
        status,
        methodCode: method_code
      });
      
      res.json({
        success: true,
        message: '获取提现记录成功',
        data: result
      });
      
    } catch (error) {
      console.error('获取提现记录失败:', error);
      res.status(500).json({
        success: false,
        message: '获取提现记录失败',
        error: error.message
      });
    }
  }
  
  /**
   * 获取提现详情
   * GET /api/v1/client/withdraw/detail/:id
   */
  async getDetail(req, res) {
    try {
      const userId = req.user.id;
      const { id } = req.params;
      
      const detail = await withdrawService.getWithdrawDetail(id, userId);
      
      if (!detail) {
        return res.status(404).json({
          success: false,
          message: '提现记录不存在'
        });
      }
      
      res.json({
        success: true,
        message: '获取提现详情成功',
        data: detail
      });
      
    } catch (error) {
      console.error('获取提现详情失败:', error);
      res.status(500).json({
        success: false,
        message: '获取提现详情失败',
        error: error.message
      });
    }
  }
  
  /**
   * 取消提现申请
   * POST /api/v1/client/withdraw/cancel/:id
   */
  async cancel(req, res) {
    try {
      const userId = req.user.id;
      const { id } = req.params;
      const { reason } = req.body;
      
      const result = await withdrawService.cancelWithdrawApplication(id, userId, reason);
      
      res.json({
        success: true,
        message: '取消提现申请成功',
        data: result
      });
      
    } catch (error) {
      console.error('取消提现申请失败:', error);
      res.status(500).json({
        success: false,
        message: error.message || '取消提现申请失败',
        error: error.message
      });
    }
  }
  
  /**
   * 添加提现账户
   * POST /api/v1/client/withdraw/account/add
   */
  async addAccount(req, res) {
    try {
      // 验证请求参数
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: '参数验证失败',
          errors: errors.array()
        });
      }
      
      const userId = req.user.id;
      const accountData = req.body;
      
      const result = await withdrawService.addWithdrawAccount(userId, accountData);
      
      res.json({
        success: true,
        message: '添加提现账户成功',
        data: result
      });
      
    } catch (error) {
      console.error('添加提现账户失败:', error);
      res.status(500).json({
        success: false,
        message: error.message || '添加提现账户失败',
        error: error.message
      });
    }
  }
  
  /**
   * 删除提现账户
   * DELETE /api/v1/client/withdraw/account/:id
   */
  async deleteAccount(req, res) {
    try {
      const userId = req.user.id;
      const { id } = req.params;
      
      await withdrawService.deleteWithdrawAccount(id, userId);
      
      res.json({
        success: true,
        message: '删除提现账户成功'
      });
      
    } catch (error) {
      console.error('删除提现账户失败:', error);
      res.status(500).json({
        success: false,
        message: error.message || '删除提现账户失败',
        error: error.message
      });
    }
  }
}

module.exports = new WithdrawController();
