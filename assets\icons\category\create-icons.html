<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>创建分类图标</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .icon-container {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin-top: 20px;
        }
        .icon-item {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            text-align: center;
            width: 200px;
        }
        .icon-svg {
            width: 64px;
            height: 64px;
            margin-bottom: 10px;
        }
        .icon-name {
            font-weight: bold;
            margin-bottom: 5px;
        }
        .icon-desc {
            color: #666;
            font-size: 12px;
        }
        button {
            background: #007aff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-top: 10px;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <h1>商品分类图标生成器</h1>
    <p>点击下载按钮可以将SVG图标保存为PNG文件</p>
    
    <div class="icon-container">
        <!-- 数码产品 -->
        <div class="icon-item">
            <svg class="icon-svg" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
                <rect x="18" y="8" width="28" height="48" rx="4" stroke="#333" stroke-width="2" fill="none"/>
                <rect x="22" y="12" width="20" height="32" fill="#f0f0f0"/>
                <circle cx="32" cy="50" r="3" fill="#333"/>
            </svg>
            <div class="icon-name">数码产品</div>
            <div class="icon-desc">mobile-phone.png</div>
            <button onclick="downloadIcon(this, 'mobile-phone')">下载PNG</button>
        </div>
        
        <!-- 家居用品 -->
        <div class="icon-item">
            <svg class="icon-svg" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M8 32L32 8L56 32V56H40V40H24V56H8V32Z" stroke="#333" stroke-width="2" fill="none"/>
                <rect x="28" y="46" width="8" height="10" fill="#333"/>
            </svg>
            <div class="icon-name">家居用品</div>
            <div class="icon-desc">house.png</div>
            <button onclick="downloadIcon(this, 'house')">下载PNG</button>
        </div>
        
        <!-- 服装鞋帽 -->
        <div class="icon-item">
            <svg class="icon-svg" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M16 20L20 16H44L48 20V56H16V20Z" stroke="#333" stroke-width="2" fill="none"/>
                <path d="M20 16V12C20 10 22 8 24 8H40C42 8 44 10 44 12V16" stroke="#333" stroke-width="2" fill="none"/>
                <circle cx="32" cy="32" r="2" fill="#333"/>
            </svg>
            <div class="icon-name">服装鞋帽</div>
            <div class="icon-desc">shopping-bag.png</div>
            <button onclick="downloadIcon(this, 'shopping-bag')">下载PNG</button>
        </div>
        
        <!-- 食品饮料 -->
        <div class="icon-item">
            <svg class="icon-svg" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M20 24H44V52C44 54 42 56 40 56H24C22 56 20 54 20 52V24Z" stroke="#333" stroke-width="2" fill="none"/>
                <path d="M16 24H48" stroke="#333" stroke-width="2"/>
                <path d="M28 8V24" stroke="#333" stroke-width="2"/>
                <path d="M36 8V24" stroke="#333" stroke-width="2"/>
                <path d="M24 8H40" stroke="#333" stroke-width="2"/>
            </svg>
            <div class="icon-name">食品饮料</div>
            <div class="icon-desc">coffee-cup.png</div>
            <button onclick="downloadIcon(this, 'coffee-cup')">下载PNG</button>
        </div>
        
        <!-- 图书文具 -->
        <div class="icon-item">
            <svg class="icon-svg" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
                <rect x="16" y="8" width="32" height="48" rx="2" stroke="#333" stroke-width="2" fill="none"/>
                <line x1="24" y1="20" x2="40" y2="20" stroke="#333" stroke-width="2"/>
                <line x1="24" y1="28" x2="40" y2="28" stroke="#333" stroke-width="2"/>
                <line x1="24" y1="36" x2="36" y2="36" stroke="#333" stroke-width="2"/>
            </svg>
            <div class="icon-name">图书文具</div>
            <div class="icon-desc">reading.png</div>
            <button onclick="downloadIcon(this, 'reading')">下载PNG</button>
        </div>
        
        <!-- 运动户外 -->
        <div class="icon-item">
            <svg class="icon-svg" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
                <circle cx="20" cy="44" r="12" stroke="#333" stroke-width="2" fill="none"/>
                <circle cx="44" cy="44" r="12" stroke="#333" stroke-width="2" fill="none"/>
                <path d="M32 44H20M44 44H36" stroke="#333" stroke-width="2"/>
                <path d="M32 20L28 32L36 32L32 20Z" stroke="#333" stroke-width="2" fill="none"/>
                <path d="M32 20V16" stroke="#333" stroke-width="2"/>
            </svg>
            <div class="icon-name">运动户外</div>
            <div class="icon-desc">bicycle.png</div>
            <button onclick="downloadIcon(this, 'bicycle')">下载PNG</button>
        </div>
        
        <!-- 默认图标 -->
        <div class="icon-item">
            <svg class="icon-svg" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
                <rect x="8" y="8" width="48" height="48" rx="8" stroke="#333" stroke-width="2" fill="none"/>
                <circle cx="32" cy="24" r="6" stroke="#333" stroke-width="2" fill="none"/>
                <path d="M20 48C20 40 25 36 32 36C39 36 44 40 44 48" stroke="#333" stroke-width="2" fill="none"/>
            </svg>
            <div class="icon-name">默认图标</div>
            <div class="icon-desc">default.png</div>
            <button onclick="downloadIcon(this, 'default')">下载PNG</button>
        </div>
    </div>

    <script>
        function downloadIcon(button, filename) {
            const iconItem = button.parentElement;
            const svg = iconItem.querySelector('.icon-svg');
            
            // 创建canvas
            const canvas = document.createElement('canvas');
            canvas.width = 64;
            canvas.height = 64;
            const ctx = canvas.getContext('2d');
            
            // 设置白色背景
            ctx.fillStyle = 'white';
            ctx.fillRect(0, 0, 64, 64);
            
            // 将SVG转换为图片
            const svgData = new XMLSerializer().serializeToString(svg);
            const img = new Image();
            const svgBlob = new Blob([svgData], {type: 'image/svg+xml;charset=utf-8'});
            const url = URL.createObjectURL(svgBlob);
            
            img.onload = function() {
                ctx.drawImage(img, 0, 0, 64, 64);
                
                // 下载PNG
                canvas.toBlob(function(blob) {
                    const link = document.createElement('a');
                    link.download = filename + '.png';
                    link.href = URL.createObjectURL(blob);
                    link.click();
                    URL.revokeObjectURL(url);
                }, 'image/png');
            };
            
            img.src = url;
        }
    </script>
</body>
</html>
