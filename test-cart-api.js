// 测试购物车API的脚本
const axios = require('axios');

// 配置
const BASE_URL = 'http://localhost:4000';
const TEST_TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6MywidXNlcm5hbWUiOiJ0ZXN0IiwiaWF0IjoxNzM2NzU5NzI5LCJleHAiOjE3MzY4NDYxMjl9.example'; // 请替换为实际的token

async function testCartAPIs() {
  console.log('开始测试购物车API...\n');
  
  const headers = {
    'Authorization': `Bearer ${TEST_TOKEN}`,
    'Content-Type': 'application/json'
  };
  
  try {
    // 1. 测试购物车列表API
    console.log('1. 测试购物车列表API...');
    try {
      const listResponse = await axios.get(`${BASE_URL}/api/v1/client/cart/list`, { headers });
      console.log('✅ 购物车列表API正常:', listResponse.status);
      console.log('   响应数据:', JSON.stringify(listResponse.data, null, 2));
    } catch (error) {
      console.log('❌ 购物车列表API失败:', error.response?.status, error.response?.data || error.message);
    }
    
    console.log('\n');
    
    // 2. 测试购物车添加API
    console.log('2. 测试购物车添加API...');
    try {
      const addResponse = await axios.post(`${BASE_URL}/api/v1/client/cart/add`, {
        goodsId: 1,
        quantity: 1
      }, { headers });
      console.log('✅ 购物车添加API正常:', addResponse.status);
      console.log('   响应数据:', JSON.stringify(addResponse.data, null, 2));
    } catch (error) {
      console.log('❌ 购物车添加API失败:', error.response?.status, error.response?.data || error.message);
    }
    
    console.log('\n');
    
    // 3. 测试购物车更新API
    console.log('3. 测试购物车更新API...');
    try {
      const updateResponse = await axios.post(`${BASE_URL}/api/v1/client/cart/update`, {
        id: 1,
        quantity: 2,
        selected: true
      }, { headers });
      console.log('✅ 购物车更新API正常:', updateResponse.status);
      console.log('   响应数据:', JSON.stringify(updateResponse.data, null, 2));
    } catch (error) {
      console.log('❌ 购物车更新API失败:', error.response?.status, error.response?.data || error.message);
      
      if (error.response?.status === 404) {
        console.log('   这是我们要修复的问题！');
      }
    }
    
    console.log('\n');
    
    // 4. 测试购物车删除API
    console.log('4. 测试购物车删除API...');
    try {
      const removeResponse = await axios.post(`${BASE_URL}/api/v1/client/cart/remove`, {
        ids: [1]
      }, { headers });
      console.log('✅ 购物车删除API正常:', removeResponse.status);
      console.log('   响应数据:', JSON.stringify(removeResponse.data, null, 2));
    } catch (error) {
      console.log('❌ 购物车删除API失败:', error.response?.status, error.response?.data || error.message);
    }
    
    console.log('\n');
    
    // 5. 测试购物车清空API
    console.log('5. 测试购物车清空API...');
    try {
      const clearResponse = await axios.post(`${BASE_URL}/api/v1/client/cart/clear`, {}, { headers });
      console.log('✅ 购物车清空API正常:', clearResponse.status);
      console.log('   响应数据:', JSON.stringify(clearResponse.data, null, 2));
    } catch (error) {
      console.log('❌ 购物车清空API失败:', error.response?.status, error.response?.data || error.message);
    }
    
  } catch (error) {
    console.error('测试过程中发生错误:', error.message);
  }
  
  console.log('\n测试完成！');
}

// 检查服务器是否运行
async function checkServer() {
  try {
    const response = await axios.get(`${BASE_URL}/api/v1/client/goods/list`);
    console.log('✅ 服务器正在运行');
    return true;
  } catch (error) {
    console.log('❌ 服务器未运行或无法访问:', error.message);
    console.log('请确保后端服务器在 http://localhost:4000 上运行');
    return false;
  }
}

// 主函数
async function main() {
  console.log('购物车API测试工具\n');
  
  const serverRunning = await checkServer();
  if (!serverRunning) {
    return;
  }
  
  console.log('');
  await testCartAPIs();
}

// 运行测试
main().catch(console.error);
