/**
 * 提现服务
 * 处理提现相关的业务逻辑
 */

const db = require('../../config/database');

class WithdrawService {
  
  /**
   * 获取提现方式配置
   */
  async getWithdrawMethods() {
    try {
      const [rows] = await db.execute(`
        SELECT 
          method_code,
          method_name,
          min_amount,
          max_amount,
          fee_rate,
          process_time,
          description,
          is_enabled
        FROM withdraw_method 
        WHERE is_enabled = 1
        ORDER BY method_code
      `);
      
      return rows;
    } catch (error) {
      console.error('获取提现方式失败:', error);
      throw new Error('获取提现方式失败');
    }
  }
  
  /**
   * 获取用户余额
   */
  async getUserBalance(userId) {
    try {
      const [rows] = await db.execute(`
        SELECT balance, total_income, today_income, month_income, total_withdraw
        FROM user 
        WHERE id = ?
      `, [userId]);
      
      if (rows.length === 0) {
        throw new Error('用户不存在');
      }
      
      return parseFloat(rows[0].balance) || 0;
    } catch (error) {
      console.error('获取用户余额失败:', error);
      throw new Error('获取用户余额失败');
    }
  }
  
  /**
   * 获取提现限制
   */
  async getWithdrawLimits(userId) {
    try {
      // 这里可以根据用户等级、VIP状态等设置不同的限制
      return {
        daily_limit: 10000.00,
        monthly_limit: 100000.00,
        min_withdraw_amount: 1.00
      };
    } catch (error) {
      console.error('获取提现限制失败:', error);
      throw new Error('获取提现限制失败');
    }
  }
  
  /**
   * 获取用户提现账户
   */
  async getUserWithdrawAccounts(userId, methodCode = null) {
    try {
      let sql = `
        SELECT 
          id,
          method_code,
          account_name,
          account_number,
          account_holder,
          bank_name,
          bank_branch,
          is_default,
          is_verified,
          status,
          created_at
        FROM user_withdraw_account 
        WHERE user_id = ? AND status = 1
      `;
      
      const params = [userId];
      
      if (methodCode) {
        sql += ' AND method_code = ?';
        params.push(methodCode);
      }
      
      sql += ' ORDER BY is_default DESC, id ASC';
      
      const [rows] = await db.execute(sql, params);
      
      // 隐藏敏感信息
      return rows.map(account => ({
        ...account,
        account_number: this.maskAccountNumber(account.account_number, account.method_code)
      }));
      
    } catch (error) {
      console.error('获取用户提现账户失败:', error);
      throw new Error('获取用户提现账户失败');
    }
  }
  
  /**
   * 计算提现手续费
   */
  async calculateWithdrawFee(methodCode, amount) {
    try {
      // 获取提现方式配置
      const [methods] = await db.execute(`
        SELECT fee_rate, min_amount, max_amount
        FROM withdraw_method 
        WHERE method_code = ? AND is_enabled = 1
      `, [methodCode]);
      
      if (methods.length === 0) {
        throw new Error('提现方式不存在或已禁用');
      }
      
      const method = methods[0];
      const feeRate = parseFloat(method.fee_rate);
      const minAmount = parseFloat(method.min_amount);
      const maxAmount = parseFloat(method.max_amount);
      
      // 验证金额范围
      if (amount < minAmount) {
        throw new Error(`提现金额不能小于${minAmount}元`);
      }
      
      if (amount > maxAmount) {
        throw new Error(`提现金额不能大于${maxAmount}元`);
      }
      
      // 计算手续费
      let fee = amount * feeRate;
      
      // 设置手续费最小值和最大值
      const minFee = this.getMinFee(methodCode);
      const maxFee = this.getMaxFee(methodCode);
      
      if (fee < minFee) {
        fee = minFee;
      }
      
      if (fee > maxFee) {
        fee = maxFee;
      }
      
      const actualAmount = amount - fee;
      
      return {
        amount: parseFloat(amount.toFixed(2)),
        fee: parseFloat(fee.toFixed(2)),
        actual_amount: parseFloat(actualAmount.toFixed(2)),
        fee_rate: feeRate
      };
      
    } catch (error) {
      console.error('计算提现手续费失败:', error);
      throw error;
    }
  }
  
  /**
   * 提交提现申请
   */
  async submitWithdrawApplication(data) {
    const connection = await db.getConnection();
    
    try {
      await connection.beginTransaction();
      
      const { userId, methodCode, amount, accountId, remark, ipAddress, userAgent } = data;
      
      // 1. 验证用户余额
      const [userRows] = await connection.execute(`
        SELECT balance FROM user WHERE id = ? FOR UPDATE
      `, [userId]);
      
      if (userRows.length === 0) {
        throw new Error('用户不存在');
      }
      
      const userBalance = parseFloat(userRows[0].balance);
      
      // 2. 计算手续费
      const feeInfo = await this.calculateWithdrawFee(methodCode, amount);
      
      if (userBalance < amount) {
        throw new Error('余额不足');
      }
      
      // 3. 验证提现账户
      const [accountRows] = await connection.execute(`
        SELECT * FROM user_withdraw_account 
        WHERE id = ? AND user_id = ? AND method_code = ? AND status = 1
      `, [accountId, userId, methodCode]);
      
      if (accountRows.length === 0) {
        throw new Error('提现账户不存在或已禁用');
      }
      
      const account = accountRows[0];
      
      // 4. 生成提现单号
      const withdrawNo = this.generateWithdrawNo();
      
      // 5. 创建提现申请
      const [result] = await connection.execute(`
        INSERT INTO withdraw_application (
          user_id, withdraw_no, amount, fee, actual_amount, 
          account_id, account_type, account_info, status, apply_time
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 0, NOW())
      `, [
        userId,
        withdrawNo,
        feeInfo.amount,
        feeInfo.fee,
        feeInfo.actual_amount,
        accountId,
        methodCode,
        JSON.stringify({
          method_code: methodCode,
          account_name: account.account_name,
          account_number: account.account_number,
          account_holder: account.account_holder,
          bank_name: account.bank_name
        })
      ]);
      
      // 6. 扣除用户余额
      await connection.execute(`
        UPDATE user SET balance = balance - ? WHERE id = ?
      `, [amount, userId]);
      
      // 7. 记录钱包交易
      await connection.execute(`
        INSERT INTO wallet_transaction (
          user_id, type, amount, balance_before, balance_after,
          related_type, related_id, title, description, status
        ) VALUES (?, 'withdraw', ?, ?, ?, 'withdraw_application', ?, ?, ?, 1)
      `, [
        userId,
        -amount,
        userBalance,
        userBalance - amount,
        result.insertId,
        '提现申请',
        `提现到${account.account_name}`,
      ]);
      
      await connection.commit();
      
      return {
        withdraw_no: withdrawNo,
        amount: feeInfo.amount,
        fee: feeInfo.fee,
        actual_amount: feeInfo.actual_amount,
        status: '待审核',
        apply_time: new Date()
      };
      
    } catch (error) {
      await connection.rollback();
      console.error('提交提现申请失败:', error);
      throw error;
    } finally {
      connection.release();
    }
  }
  
  /**
   * 获取提现记录
   */
  async getWithdrawRecords(params) {
    try {
      const { userId, page, limit, status, methodCode } = params;
      const offset = (page - 1) * limit;
      
      let sql = `
        SELECT 
          id, withdraw_no, amount, fee, actual_amount,
          account_type, status, apply_time, complete_time,
          audit_remark, failure_reason
        FROM withdraw_application 
        WHERE user_id = ?
      `;
      
      const queryParams = [userId];
      
      if (status !== undefined) {
        sql += ' AND status = ?';
        queryParams.push(status);
      }
      
      if (methodCode) {
        sql += ' AND account_type = ?';
        queryParams.push(methodCode);
      }
      
      sql += ' ORDER BY apply_time DESC LIMIT ? OFFSET ?';
      queryParams.push(limit, offset);
      
      const [rows] = await db.execute(sql, queryParams);
      
      // 获取总数
      let countSql = 'SELECT COUNT(*) as total FROM withdraw_application WHERE user_id = ?';
      const countParams = [userId];
      
      if (status !== undefined) {
        countSql += ' AND status = ?';
        countParams.push(status);
      }
      
      if (methodCode) {
        countSql += ' AND account_type = ?';
        countParams.push(methodCode);
      }
      
      const [countRows] = await db.execute(countSql, countParams);
      
      return {
        list: rows.map(row => ({
          ...row,
          status_text: this.getStatusText(row.status)
        })),
        total: countRows[0].total,
        page,
        limit
      };
      
    } catch (error) {
      console.error('获取提现记录失败:', error);
      throw new Error('获取提现记录失败');
    }
  }
  
  // 辅助方法
  maskAccountNumber(accountNumber, methodCode) {
    if (!accountNumber) return '';
    
    if (methodCode === 'bank_card') {
      // 银行卡号只显示前4位和后4位
      if (accountNumber.length > 8) {
        return accountNumber.substring(0, 4) + '****' + accountNumber.substring(accountNumber.length - 4);
      }
    } else {
      // 其他账号显示前3位和后3位
      if (accountNumber.length > 6) {
        return accountNumber.substring(0, 3) + '****' + accountNumber.substring(accountNumber.length - 3);
      }
    }
    
    return accountNumber;
  }
  
  getMinFee(methodCode) {
    const minFees = {
      'wechat': 0.60,
      'alipay': 0.55,
      'bank_card': 1.00
    };
    return minFees[methodCode] || 0;
  }
  
  getMaxFee(methodCode) {
    const maxFees = {
      'wechat': 25.00,
      'alipay': 25.00,
      'bank_card': 50.00
    };
    return maxFees[methodCode] || 100;
  }
  
  generateWithdrawNo() {
    const now = new Date();
    const dateStr = now.toISOString().slice(0, 10).replace(/-/g, '');
    const timeStr = now.toTimeString().slice(0, 8).replace(/:/g, '');
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    return `WD${dateStr}${timeStr}${random}`;
  }
  
  getStatusText(status) {
    const statusMap = {
      0: '待审核',
      1: '审核通过',
      2: '审核拒绝',
      3: '处理中',
      4: '已完成',
      5: '已取消'
    };
    return statusMap[status] || '未知状态';
  }
}

module.exports = new WithdrawService();
