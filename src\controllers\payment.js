const OrderModel = require('../models/order');
const logger = require('../utils/logger');
const { success, error } = require('../utils/response');
const crypto = require('crypto');

/**
 * 创建支付订单
 * 生成微信支付所需的参数
 */
const createPayment = async (req, res) => {
  try {
    const userId = req.user.id;
    const { orderNo } = req.body;
    
    // 查询订单信息
    const order = await OrderModel.getByOrderNo(orderNo, userId);
    
    if (!order) {
      return error(res, '订单不存在', 404);
    }
    
    // 检查订单状态是否为待支付
    if (order.status !== 0) {
      return error(res, '订单状态不允许支付', 400);
    }
    
    // 实际项目中，这里需要调用微信支付API生成支付参数
    // 由于是示例代码，我们模拟返回支付参数
    
    // 生成随机字符串
    const nonceStr = crypto.randomBytes(16).toString('hex');
    // 生成时间戳
    const timeStamp = Math.floor(Date.now() / 1000).toString();
    
    // 模拟返回支付参数
    const paymentParams = {
      orderNo,
      paymentType: 'wechat',
      timeStamp,
      nonceStr,
      package: `prepay_id=wx${timeStamp}`,
      signType: 'MD5',
      paySign: crypto.createHash('md5').update(`${timeStamp}${nonceStr}`).digest('hex'),
      totalAmount: order.totalAmount
    };
    
    return success(res, paymentParams, '创建支付订单成功');
  } catch (err) {
    logger.error(`创建支付订单失败: ${err.message}`);
    return error(res, '创建支付订单失败', 500);
  }
};

/**
 * 查询支付状态
 */
const getPaymentStatus = async (req, res) => {
  try {
    const userId = req.user.id;
    const { orderNo } = req.params;
    
    // 查询订单信息
    const order = await OrderModel.getByOrderNo(orderNo, userId);
    
    if (!order) {
      return error(res, '订单不存在', 404);
    }
    
    // 返回支付状态
    const paymentStatus = order.paymentStatus;
    
    return success(res, { 
      orderNo,
      paymentStatus,
      isPaid: paymentStatus === 1
    }, '获取支付状态成功');
  } catch (err) {
    logger.error(`查询支付状态失败: ${err.message}`);
    return error(res, '查询支付状态失败', 500);
  }
};

/**
 * 支付成功回调
 * 实际项目中，这个接口会接收微信支付平台的回调
 * 这里简化为前端直接调用
 */
const paymentCallback = async (req, res) => {
  try {
    const { orderNo } = req.body;
    
    // 实际项目中，这里需要验证签名等信息
    // 由于是示例代码，我们简化处理
    
    // 更新订单状态为已支付(1)
    const result = await OrderModel.updateStatus(orderNo, 1);
    
    if (!result) {
      return error(res, '订单不存在', 404);
    }
    
    // 实际项目中，这里还需要处理分润等逻辑
    
    return success(res, null, '支付成功');
  } catch (err) {
    logger.error(`支付回调处理失败: ${err.message}`);
    return error(res, '支付处理失败', 500);
  }
};

module.exports = {
  createPayment,
  getPaymentStatus,
  paymentCallback
}; 