const mysql = require('mysql2/promise');

async function checkAndUpdateTeamTable() {
  let connection;
  
  try {
    // 创建数据库连接
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: 'wo587129955',
      database: 'mall'
    });

    console.log('✅ 数据库连接成功');

    // 查看团队表结构
    const [tableStructure] = await connection.execute('DESCRIBE team');
    console.log('\n📋 当前团队表结构:');
    console.table(tableStructure);

    // 检查是否有邀请码字段
    const hasInviteCode = tableStructure.some(field => field.Field === 'invite_code');
    const hasLevel = tableStructure.some(field => field.Field === 'level');

    console.log('\n🔍 字段检查结果:');
    console.log(`invite_code 字段: ${hasInviteCode ? '✅ 存在' : '❌ 不存在'}`);
    console.log(`level 字段: ${hasLevel ? '✅ 存在' : '❌ 不存在'}`);

    // 添加缺失的字段
    if (!hasInviteCode) {
      console.log('\n🔧 添加 invite_code 字段...');
      await connection.execute(`
        ALTER TABLE team 
        ADD COLUMN invite_code VARCHAR(20) UNIQUE COMMENT '团队邀请码'
      `);
      console.log('✅ invite_code 字段添加成功');
    }

    if (!hasLevel) {
      console.log('\n🔧 添加 level 字段...');
      await connection.execute(`
        ALTER TABLE team 
        ADD COLUMN level INT DEFAULT 1 COMMENT '团队等级'
      `);
      console.log('✅ level 字段添加成功');
    }

    // 查看现有团队数据
    const [teams] = await connection.execute('SELECT id, name, invite_code, level FROM team');
    console.log('\n📊 现有团队数据:');
    console.table(teams);

    // 为没有邀请码的团队生成邀请码
    const teamsWithoutCode = teams.filter(team => !team.invite_code);
    
    if (teamsWithoutCode.length > 0) {
      console.log(`\n🎯 为 ${teamsWithoutCode.length} 个团队生成邀请码...`);
      
      for (const team of teamsWithoutCode) {
        // 生成唯一邀请码：TEAM + 6位数字ID
        const inviteCode = `TEAM${String(team.id).padStart(6, '0')}`;
        
        await connection.execute(
          'UPDATE team SET invite_code = ? WHERE id = ?',
          [inviteCode, team.id]
        );
        
        console.log(`✅ 团队 ${team.name || team.id} 邀请码: ${inviteCode}`);
      }
    } else {
      console.log('\n✅ 所有团队都已有邀请码');
    }

    // 查看更新后的团队数据
    const [updatedTeams] = await connection.execute('SELECT id, name, invite_code, level FROM team');
    console.log('\n📊 更新后的团队数据:');
    console.table(updatedTeams);

    // 验证邀请码唯一性
    const [duplicates] = await connection.execute(`
      SELECT invite_code, COUNT(*) as count 
      FROM team 
      WHERE invite_code IS NOT NULL 
      GROUP BY invite_code 
      HAVING COUNT(*) > 1
    `);

    if (duplicates.length > 0) {
      console.log('\n⚠️ 发现重复的邀请码:');
      console.table(duplicates);
    } else {
      console.log('\n✅ 所有邀请码都是唯一的');
    }

    console.log('\n🎉 团队表结构检查和更新完成！');

  } catch (error) {
    console.error('❌ 操作失败:', error.message);
    
    // 如果是字段已存在的错误，继续执行
    if (error.message.includes('Duplicate column')) {
      console.log('ℹ️ 字段已存在，跳过添加');
    }
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 数据库连接已关闭');
    }
  }
}

// 执行检查和更新
checkAndUpdateTeamTable();
