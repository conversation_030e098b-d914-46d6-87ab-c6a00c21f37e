# 地址API和订单创建问题修复报告

## 问题描述

用户在WiFi共享小程序中遇到订单创建失败的问题：
- 成功调用 `GET /api/v1/client/address/default` 获取默认地址（返回200状态码）
- 但在调用 `POST /api/v1/client/order/create` 创建订单时失败，返回400错误："收货地址不存在"
- 两个请求使用相同的Authorization Bearer token，排除了认证问题

## 问题根因分析

通过代码分析发现了以下关键问题：

### 1. 数据库表名不匹配
- **默认地址API** (`/api/v1/client/address/default`) 返回的是**模拟数据**
- **订单创建API** (`/api/v1/client/order/create`) 查询的是 `address` 表
- 但实际的地址数据存储在 `user_address` 表中

### 2. API路由不一致
- 前端调用的地址API：`/api/v1/client/address/*`
- 实际的地址管理API：`/api/v1/client/user/address/*`

### 3. 字段名不匹配
- 订单创建时期望地址有 `detail` 字段
- 但 `user_address` 表使用的是 `address` 字段

## 修复方案

### 1. 修复默认地址API
**文件**: `src/routes/v1.js` (第211-327行)

将模拟数据的地址API改为查询真实数据库：

```javascript
// 修复前：返回硬编码的模拟数据
router.get('/client/address/default', (req, res) => {
  return res.json({
    code: 0,
    message: '获取默认地址成功',
    data: {
      id: 1,
      name: '张三',
      // ... 硬编码数据
    }
  });
});

// 修复后：查询真实的user_address表
router.get('/client/address/default', verifyToken, async (req, res) => {
  try {
    const userId = req.user.id;
    const addresses = await db.query(
      'SELECT * FROM user_address WHERE user_id = ? AND is_default = 1 ORDER BY id DESC LIMIT 1',
      [userId]
    );
    // ... 处理查询结果
  } catch (error) {
    // ... 错误处理
  }
});
```

### 2. 修复地址列表API
同样在 `src/routes/v1.js` 中添加了真实的地址列表查询API。

### 3. 修复订单创建中的地址查询
**文件**: `src/routes/order.js` (第339-344行)

```javascript
// 修复前：查询不存在的address表
const addresses = await db.query('SELECT * FROM address WHERE id = ? AND user_id = ?', [addressId, userId]);

// 修复后：查询正确的user_address表
const addresses = await db.query('SELECT * FROM user_address WHERE id = ? AND user_id = ?', [addressId, userId]);
```

### 4. 修复地址字段映射
**文件**: `src/routes/order.js` (第422行)

```javascript
// 修复前：使用不存在的detail字段
`${address.province}${address.city}${address.district}${address.detail}`,

// 修复后：使用正确的address字段
`${address.province}${address.city}${address.district}${address.address}`,
```

## 验证结果

运行测试脚本 `test-address-order-fix.js` 验证修复效果：

```
✅ user_address表已存在
✅ 使用现有用户，ID: 3
✅ 使用现有地址，ID: 2
✅ 默认地址查询成功
✅ 订单地址查询成功
✅ 地址字段映射正确，使用address字段而不是detail字段
```

## 修复效果

1. ✅ **默认地址API** 现在查询真实的 `user_address` 表数据
2. ✅ **地址列表API** 现在查询真实的 `user_address` 表数据
3. ✅ **订单创建API** 现在正确查询 `user_address` 表而不是不存在的 `address` 表
4. ✅ **地址字段映射** 现在使用正确的 `address` 字段而不是 `detail` 字段

## 测试建议

1. **前端测试**：
   - 测试默认地址获取功能
   - 测试地址列表显示功能
   - 测试订单创建流程

2. **API测试**：
   ```bash
   # 测试默认地址API
   curl -H "Authorization: Bearer YOUR_TOKEN" \
        http://localhost:3000/api/v1/client/address/default

   # 测试订单创建API
   curl -X POST -H "Authorization: Bearer YOUR_TOKEN" \
        -H "Content-Type: application/json" \
        -d '{"addressId": 1, "goods": [{"goodsId": 1, "quantity": 1}]}' \
        http://localhost:3000/api/v1/client/order/create
   ```

## 注意事项

1. 确保 `user_address` 表已正确创建
2. 确保用户有有效的收货地址数据
3. 确保前端传递正确的 `addressId` 参数
4. 建议在生产环境部署前进行完整的端到端测试

现在用户应该能够正常创建订单，不再出现"收货地址不存在"的错误。
