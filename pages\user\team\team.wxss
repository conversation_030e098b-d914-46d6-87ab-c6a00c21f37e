/* pages/user/team/team.wxss */
.team-card {
  background-color: #ffffff;
  border-radius: 12px;
  padding: 16px;
  margin: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

/* 团队头部 */
.team-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.team-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.team-level {
  background: linear-gradient(45deg, #ff6b6b, #ffa726);
  color: white;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
}

.team-stats {
  display: flex;
  justify-content: space-between;
}

.stat-item {
  text-align: center;
  flex: 1;
}

.stat-value {
  font-size: 18px;
  font-weight: bold;
  color: #00c853;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 12px;
  color: #666;
}

.team-members {
  background-color: #ffffff;
  border-radius: 12px;
  padding: 16px;
  margin: 15px;
  margin-top: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.section-header {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 15px;
}

.empty-team {
  padding: 30px 0;
  text-align: center;
}

.empty-text {
  color: #999;
  font-size: 14px;
  margin-bottom: 20px;
}

.btn-create-team {
  display: inline-block;
  background-color: #00c853;
  color: #fff;
  font-size: 14px;
  padding: 8px 20px;
  border-radius: 20px;
  border: none;
  margin-top: 10px;
}

.member-list {
  max-height: 300px;
  overflow-y: auto;
}

.member-item {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f5f5f5;
}

.member-item:last-child {
  border-bottom: none;
}

.member-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  margin-right: 12px;
}

.member-info {
  flex: 1;
}

.member-name {
  font-size: 14px;
  color: #333;
}

.member-role {
  font-size: 12px;
  color: #00c853;
  margin-top: 4px;
}

.member-joined {
  font-size: 12px;
  color: #999;
}

.load-more {
  text-align: center;
  color: #00c853;
  font-size: 14px;
  padding: 10px 0;
  margin-top: 10px;
}

.action-buttons {
  display: flex;
  justify-content: space-between;
  padding: 15px;
}

.btn-invite, .btn-income {
  flex: 1;
  margin: 0 5px;
  background-color: #00c853;
  color: white;
  border-radius: 30px;
  font-size: 14px;
  padding: 8px 0;
  text-align: center;
}

.btn-income {
  background-color: #ffffff;
  color: #00c853;
  border: 1px solid #00c853;
}

/* 详细收益数据 */
.income-details {
  margin-top: 30rpx;
  padding-top: 30rpx;
  border-top: 1rpx solid #f0f0f0;
}

.income-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.income-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.income-label {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 8rpx;
}

.income-value {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

.team-info {
  margin-top: 20rpx;
  padding-top: 20rpx;
  border-top: 1rpx solid #f0f0f0;
  text-align: center;
}

.create-time {
  font-size: 24rpx;
  color: #999;
}

/* 邀请弹窗样式 */
.invite-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
}

.modal-content {
  background: white;
  border-radius: 20rpx;
  width: 600rpx;
  max-height: 80vh;
  position: relative;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.modal-close {
  font-size: 40rpx;
  color: #999;
  padding: 10rpx;
}

.modal-body {
  padding: 30rpx;
}

.qr-section {
  text-align: center;
  margin-bottom: 40rpx;
}

.section-title {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 20rpx;
  display: block;
}

.qr-code {
  width: 300rpx;
  height: 300rpx;
  border: 1rpx solid #f0f0f0;
  border-radius: 10rpx;
}

.qr-fallback {
  width: 300rpx;
  height: 300rpx;
  border: 1rpx solid #f0f0f0;
  border-radius: 10rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #f8f9fa;
}

.fallback-icon {
  font-size: 48rpx;
  margin-bottom: 10rpx;
}

.fallback-text {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 5rpx;
}

.fallback-tip {
  font-size: 20rpx;
  color: #999;
}

.invite-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #f8f9fa;
  padding: 20rpx;
  border-radius: 10rpx;
  margin-bottom: 30rpx;
}

.invite-code {
  font-size: 26rpx;
  color: #666;
}

.copy-btn {
  background: #07c160;
  color: white;
  border-radius: 30rpx;
  padding: 10rpx 20rpx;
  font-size: 24rpx;
  border: none;
}

.share-tips {
  text-align: center;
}

.share-tips text {
  font-size: 24rpx;
  color: #999;
}

/* 快捷分享区域 */
.quick-share {
  background: white;
  margin: 20rpx;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.share-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
  text-align: center;
}

.share-buttons {
  display: flex;
  justify-content: space-around;
  align-items: center;
}

.share-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  background: transparent;
  border: none;
  padding: 20rpx 10rpx;
  border-radius: 16rpx;
  transition: all 0.3s ease;
  min-width: 120rpx;
}

.share-btn:active {
  transform: scale(0.95);
  background: rgba(0, 0, 0, 0.05);
}

.share-btn.wechat:active {
  background: rgba(7, 193, 96, 0.1);
}

.share-btn.timeline:active {
  background: rgba(255, 193, 7, 0.1);
}

.share-btn.qrcode:active {
  background: rgba(0, 123, 255, 0.1);
}

.share-btn.copy:active {
  background: rgba(108, 117, 125, 0.1);
}

.share-icon {
  font-size: 40rpx;
  margin-bottom: 8rpx;
  line-height: 1;
}

.share-text {
  font-size: 22rpx;
  color: #666;
  line-height: 1;
}

/* 分享按钮悬停效果 */
.share-btn.wechat .share-text {
  color: #07c160;
}

.share-btn.timeline .share-text {
  color: #ffc107;
}

.share-btn.qrcode .share-text {
  color: #007bff;
}

.share-btn.copy .share-text {
  color: #6c757d;
}

ad {
  margin: 15px;
}