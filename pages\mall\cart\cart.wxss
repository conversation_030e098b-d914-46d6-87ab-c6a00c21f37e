/* pages/mall/cart/cart.wxss */
.cart-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 100rpx; /* 为底部结算区域留出空间 */
}

/* 广告区域 */
.ad-section {
  position: relative;
  width: 100%;
  height: 200rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
}

.ad-banner {
  width: 100%;
  height: 100%;
}

.ad-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.8), rgba(118, 75, 162, 0.8));
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.ad-title {
  color: #ffffff;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.ad-subtitle {
  color: #ffffff;
  font-size: 24rpx;
  opacity: 0.9;
}

/* 购物车主要内容 */
.cart-content {
  flex: 1;
}

/* 全选区域 */
.select-all-section {
  background-color: #ffffff;
  padding: 30rpx;
  margin-bottom: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.select-all-wrapper {
  display: flex;
  align-items: center;
}

.select-all-text {
  font-size: 32rpx;
  color: #333333;
  margin-left: 20rpx;
}

/* 复选框样式 */
.checkbox {
  width: 36rpx;
  height: 36rpx;
  border: 2rpx solid #cccccc;
  border-radius: 6rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.checkbox.checked {
  background-color: #ff6b6b;
  border-color: #ff6b6b;
}

.checkbox-icon {
  color: #ffffff;
  font-size: 24rpx;
  font-weight: bold;
}

/* 商品列表 */
.cart-list {
  background-color: #ffffff;
}

.cart-item {
  border-bottom: 1rpx solid #f0f0f0;
  padding: 30rpx;
}

.cart-item:last-child {
  border-bottom: none;
}

.item-wrapper {
  display: flex;
  align-items: flex-start;
}

/* 商品选择框 */
.item-checkbox {
  margin-right: 20rpx;
  padding-top: 10rpx;
}

/* 商品内容区域 */
.item-content {
  flex: 1;
  display: flex;
}

/* 商品图片 */
.item-image {
  width: 180rpx;
  height: 180rpx;
  margin-right: 20rpx;
  border-radius: 12rpx;
  overflow: hidden;
  background-color: #f8f8f8;
}

.goods-image {
  width: 100%;
  height: 100%;
}

/* 商品详情 */
.item-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  min-height: 180rpx;
}

.goods-name {
  font-size: 30rpx;
  color: #333333;
  line-height: 1.4;
  margin-bottom: 10rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.goods-specs {
  font-size: 24rpx;
  color: #999999;
  margin-bottom: 15rpx;
}

.goods-price {
  font-size: 32rpx;
  color: #ff6b6b;
  font-weight: bold;
  margin-bottom: 10rpx;
}

/* 库存提示 */
.stock-info {
  margin-bottom: 15rpx;
}

.stock-text {
  font-size: 24rpx;
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
  background-color: #e8f5e8;
  color: #52c41a;
}

.stock-text.low-stock {
  background-color: #fff7e6;
  color: #fa8c16;
}

.stock-text.out-of-stock {
  background-color: #fff2f0;
  color: #ff4d4f;
}

/* 数量控制 */
.quantity-controls {
  display: flex;
  align-items: center;
  align-self: flex-start;
}

.quantity-btn {
  width: 60rpx;
  height: 60rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #ffffff;
  transition: all 0.3s ease;
}

.quantity-btn.decrease {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.quantity-btn.increase {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

.quantity-btn:active {
  background-color: #f0f0f0;
}

.quantity-btn.disabled {
  background-color: #f8f8f8;
  color: #cccccc;
  border-color: #f0f0f0;
}

.quantity-icon {
  font-size: 32rpx;
  color: #666666;
  font-weight: bold;
}

.quantity-display {
  width: 80rpx;
  height: 60rpx;
  border-top: 2rpx solid #e0e0e0;
  border-bottom: 2rpx solid #e0e0e0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #333333;
  background-color: #ffffff;
}

/* 删除按钮 */
.item-delete {
  width: 60rpx;
  height: 60rpx;
  margin-left: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #ff6b6b;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.item-delete:active {
  background-color: #ff5252;
  transform: scale(0.95);
}

.delete-icon {
  color: #ffffff;
  font-size: 36rpx;
  font-weight: bold;
}

/* 空状态 */
.empty-cart {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 60rpx;
  background-color: #ffffff;
  margin: 20rpx;
  border-radius: 16rpx;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 40rpx;
  opacity: 0.6;
}

.empty-title {
  font-size: 32rpx;
  color: #333333;
  margin-bottom: 15rpx;
  font-weight: 500;
}

.empty-subtitle {
  font-size: 28rpx;
  color: #999999;
  margin-bottom: 50rpx;
}

.empty-action-btn {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: #ffffff;
  border: none;
  border-radius: 50rpx;
  padding: 20rpx 60rpx;
  font-size: 28rpx;
}

.empty-action-btn::after {
  display: none;
}

/* 加载状态 */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx;
  background-color: #ffffff;
  margin: 20rpx;
  border-radius: 16rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 30rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #999999;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 底部结算区域 */
.checkout-bottom {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  background-color: #ffffff;
  border-top: 1rpx solid #e0e0e0;
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.checkout-info {
  flex: 1;
  display: flex;
  align-items: center;
}

.total-label {
  font-size: 28rpx;
  color: #333333;
  margin-right: 10rpx;
}

.total-price {
  font-size: 36rpx;
  color: #ff6b6b;
  font-weight: bold;
}

.checkout-btn {
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 50rpx;
  padding: 24rpx 60rpx;
  transition: all 0.3s ease;
}

.checkout-btn.disabled {
  background: #cccccc;
  opacity: 0.6;
}

.checkout-btn:active:not(.disabled) {
  transform: scale(0.98);
}

.checkout-text {
  color: #ffffff;
  font-size: 30rpx;
  font-weight: 500;
}

/* 适配暗色模式 */
@media (prefers-color-scheme: dark) {
  .cart-container {
    background-color: #1a1a1a;
  }
  
  .select-all-section,
  .cart-list,
  .empty-cart,
  .loading-state,
  .checkout-bottom {
    background-color: #2d2d2d;
  }
  
  .select-all-text,
  .goods-name,
  .total-label {
    color: #ffffff;
  }
  
  .empty-title {
    color: #ffffff;
  }
  
  .goods-specs,
  .loading-text {
    color: #cccccc;
  }
  
  .cart-item {
    border-bottom-color: #404040;
  }
  
  .checkout-bottom {
    border-top-color: #404040;
  }
  
  .quantity-btn,
  .quantity-display {
    background-color: #2d2d2d;
    border-color: #404040;
    color: #ffffff;
  }
  
  .quantity-btn.disabled {
    background-color: #1a1a1a;
    color: #666666;
  }
}

/* 响应式设计 */
@media screen and (max-width: 750rpx) {
  .item-image {
    width: 160rpx;
    height: 160rpx;
  }
  
  .item-details {
    min-height: 160rpx;
  }
  
  .goods-name {
    font-size: 28rpx;
  }
  
  .goods-price {
    font-size: 30rpx;
  }
} 