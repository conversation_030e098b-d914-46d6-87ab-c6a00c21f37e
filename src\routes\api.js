const express = require('express');
const router = express.Router();

// 引入各模块路由
const authRoutes = require('./auth');
const userRoutes = require('./user');
const wifiRoutes = require('./wifi');
const goodsRoutes = require('./goods');
const orderRoutes = require('./order');
const teamRoutes = require('./team');
const adsRoutes = require('./ads');
const cartRoutes = require('./cart');
const incomeRoutes = require('./income');
const paymentRoutes = require('./payment');
const withdrawRoutes = require('./withdraw');

// 基本API信息
router.get('/', (req, res) => {
  res.json({
    name: 'WiFi共享商业系统API',
    version: '1.0.0',
    status: 'running'
  });
});

// 注册各模块路由
router.use('/auth', authRoutes);
router.use('/users', userRoutes);
router.use('/wifi', wifiRoutes);
router.use('/goods', goodsRoutes);
router.use('/orders', orderRoutes);
router.use('/team', teamRoutes);
router.use('/ads', adsRoutes);
router.use('/cart', cartRoutes);
router.use('/income', incomeRoutes);
router.use('/payment', paymentRoutes);
router.use('/withdraw', withdrawRoutes);

module.exports = router; 