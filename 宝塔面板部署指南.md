# 🚀 宝塔面板部署WiFi共享商城后端服务器指南

## 📋 部署前准备

### 1. 确认项目文件已上传
确保 `wifi-share-server` 文件夹已完整上传到宝塔面板的网站目录中。

### 2. 检查必要文件
确认以下关键文件存在：
- `app.js` - 应用入口文件
- `server.js` - 服务器启动文件
- `package.json` - 依赖配置文件
- `.env` - 环境变量配置文件
- `ecosystem.config.js` - PM2配置文件

## 🛠️ 环境安装

### 1. 安装Node.js
1. 登录宝塔面板
2. 进入"软件商店"
3. 搜索"Node.js"并安装（推荐版本：Node.js 16.x 或 18.x）
4. 安装完成后，在终端中验证：
   ```bash
   node -v
   npm -v
   ```

### 2. 安装MySQL数据库
1. 在软件商店中安装MySQL 8.0
2. 设置root密码（建议使用强密码）
3. 创建数据库：
   - 数据库名：`mall`
   - 字符集：`utf8mb4`
   - 排序规则：`utf8mb4_unicode_ci`

### 3. 安装PM2进程管理器
```bash
npm install -g pm2
```

## ⚙️ 配置步骤

### 1. 修改数据库密码
编辑 `.env` 文件，将数据库密码改为您在宝塔面板中设置的MySQL root密码：
```env
DB_PASSWORD=您的MySQL密码
```

### 2. 安装项目依赖
在项目根目录执行：
```bash
cd /www/wwwroot/您的域名/wifi-share-server
npm install
```

### 3. 初始化数据库
运行数据库初始化脚本：
```bash
node init-database.js
```

## 🚀 启动服务器

### 方法一：使用PM2启动（推荐）
```bash
# 启动服务
pm2 start ecosystem.config.js --env production

# 查看运行状态
pm2 status

# 查看日志
pm2 logs wifi-share-server

# 重启服务
pm2 restart wifi-share-server

# 停止服务
pm2 stop wifi-share-server
```

### 方法二：直接启动
```bash
# 生产环境启动
npm start

# 或者
node server.js
```

### 方法三：使用宝塔面板Node.js管理器
1. 进入"软件商店" → "Node.js" → "设置"
2. 添加Node.js项目：
   - 项目名称：wifi-share-server
   - 运行目录：/www/wwwroot/您的域名/wifi-share-server
   - 启动文件：server.js
   - 端口：4000

## 🔧 防火墙配置

### 1. 开放端口
在宝塔面板中开放4000端口：
1. 进入"安全"
2. 添加端口规则：端口4000，备注：WiFi共享API服务

### 2. 配置反向代理（可选）
如果需要通过域名访问，可以配置Nginx反向代理：
1. 创建网站（域名：api.您的域名.com）
2. 配置反向代理：
   - 代理名称：wifi-api
   - 目标URL：http://127.0.0.1:4000
   - 发送域名：$host

## ✅ 验证部署

### 1. 检查服务状态
```bash
# 检查端口是否监听
netstat -tlnp | grep 4000

# 检查PM2状态
pm2 status
```

### 2. 测试API接口
```bash
# 测试健康检查接口
curl http://localhost:4000/api/v1/health

# 或者在浏览器中访问
http://您的服务器IP:4000/api/v1/health
```

### 3. 查看日志
```bash
# PM2日志
pm2 logs wifi-share-server

# 应用日志
tail -f logs/combined.log
```

## 🔍 常见问题解决

### 1. 端口被占用
```bash
# 查看端口占用
lsof -i:4000

# 杀死占用进程
kill -9 进程ID
```

### 2. 数据库连接失败
- 检查MySQL服务是否启动
- 验证数据库密码是否正确
- 确认数据库名称是否为`mall`

### 3. 依赖安装失败
```bash
# 清除缓存重新安装
npm cache clean --force
rm -rf node_modules
npm install
```

### 4. 权限问题
```bash
# 设置文件权限
chmod -R 755 /www/wwwroot/您的域名/wifi-share-server
chown -R www:www /www/wwwroot/您的域名/wifi-share-server
```

## 📊 监控和维护

### 1. 设置开机自启
```bash
# PM2开机自启
pm2 startup
pm2 save
```

### 2. 日志轮转
```bash
# 安装PM2日志轮转
pm2 install pm2-logrotate
```

### 3. 监控面板
访问PM2监控面板：
```bash
pm2 web
```

## 🔄 更新部署

当需要更新代码时：
```bash
# 1. 上传新代码
# 2. 安装新依赖
npm install

# 3. 重启服务
pm2 restart wifi-share-server

# 4. 查看状态
pm2 status
```

## 📞 技术支持

如果遇到问题，请检查：
1. 服务器日志：`pm2 logs wifi-share-server`
2. 应用日志：`tail -f logs/error.log`
3. 数据库连接：运行 `node check-db-connection.js`

---

**部署完成后，您的WiFi共享商城后端API将在以下地址运行：**
- 本地访问：http://localhost:4000
- 外网访问：http://您的服务器IP:4000
- 域名访问：http://api.您的域名.com（如果配置了反向代理）
