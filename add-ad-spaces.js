const mysql = require('mysql2/promise');

async function addAdSpaces() {
  const conn = await mysql.createConnection({
    host: 'localhost',
    user: 'root',
    password: 'wo587129955',
    database: 'mall'
  });
  
  const spaces = [
    {
      name: '首页轮播图',
      code: 'home_banner',
      width: 600,
      height: 300,
      description: '首页顶部大图轮播广告位',
      price: 100.00,
      status: 1
    },
    {
      name: '首页推荐位',
      code: 'home_recommend',
      width: 300,
      height: 200,
      description: '首页中部推荐商品广告位',
      price: 60.00,
      status: 1
    },
    {
      name: '首页中部推荐位',
      code: 'home_middle',
      width: 300,
      height: 200,
      description: '首页中部推荐位广告',
      price: 0.00,
      status: 1
    },
    {
      name: '首页底部广告栏',
      code: 'home_bottom',
      width: 300,
      height: 200,
      description: '首页底部广告栏',
      price: 0.00,
      status: 1
    }
  ];
  
  for(const space of spaces) {
    try {
      await conn.execute(
        'INSERT IGNORE INTO ad_space (name, code, width, height, description, price, status, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), NOW())',
        [space.name, space.code, space.width, space.height, space.description, space.price, space.status]
      );
      console.log(`Added ad space: ${space.name}`);
    } catch(err) {
      console.error(`Error adding ${space.name}:`, err.message);
    }
  }
  
  await conn.end();
  console.log('Finished adding ad spaces');
}

addAdSpaces().catch(console.error); 