-- 完善提现系统数据表
-- 执行时间：2025年1月29日

USE mall;

-- 1. 完善提现申请表，添加更多提现方式支持
DROP TABLE IF EXISTS `withdraw_application`;
CREATE TABLE `withdraw_application` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '提现申请ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `amount` decimal(10,2) NOT NULL COMMENT '提现金额',
  `fee` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '手续费',
  `actual_amount` decimal(10,2) NOT NULL COMMENT '实际到账金额',
  `withdraw_type` varchar(20) NOT NULL COMMENT '提现方式：wechat,alipay,bank_card',
  `account_info` text NOT NULL COMMENT '账户信息JSON',
  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态：0待审核，1审核通过，2审核拒绝，3处理中，4已完成，5已取消',
  `apply_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '申请时间',
  `audit_time` datetime DEFAULT NULL COMMENT '审核时间',
  `audit_user_id` int(11) DEFAULT NULL COMMENT '审核人ID',
  `audit_remark` varchar(255) DEFAULT NULL COMMENT '审核备注',
  `process_time` datetime DEFAULT NULL COMMENT '处理时间',
  `complete_time` datetime DEFAULT NULL COMMENT '完成时间',
  `transaction_no` varchar(50) DEFAULT NULL COMMENT '交易流水号',
  `third_party_no` varchar(100) DEFAULT NULL COMMENT '第三方交易号',
  `remark` varchar(255) DEFAULT NULL COMMENT '用户备注',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `status` (`status`),
  KEY `withdraw_type` (`withdraw_type`),
  KEY `apply_time` (`apply_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='提现申请表';

-- 2. 用户提现账户表（支持多种提现方式）
CREATE TABLE IF NOT EXISTS `user_withdraw_account` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '账户ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `account_type` varchar(20) NOT NULL COMMENT '账户类型：wechat,alipay,bank_card',
  `account_name` varchar(100) NOT NULL COMMENT '账户名称',
  `account_info` text NOT NULL COMMENT '账户详细信息JSON',
  `is_default` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否默认：0否，1是',
  `is_verified` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已验证：0否，1是',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0禁用，1启用',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `account_type` (`account_type`),
  KEY `is_default` (`is_default`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户提现账户表';

-- 3. 提现配置表
CREATE TABLE IF NOT EXISTS `withdraw_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `withdraw_type` varchar(20) NOT NULL COMMENT '提现方式',
  `min_amount` decimal(10,2) NOT NULL DEFAULT '1.00' COMMENT '最小提现金额',
  `max_amount` decimal(10,2) NOT NULL DEFAULT '50000.00' COMMENT '最大提现金额',
  `daily_limit` decimal(10,2) NOT NULL DEFAULT '10000.00' COMMENT '每日限额',
  `fee_type` tinyint(1) NOT NULL DEFAULT '1' COMMENT '手续费类型：1按比例，2固定金额',
  `fee_rate` decimal(5,4) NOT NULL DEFAULT '0.0100' COMMENT '手续费比例',
  `fee_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '固定手续费',
  `min_fee` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '最小手续费',
  `max_fee` decimal(10,2) NOT NULL DEFAULT '100.00' COMMENT '最大手续费',
  `process_time` varchar(50) DEFAULT '1-3个工作日' COMMENT '处理时间说明',
  `is_enabled` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用：0否，1是',
  `sort_order` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `withdraw_type` (`withdraw_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='提现配置表';

-- 4. 插入提现配置数据
INSERT INTO `withdraw_config` (`withdraw_type`, `min_amount`, `max_amount`, `daily_limit`, `fee_type`, `fee_rate`, `fee_amount`, `min_fee`, `max_fee`, `process_time`, `sort_order`) VALUES
('wechat', 1.00, 20000.00, 5000.00, 1, 0.0060, 0.00, 0.10, 25.00, '实时到账', 1),
('alipay', 1.00, 20000.00, 5000.00, 1, 0.0060, 0.00, 0.10, 25.00, '实时到账', 2),
('bank_card', 50.00, 50000.00, 20000.00, 1, 0.0100, 0.00, 1.00, 50.00, '1-3个工作日', 3)
ON DUPLICATE KEY UPDATE
  `min_amount` = VALUES(`min_amount`),
  `max_amount` = VALUES(`max_amount`),
  `daily_limit` = VALUES(`daily_limit`),
  `fee_rate` = VALUES(`fee_rate`),
  `process_time` = VALUES(`process_time`),
  `updated_at` = CURRENT_TIMESTAMP;

-- 5. 为测试用户添加提现账户
INSERT INTO `user_withdraw_account` (`user_id`, `account_type`, `account_name`, `account_info`, `is_default`, `is_verified`) VALUES
(1, 'wechat', '微信零钱', '{"openid": "oW20C7mVlW8e3W2AgUGtDTJeAbQU", "nickname": "润生", "real_name": "张三"}', 1, 1),
(1, 'alipay', '支付宝账户', '{"account": "138****8888", "real_name": "张三", "alipay_user_id": ""}', 0, 0),
(1, 'bank_card', '工商银行储蓄卡', '{"bank_name": "中国工商银行", "bank_code": "ICBC", "card_number": "6222****1234", "card_holder": "张三", "bank_branch": "北京分行"}', 0, 1)
ON DUPLICATE KEY UPDATE updated_at = CURRENT_TIMESTAMP;

-- 6. 插入一些测试提现记录
INSERT INTO `withdraw_application` (`user_id`, `amount`, `fee`, `actual_amount`, `withdraw_type`, `account_info`, `status`, `apply_time`, `audit_time`, `complete_time`, `transaction_no`) VALUES
(1, 100.00, 1.00, 99.00, 'wechat', '{"openid": "oW20C7mVlW8e3W2AgUGtDTJeAbQU", "nickname": "润生"}', 4, DATE_SUB(NOW(), INTERVAL 2 DAY), DATE_SUB(NOW(), INTERVAL 2 DAY), DATE_SUB(NOW(), INTERVAL 2 DAY), 'WD202501270001'),
(1, 200.00, 2.00, 198.00, 'bank_card', '{"bank_name": "中国工商银行", "card_number": "6222****1234", "card_holder": "张三"}', 4, DATE_SUB(NOW(), INTERVAL 5 DAY), DATE_SUB(NOW(), INTERVAL 4 DAY), DATE_SUB(NOW(), INTERVAL 3 DAY), 'WD202501240001'),
(1, 50.00, 0.50, 49.50, 'wechat', '{"openid": "oW20C7mVlW8e3W2AgUGtDTJeAbQU", "nickname": "润生"}', 1, DATE_SUB(NOW(), INTERVAL 1 DAY), NOW(), NULL, 'WD202501280001')
ON DUPLICATE KEY UPDATE updated_at = CURRENT_TIMESTAMP;

-- 7. 更新用户总提现金额
UPDATE `user` SET 
  `total_withdraw` = (
    SELECT COALESCE(SUM(actual_amount), 0) 
    FROM withdraw_application 
    WHERE user_id = user.id AND status = 4
  )
WHERE id = 1;

-- 8. 显示创建结果
SELECT '✅ 提现系统表创建完成' as message;

-- 9. 验证数据
SELECT 
  '提现配置' as '数据类型',
  COUNT(*) as '记录数'
FROM withdraw_config
UNION ALL
SELECT 
  '用户提现账户' as '数据类型',
  COUNT(*) as '记录数'
FROM user_withdraw_account
UNION ALL
SELECT 
  '提现申请记录' as '数据类型',
  COUNT(*) as '记录数'
FROM withdraw_application;
