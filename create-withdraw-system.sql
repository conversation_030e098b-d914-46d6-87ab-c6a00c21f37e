-- 创建完整的提现系统
-- 执行时间：2025年1月29日

USE mall;

-- 1. 用户提现账户表（支持多种提现方式）
CREATE TABLE IF NOT EXISTS `user_withdraw_account` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '账户ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `account_type` varchar(20) NOT NULL COMMENT '账户类型：wechat,alipay,bank_card',
  `account_name` varchar(100) NOT NULL COMMENT '账户名称',
  `account_info` text NOT NULL COMMENT '账户信息JSON',
  `is_default` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否默认：0否，1是',
  `is_verified` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已验证：0否，1是',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0禁用，1启用',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `account_type` (`account_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户提现账户表';

-- 2. 提现申请表（增强版）
DROP TABLE IF EXISTS `withdraw_application`;
CREATE TABLE `withdraw_application` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '提现申请ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `withdraw_no` varchar(32) NOT NULL COMMENT '提现单号',
  `amount` decimal(10,2) NOT NULL COMMENT '提现金额',
  `fee` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '手续费',
  `actual_amount` decimal(10,2) NOT NULL COMMENT '实际到账金额',
  `account_id` int(11) NOT NULL COMMENT '提现账户ID',
  `account_type` varchar(20) NOT NULL COMMENT '提现方式',
  `account_info` text NOT NULL COMMENT '提现账户信息',
  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态：0待审核，1审核通过，2审核拒绝，3处理中，4已完成，5已取消',
  `apply_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '申请时间',
  `audit_user_id` int(11) DEFAULT NULL COMMENT '审核人ID',
  `audit_time` datetime DEFAULT NULL COMMENT '审核时间',
  `audit_remark` varchar(255) DEFAULT NULL COMMENT '审核备注',
  `process_time` datetime DEFAULT NULL COMMENT '处理时间',
  `complete_time` datetime DEFAULT NULL COMMENT '完成时间',
  `transaction_id` varchar(64) DEFAULT NULL COMMENT '第三方交易ID',
  `failure_reason` varchar(255) DEFAULT NULL COMMENT '失败原因',
  `remark` varchar(255) DEFAULT NULL COMMENT '申请备注',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `withdraw_no` (`withdraw_no`),
  KEY `user_id` (`user_id`),
  KEY `status` (`status`),
  KEY `apply_time` (`apply_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='提现申请表';

-- 3. 提现配置表
CREATE TABLE IF NOT EXISTS `withdraw_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `account_type` varchar(20) NOT NULL COMMENT '账户类型',
  `min_amount` decimal(10,2) NOT NULL DEFAULT '1.00' COMMENT '最小提现金额',
  `max_amount` decimal(10,2) NOT NULL DEFAULT '50000.00' COMMENT '最大提现金额',
  `daily_limit` decimal(10,2) NOT NULL DEFAULT '10000.00' COMMENT '每日限额',
  `fee_type` tinyint(1) NOT NULL DEFAULT '1' COMMENT '手续费类型：1按比例，2固定金额',
  `fee_rate` decimal(5,4) NOT NULL DEFAULT '0.0100' COMMENT '手续费比例',
  `fee_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '固定手续费',
  `min_fee` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '最小手续费',
  `max_fee` decimal(10,2) NOT NULL DEFAULT '100.00' COMMENT '最大手续费',
  `is_enabled` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用',
  `process_time` varchar(50) DEFAULT '1-3个工作日' COMMENT '处理时间说明',
  `description` varchar(255) DEFAULT NULL COMMENT '说明',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `account_type` (`account_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='提现配置表';

-- 4. 提现日志表
CREATE TABLE IF NOT EXISTS `withdraw_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `withdraw_id` int(11) NOT NULL COMMENT '提现申请ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `action` varchar(50) NOT NULL COMMENT '操作类型',
  `status_before` tinyint(1) NOT NULL COMMENT '操作前状态',
  `status_after` tinyint(1) NOT NULL COMMENT '操作后状态',
  `operator_id` int(11) DEFAULT NULL COMMENT '操作人ID',
  `operator_type` varchar(20) DEFAULT 'system' COMMENT '操作人类型：user,admin,system',
  `remark` varchar(255) DEFAULT NULL COMMENT '操作备注',
  `ip_address` varchar(45) DEFAULT NULL COMMENT 'IP地址',
  `user_agent` varchar(255) DEFAULT NULL COMMENT '用户代理',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `withdraw_id` (`withdraw_id`),
  KEY `user_id` (`user_id`),
  KEY `created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='提现日志表';

-- 插入提现配置数据
INSERT INTO `withdraw_config` (`account_type`, `min_amount`, `max_amount`, `daily_limit`, `fee_type`, `fee_rate`, `fee_amount`, `min_fee`, `max_fee`, `process_time`, `description`) VALUES
('wechat', 1.00, 20000.00, 5000.00, 1, 0.0060, 0.00, 0.60, 25.00, '实时到账', '微信零钱提现，实时到账'),
('alipay', 1.00, 50000.00, 10000.00, 1, 0.0055, 0.00, 0.55, 25.00, '2小时内', '支付宝余额提现，2小时内到账'),
('bank_card', 100.00, 50000.00, 20000.00, 1, 0.0100, 0.00, 1.00, 50.00, '1-3个工作日', '银行卡提现，1-3个工作日到账')
ON DUPLICATE KEY UPDATE
  `min_amount` = VALUES(`min_amount`),
  `max_amount` = VALUES(`max_amount`),
  `daily_limit` = VALUES(`daily_limit`),
  `fee_rate` = VALUES(`fee_rate`),
  `updated_at` = CURRENT_TIMESTAMP;

-- 为测试用户插入提现账户
INSERT INTO `user_withdraw_account` (`user_id`, `account_type`, `account_name`, `account_info`, `is_default`, `is_verified`) VALUES
(1, 'wechat', '微信零钱', '{"openid": "oW20C7mVlW8e3W2AgUGtDTJeAbQU", "nickname": "润生", "real_name": "张润生"}', 1, 1),
(1, 'alipay', '支付宝账户', '{"account": "138****8888", "real_name": "张润生", "account_type": "phone"}', 0, 0),
(1, 'bank_card', '工商银行储蓄卡', '{"bank_name": "中国工商银行", "bank_code": "ICBC", "card_number": "6222****1234", "card_holder": "张润生", "bank_branch": "北京分行"}', 0, 0)
ON DUPLICATE KEY UPDATE updated_at = CURRENT_TIMESTAMP;

-- 插入一些测试提现申请
INSERT INTO `withdraw_application` (`user_id`, `withdraw_no`, `amount`, `fee`, `actual_amount`, `account_id`, `account_type`, `account_info`, `status`, `apply_time`) VALUES
(1, CONCAT('WD', DATE_FORMAT(NOW(), '%Y%m%d'), '001'), 100.00, 1.00, 99.00, 1, 'wechat', '{"openid": "oW20C7mVlW8e3W2AgUGtDTJeAbQU", "nickname": "润生"}', 4, DATE_SUB(NOW(), INTERVAL 2 DAY)),
(1, CONCAT('WD', DATE_FORMAT(NOW(), '%Y%m%d'), '002'), 200.00, 2.00, 198.00, 2, 'alipay', '{"account": "138****8888", "real_name": "张润生"}', 1, DATE_SUB(NOW(), INTERVAL 1 DAY)),
(1, CONCAT('WD', DATE_FORMAT(NOW(), '%Y%m%d'), '003'), 50.00, 0.60, 49.40, 1, 'wechat', '{"openid": "oW20C7mVlW8e3W2AgUGtDTJeAbQU", "nickname": "润生"}', 0, NOW())
ON DUPLICATE KEY UPDATE updated_at = CURRENT_TIMESTAMP;

-- 插入提现日志
INSERT INTO `withdraw_log` (`withdraw_id`, `user_id`, `action`, `status_before`, `status_after`, `operator_type`, `remark`) VALUES
(1, 1, 'apply', 0, 0, 'user', '用户申请提现'),
(1, 1, 'audit_pass', 0, 1, 'admin', '管理员审核通过'),
(1, 1, 'process', 1, 3, 'system', '系统开始处理'),
(1, 1, 'complete', 3, 4, 'system', '提现完成'),
(2, 1, 'apply', 0, 0, 'user', '用户申请提现'),
(2, 1, 'audit_pass', 0, 1, 'admin', '管理员审核通过'),
(3, 1, 'apply', 0, 0, 'user', '用户申请提现')
ON DUPLICATE KEY UPDATE created_at = CURRENT_TIMESTAMP;

-- 显示创建结果
SELECT '✅ 提现系统创建完成' as message;

-- 验证表结构
SELECT 
  table_name as '表名',
  table_comment as '表说明'
FROM information_schema.tables 
WHERE table_schema = 'mall' 
AND table_name IN ('user_withdraw_account', 'withdraw_application', 'withdraw_config', 'withdraw_log')
ORDER BY table_name;
