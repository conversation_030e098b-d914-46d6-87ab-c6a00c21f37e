// src/routes/logistics.js
// 物流相关路由

const express = require('express');
const router = express.Router();
const { verifyToken } = require('../middlewares/auth');
const { success, error } = require('../utils/response');
// const logisticsService = require('../services/logistics');
const db = require('../database');
const logger = require('../utils/logger');

// 获取订单状态文本
function getOrderStatusText(status) {
  const statusMap = {
    'pending': '待付款',
    'paid': '已付款',
    'shipped': '已发货',
    'delivered': '已送达',
    'completed': '已完成',
    'cancelled': '已取消',
    'refunded': '已退款'
  };
  return statusMap[status] || '未知状态';
}

/**
 * 获取物流公司列表
 * GET /api/v1/logistics/companies
 */
router.get('/companies', async (req, res) => {
  try {
    const companies = [
      { name: '顺丰速运', code: 'SF', apiCode: 'SF' },
      { name: '圆通速递', code: 'YTO', apiCode: 'YTO' },
      { name: '中通快递', code: 'ZTO', apiCode: 'ZTO' },
      { name: '申通快递', code: 'STO', apiCode: 'STO' },
      { name: '韵达速递', code: 'YD', apiCode: 'YD' },
      { name: '极兔速递', code: 'JTSD', apiCode: 'JTSD' },
      { name: '京东物流', code: 'JD', apiCode: 'JD' },
      { name: '中国邮政', code: 'EMS', apiCode: 'EMS' }
    ];
    return success(res, companies, '获取物流公司列表成功');
  } catch (err) {
    logger.error('获取物流公司列表失败:', err);
    return error(res, '获取物流公司列表失败', 500);
  }
});

/**
 * 查询物流信息
 * GET /api/v1/logistics/track/:companyCode/:trackingNumber
 */
router.get('/track/:companyCode/:trackingNumber', async (req, res) => {
  try {
    const { companyCode, trackingNumber } = req.params;

    if (!companyCode || !trackingNumber) {
      return error(res, '物流公司代码和运单号不能为空', 400);
    }

    // 返回模拟物流数据
    const mockData = {
      company: '顺丰速运',
      trackingNumber: trackingNumber,
      status: 2,
      statusDesc: '在途中',
      traces: [
        {
          time: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString().replace('T', ' ').slice(0, 19),
          status: '派送中',
          desc: '快件正在派送中，请您准备签收（快递员：张师傅，电话：138****5678）',
          location: '北京市朝阳区'
        },
        {
          time: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString().replace('T', ' ').slice(0, 19),
          status: '运输中',
          desc: '快件到达北京朝阳区分拣中心',
          location: '北京市朝阳区'
        }
      ],
      lastUpdate: new Date().toISOString()
    };

    return success(res, mockData, '查询物流信息成功');
  } catch (err) {
    logger.error('查询物流信息失败:', err);
    return error(res, '查询物流信息失败', 500);
  }
});

/**
 * 根据订单ID查询物流信息
 * GET /api/v1/client/logistics/order/:orderId
 */
router.get('/order/:orderId', verifyToken, async (req, res) => {
  try {
    const { orderId } = req.params;
    const userId = req.user.id;

    // 查询订单信息
    const orders = await db.query(
      'SELECT * FROM orders WHERE id = ? AND user_id = ?',
      [orderId, userId]
    );

    if (!orders || orders.length === 0) {
      return error(res, '订单不存在', 404);
    }

    const order = orders[0];

    // 检查订单状态，只有已发货的订单才有物流信息
    if (order.status !== 'shipped' && order.status !== 'delivered') {
      return success(res, {
        hasLogistics: false,
        message: '该订单暂未发货，无物流信息',
        orderInfo: {
          id: order.id,
          orderNo: order.order_no,
          status: order.status,
          statusText: getOrderStatusText(order.status)
        }
      }, '订单状态查询成功');
    }

    // 返回模拟物流信息
    const responseData = {
      hasLogistics: true,
      company: '顺丰速运',
      trackingNumber: order.logistics_no || 'SF1234567890',
      status: 2,
      statusDesc: '在途中',
      traces: [
        {
          time: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString().replace('T', ' ').slice(0, 19),
          status: '派送中',
          desc: '快件正在派送中，请您准备签收（快递员：张师傅，电话：138****5678）',
          location: '北京市朝阳区'
        },
        {
          time: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString().replace('T', ' ').slice(0, 19),
          status: '运输中',
          desc: '快件到达北京朝阳区分拣中心',
          location: '北京市朝阳区'
        },
        {
          time: new Date(Date.now() - 8 * 60 * 60 * 1000).toISOString().replace('T', ' ').slice(0, 19),
          status: '已发货',
          desc: '商品已从仓库发出',
          location: '上海市浦东新区'
        }
      ],
      lastUpdate: new Date().toISOString(),
      orderInfo: {
        id: order.id,
        orderNo: order.order_no,
        logistics_company: order.logistics_company || 'SF',
        logistics_no: order.logistics_no || 'SF1234567890',
        receiver_name: order.receiver_name,
        receiver_phone: order.receiver_phone,
        receiver_address: order.receiver_address,
        status: order.status,
        statusText: getOrderStatusText(order.status)
      }
    };

    return success(res, responseData, '查询物流信息成功');
  } catch (err) {
    logger.error('查询订单物流信息失败:', err);
    return error(res, '查询物流信息失败', 500);
  }
});

/**
 * 更新订单物流信息（管理端）
 * PUT /api/v1/admin/logistics/order/:orderId
 */
router.put('/admin/order/:orderId', verifyToken, async (req, res) => {
  try {
    const { orderId } = req.params;
    const { logistics_company, logistics_no } = req.body;

    if (!logistics_company || !logistics_no) {
      return error(res, '物流公司和运单号不能为空', 400);
    }

    // 简单验证物流公司代码
    const validCompanies = ['SF', 'YTO', 'ZTO', 'STO', 'YD', 'JTSD', 'JD', 'EMS'];
    if (!validCompanies.includes(logistics_company)) {
      return error(res, '不支持的物流公司', 400);
    }

    // 查询订单是否存在
    const orders = await db.query('SELECT * FROM orders WHERE id = ?', [orderId]);
    
    if (!orders || orders.length === 0) {
      return error(res, '订单不存在', 404);
    }

    const order = orders[0];

    // 更新物流信息
    await db.query(
      `UPDATE orders SET 
        logistics_company = ?, 
        logistics_no = ?, 
        shipping_time = CASE WHEN shipping_time IS NULL THEN NOW() ELSE shipping_time END,
        status = CASE WHEN status = 1 THEN 2 ELSE status END,
        updated_at = NOW() 
      WHERE id = ?`,
      [logistics_company, logistics_no, orderId]
    );

    // 记录操作日志
    logger.info(`订单 ${order.order_no} 物流信息已更新: ${logistics_company} - ${logistics_no}`);

    return success(res, null, '物流信息更新成功');
  } catch (err) {
    logger.error('更新物流信息失败:', err);
    return error(res, '更新物流信息失败', 500);
  }
});

/**
 * 批量更新物流信息（管理端）
 * POST /api/v1/admin/logistics/batch-update
 */
router.post('/admin/batch-update', verifyToken, async (req, res) => {
  try {
    const { orders } = req.body;

    if (!orders || !Array.isArray(orders) || orders.length === 0) {
      return error(res, '订单列表不能为空', 400);
    }

    const results = [];

    for (const orderUpdate of orders) {
      const { orderId, logistics_company, logistics_no } = orderUpdate;

      try {
        // 简单验证物流公司代码
        const validCompanies = ['SF', 'YTO', 'ZTO', 'STO', 'YD', 'JTSD', 'JD', 'EMS'];
        if (!validCompanies.includes(logistics_company)) {
          results.push({
            orderId,
            success: false,
            error: '不支持的物流公司'
          });
          continue;
        }

        // 更新物流信息
        await db.query(
          `UPDATE orders SET
            logistics_company = ?,
            logistics_no = ?,
            shipping_time = CASE WHEN shipping_time IS NULL THEN NOW() ELSE shipping_time END,
            status = CASE WHEN status = 1 THEN 2 ELSE status END,
            updated_at = NOW()
          WHERE id = ?`,
          [logistics_company, logistics_no, orderId]
        );

        results.push({
          orderId,
          success: true,
          message: '更新成功'
        });

        logger.info(`批量更新物流信息: 订单ID ${orderId} - ${logistics_company} - ${logistics_no}`);
      } catch (err) {
        results.push({
          orderId,
          success: false,
          error: err.message
        });
      }
    }

    return success(res, { results }, '批量更新完成');
  } catch (err) {
    logger.error('批量更新物流信息失败:', err);
    return error(res, '批量更新物流信息失败', 500);
  }
});

module.exports = router;
