// utils/network-diagnostic.js
// 网络连接诊断工具

const config = require('../config/config.js')

/**
 * 网络连接诊断工具
 */
class NetworkDiagnostic {
  
  /**
   * 运行完整的网络诊断
   */
  static async runFullDiagnostic() {
    console.log('🔍 开始网络诊断...')
    
    const results = {
      networkStatus: await this.checkNetworkStatus(),
      serverReachability: await this.checkServerReachability(),
      dnsResolution: await this.checkDNSResolution(),
      portConnectivity: await this.checkPortConnectivity(),
      recommendations: []
    }
    
    // 生成建议
    results.recommendations = this.generateRecommendations(results)
    
    // 输出诊断报告
    this.printDiagnosticReport(results)
    
    return results
  }
  
  /**
   * 检查网络状态
   */
  static async checkNetworkStatus() {
    return new Promise((resolve) => {
      wx.getNetworkType({
        success: (res) => {
          console.log('📶 网络类型:', res.networkType)
          resolve({
            success: true,
            networkType: res.networkType,
            isConnected: res.networkType !== 'none'
          })
        },
        fail: (error) => {
          console.error('❌ 获取网络状态失败:', error)
          resolve({
            success: false,
            error: error.errMsg
          })
        }
      })
    })
  }
  
  /**
   * 检查服务器可达性
   */
  static async checkServerReachability() {
    const testUrl = config.api.baseUrl + '/health'
    
    return new Promise((resolve) => {
      wx.request({
        url: testUrl,
        method: 'GET',
        timeout: 5000,
        success: (res) => {
          console.log('✅ 服务器连接成功:', res.statusCode)
          resolve({
            success: true,
            statusCode: res.statusCode,
            response: res.data
          })
        },
        fail: (error) => {
          console.error('❌ 服务器连接失败:', error.errMsg)
          resolve({
            success: false,
            error: error.errMsg
          })
        }
      })
    })
  }
  
  /**
   * 检查DNS解析
   */
  static async checkDNSResolution() {
    // 尝试解析域名
    const hostname = config.api.baseUrl.replace(/^https?:\/\//, '').replace(/:\d+.*$/, '')
    
    if (hostname === 'localhost' || hostname.match(/^\d+\.\d+\.\d+\.\d+$/)) {
      return {
        success: true,
        hostname: hostname,
        isLocalhost: true
      }
    }
    
    // 对于实际域名，尝试简单的连接测试
    return new Promise((resolve) => {
      wx.request({
        url: `http://${hostname}`,
        method: 'GET',
        timeout: 3000,
        success: () => {
          resolve({
            success: true,
            hostname: hostname
          })
        },
        fail: (error) => {
          resolve({
            success: false,
            hostname: hostname,
            error: error.errMsg
          })
        }
      })
    })
  }
  
  /**
   * 检查端口连通性
   */
  static async checkPortConnectivity() {
    const testPorts = [4000, 3000, 8080]
    const results = []
    
    for (const port of testPorts) {
      const result = await this.testPort(port)
      results.push(result)
    }
    
    return results
  }
  
  /**
   * 测试特定端口
   */
  static async testPort(port) {
    const testUrl = `http://localhost:${port}`
    
    return new Promise((resolve) => {
      wx.request({
        url: testUrl,
        method: 'GET',
        timeout: 2000,
        success: (res) => {
          resolve({
            port: port,
            success: true,
            statusCode: res.statusCode
          })
        },
        fail: (error) => {
          resolve({
            port: port,
            success: false,
            error: error.errMsg
          })
        }
      })
    })
  }
  
  /**
   * 生成建议
   */
  static generateRecommendations(results) {
    const recommendations = []
    
    // 网络状态建议
    if (!results.networkStatus.success || !results.networkStatus.isConnected) {
      recommendations.push({
        type: 'network',
        priority: 'high',
        message: '检查设备网络连接，确保已连接到Wi-Fi或移动网络'
      })
    }
    
    // 服务器连接建议
    if (!results.serverReachability.success) {
      recommendations.push({
        type: 'server',
        priority: 'high',
        message: '后端服务未启动或不可访问，请启动后端服务'
      })
      
      recommendations.push({
        type: 'server',
        priority: 'medium',
        message: '运行命令: cd wifi-share-server && npm run dev'
      })
    }
    
    // 端口连通性建议
    const workingPorts = results.portConnectivity.filter(p => p.success)
    if (workingPorts.length === 0) {
      recommendations.push({
        type: 'port',
        priority: 'medium',
        message: '没有检测到运行中的服务，请确认后端服务已正确启动'
      })
    } else {
      const workingPort = workingPorts[0].port
      if (workingPort !== 4000) {
        recommendations.push({
          type: 'port',
          priority: 'low',
          message: `检测到服务运行在端口${workingPort}，考虑更新配置文件`
        })
      }
    }
    
    return recommendations
  }
  
  /**
   * 打印诊断报告
   */
  static printDiagnosticReport(results) {
    console.log('\n📋 网络诊断报告:')
    console.log('='.repeat(50))
    
    // 网络状态
    if (results.networkStatus.success) {
      console.log(`✅ 网络状态: ${results.networkStatus.networkType}`)
    } else {
      console.log('❌ 网络状态: 检测失败')
    }
    
    // 服务器连接
    if (results.serverReachability.success) {
      console.log(`✅ 服务器连接: 成功 (${results.serverReachability.statusCode})`)
    } else {
      console.log('❌ 服务器连接: 失败')
    }
    
    // 端口连通性
    const workingPorts = results.portConnectivity.filter(p => p.success)
    if (workingPorts.length > 0) {
      console.log(`✅ 可用端口: ${workingPorts.map(p => p.port).join(', ')}`)
    } else {
      console.log('❌ 端口连通性: 无可用服务')
    }
    
    // 建议
    if (results.recommendations.length > 0) {
      console.log('\n💡 建议:')
      results.recommendations.forEach((rec, index) => {
        const priority = rec.priority === 'high' ? '🔴' : rec.priority === 'medium' ? '🟡' : '🟢'
        console.log(`${priority} ${index + 1}. ${rec.message}`)
      })
    }
    
    console.log('='.repeat(50))
  }
  
  /**
   * 快速连接测试
   */
  static async quickConnectTest() {
    console.log('⚡ 快速连接测试...')
    
    const serverTest = await this.checkServerReachability()
    
    if (serverTest.success) {
      console.log('✅ 后端服务连接正常')
      return true
    } else {
      console.log('❌ 后端服务连接失败')
      console.log('💡 请确保后端服务已启动: cd wifi-share-server && npm run dev')
      return false
    }
  }
}

module.exports = NetworkDiagnostic 