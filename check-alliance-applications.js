const mysql = require('mysql2/promise');

async function checkAllianceApplications() {
  let connection;
  
  try {
    // 连接数据库
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: 'wo587129955',
      database: 'mall'
    });
    
    console.log('🔍 查询联盟申请信息...\n');
    
    // 1. 查询所有申请记录
    console.log('1️⃣ 所有联盟申请记录:');
    const [applications] = await connection.execute(`
      SELECT 
        id,
        user_id,
        name,
        contact,
        phone,
        email,
        area,
        description,
        status,
        created_at,
        updated_at
      FROM team_apply 
      ORDER BY created_at DESC
    `);
    
    if (applications.length > 0) {
      console.table(applications);
      console.log(`\n📊 总共找到 ${applications.length} 条申请记录\n`);
      
      // 2. 按状态分组统计
      console.log('2️⃣ 申请状态统计:');
      const [statusStats] = await connection.execute(`
        SELECT 
          status,
          COUNT(*) as count,
          CASE 
            WHEN status = 0 THEN '待审核'
            WHEN status = 1 THEN '已通过'
            WHEN status = 2 THEN '已拒绝'
            ELSE '未知状态'
          END as status_name
        FROM team_apply 
        GROUP BY status
        ORDER BY status
      `);
      
      console.table(statusStats);
      
      // 3. 查询最近的申请
      console.log('\n3️⃣ 最近的申请详情:');
      const [recentApplications] = await connection.execute(`
        SELECT 
          id,
          name as 公司名称,
          contact as 联系人,
          phone as 手机号,
          email as 邮箱,
          area as 地区,
          description as 描述,
          CASE 
            WHEN status = 0 THEN '待审核'
            WHEN status = 1 THEN '已通过'
            WHEN status = 2 THEN '已拒绝'
            ELSE '未知状态'
          END as 状态,
          created_at as 申请时间
        FROM team_apply 
        ORDER BY created_at DESC 
        LIMIT 5
      `);
      
      console.table(recentApplications);
      
      // 4. 查询关联的用户信息
      console.log('\n4️⃣ 申请人用户信息:');
      const [userInfo] = await connection.execute(`
        SELECT 
          ta.id as 申请ID,
          ta.name as 公司名称,
          ta.contact as 联系人,
          u.id as 用户ID,
          u.nickname as 用户昵称,
          u.phone as 用户手机,
          ta.created_at as 申请时间
        FROM team_apply ta
        LEFT JOIN user u ON ta.user_id = u.id
        ORDER BY ta.created_at DESC
      `);
      
      console.table(userInfo);
      
    } else {
      console.log('❌ 没有找到任何联盟申请记录');
      
      // 检查表结构
      console.log('\n🔧 检查表结构:');
      const [tableStructure] = await connection.execute('DESCRIBE team_apply');
      console.table(tableStructure);
    }
    
    // 5. 检查表是否存在
    console.log('\n5️⃣ 验证表存在性:');
    const [tableExists] = await connection.execute(`
      SELECT COUNT(*) as table_exists 
      FROM information_schema.tables 
      WHERE table_schema = 'mall' AND table_name = 'team_apply'
    `);
    
    console.log('team_apply表存在:', tableExists[0].table_exists > 0 ? '是' : '否');
    
    // 6. 检查最新的数据库操作
    console.log('\n6️⃣ 最近的数据库活动:');
    const [recentActivity] = await connection.execute(`
      SELECT 
        table_name,
        update_time,
        table_rows
      FROM information_schema.tables 
      WHERE table_schema = 'mall' 
      AND table_name IN ('team_apply', 'user')
      ORDER BY update_time DESC
    `);
    
    console.table(recentActivity);
    
  } catch (error) {
    console.error('❌ 查询失败:', error.message);
    
    // 如果是表不存在的错误，提供建议
    if (error.message.includes("doesn't exist")) {
      console.log('\n💡 建议:');
      console.log('1. 检查数据库连接是否正确');
      console.log('2. 确认team_apply表是否已创建');
      console.log('3. 运行建表SQL脚本');
    }
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// 运行查询
checkAllianceApplications();
