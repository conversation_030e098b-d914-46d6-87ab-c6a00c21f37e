console.log('🔧 开始创建提现系统...');

const mysql = require('mysql2/promise');

async function test() {
  try {
    const connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: 'wo587129955',
      database: 'mall'
    });
    
    console.log('✅ 数据库连接成功');
    
    // 创建提现配置表
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS withdraw_config (
        id int(11) NOT NULL AUTO_INCREMENT,
        withdraw_type varchar(20) NOT NULL,
        type_name varchar(50) NOT NULL,
        min_amount decimal(10,2) NOT NULL DEFAULT '1.00',
        max_amount decimal(10,2) NOT NULL DEFAULT '50000.00',
        fee_rate decimal(5,4) NOT NULL DEFAULT '0.0100',
        process_time varchar(50) DEFAULT '1-3个工作日',
        icon varchar(100) DEFAULT NULL,
        is_enabled tinyint(1) NOT NULL DEFAULT '1',
        sort_order int(11) NOT NULL DEFAULT '0',
        created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        UNIQUE KEY withdraw_type (withdraw_type)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
    `);
    
    console.log('✅ 提现配置表创建成功');
    
    // 插入配置数据
    await connection.execute(`
      INSERT INTO withdraw_config (withdraw_type, type_name, min_amount, max_amount, fee_rate, process_time, icon, sort_order) VALUES
      ('wechat', '微信零钱', 1.00, 20000.00, 0.0060, '实时到账', '/assets/icons/wechat.png', 1),
      ('alipay', '支付宝', 1.00, 20000.00, 0.0060, '实时到账', '/assets/icons/alipay.png', 2),
      ('bank_card', '银行卡', 50.00, 50000.00, 0.0100, '1-3个工作日', '/assets/icons/bank.png', 3)
      ON DUPLICATE KEY UPDATE
        type_name = VALUES(type_name),
        min_amount = VALUES(min_amount),
        max_amount = VALUES(max_amount),
        fee_rate = VALUES(fee_rate),
        process_time = VALUES(process_time),
        icon = VALUES(icon)
    `);
    
    console.log('✅ 配置数据插入成功');
    
    // 查询结果
    const [results] = await connection.execute('SELECT * FROM withdraw_config ORDER BY sort_order');
    console.log('📋 提现配置:');
    console.table(results);
    
    await connection.end();
    console.log('🎉 提现系统创建完成！');
    
  } catch (error) {
    console.error('❌ 错误:', error.message);
  }
}

test();
