﻿const express = require('express');
const router = express.Router();
const { verifyToken } = require('../middlewares/auth');
const regionController = require('../controllers/region');

/**
 * 地区管理路由
 */

// 获取地区列表
router.get('/list', verifyToken, regionController.getRegionList);

// 获取地区详情
router.get('/detail/:id', verifyToken, regionController.getRegionDetail);

// 创建地区
router.post('/create', verifyToken, regionController.createRegion);

// 更新地区
router.put('/update/:id', verifyToken, regionController.updateRegion);

// 删除地区
router.delete('/delete/:id', verifyToken, regionController.deleteRegion);

// 获取地区树形结构
router.get('/tree', verifyToken, regionController.getRegionTree);

module.exports = router;
