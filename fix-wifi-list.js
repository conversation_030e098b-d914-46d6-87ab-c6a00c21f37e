/**
 * WiFi列表修复脚本
 * 直接查询数据库并显示所有WiFi记录，不带任何过滤条件
 */

// 引入数据库连接
const db = require('./src/database');
const { success, error } = require('./src/utils/response');

// 直接查询所有WiFi记录
async function getAllWifi() {
  try {
    console.log('开始查询所有WiFi记录...');
    
    // 查询总数
    const countResult = await db.query('SELECT COUNT(*) as total FROM wifi');
    
    // 处理MySQL2返回的[rows, fields]格式
    let total = 0;
    if (Array.isArray(countResult) && countResult.length > 0) {
      if (Array.isArray(countResult[0]) && countResult[0].length > 0) {
        total = countResult[0][0]?.total || 0;
      } else if (countResult[0]?.total !== undefined) {
        total = countResult[0].total;
      }
    }
    
    console.log(`数据库中共有 ${total} 条WiFi记录`);
    
    // 查询所有记录
    const listResult = await db.query('SELECT * FROM wifi ORDER BY created_at DESC');
    
    // 处理MySQL2返回的[rows, fields]格式
    let rows = [];
    if (Array.isArray(listResult) && listResult.length > 0) {
      if (Array.isArray(listResult[0])) {
        rows = listResult[0];
      } else {
        rows = listResult;
      }
    }
    
    console.log('查询结果:', rows);
    console.log(`成功获取 ${rows.length} 条WiFi记录`);
    
    // 按status分组统计
    const statusCounts = {};
    rows.forEach(row => {
      const status = row.status;
      statusCounts[status] = (statusCounts[status] || 0) + 1;
    });
    
    console.log('按status分组统计:', statusCounts);
    
    // 按user_id分组统计
    const userCounts = {};
    rows.forEach(row => {
      const userId = row.user_id;
      userCounts[userId] = (userCounts[userId] || 0) + 1;
    });
    
    console.log('按user_id分组统计:', userCounts);
    
    return rows;
  } catch (err) {
    console.error('查询失败:', err);
    return null;
  }
}

// 执行查询
getAllWifi()
  .then(result => {
    if (result) {
      console.log('查询成功，程序退出');
      process.exit(0);
    } else {
      console.error('查询失败，程序退出');
      process.exit(1);
    }
  })
  .catch(err => {
    console.error('执行出错:', err);
    process.exit(1);
  }); 