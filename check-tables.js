const mysql = require('mysql2/promise');
const config = require('./config');

async function checkTables() {
  try {
    console.log('正在连接数据库...');
    const connection = await mysql.createConnection({
      host: config.database.host,
      user: config.database.user,
      password: config.database.password,
      database: config.database.database
    });
    
    console.log('正在检查数据库表...');
    const [tables] = await connection.query('SHOW TABLES');
    console.log('数据库中的表:');
    console.log(JSON.stringify(tables, null, 2));
    
    // 检查team_apply表是否存在
    let teamApplyExists = false;
    for(const table of tables) {
      for(const key in table) {
        if(table[key] === 'team_apply') {
          teamApplyExists = true;
          break;
        }
      }
    }
    
    console.log(`team_apply表${teamApplyExists ? '存在' : '不存在'}`);
    
    if(!teamApplyExists) {
      console.log('需要创建team_apply表');
    }
    
    await connection.end();
  } catch (err) {
    console.error('检查数据库表时出错:', err);
  }
}

checkTables(); 