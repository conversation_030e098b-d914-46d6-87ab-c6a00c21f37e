// 测试真实API数据获取
const axios = require('axios');
const jwt = require('jsonwebtoken');

async function testRealAPIData() {
  try {
    console.log('🧪 测试真实API数据获取...\n');
    
    // 1. 生成测试token
    const testUser = {
      id: 1,
      openid: 'oW20C7mVlW8e3W2AgUGtDTJeAbQU'
    };
    
    const token = jwt.sign(
      testUser,
      'wifi_share_secret_key',
      { expiresIn: '24h' }
    );
    
    console.log('1️⃣ 生成的测试token:', token.substring(0, 50) + '...\n');
    
    // 2. 测试收入统计接口
    console.log('2️⃣ 测试收入统计接口...');
    try {
      const response = await axios.get('http://localhost:4000/api/v1/client/income/stats', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        timeout: 10000
      });
      
      console.log('✅ API调用成功:');
      console.log('状态码:', response.status);
      console.log('响应头:', response.headers);
      console.log('响应数据:', JSON.stringify(response.data, null, 2));
      
      // 分析响应数据结构
      const res = response.data;
      console.log('\n📊 数据结构分析:');
      console.log('- success:', res.success);
      console.log('- code:', res.code);
      console.log('- message:', res.message);
      console.log('- data:', res.data);
      
      if ((res.code === 0 || res.code === 200 || res.success === true) && res.data) {
        const data = res.data;
        console.log('\n💰 钱包数据解析:');
        console.log('- 余额 (balance):', data.balance || '未设置');
        console.log('- WiFi收入 (wifi_income):', data.wifi_income || '未设置');
        console.log('- 团队收入 (team_income):', data.team_income || '未设置');
        console.log('- 广告收入 (ad_income):', data.ad_income || '未设置');
        console.log('- 商品收入 (goods_income):', data.goods_income || '未设置');
        
        // 检查嵌套结构
        if (data.total) {
          console.log('\n📦 嵌套total结构:');
          console.log('- total.balance:', data.total.balance || '未设置');
          console.log('- total.wifi_income:', data.total.wifi_income || '未设置');
          console.log('- total.team_income:', data.total.team_income || '未设置');
          console.log('- total.ad_income:', data.total.ad_income || '未设置');
          console.log('- total.goods_income:', data.total.goods_income || '未设置');
        }
        
        // 生成前端映射代码
        console.log('\n🔧 前端数据映射:');
        const mappedData = {
          balance: data.balance || data.total?.balance || '0.00',
          incomeStats: {
            wifi: data.wifi_income || data.total?.wifi_income || '0.00',
            team: data.team_income || data.referral_income || data.total?.team_income || '0.00',
            ads: data.ad_income || data.advertisement_income || data.total?.ad_income || '0.00',
            mall: data.goods_income || data.mall_income || data.total?.goods_income || '0.00'
          }
        };
        
        console.log('映射结果:', JSON.stringify(mappedData, null, 2));
        
        // 验证数据是否为真实数据
        const hasRealData = parseFloat(mappedData.balance) > 0 || 
                           parseFloat(mappedData.incomeStats.wifi) > 0 ||
                           parseFloat(mappedData.incomeStats.team) > 0 ||
                           parseFloat(mappedData.incomeStats.ads) > 0 ||
                           parseFloat(mappedData.incomeStats.mall) > 0;
        
        if (hasRealData) {
          console.log('✅ 检测到真实数据！');
        } else {
          console.log('⚠️ 所有数据都为0，可能是新用户或数据未生成');
        }
        
      } else {
        console.log('❌ 响应格式不正确或无数据');
      }
      
    } catch (apiError) {
      console.log('❌ API调用失败:');
      if (apiError.response) {
        console.log('状态码:', apiError.response.status);
        console.log('错误信息:', JSON.stringify(apiError.response.data, null, 2));
        
        if (apiError.response.status === 401) {
          console.log('🔐 认证失败，可能是token无效');
        } else if (apiError.response.status === 404) {
          console.log('🔍 接口不存在，请检查API路径');
        } else if (apiError.response.status === 500) {
          console.log('💥 服务器内部错误');
        }
      } else if (apiError.request) {
        console.log('🌐 网络请求失败，无响应');
        console.log('请求配置:', apiError.config);
      } else {
        console.log('⚙️ 请求配置错误:', apiError.message);
      }
    }
    
    // 3. 测试后端服务状态
    console.log('\n3️⃣ 测试后端服务状态...');
    try {
      const healthResponse = await axios.get('http://localhost:4000/health', {
        timeout: 5000
      });
      
      console.log('✅ 后端服务正常运行');
      console.log('健康检查响应:', healthResponse.data);
      
    } catch (healthError) {
      console.log('❌ 后端服务可能未启动');
      console.log('错误:', healthError.message);
    }
    
    // 4. 生成调试建议
    console.log('\n🔧 调试建议:');
    console.log('1. 确保后端服务已启动 (npm run dev)');
    console.log('2. 检查API接口是否正确实现');
    console.log('3. 验证用户token是否有效');
    console.log('4. 确认数据库中有收入记录');
    console.log('5. 检查前端API配置是否正确');
    
    // 5. 生成前端修复代码
    console.log('\n💻 前端修复代码:');
    console.log(`
// 在钱包页面添加实时数据获取调试
fetchWalletData: function () {
  console.log('🔧 开始获取钱包数据...')
  this.setData({ loading: true })

  request({
    url: API.income.stats,
    method: 'GET'
  }).then(res => {
    console.log('💰 API响应:', res)
    
    if ((res.code === 0 || res.code === 200 || res.success === true) && res.data) {
      const data = res.data
      
      // 真实数据映射（不使用默认值）
      const walletData = {
        balance: data.balance || data.total?.balance || '0.00',
        incomeStats: {
          wifi: data.wifi_income || data.total?.wifi_income || '0.00',
          team: data.team_income || data.total?.team_income || '0.00',
          ads: data.ad_income || data.total?.ad_income || '0.00',
          mall: data.goods_income || data.total?.goods_income || '0.00'
        },
        loading: false
      }
      
      console.log('📊 设置钱包数据:', walletData)
      this.setData(walletData)
      
      // 检查是否有真实数据
      const hasData = Object.values(walletData.incomeStats).some(val => parseFloat(val) > 0)
      if (!hasData && parseFloat(walletData.balance) === 0) {
        showToast('暂无收入数据')
      }
      
    } else {
      console.error('❌ API响应格式错误:', res)
      showToast('获取数据失败')
      this.setData({ loading: false })
    }
  }).catch(err => {
    console.error('❌ API调用失败:', err)
    showToast('网络错误，请重试')
    this.setData({ loading: false })
  })
}
`);
    
  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error.message);
  }
}

// 运行测试
testRealAPIData();
