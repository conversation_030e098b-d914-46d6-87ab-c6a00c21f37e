<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>联盟管理API测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1, h2 {
            color: #333;
        }
        .api-test {
            border: 1px solid #ddd;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .api-url {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            margin: 10px 0;
        }
        .result {
            background: #e9ecef;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 300px;
            overflow-y: auto;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .status {
            padding: 5px 10px;
            border-radius: 3px;
            font-size: 12px;
            font-weight: bold;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔗 联盟管理API测试工具</h1>
        <p>测试后端联盟管理接口是否正常工作</p>
    </div>

    <div class="container">
        <h2>📋 联盟申请列表</h2>
        <div class="api-test">
            <div class="api-url">GET http://localhost:4000/api/v1/admin/alliance/list</div>
            <button onclick="testAllianceList()">测试接口</button>
            <div id="allianceListResult" class="result"></div>
        </div>
    </div>

    <div class="container">
        <h2>📊 联盟统计信息</h2>
        <div class="api-test">
            <div class="api-url">GET http://localhost:4000/api/v1/admin/alliance/stats</div>
            <button onclick="testAllianceStats()">测试接口</button>
            <div id="allianceStatsResult" class="result"></div>
        </div>
    </div>

    <div class="container">
        <h2>🔍 联盟申请详情</h2>
        <div class="api-test">
            <div class="api-url">GET http://localhost:4000/api/v1/admin/alliance/detail/1</div>
            <button onclick="testAllianceDetail(1)">测试接口 (ID: 1)</button>
            <div id="allianceDetailResult" class="result"></div>
        </div>
    </div>

    <div class="container">
        <h2>✅ 审核联盟申请</h2>
        <div class="api-test">
            <div class="api-url">POST http://localhost:4000/api/v1/admin/alliance/audit/1</div>
            <button onclick="testAllianceAudit(1, 1)">通过申请 (ID: 1)</button>
            <button onclick="testAllianceAudit(1, 2)">拒绝申请 (ID: 1)</button>
            <div id="allianceAuditResult" class="result"></div>
        </div>
    </div>

    <div class="container">
        <h2>🌐 平台收益统计</h2>
        <div class="api-test">
            <div class="api-url">GET http://localhost:4000/api/v1/admin/platform/overview</div>
            <button onclick="testPlatformOverview()">测试平台概览</button>
            <div id="platformOverviewResult" class="result"></div>
        </div>
    </div>

    <script>
        // 通用API请求函数
        async function apiRequest(url, method = 'GET', data = null) {
            try {
                const options = {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json',
                    }
                };
                
                if (data) {
                    options.body = JSON.stringify(data);
                }
                
                const response = await fetch(url, options);
                const result = await response.json();
                
                return {
                    status: response.status,
                    data: result
                };
            } catch (error) {
                return {
                    status: 'error',
                    data: { error: error.message }
                };
            }
        }

        // 显示结果
        function displayResult(elementId, result) {
            const element = document.getElementById(elementId);
            const statusClass = result.status === 200 ? 'success' : 'error';
            
            element.innerHTML = `
                <div class="status ${statusClass}">
                    状态: ${result.status}
                </div>
                ${JSON.stringify(result.data, null, 2)}
            `;
        }

        // 测试联盟申请列表
        async function testAllianceList() {
            const result = await apiRequest('http://localhost:4000/api/v1/admin/alliance/list?page=1&limit=10');
            displayResult('allianceListResult', result);
        }

        // 测试联盟统计
        async function testAllianceStats() {
            const result = await apiRequest('http://localhost:4000/api/v1/admin/alliance/stats');
            displayResult('allianceStatsResult', result);
        }

        // 测试联盟申请详情
        async function testAllianceDetail(id) {
            const result = await apiRequest(`http://localhost:4000/api/v1/admin/alliance/detail/${id}`);
            displayResult('allianceDetailResult', result);
        }

        // 测试联盟申请审核
        async function testAllianceAudit(id, status) {
            const data = {
                status: status,
                remark: status === 1 ? '申请通过' : '申请不符合要求'
            };
            const result = await apiRequest(`http://localhost:4000/api/v1/admin/alliance/audit/${id}`, 'POST', data);
            displayResult('allianceAuditResult', result);
        }

        // 测试平台概览
        async function testPlatformOverview() {
            const result = await apiRequest('http://localhost:4000/api/v1/admin/platform/overview');
            displayResult('platformOverviewResult', result);
        }

        // 页面加载时自动测试联盟列表
        window.onload = function() {
            console.log('🚀 联盟管理API测试工具已加载');
            testAllianceList();
        };
    </script>
</body>
</html>
