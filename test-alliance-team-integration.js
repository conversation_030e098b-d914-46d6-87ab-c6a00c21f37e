const db = require('./config/database');

async function testAllianceTeamIntegration() {
  try {
    console.log('🔍 测试联盟申请与团队管理的关联功能...\n');
    console.log('正在连接数据库...');

    // 1. 检查联盟申请列表（包含团队信息）
    console.log('📋 联盟申请列表（包含关联团队信息）:');
    const applications = await db.query(`
      SELECT
        ta.id,
        ta.user_id,
        ta.name,
        ta.contact,
        ta.phone,
        ta.area,
        ta.status,
        ta.created_at,
        u.nickname as user_nickname,
        u.is_leader,
        u.team_id,
        t.id as team_id,
        t.name as team_name,
        t.member_count as team_member_count,
        t.created_at as team_created_at,
        CASE
          WHEN ta.status = 0 THEN '待审核'
          WHEN ta.status = 1 THEN '已通过'
          WHEN ta.status = 2 THEN '已拒绝'
          ELSE '未知'
        END as status_text,
        CASE
          WHEN ta.status = 1 AND t.id IS NOT NULL THEN '已创建团队'
          WHEN ta.status = 1 AND t.id IS NULL THEN '审核通过但团队创建失败'
          WHEN ta.status = 0 THEN '等待审核'
          WHEN ta.status = 2 THEN '申请被拒绝'
          ELSE '状态异常'
        END as process_status
      FROM team_apply ta
      LEFT JOIN user u ON ta.user_id = u.id
      LEFT JOIN team t ON t.leader_id = ta.user_id AND ta.status = 1
      ORDER BY ta.created_at DESC
      LIMIT 10
    `);

    if (applications && applications.length > 0) {
      console.table(applications.map(app => ({
        申请ID: app.id,
        用户昵称: app.user_nickname,
        团队名称: app.name,
        申请状态: app.status_text,
        处理状态: app.process_status,
        关联团队ID: app.team_id || '无',
        关联团队名: app.team_name || '无',
        团队成员数: app.team_member_count || 0,
        申请时间: app.created_at?.toISOString().split('T')[0],
        团队创建时间: app.team_created_at?.toISOString().split('T')[0] || '无'
      })));
    } else {
      console.log('❌ 没有找到联盟申请记录');
    }

    console.log('\n' + '='.repeat(80) + '\n');

    // 2. 检查团队列表（包含来源信息）
    console.log('🏢 团队列表（包含来源信息）:');
    const teams = await db.query(`
      SELECT 
        t.*,
        u.nickname as leader_name,
        u.phone as leader_phone,
        ta.id as alliance_apply_id,
        ta.area as alliance_area,
        ta.description as alliance_description,
        ta.created_at as alliance_apply_time,
        CASE 
          WHEN ta.id IS NOT NULL THEN '联盟申请'
          ELSE '手动创建'
        END as team_source,
        CASE 
          WHEN ta.id IS NOT NULL THEN 'alliance'
          ELSE 'manual'
        END as source_type
      FROM team t
      LEFT JOIN user u ON t.leader_id = u.id
      LEFT JOIN team_apply ta ON ta.user_id = t.leader_id AND ta.status = 1
      ORDER BY t.created_at DESC
      LIMIT 10
    `);

    if (teams && teams.length > 0) {
      console.table(teams.map(team => ({
        团队ID: team.id,
        团队名称: team.name,
        团长: team.leader_name,
        成员数: team.member_count,
        来源: team.team_source,
        来源类型: team.source_type,
        联盟申请ID: team.alliance_apply_id || '无',
        申请区域: team.alliance_area || '无',
        创建时间: team.created_at?.toISOString().split('T')[0],
        申请时间: team.alliance_apply_time?.toISOString().split('T')[0] || '无'
      })));
    } else {
      console.log('❌ 没有找到团队记录');
    }

    console.log('\n' + '='.repeat(80) + '\n');

    // 3. 检查数据一致性
    console.log('🔍 数据一致性检查:');
    
    // 检查已通过的联盟申请是否都有对应的团队
    const approvedApplications = await db.query(`
      SELECT ta.*, t.id as team_id
      FROM team_apply ta
      LEFT JOIN team t ON t.leader_id = ta.user_id
      WHERE ta.status = 1
    `);

    const inconsistentData = approvedApplications.filter(app => !app.team_id);
    
    if (inconsistentData.length > 0) {
      console.log('⚠️  发现数据不一致的情况:');
      console.table(inconsistentData.map(app => ({
        申请ID: app.id,
        用户ID: app.user_id,
        申请名称: app.name,
        状态: '已通过但无对应团队',
        申请时间: app.created_at?.toISOString().split('T')[0]
      })));
    } else {
      console.log('✅ 所有已通过的联盟申请都有对应的团队');
    }

    // 检查团队是否都有正确的团长设置
    const teamsWithoutLeader = await db.query(`
      SELECT t.*, u.is_leader
      FROM team t
      LEFT JOIN user u ON t.leader_id = u.id
      WHERE u.is_leader != 1 OR u.is_leader IS NULL
    `);

    if (teamsWithoutLeader.length > 0) {
      console.log('⚠️  发现团队团长设置异常:');
      console.table(teamsWithoutLeader.map(team => ({
        团队ID: team.id,
        团队名称: team.name,
        团长ID: team.leader_id,
        团长状态: team.is_leader === 1 ? '正常' : '异常',
        问题: '用户is_leader字段不为1'
      })));
    } else {
      console.log('✅ 所有团队的团长设置都正常');
    }

    console.log('\n✅ 联盟申请与团队管理关联功能测试完成！');

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    console.error('错误详情:', error);
  } finally {
    console.log('关闭数据库连接...');
    if (db.pool) {
      await db.pool.end();
    }
    process.exit(0);
  }
}

// 运行测试
testAllianceTeamIntegration();
