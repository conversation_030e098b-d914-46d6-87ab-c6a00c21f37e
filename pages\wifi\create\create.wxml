<!--pages/wifi/create/create.wxml-->
<!--WiFi码创建页面模板-->

<view class="container">
  <!-- 顶部说明区域 -->
  <view class="header-section">
    <view class="wifi-icon">
      <image src="/assets/icons/wifi-large.png" class="wifi-image" mode="aspectFit"></image>
    </view>
    <view class="description">
      <text class="title">WiFi一键快速连接</text>
      <text class="subtitle">无需告知密码 安全防蹭网</text>
    </view>
  </view>

  <!-- 表单区域 -->
  <view class="form-section">
    <!-- WiFi标题 -->
    <view class="form-item">
      <view class="form-label">
        <text>WiFi标题</text>
        <text class="required">*</text>
      </view>
      <view class="form-input-wrapper">
        <input 
          class="form-input {{formErrors.title ? 'error' : ''}}"
          type="text"
          placeholder="请输入WiFi标题，如：咖啡厅WiFi"
          value="{{formData.title}}"
          maxlength="20"
          bindinput="onInputChange"
          data-field="title"
        />
        <view class="input-counter">{{formData.title.length}}/20</view>
      </view>
      <view class="form-error" wx:if="{{formErrors.title}}">
        <text>{{formErrors.title}}</text>
      </view>
    </view>

    <!-- WiFi名称 -->
    <view class="form-item">
      <view class="form-label">
        <text>WiFi名称</text>
        <text class="required">*</text>
      </view>
      <view class="form-input-wrapper">
        <input 
          class="form-input {{formErrors.ssid ? 'error' : ''}}"
          type="text"
          placeholder="请输入WiFi网络名称(SSID)"
          value="{{formData.ssid}}"
          maxlength="32"
          bindinput="onInputChange"
          data-field="ssid"
        />
        <view class="input-counter">{{formData.ssid.length}}/32</view>
      </view>
      <view class="form-error" wx:if="{{formErrors.ssid}}">
        <text>{{formErrors.ssid}}</text>
      </view>
    </view>

    <!-- WiFi密码 -->
    <view class="form-item">
      <view class="form-label">
        <text>WiFi密码</text>
        <text class="required">*</text>
      </view>
      <view class="form-input-wrapper">
        <input 
          class="form-input {{formErrors.password ? 'error' : ''}}"
          type="text"
          password="{{true}}"
          placeholder="请输入WiFi密码，至少8位"
          value="{{formData.password}}"
          maxlength="63"
          bindinput="onInputChange"
          data-field="password"
        />
        <view class="input-counter">{{formData.password.length}}/63</view>
      </view>
      <view class="form-error" wx:if="{{formErrors.password}}">
        <text>{{formErrors.password}}</text>
      </view>
    </view>

    <!-- 商户名称 -->
    <view class="form-item">
      <view class="form-label">
        <text>商户名称</text>
        <text class="required">*</text>
      </view>
      <view class="form-input-wrapper">
        <input 
          class="form-input {{formErrors.merchantName ? 'error' : ''}}"
          type="text"
          placeholder="请输入商户名称"
          value="{{formData.merchantName}}"
          maxlength="20"
          bindinput="onInputChange"
          data-field="merchantName"
        />
        <view class="input-counter">{{formData.merchantName.length}}/20</view>
      </view>
      <view class="form-error" wx:if="{{formErrors.merchantName}}">
        <text>{{formErrors.merchantName}}</text>
      </view>
    </view>
  </view>

  <!-- 操作按钮区域 -->
  <view class="action-section">
    <view class="action-buttons">
      <button 
        class="btn btn-secondary" 
        bindtap="onResetForm"
        disabled="{{isSubmitting}}"
      >
        重置
      </button>
      <button 
        class="btn btn-primary {{isSubmitting ? 'loading' : ''}}" 
        bindtap="onCreateWifiCode"
        disabled="{{isSubmitting}}"
      >
        {{isSubmitting ? '创建中...' : '创建WiFi码'}}
      </button>
    </view>
    
    <!-- 帮助说明 -->
    <view class="help-tip" bindtap="onHelpTap">
      <text class="help-icon">?</text>
      <text class="help-text">使用说明</text>
    </view>
  </view>

  <!-- 广告区域 -->
  <view class="ad-section">
    <view class="ad-banner" bindtap="onAdTap">
      <image src="{{adData.image}}" class="ad-image" mode="aspectFill"></image>
      <view class="ad-content">
        <text class="ad-title">{{adData.title}}</text>
        <text class="ad-subtitle">{{adData.subtitle}}</text>
      </view>
    </view>
  </view>

  <!-- 底部间距 -->
  <view class="bottom-spacing"></view>
</view> 