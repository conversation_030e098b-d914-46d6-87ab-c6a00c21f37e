const express = require('express');
const router = express.Router();
const teamController = require('../controllers/team');
const { verifyToken, optionalAuth } = require('../middlewares/auth');
const { success, error } = require('../utils/response');
const db = require('../database');

// 获取团队信息 - 使用可选认证
router.get('/info', optionalAuth, teamController.getTeamInfo);

// 获取团队成员列表
router.get('/members', verifyToken, teamController.getTeamMembers);

// 创建团队
router.post('/create', verifyToken, async (req, res) => {
  console.log('客户端创建团队');
  try {
    const userId = req.user.id;
    const { name } = req.body;
    
    if (!name) {
      return error(res, '团队名称不能为空');
    }
    
    // 检查用户是否已经是团队成员
    const memberCheck = await db.query(
      'SELECT * FROM team_member WHERE user_id = ?',
      [userId]
    );
    
    if (memberCheck && memberCheck.length > 0) {
      return error(res, '您已经是团队成员，无法创建新团队');
    }
    
    // 创建团队
    const teamResult = await db.query(
      'INSERT INTO team (name, leader_id, member_count) VALUES (?, ?, 1)',
      [name, userId]
    );
    
    const teamId = teamResult.insertId;
    
    // 添加用户为团队成员（团长角色）
    await db.query(
      'INSERT INTO team_member (team_id, user_id, role) VALUES (?, ?, ?)',
      [teamId, userId, 'leader']
    );
    
    // 更新用户为团长
    await db.query(
      'UPDATE user SET is_leader = 1 WHERE id = ?',
      [userId]
    );
    
    return success(res, { id: teamId, name }, '创建团队成功');
  } catch (err) {
    console.error('创建团队失败:', err);
    return error(res, '创建团队失败，请稍后重试');
  }
});

module.exports = router; 