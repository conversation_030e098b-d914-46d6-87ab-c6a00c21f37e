<!--pages/user/income-details/income-details.wxml-->
<view class="income-details-container">
  <!-- 统计信息头部 -->
  <view class="stats-header">
    <view class="stats-card">
      <view class="stats-title">收入概览</view>
      <view class="stats-grid">
        <view class="stats-item">
          <view class="stats-value">¥{{formatAmount(totalStats.total_income)}}</view>
          <view class="stats-label">累计收入</view>
        </view>
        <view class="stats-item">
          <view class="stats-value">¥{{formatAmount(totalStats.today_income)}}</view>
          <view class="stats-label">今日收入</view>
        </view>
        <view class="stats-item">
          <view class="stats-value">¥{{formatAmount(totalStats.month_income)}}</view>
          <view class="stats-label">本月收入</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 筛选器 -->
  <view class="filter-section">
    <view class="filter-label">筛选类型</view>
    <picker
      class="filter-picker"
      range="{{typeOptions}}"
      range-key="label"
      value="{{filterType}}"
      bindchange="onFilterChange"
    >
      <view class="picker-text">
        {{currentFilterLabel}}
        <text class="picker-arrow">▼</text>
      </view>
    </picker>
  </view>

  <!-- 收入明细列表 -->
  <view class="income-list">
    <!-- 收入项 -->
    <view 
      class="income-item"
      wx:for="{{incomeList}}" 
      wx:key="id"
      data-item="{{item}}"
      bindtap="onViewDetail"
    >
      <!-- 收入头部 -->
      <view class="income-header">
        <view class="income-type">
          <text class="type-icon">{{getIncomeTypeIcon(item.type)}}</text>
          <text class="type-name">{{getIncomeTypeText(item.type)}}</text>
        </view>
        <view class="income-amount">+¥{{formatAmount(item.amount)}}</view>
      </view>

      <!-- 收入内容 -->
      <view class="income-content">
        <view class="income-title" wx:if="{{item.title}}">{{item.title}}</view>
        <view class="income-desc" wx:if="{{item.description}}">{{item.description}}</view>
        <view class="income-desc" wx:elif="{{item.type}}">
          {{item.type === 'wifi_share' ? 'WiFi分享收益' : item.type === 'goods_sale' ? '商品销售分润' : item.type === 'advertisement' ? '广告点击收益' : '其他收益'}}
        </view>
      </view>

      <!-- 收入底部 -->
      <view class="income-footer">
        <view class="income-time">{{formatTime(item.created_at || item.createTime)}}</view>
        <view class="income-status">
          <text class="status-text {{item.status === 1 ? 'confirmed' : 'pending'}}">
            {{item.status === 1 ? '已到账' : '处理中'}}
          </text>
        </view>
      </view>

      <!-- 来源信息 -->
      <view class="income-source" wx:if="{{item.source_id || item.order_no}}">
        <text class="source-label">订单号</text>
        <text class="source-value">
          {{item.order_no || item.source_id || '--'}}
        </text>
      </view>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{!loading && incomeList.length === 0}}">
      <image class="empty-icon" src="/assets/icons/empty.svg" mode="aspectFit"></image>
      <view class="empty-text">暂无收入记录</view>
      <button class="empty-action" bindtap="onBackToWallet">返回钱包</button>
    </view>

    <!-- 加载更多 -->
    <view class="load-more" wx:if="{{hasMore && incomeList.length > 0}}">
      <view class="load-more-text" wx:if="{{loadingMore}}">加载中...</view>
      <view class="load-more-text" wx:else>上拉加载更多</view>
    </view>

    <!-- 没有更多 -->
    <view class="no-more" wx:if="{{!hasMore && incomeList.length > 0}}">
      <text>没有更多记录了</text>
    </view>
  </view>

  <!-- 底部统计 -->
  <view class="bottom-stats" wx:if="{{incomeList.length > 0}}">
    <text class="stats-text">共 {{total}} 条收入记录</text>
  </view>

  <!-- 加载状态 -->
  <view class="loading-mask" wx:if="{{loading}}">
    <view class="loading-content">
      <view class="loading-spinner"></view>
      <text>加载中...</text>
    </view>
  </view>

  <!-- 刷新状态 -->
  <view class="refresh-indicator" wx:if="{{refreshing}}">
    <view class="refresh-spinner"></view>
    <text>刷新中...</text>
  </view>
</view>
