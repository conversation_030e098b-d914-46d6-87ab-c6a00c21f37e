const mysql = require('mysql2/promise');

async function checkWalletData() {
  const connection = await mysql.createConnection({
    host: 'localhost',
    user: 'root',
    password: 'wo587129955',
    database: 'mall'
  });
  
  try {
    console.log('🔍 检查钱包相关表和数据...\n');
    
    // 检查users表是否存在
    const [userTables] = await connection.execute(
      "SELECT table_name FROM information_schema.tables WHERE table_schema = 'mall' AND table_name = 'users'"
    );
    
    if (userTables.length === 0) {
      console.log('❌ users表不存在，正在创建...');
      
      // 创建users表
      await connection.execute(`
        CREATE TABLE users (
          id int(11) NOT NULL AUTO_INCREMENT,
          nickname varchar(50) NOT NULL COMMENT '昵称',
          phone varchar(20) DEFAULT NULL COMMENT '手机号',
          avatar varchar(255) DEFAULT NULL COMMENT '头像',
          balance decimal(10,2) DEFAULT 0.00 COMMENT '余额',
          total_income decimal(10,2) DEFAULT 0.00 COMMENT '总收入',
          role enum('user','admin','team_leader') DEFAULT 'user' COMMENT '角色',
          status tinyint(1) DEFAULT 1 COMMENT '状态：0禁用，1启用',
          created_at timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
          updated_at timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
          PRIMARY KEY (id),
          UNIQUE KEY phone (phone),
          KEY role (role),
          KEY status (status)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表'
      `);
      
      console.log('✅ users表创建成功');
    } else {
      console.log('✅ users表已存在');
    }
    
    // 检查wallet_transactions表是否存在
    const [transactionTables] = await connection.execute(
      "SELECT table_name FROM information_schema.tables WHERE table_schema = 'mall' AND table_name = 'wallet_transactions'"
    );
    
    if (transactionTables.length === 0) {
      console.log('❌ wallet_transactions表不存在，正在创建...');
      
      // 创建wallet_transactions表
      await connection.execute(`
        CREATE TABLE wallet_transactions (
          id int(11) NOT NULL AUTO_INCREMENT,
          user_id int(11) NOT NULL COMMENT '用户ID',
          type enum('income','expense') NOT NULL COMMENT '交易类型：income收入，expense支出',
          amount decimal(10,2) NOT NULL COMMENT '交易金额',
          balance_before decimal(10,2) NOT NULL COMMENT '交易前余额',
          balance_after decimal(10,2) NOT NULL COMMENT '交易后余额',
          business_type varchar(50) NOT NULL COMMENT '业务类型',
          description varchar(255) DEFAULT NULL COMMENT '交易描述',
          admin_id int(11) DEFAULT NULL COMMENT '操作管理员ID',
          created_at timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
          PRIMARY KEY (id),
          KEY user_id (user_id),
          KEY type (type),
          KEY business_type (business_type),
          KEY created_at (created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='钱包交易记录表'
      `);
      
      console.log('✅ wallet_transactions表创建成功');
    } else {
      console.log('✅ wallet_transactions表已存在');
    }
    
    // 检查是否有用户数据
    const [users] = await connection.execute('SELECT * FROM users LIMIT 10');
    console.log(`📊 当前用户数量: ${users.length}`);
    
    if (users.length === 0) {
      console.log('🔧 没有用户数据，正在创建测试用户...');
      
      // 创建测试用户
      await connection.execute(`
        INSERT INTO users (nickname, phone, balance, total_income, role, status) VALUES
        ('张三', '***********', 1500.50, 2000.00, 'user', 1),
        ('李四', '***********', 2300.75, 3500.00, 'user', 1),
        ('王五', '***********', 500.00, 800.00, 'user', 1),
        ('赵六', '***********', 0.00, 0.00, 'user', 1),
        ('团队长A', '***********', 5000.00, 8000.00, 'team_leader', 1)
      `);
      
      console.log('✅ 测试用户创建成功');
      
      // 为用户创建一些交易记录
      const [newUsers] = await connection.execute('SELECT id, nickname, balance FROM users');
      
      for (const user of newUsers) {
        if (user.balance > 0) {
          // 创建收入记录
          await connection.execute(`
            INSERT INTO wallet_transactions (user_id, type, amount, balance_before, balance_after, business_type, description) VALUES
            (?, 'income', ?, 0.00, ?, 'wifi_share', '分享WiFi收益'),
            (?, 'income', ?, ?, ?, 'referral', '推荐奖励')
          `, [
            user.id, user.balance * 0.6, user.balance * 0.6,
            user.id, user.balance * 0.4, user.balance * 0.6, user.balance
          ]);
        }
      }
      
      console.log('✅ 测试交易记录创建成功');
    } else {
      console.log('📋 现有用户列表:');
      users.forEach(user => {
        console.log(`  - ID: ${user.id}, 昵称: ${user.nickname}, 手机: ${user.phone}, 余额: ¥${user.balance}, 角色: ${user.role}`);
      });
    }
    
    // 查询交易记录
    const [transactions] = await connection.execute('SELECT COUNT(*) as count FROM wallet_transactions');
    console.log(`💰 交易记录数量: ${transactions[0].count}`);
    
    console.log('\n✅ 钱包数据检查完成！');
    
  } catch (error) {
    console.error('❌ 错误:', error.message);
  } finally {
    await connection.end();
  }
}

checkWalletData();
