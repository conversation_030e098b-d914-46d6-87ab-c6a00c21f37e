const OrderModel = require('../models/order');
const logger = require('../utils/logger');
const { success, error } = require('../utils/response');

/**
 * 创建订单
 */
const createOrder = async (req, res) => {
  try {
    const userId = req.user.id;
    const { items, address, totalAmount, remark, paymentMethod, deliveryFee } = req.body;
    
    // 验证必填字段
    if (!items || !items.length || !address || !totalAmount) {
      return error(res, '缺少必填字段', 400);
    }
    
    // 验证地址信息
    if (!address.name || !address.phone || !address.province || 
        !address.city || !address.district || !address.detail) {
      return error(res, '地址信息不完整', 400);
    }
    
    // 验证商品列表
    for (const item of items) {
      if (!item.goodsId || !item.goodsName || !item.price || !item.quantity) {
        return error(res, '商品信息不完整', 400);
      }
    }
    
    const orderData = {
      userId,
      items,
      address,
      totalAmount: parseFloat(totalAmount),
      remark: remark || '',
      paymentMethod: paymentMethod || 'wechat',
      deliveryFee: parseFloat(deliveryFee || 0)
    };
    
    const order = await OrderModel.create(orderData);
    
    return success(res, order, '创建订单成功');
  } catch (err) {
    logger.error(`创建订单失败: ${err.message}`);
    
    // 处理库存不足的错误
    if (err.message.includes('stock')) {
      return error(res, '商品库存不足', 400);
    }
    
    return error(res, '创建订单失败', 500);
  }
};

/**
 * 获取订单列表
 */
const getOrderList = async (req, res) => {
  try {
    const userId = req.user.id;
    const {
      status = -1,
      page = 1,
      limit = 10,
      keyword = '',
      timeRange = '',
      amountRange = ''
    } = req.query;

    const result = await OrderModel.list({
      userId,
      status: parseInt(status),
      page: parseInt(page),
      limit: parseInt(limit),
      keyword,
      timeRange,
      amountRange
    });

    return success(res, result, '获取订单列表成功');
  } catch (err) {
    logger.error(`获取订单列表失败: ${err.message}`);
    return error(res, '获取订单列表失败', 500);
  }
};

/**
 * 获取订单详情
 */
const getOrderDetail = async (req, res) => {
  try {
    const userId = req.user.id;
    const { orderNo } = req.params;
    
    const order = await OrderModel.getByOrderNo(orderNo, userId);
    
    if (!order) {
      return error(res, '订单不存在', 404);
    }
    
    return success(res, order, '获取订单详情成功');
  } catch (err) {
    logger.error(`获取订单详情失败: ${err.message}`);
    return error(res, '获取订单详情失败', 500);
  }
};

/**
 * 取消订单
 */
const cancelOrder = async (req, res) => {
  try {
    const userId = req.user.id;
    const { orderNo } = req.params;
    
    const result = await OrderModel.cancel(orderNo, userId);
    
    return success(res, null, '取消订单成功');
  } catch (err) {
    logger.error(`取消订单失败: ${err.message}`);
    
    // 处理特定错误
    if (err.message.includes('状态不允许')) {
      return error(res, '订单状态不允许取消', 400);
    } else if (err.message.includes('不存在')) {
      return error(res, '订单不存在', 404);
    }
    
    return error(res, '取消订单失败', 500);
  }
};

/**
 * 确认收货
 */
const confirmReceipt = async (req, res) => {
  try {
    const userId = req.user.id;
    const { orderNo } = req.params;
    
    // 检查订单是否存在且属于该用户
    const order = await OrderModel.getByOrderNo(orderNo, userId);
    
    if (!order) {
      return error(res, '订单不存在', 404);
    }
    
    // 检查订单状态是否为待收货(2)
    if (order.status !== 2) {
      return error(res, '订单状态不允许确认收货', 400);
    }
    
    // 更新订单状态为已完成(3)
    await OrderModel.updateStatus(orderNo, 3);
    
    return success(res, null, '确认收货成功');
  } catch (err) {
    logger.error(`确认收货失败: ${err.message}`);
    return error(res, '确认收货失败', 500);
  }
};

/**
 * 获取订单状态统计
 */
const getOrderStats = async (req, res) => {
  try {
    const userId = req.user.id;
    
    // 查询各状态订单数量
    const sql = `
      SELECT status, COUNT(*) as count
      FROM orders
      WHERE user_id = ?
      GROUP BY status
    `;
    
    // 实现这个功能需要直接使用数据库查询，这里返回模拟数据
    const stats = {
      total: 0,
      waitPay: 0,  // 待支付(0)
      waitDeliver: 0,  // 待发货(1)
      waitReceive: 0,  // 待收货(2)
      completed: 0,  // 已完成(3)
      cancelled: 0   // 已取消(4)
    };
    
    return success(res, stats, '获取订单统计成功');
  } catch (err) {
    logger.error(`获取订单统计失败: ${err.message}`);
    return error(res, '获取订单统计失败', 500);
  }
};

/**
 * 更新订单状态（管理端）
 */
const updateOrderStatus = async (req, res) => {
  try {
    const { orderNo } = req.params;
    const { status, logisticsNo, logisticsCompany } = req.body;
    
    // 验证状态值
    if (![0, 1, 2, 3, 4].includes(status)) {
      return error(res, '无效的状态值', 400);
    }
    
    // 发货时需要物流信息
    if (status === 2 && (!logisticsNo || !logisticsCompany)) {
      return error(res, '发货时需要提供物流信息', 400);
    }
    
    const extraData = {};
    if (logisticsNo) extraData.logisticsNo = logisticsNo;
    if (logisticsCompany) extraData.logisticsCompany = logisticsCompany;
    
    const result = await OrderModel.updateStatus(orderNo, status, extraData);
    
    if (!result) {
      return error(res, '订单不存在', 404);
    }
    
    return success(res, null, '更新订单状态成功');
  } catch (err) {
    logger.error(`更新订单状态失败: ${err.message}`);
    return error(res, '更新订单状态失败', 500);
  }
};

module.exports = {
  createOrder,
  getOrderList,
  getOrderDetail,
  cancelOrder,
  confirmReceipt,
  getOrderStats,
  updateOrderStatus
}; 