const db = require('../database');
const logger = require('../utils/logger');

/**
 * 团队模型
 */
class TeamModel {
  /**
   * 创建团长申请
   * @param {object} data 申请数据
   * @returns {Promise<object>} 申请结果
   */
  static async createLeaderApplication(data) {
    try {
      const { userId, name, phone, idCard, idCardFront, idCardBack, reason } = data;
      
      const sql = `
        INSERT INTO leader_applications (
          user_id, name, phone, id_card, id_card_front, id_card_back, 
          reason, status, created_at, updated_at
        )
        VALUES (?, ?, ?, ?, ?, ?, ?, 0, NOW(), NOW())
      `;
      
      const result = await db.query(sql, [
        userId, name, phone, idCard, idCardFront, idCardBack, reason
      ]);
      
      if (result.insertId) {
        return { 
          id: result.insertId,
          ...data,
          status: 0,  // 待审核
          createdAt: new Date()
        };
      } else {
        throw new Error('创建团长申请失败');
      }
    } catch (error) {
      logger.error(`创建团长申请失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 获取用户的团长申请记录
   * @param {number} userId 用户ID
   * @returns {Promise<object|null>} 申请记录
   */
  static async getLeaderApplication(userId) {
    try {
      const sql = `
        SELECT id, user_id, name, phone, id_card, id_card_front, id_card_back,
               reason, status, remark, created_at, updated_at
        FROM leader_applications
        WHERE user_id = ?
        ORDER BY created_at DESC
        LIMIT 1
      `;
      
      const result = await db.query(sql, [userId]);
      
      return result.length > 0 ? result[0] : null;
    } catch (error) {
      logger.error(`获取用户团长申请记录失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 获取团队成员列表
   * @param {number} leaderId 团长ID
   * @param {object} params 查询参数
   * @returns {Promise<object>} 团队成员列表和分页信息
   */
  static async getTeamMembers({ leaderId, level, page = 1, limit = 10 }) {
    try {
      let whereClause = 'WHERE team_leader_id = ?';
      const params = [leaderId];
      
      if (level !== undefined) {
        whereClause += ' AND level = ?';
        params.push(level);
      }
      
      const offset = (page - 1) * limit;
      
      const countSql = `
        SELECT COUNT(*) as total
        FROM team_members
        ${whereClause}
      `;
      
      const listSql = `
        SELECT tm.id, tm.user_id, tm.team_leader_id, tm.level, tm.created_at,
               u.nickname, u.avatar, u.phone
        FROM team_members tm
        LEFT JOIN users u ON tm.user_id = u.id
        ${whereClause}
        ORDER BY tm.created_at DESC
        LIMIT ?, ?
      `;
      
      const [countResult, membersResult] = await Promise.all([
        db.query(countSql, params),
        db.query(listSql, [...params, offset, parseInt(limit)])
      ]);
      
      const total = countResult[0].total;
      
      return {
        list: membersResult,
        pagination: {
          total,
          page: parseInt(page),
          limit: parseInt(limit),
          pages: Math.ceil(total / limit)
        }
      };
    } catch (error) {
      logger.error(`获取团队成员列表失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 添加团队成员
   * @param {object} data 成员数据
   * @returns {Promise<object>} 添加结果
   */
  static async addTeamMember(data) {
    try {
      const { userId, leaderUserId, inviteCode, level = 1 } = data;
      
      // 检查是否已经是团队成员
      const checkSql = 'SELECT id FROM team_members WHERE user_id = ?';
      const checkResult = await db.query(checkSql, [userId]);
      
      if (checkResult.length > 0) {
        throw new Error('用户已经是团队成员');
      }
      
      // 获取团长ID
      let leaderId = leaderUserId;
      
      // 如果通过邀请码，需要查找团长ID
      if (inviteCode && !leaderId) {
        const leaderSql = 'SELECT id FROM users WHERE invite_code = ? AND is_leader = 1';
        const leaderResult = await db.query(leaderSql, [inviteCode]);
        
        if (leaderResult.length === 0) {
          throw new Error('无效的邀请码');
        }
        
        leaderId = leaderResult[0].id;
      }
      
      // 添加团队成员
      const insertSql = `
        INSERT INTO team_members (
          user_id, team_leader_id, level, created_at, updated_at
        )
        VALUES (?, ?, ?, NOW(), NOW())
      `;
      
      const result = await db.query(insertSql, [userId, leaderId, level]);
      
      if (result.insertId) {
        return { 
          id: result.insertId,
          userId,
          teamLeaderId: leaderId,
          level,
          createdAt: new Date()
        };
      } else {
        throw new Error('添加团队成员失败');
      }
    } catch (error) {
      logger.error(`添加团队成员失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 获取用户的团队信息
   * @param {number} userId 用户ID
   * @returns {Promise<object|null>} 团队信息
   */
  static async getUserTeamInfo(userId) {
    try {
      // 检查用户是否为团长
      const leaderSql = 'SELECT is_leader FROM users WHERE id = ?';
      const leaderResult = await db.query(leaderSql, [userId]);
      
      if (leaderResult.length === 0) {
        return null;
      }
      
      const isLeader = Boolean(leaderResult[0].is_leader);
      
      // 如果是团长，获取团队总人数和各级别人数
      if (isLeader) {
        const memberStatsSql = `
          SELECT 
            COUNT(*) as total,
            SUM(CASE WHEN level = 1 THEN 1 ELSE 0 END) as level1,
            SUM(CASE WHEN level = 2 THEN 1 ELSE 0 END) as level2,
            SUM(CASE WHEN level = 3 THEN 1 ELSE 0 END) as level3
          FROM team_members
          WHERE team_leader_id = ?
        `;
        
        const statsResult = await db.query(memberStatsSql, [userId]);
        
        // 获取团队总收益
        const profitSql = `
          SELECT SUM(amount) as total_profit
          FROM profits
          WHERE user_id = ? AND source_type = 'team'
        `;
        
        const profitResult = await db.query(profitSql, [userId]);
        
        return {
          isLeader: true,
          teamSize: statsResult[0].total || 0,
          level1Members: statsResult[0].level1 || 0,
          level2Members: statsResult[0].level2 || 0,
          level3Members: statsResult[0].level3 || 0,
          totalProfit: profitResult[0].total_profit || 0
        };
      } 
      // 如果不是团长，获取所属团队信息
      else {
        const memberSql = `
          SELECT tm.team_leader_id, tm.level, u.nickname, u.avatar
          FROM team_members tm
          LEFT JOIN users u ON tm.team_leader_id = u.id
          WHERE tm.user_id = ?
        `;
        
        const memberResult = await db.query(memberSql, [userId]);
        
        if (memberResult.length === 0) {
          return {
            isLeader: false,
            hasTeam: false
          };
        }
        
        return {
          isLeader: false,
          hasTeam: true,
          leaderId: memberResult[0].team_leader_id,
          leaderName: memberResult[0].nickname,
          leaderAvatar: memberResult[0].avatar,
          level: memberResult[0].level
        };
      }
    } catch (error) {
      logger.error(`获取用户团队信息失败: ${error.message}`);
      throw error;
    }
  }
  
  /**
   * 审核团长申请
   * @param {number} applicationId 申请ID
   * @param {object} data 审核数据
   * @returns {Promise<boolean>} 审核是否成功
   */
  static async auditLeaderApplication(applicationId, data) {
    try {
      return await db.transaction(async (connection) => {
        const { status, remark } = data;
        
        // 查询申请信息
        const querySql = 'SELECT user_id FROM leader_applications WHERE id = ?';
        const applicationResult = await connection.execute(querySql, [applicationId]);
        
        if (applicationResult[0].length === 0) {
          throw new Error('申请不存在');
        }
        
        const userId = applicationResult[0][0].user_id;
        
        // 更新申请状态
        const updateSql = `
          UPDATE leader_applications
          SET status = ?, remark = ?, updated_at = NOW()
          WHERE id = ?
        `;
        
        await connection.execute(updateSql, [status, remark || null, applicationId]);
        
        // 如果审核通过，将用户设置为团长
        if (status === 1) {
          const userSql = 'UPDATE users SET is_leader = 1, updated_at = NOW() WHERE id = ?';
          await connection.execute(userSql, [userId]);
        }
        
        return true;
      });
    } catch (error) {
      logger.error(`审核团长申请失败: ${error.message}`);
      throw error;
    }
  }
}

module.exports = TeamModel; 