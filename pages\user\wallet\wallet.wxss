/* pages/user/wallet/wallet.wxss */
.wallet-container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 账户余额区域 */
.balance-section {
  background-color: #07c160;
  color: #fff;
  border-radius: 16rpx;
  padding: 40rpx 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(7, 193, 96, 0.2);
  text-align: center;
}

.balance-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.balance-title {
  font-size: 28rpx;
  flex: 1;
  text-align: center;
}

.refresh-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  transition: all 0.3s;
}

.refresh-btn:active {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(0.95);
}

.refresh-icon {
  font-size: 32rpx;
}

.balance-amount {
  font-size: 60rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.balance-subtitle {
  font-size: 24rpx;
  opacity: 0.8;
}

/* 操作按钮区域 */
.action-buttons {
  display: flex;
  justify-content: space-around;
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx 0;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.action-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 33.33%;
}

.action-icon {
  font-size: 40rpx;
  margin-bottom: 10rpx;
}

.action-text {
  font-size: 28rpx;
  color: #333;
}

/* 收益来源区域 */
.income-section {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  border-bottom: 1rpx solid #eee;
  padding-bottom: 15rpx;
}

.income-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  padding: 15rpx 0;
  font-size: 28rpx;
  color: #666;
  border-bottom: 1rpx solid #f5f5f5;
}

.income-item:last-child {
  border-bottom: none;
}

.income-left {
  display: flex;
  align-items: center;
}

.income-icon {
  font-size: 32rpx;
  margin-right: 16rpx;
}

.income-label {
  font-size: 28rpx;
  color: #333;
}

.income-value {
  color: #07c160;
  font-weight: 500;
  font-size: 28rpx;
}

/* 收支明细区域 */
.transactions-section {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.transaction-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #eee;
}

.transaction-info {
  flex: 1;
}

.transaction-title {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 8rpx;
}

.transaction-time {
  font-size: 24rpx;
  color: #999;
}

.transaction-amount {
  font-size: 32rpx;
  font-weight: 500;
}

.transaction-amount.income {
  color: #07c160;
}

.transaction-amount.expense {
  color: #ff6b6b;
}

.view-more {
  text-align: center;
  color: #07c160;
  font-size: 28rpx;
  padding: 20rpx 0 0;
}

.empty-transactions {
  text-align: center;
  padding: 30rpx 0;
  color: #999;
  font-size: 28rpx;
}

/* 广告容器 */
.ad-container {
  margin: 20rpx 0;
  border-radius: 16rpx;
  overflow: hidden;
}

/* 加载状态 */
.loading-container {
  text-align: center;
  padding: 30rpx 0;
}

.loading-text {
  color: #999;
  font-size: 28rpx;
} 