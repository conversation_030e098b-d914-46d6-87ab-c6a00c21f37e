// pages/user/withdraw-records/withdraw-records.js
// 提现记录页面

const request = require('../../../utils/request.js')

Page({
  data: {
    records: [],
    stats: {},
    loading: false,
    refreshing: false,
    hasMore: true,
    page: 1,
    limit: 10,
    filterStatus: '', // 筛选状态
    showStats: true,
    showFilter: true
  },

  onLoad(options) {
    console.log('📋 提现记录页面加载')
    this.loadWithdrawRecords()
    this.loadWithdrawStats()
  },

  onShow() {
    // 页面显示时刷新数据
    this.refreshData()
  },

  onPullDownRefresh() {
    console.log('📋 下拉刷新提现记录')
    this.refreshData()
  },

  onReachBottom() {
    console.log('📋 上拉加载更多提现记录')
    if (this.data.hasMore && !this.data.loading) {
      this.loadMoreRecords()
    }
  },

  // 刷新数据
  refreshData() {
    this.setData({
      refreshing: true,
      page: 1,
      hasMore: true,
      records: []
    })
    
    Promise.all([
      this.loadWithdrawRecords(),
      this.loadWithdrawStats()
    ]).finally(() => {
      this.setData({ refreshing: false })
      wx.stopPullDownRefresh()
    })
  },

  // 加载提现记录
  async loadWithdrawRecords() {
    if (this.data.loading) return
    
    this.setData({ loading: true })
    
    try {
      console.log('📋 加载提现记录，页码:', this.data.page)
      
      const params = {
        page: this.data.page,
        limit: this.data.limit
      }
      
      // 添加状态筛选
      if (this.data.filterStatus !== '') {
        params.status = this.data.filterStatus
      }
      
      const result = await request.get('/api/v1/client/withdraw/records', params)
      
      if (result.success) {
        const newRecords = result.data.list || []
        
        // 格式化记录数据
        const formattedRecords = newRecords.map(record => ({
          ...record,
          apply_time: this.formatTime(record.apply_time),
          complete_time: record.complete_time ? this.formatTime(record.complete_time) : null,
          amount: parseFloat(record.amount).toFixed(2),
          fee: parseFloat(record.fee).toFixed(2),
          actual_amount: parseFloat(record.actual_amount).toFixed(2)
        }))
        
        if (this.data.page === 1) {
          // 首页加载，替换数据
          this.setData({
            records: formattedRecords,
            hasMore: formattedRecords.length >= this.data.limit
          })
        } else {
          // 加载更多，追加数据
          this.setData({
            records: [...this.data.records, ...formattedRecords],
            hasMore: formattedRecords.length >= this.data.limit
          })
        }
        
        console.log('📋 提现记录加载成功:', formattedRecords.length, '条')
      } else {
        console.error('📋 加载提现记录失败:', result.message)
        wx.showToast({
          title: result.message || '加载失败',
          icon: 'none'
        })
      }
    } catch (error) {
      console.error('📋 加载提现记录异常:', error)
      wx.showToast({
        title: '网络错误，请重试',
        icon: 'none'
      })
    } finally {
      this.setData({ loading: false })
    }
  },

  // 加载更多记录
  loadMoreRecords() {
    this.setData({
      page: this.data.page + 1
    })
    this.loadWithdrawRecords()
  },

  // 加载提现统计
  async loadWithdrawStats() {
    try {
      console.log('📊 加载提现统计')
      
      const result = await request.get('/api/v1/client/withdraw/stats')
      
      if (result.success) {
        this.setData({
          stats: {
            totalAmount: parseFloat(result.data.total_amount || 0).toFixed(2),
            totalCount: result.data.total_count || 0,
            successCount: result.data.success_count || 0
          }
        })
        console.log('📊 提现统计加载成功:', this.data.stats)
      }
    } catch (error) {
      console.error('📊 加载提现统计失败:', error)
    }
  },

  // 筛选状态改变
  onFilterChange(e) {
    const status = e.currentTarget.dataset.status
    console.log('🔍 筛选状态改变:', status)
    
    this.setData({
      filterStatus: status,
      page: 1,
      records: [],
      hasMore: true
    })
    
    this.loadWithdrawRecords()
  },

  // 点击记录项
  onRecordTap(e) {
    const record = e.currentTarget.dataset.record
    console.log('📋 点击提现记录:', record)
    
    // 显示记录详情
    this.showRecordDetail(record)
  },

  // 显示记录详情
  showRecordDetail(record) {
    const statusText = this.getStatusText(record.status)
    const withdrawType = record.withdraw_type === 'wechat' ? '微信' : '银行卡'
    
    let content = `提现方式：${withdrawType}\n`
    content += `提现金额：¥${record.amount}\n`
    content += `手续费：¥${record.fee}\n`
    content += `实际到账：¥${record.actual_amount}\n`
    content += `申请时间：${record.apply_time}\n`
    content += `状态：${statusText}\n`
    
    if (record.complete_time) {
      content += `完成时间：${record.complete_time}\n`
    }
    
    if (record.failure_reason) {
      content += `失败原因：${record.failure_reason}\n`
    }
    
    if (record.withdraw_no) {
      content += `提现单号：${record.withdraw_no}`
    }
    
    wx.showModal({
      title: '提现详情',
      content: content,
      showCancel: false,
      confirmText: '知道了'
    })
  },

  // 获取状态文本
  getStatusText(status) {
    const statusMap = {
      0: '待审核',
      1: '审核通过',
      2: '审核拒绝',
      3: '处理中',
      4: '已完成',
      5: '已取消'
    }
    return statusMap[status] || '未知状态'
  },

  // 格式化时间
  formatTime(timeString) {
    if (!timeString) return ''
    
    const date = new Date(timeString)
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const hour = String(date.getHours()).padStart(2, '0')
    const minute = String(date.getMinutes()).padStart(2, '0')
    
    return `${year}-${month}-${day} ${hour}:${minute}`
  },

  // 分享页面
  onShareAppMessage() {
    return {
      title: 'WiFi共享商城 - 提现记录',
      path: '/pages/user/withdraw-records/withdraw-records'
    }
  }
})
