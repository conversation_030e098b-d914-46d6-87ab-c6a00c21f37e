const mysql = require('mysql2/promise');

async function checkProfitLogStructure() {
  let connection;
  
  try {
    // 连接数据库
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: 'wo587129955',
      database: 'mall'
    });
    
    console.log('🔍 检查profit_log表结构...\n');
    
    // 1. 查看表结构
    console.log('1️⃣ profit_log表字段结构:');
    const [structure] = await connection.execute('DESCRIBE profit_log');
    console.table(structure);
    
    // 2. 查看表的创建语句
    console.log('\n2️⃣ profit_log表创建语句:');
    const [createTable] = await connection.execute('SHOW CREATE TABLE profit_log');
    console.log(createTable[0]['Create Table']);
    
    // 3. 检查是否有数据
    console.log('\n3️⃣ profit_log表数据统计:');
    const [count] = await connection.execute('SELECT COUNT(*) as total FROM profit_log');
    console.log(`总记录数: ${count[0].total}`);
    
    // 4. 如果有数据，显示前几条
    if (count[0].total > 0) {
      console.log('\n4️⃣ 最近的记录:');
      const [records] = await connection.execute('SELECT * FROM profit_log ORDER BY created_at DESC LIMIT 3');
      console.table(records);
    } else {
      console.log('\n4️⃣ 表中暂无数据');
    }
    
    // 5. 测试插入一条示例数据（不实际插入）
    console.log('\n5️⃣ 测试数据插入语句:');
    const testInsertSQL = `
      INSERT INTO profit_log 
      (user_id, team_id, amount, source_type, source_id, order_no, role, rate, total_amount, status, remark) 
      VALUES 
      (1, 1, 10.50, 1, 1, 'TEST001', 'user', 20, 52.50, 0, '测试分润记录')
    `;
    console.log(testInsertSQL);
    
    // 6. 检查相关的收入API可能使用的字段
    console.log('\n6️⃣ 收入API相关字段检查:');
    const requiredFields = ['user_id', 'amount', 'source_type', 'role', 'status', 'created_at'];
    const existingFields = structure.map(field => field.Field);
    
    requiredFields.forEach(field => {
      const exists = existingFields.includes(field);
      console.log(`${field}: ${exists ? '✅ 存在' : '❌ 缺失'}`);
    });
    
    // 7. 检查用户表的balance字段
    console.log('\n7️⃣ 用户表余额字段:');
    const [userBalance] = await connection.execute('SELECT id, balance FROM user');
    console.table(userBalance);
    
    // 8. 检查团队表的收入字段
    console.log('\n8️⃣ 团队表收入字段:');
    const [teamIncome] = await connection.execute('SELECT id, name, total_profit, total_income, month_income, today_income FROM team');
    console.table(teamIncome);
    
  } catch (error) {
    console.error('❌ 检查失败:', error.message);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// 运行检查
checkProfitLogStructure();
