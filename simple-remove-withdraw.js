/**
 * 简化的移除提现页面脚本
 */

const fs = require('fs');
const path = require('path');

console.log('🚀 开始移除多余的提现页面...');

// 前端项目路径
const frontendPath = '../wifi-share-miniapp';

// 检查前端项目是否存在
if (!fs.existsSync(frontendPath)) {
  console.log('❌ 前端项目目录不存在:', frontendPath);
  process.exit(1);
}

console.log('✅ 找到前端项目目录:', frontendPath);

// 需要移除的文件列表
const filesToRemove = [
  'pages/user/withdraw/withdraw.wxml',
  'pages/user/withdraw/withdraw.js',
  'pages/user/withdraw/withdraw.wxss',
  'pages/user/withdraw/withdraw.json'
];

// 移除文件
let removedCount = 0;
let notFoundCount = 0;

console.log('\n📁 移除提现页面文件:');
filesToRemove.forEach(file => {
  const filePath = path.join(frontendPath, file);
  
  if (fs.existsSync(filePath)) {
    try {
      fs.unlinkSync(filePath);
      console.log(`  ✅ 已移除: ${file}`);
      removedCount++;
    } catch (error) {
      console.log(`  ❌ 移除失败: ${file} - ${error.message}`);
    }
  } else {
    console.log(`  ⚠️  文件不存在: ${file}`);
    notFoundCount++;
  }
});

// 尝试移除空目录
const withdrawDir = path.join(frontendPath, 'pages/user/withdraw');
if (fs.existsSync(withdrawDir)) {
  try {
    const dirContents = fs.readdirSync(withdrawDir);
    if (dirContents.length === 0) {
      fs.rmdirSync(withdrawDir);
      console.log('  ✅ 已移除空目录: pages/user/withdraw');
    } else {
      console.log('  ⚠️  目录不为空，未移除: pages/user/withdraw');
    }
  } catch (error) {
    console.log(`  ❌ 移除目录失败: ${error.message}`);
  }
}

// 更新app.json
console.log('\n📝 更新app.json:');
const appJsonPath = path.join(frontendPath, 'app.json');

if (fs.existsSync(appJsonPath)) {
  try {
    const appJsonContent = fs.readFileSync(appJsonPath, 'utf8');
    const appJson = JSON.parse(appJsonContent);
    
    if (appJson.pages) {
      const originalLength = appJson.pages.length;
      appJson.pages = appJson.pages.filter(page => page !== 'pages/user/withdraw/withdraw');
      
      if (appJson.pages.length < originalLength) {
        fs.writeFileSync(appJsonPath, JSON.stringify(appJson, null, 2), 'utf8');
        console.log('  ✅ 已从app.json移除提现页面注册');
      } else {
        console.log('  ⚠️  app.json中未找到提现页面注册');
      }
    }
  } catch (error) {
    console.log(`  ❌ 更新app.json失败: ${error.message}`);
  }
} else {
  console.log('  ❌ app.json文件不存在');
}

// 更新钱包页面
console.log('\n💰 更新钱包页面:');
const walletJsPath = path.join(frontendPath, 'pages/user/wallet/wallet.js');

if (fs.existsSync(walletJsPath)) {
  try {
    let walletContent = fs.readFileSync(walletJsPath, 'utf8');
    
    // 检查是否包含提现跳转代码
    if (walletContent.includes('pages/user/withdraw/withdraw')) {
      // 替换提现方法
      const newWithdrawMethod = `  onWithdraw: function () {
    console.log('🔧 提现按钮被点击')
    wx.showToast({
      title: '提现功能开发中',
      icon: 'none'
    })
  },`;
      
      // 使用正则表达式替换提现方法
      walletContent = walletContent.replace(
        /onWithdraw:\s*function\s*\([^)]*\)\s*\{[^}]*\}/g,
        newWithdrawMethod.trim()
      );
      
      fs.writeFileSync(walletJsPath, walletContent, 'utf8');
      console.log('  ✅ 已更新钱包页面提现方法');
    } else {
      console.log('  ⚠️  钱包页面中未找到提现跳转代码');
    }
  } catch (error) {
    console.log(`  ❌ 更新钱包页面失败: ${error.message}`);
  }
} else {
  console.log('  ❌ 钱包页面JS文件不存在');
}

console.log('\n🎉 移除操作完成！');
console.log(`📊 统计: 移除${removedCount}个文件，${notFoundCount}个文件不存在`);
console.log('\n💡 现在钱包页面的提现按钮将显示"功能开发中"提示');
console.log('✅ 多余的提现页面已成功移除！');
