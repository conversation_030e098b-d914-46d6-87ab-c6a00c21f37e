/* pages/mall/home/<USER>/
page {
  background: #f5f5f5;
}

.mall-container {
  padding-bottom: 100rpx;
}

/* 自定义导航栏 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: #ffffff;
  border-bottom: 1rpx solid #e9ecef;
  padding-top: var(--status-bar-height, 44rpx);
}

.navbar-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 88rpx;
  padding: 0 32rpx;
}

.navbar-left {
  flex: 1;
}

.navbar-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333333;
}

.navbar-right {
  display: flex;
  align-items: center;
}

/* 购物车相关样式已移除 */

/* 广告流量区域 */
.ad-traffic-section {
  width: 100%;
  min-height: 280rpx;
  position: relative;
  margin-bottom: 30rpx;
  margin-top: calc(var(--status-bar-height, 44rpx) + 88rpx + 20rpx);
  padding: 0;
}

/* 微信广告容器 */
.wechat-ad-container {
  width: 100%;
  min-height: 250rpx;
  height: auto;
  display: block;
  background-color: #f8f9fa;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
  margin: 0 20rpx;
  box-sizing: border-box;
}

/* 微信广告组件样式 */
.wechat-ad-container ad {
  width: 100% !important;
  height: auto !important;
  min-height: 200rpx !important;
  display: block !important;
}

/* 广告收益信息 */
.ad-revenue-info {
  text-align: center;
  padding: 10rpx 0;
  margin-top: 10rpx;
}

.revenue-text {
  font-size: 24rpx;
  color: #07c160;
  font-weight: bold;
  background: linear-gradient(90deg, #07c160, #00d4aa);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* 中间广告容器 */
.middle-ad-container {
  width: 100%;
  margin: 30rpx 0;
  padding: 0 20rpx;
  box-sizing: border-box;
}

.middle-ad-container .wechat-ad-container {
  min-height: 180rpx;
  height: auto;
  display: block;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  margin: 0;
}

/* 中间广告组件样式 */
.middle-ad-container ad {
  width: 100% !important;
  height: auto !important;
  min-height: 150rpx !important;
  display: block !important;
}

/* 广告降级显示 */
.ad-fallback {
  width: 100%;
  height: 300rpx;
}

.traffic-banner,
.fallback-banner {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
}

.traffic-image,
.fallback-image {
  width: 100%;
  height: 300rpx;
  display: block;
  object-fit: cover;
}

.traffic-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: 20rpx;
  background: linear-gradient(transparent, rgba(7, 193, 96, 0.8));
  color: #ffffff;
  text-align: center;
}

.traffic-title {
  font-size: 32rpx;
  font-weight: bold;
  display: block;
  margin-bottom: 8rpx;
  text-shadow: 0 1rpx 3rpx rgba(0,0,0,0.3);
}

.traffic-subtitle {
  font-size: 24rpx;
  opacity: 0.95;
  text-shadow: 0 1rpx 2rpx rgba(0,0,0,0.3);
}

/* 搜索区域 */
.search-section {
  padding: 20rpx 30rpx;
}

.search-container {
  background-color: #ffffff;
  border-radius: 8rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.search-box {
  display: flex;
  align-items: center;
  height: 70rpx;
  padding: 0 20rpx;
}

.search-icon {
  margin-right: 10rpx;
  color: #999999;
}

.icon-search {
  font-size: 28rpx;
}

.search-placeholder {
  color: #999999;
  font-size: 28rpx;
  flex: 1;
}

.search-divider {
  width: 2rpx;
  height: 32rpx;
  background-color: #e0e0e0;
  margin: 0 10rpx;
}

/* 商品分类入口 */
.category-section {
  background-color: #ffffff;
  padding: 20rpx 20rpx 10rpx;
  margin-bottom: 20rpx;
}

.category-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  padding: 0 10rpx;
}

.category-title text {
  font-size: 30rpx;
  font-weight: 500;
}

.more-categories {
  display: flex;
  align-items: center;
}

.more-categories text {
  font-size: 26rpx;
  color: #666;
}

.more-icon {
  margin-left: 6rpx;
}

.category-scroll {
  white-space: nowrap;
  display: flex;
  padding: 10rpx 0;
}

.category-item {
  display: inline-flex;
  flex-direction: column;
  align-items: center;
  width: 140rpx;
  margin: 0 10rpx;
}

.category-icon-wrapper {
  width: 100rpx;
  height: 100rpx;
  background-color: #f5f8ff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10rpx;
}

.category-icon {
  width: 60rpx;
  height: 60rpx;
}

.category-name {
  font-size: 24rpx;
  color: #333;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 140rpx;
}

/* 推荐商品标题区域 */
.recommend-section {
  margin: 20rpx 0;
}

.recommend-header {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 30rpx;
}

.recommend-line {
  flex: 1;
  height: 1px;
  background-color: #e0e0e0;
}

.recommend-title {
  padding: 0 20rpx;
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
}

/* 商品列表区域 */
.goods-section {
  padding: 0 20rpx;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40rpx 0;
}

.loading-spinner {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid #e0e0e0;
  border-top-color: #07c160;
  border-radius: 50%;
  animation: spin 0.8s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 24rpx;
  color: #999999;
}

/* 商品网格 */
.goods-grid {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -10rpx;
}

.goods-item {
  width: 50%;
  padding: 0 10rpx;
  margin-bottom: 20rpx;
  box-sizing: border-box;
}

.goods-card {
  background-color: #ffffff;
  border-radius: 8rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.goods-image-container {
  width: 100%;
  height: 345rpx;
  position: relative;
  overflow: hidden;
}

.goods-image {
  width: 100%;
  height: 100%;
  display: block;
  object-fit: cover;
}

.goods-tags {
  position: absolute;
  top: 10rpx;
  left: 10rpx;
  display: flex;
  flex-wrap: wrap;
}

.goods-tag {
  font-size: 20rpx;
  color: #ffffff;
  padding: 4rpx 10rpx;
  border-radius: 4rpx;
  margin-right: 10rpx;
  margin-bottom: 10rpx;
}

.goods-info {
  padding: 16rpx;
}

.goods-name {
  font-size: 28rpx;
  color: #333333;
  margin-bottom: 10rpx;
  line-height: 1.3;
  height: 74rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.goods-price-row {
  display: flex;
  align-items: baseline;
  margin-bottom: 10rpx;
}

.goods-price {
  font-size: 32rpx;
  color: #ff4d4f;
  font-weight: bold;
}

.goods-original-price {
  font-size: 24rpx;
  color: #999999;
  text-decoration: line-through;
  margin-left: 10rpx;
}

.goods-bottom-row {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.goods-stats {
  flex: 1;
}

.goods-sales {
  font-size: 22rpx;
  color: #999999;
}

/* 购物车按钮样式已移除 */

/* 加载更多按钮 */
.load-more-section {
  padding: 30rpx 0;
  text-align: center;
}

.load-more-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 10rpx 40rpx;
  background-color: #f5f5f5;
  border-radius: 30rpx;
}

.load-more-btn.loading {
  background-color: #f0f0f0;
}

.load-more-spinner {
  width: 28rpx;
  height: 28rpx;
  border: 3rpx solid #e0e0e0;
  border-top-color: #07c160;
  border-radius: 50%;
  animation: spin 0.8s linear infinite;
  margin-right: 10rpx;
}

.load-more-text {
  font-size: 24rpx;
  color: #666666;
}

/* 没有更多数据提示 */
.no-more-data {
  padding: 30rpx 0;
  text-align: center;
}

.no-more-text {
  font-size: 24rpx;
  color: #999999;
}

/* 空状态展示 */
.empty-goods {
  padding: 100rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.empty-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}

.empty-title {
  font-size: 32rpx;
  color: #333333;
  margin-bottom: 10rpx;
}

.empty-subtitle {
  font-size: 24rpx;
  color: #999999;
  margin-bottom: 30rpx;
}

.empty-refresh-btn {
  font-size: 26rpx;
  color: #07c160;
  border: 1rpx solid #07c160;
  background-color: #ffffff;
  border-radius: 30rpx;
  padding: 10rpx 30rpx;
}

/* 底部广告区域 */
.bottom-ad-section {
  margin-top: 30rpx;
  padding: 0 20rpx;
}

/* 底部微信广告容器 */
.wechat-bottom-ad-container {
  width: 100%;
  min-height: 250rpx;
  height: auto;
  display: block;
  background-color: #f8f9fa;
  border-radius: 12rpx;
  overflow: hidden;
  margin: 20rpx;
  box-sizing: border-box;
}

/* 底部广告组件样式 */
.wechat-bottom-ad-container ad {
  width: 100% !important;
  height: auto !important;
  min-height: 200rpx !important;
  display: block !important;
}

.ad-section-title {
  text-align: center;
  margin-bottom: 20rpx;
}

.ad-title-text {
  font-size: 28rpx;
  color: #999999;
  position: relative;
  padding: 0 20rpx;
}

.ad-title-text::before,
.ad-title-text::after {
  content: '';
  display: inline-block;
  width: 40rpx;
  height: 1px;
  background-color: #e0e0e0;
  vertical-align: middle;
  margin: 0 10rpx;
}

.bottom-ad-banner {
  width: 100%;
  height: 200rpx;
  position: relative;
  overflow: hidden;
  border-radius: 8rpx;
  margin-bottom: 20rpx;
}

.bottom-ad-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.bottom-ad-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30rpx;
  background: linear-gradient(90deg, rgba(0,0,0,0.6) 0%, rgba(0,0,0,0.3) 50%, rgba(0,0,0,0) 100%);
}

.bottom-ad-content {
  color: #ffffff;
}

.bottom-ad-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
  display: block;
}

.bottom-ad-subtitle {
  font-size: 24rpx;
  opacity: 0.9;
}

.bottom-ad-action {
  background-color: #ffffff;
  border-radius: 30rpx;
  padding: 10rpx 20rpx;
}

.bottom-ad-btn {
  font-size: 24rpx;
  color: #07c160;
}

/* 底部安全间距 */
.bottom-safe-area {
  height: 40rpx;
}

/* 搜索模态框 */
.search-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 999;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.search-modal-content {
  width: 100%;
  background-color: #ffffff;
  padding: 30rpx;
  box-sizing: border-box;
}

.search-input-container {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.search-input {
  flex: 1;
  height: 70rpx;
  background-color: #f5f5f5;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
}

.search-btn {
  margin-left: 20rpx;
  width: 120rpx;
  height: 70rpx;
  line-height: 70rpx;
  background-color: #07c160;
  color: #ffffff;
  font-size: 28rpx;
  border-radius: 8rpx;
  padding: 0;
}

/* 搜索历史 */
.search-history {
  margin-top: 20rpx;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.history-title {
  font-size: 28rpx;
  color: #333333;
}

.history-clear {
  font-size: 24rpx;
  color: #999999;
}

.history-tags {
  display: flex;
  flex-wrap: wrap;
}

.history-tag {
  font-size: 24rpx;
  color: #666666;
  background-color: #f5f5f5;
  padding: 10rpx 20rpx;
  border-radius: 30rpx;
  margin-right: 20rpx;
  margin-bottom: 20rpx;
} 