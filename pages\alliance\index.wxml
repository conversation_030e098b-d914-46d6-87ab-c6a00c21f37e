<!--pages/alliance/index.wxml-->
<view class="alliance-container">
  <!-- 页面标题 -->
  <view class="page-header">
    <view class="header-title">联盟团长入驻</view>
    <view class="header-desc">加入WiFi共享联盟，成为团长享受更多收益</view>
  </view>

  <!-- 已是团长 -->
  <block wx:if="{{isLeader}}">
    <!-- 标签页导航 -->
    <view class="tab-nav">
      <view class="tab-item {{activeTab === 'team' ? 'active' : ''}}" bindtap="onTabChange" data-tab="team">
        <text>团队管理</text>
      </view>
      <view class="tab-item {{activeTab === 'profit' ? 'active' : ''}}" bindtap="onTabChange" data-tab="profit">
        <text>收益管理</text>
      </view>
    </view>

    <!-- 团队管理标签页 -->
    <view class="tab-content" wx:if="{{activeTab === 'team'}}">
      <!-- 团队数据卡片 -->
      <view class="team-stats">
        <view class="stat-item">
          <view class="stat-value">{{teamInfo.memberCount}}</view>
          <view class="stat-label">团队成员</view>
        </view>
        <view class="stat-item">
          <view class="stat-value">{{teamInfo.wifiCount}}</view>
          <view class="stat-label">WiFi码数量</view>
        </view>
        <view class="stat-item">
          <view class="stat-value">{{teamInfo.totalProfit}}元</view>
          <view class="stat-label">累计收益</view>
        </view>
      </view>

      <!-- 团队成员列表 -->
      <view class="team-members">
        <view class="section-title">团队成员</view>
        
        <view class="member-list">
          <block wx:if="{{membersList.length > 0}}">
            <view class="member-item" wx:for="{{membersList}}" wx:key="id">
              <image class="member-avatar" src="{{item.avatar || '/assets/images/default-avatar.png'}}"></image>
              <view class="member-info">
                <view class="member-name">{{item.nickname || '用户' + item.id}}</view>
                <view class="member-date">加入时间: {{item.created_at}}</view>
              </view>
              <view class="member-contribution">
                <view class="contribution-value">{{item.wifi_count || 0}}</view>
                <view class="contribution-label">WiFi码</view>
              </view>
            </view>
            
            <!-- 加载更多 -->
            <view class="load-more" wx:if="{{hasMore}}" bindtap="loadMoreMembers">
              <text wx:if="{{!loading}}">加载更多</text>
              <text wx:else>加载中...</text>
            </view>
          </block>
          
          <view class="empty-tip" wx:else>
            <text wx:if="{{loading}}">加载中...</text>
            <text wx:else>暂无团队成员</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 收益管理标签页 -->
    <view class="tab-content" wx:if="{{activeTab === 'profit'}}">
      <!-- 收益数据卡片 -->
      <view class="profit-card">
        <view class="profit-header">
          <text>团队总收益</text>
          <text class="profit-amount">¥ {{teamInfo.totalProfit}}</text>
        </view>
        <view class="profit-actions">
          <view class="action-button" bindtap="onViewProfitDetails">查看明细</view>
        </view>
      </view>

      <!-- 分润规则 -->
      <view class="profit-rules">
        <view class="section-title">分润规则</view>
        <view class="rule-item">
          <text class="rule-label">平台分润比例:</text>
          <text class="rule-value">{{profitRules.platformRate}}%</text>
        </view>
        <view class="rule-item">
          <text class="rule-label">团长分润比例:</text>
          <text class="rule-value">{{profitRules.leaderRate}}%</text>
        </view>
        <view class="rule-item">
          <text class="rule-label">成员分润比例:</text>
          <text class="rule-value">{{profitRules.memberRate}}%</text>
        </view>
        
        <view class="rule-tips">
          <text>说明: 分润比例根据团队等级可能会有所调整，详情请联系客服</text>
        </view>
      </view>

      <!-- 收益提示 -->
      <view class="profit-tips">
        <view class="tip-title">
          <text class="tip-icon">💡</text>
          <text>提高收益小贴士</text>
        </view>
        <view class="tip-content">
          <text>1. 邀请更多成员加入团队</text>
          <text>2. 鼓励团队成员创建更多WiFi码</text>
          <text>3. 推广商城商品获取销售分润</text>
        </view>
      </view>
    </view>
  </block>

  <!-- 申请中状态 -->
  <block wx:elif="{{hasApplied}}">
    <view class="status-section">
      <view class="status-icon">📝</view>
      <view class="status-title">您已提交团长申请</view>
      <view class="status-desc">当前状态: {{getStatusText()}}</view>
      <view class="status-tip">我们会尽快审核您的申请，请耐心等待</view>
      <view class="status-button" bindtap="onCheckStatus">查看申请进度</view>
    </view>
  </block>

  <!-- 团长申请表单 -->
  <block wx:else>
    <view class="form-section">
      <view class="form-title">团长申请表</view>
      
      <view class="form-item">
        <view class="form-label">团队名称 <text class="required">*</text></view>
        <input class="form-input" placeholder="请输入团队名称" value="{{formData.name}}" bindinput="onInputChange" data-field="name" />
      </view>

      <view class="form-item">
        <view class="form-label">联系人 <text class="required">*</text></view>
        <input class="form-input" placeholder="请输入联系人姓名" value="{{formData.contact}}" bindinput="onInputChange" data-field="contact" />
      </view>

      <view class="form-item">
        <view class="form-label">联系电话 <text class="required">*</text></view>
        <input class="form-input" placeholder="请输入联系电话" type="number" value="{{formData.phone}}" bindinput="onInputChange" data-field="phone" />
      </view>

      <view class="form-item">
        <view class="form-label">电子邮箱</view>
        <input class="form-input" placeholder="请输入电子邮箱" type="text" value="{{formData.email}}" bindinput="onInputChange" data-field="email" />
      </view>

      <view class="form-item">
        <view class="form-label">所在区域 <text class="required">*</text></view>
        <picker class="form-picker" range="{{areaOptions}}" value="{{selectedAreaIndex}}" bindchange="onSelectArea">
          <view class="picker-content">
            <text wx:if="{{formData.area}}">{{formData.area}}</text>
            <text wx:else class="placeholder">请选择所在区域</text>
          </view>
        </picker>
      </view>

      <view class="form-item">
        <view class="form-label">团队简介</view>
        <textarea class="form-textarea" placeholder="请简要描述您的团队情况..." value="{{formData.description}}" bindinput="onInputChange" data-field="description" maxlength="200" />
      </view>

      <view class="form-tips">
        <text>提示：带 * 号的为必填项</text>
      </view>

      <view class="submit-button {{submitting ? 'disabled' : ''}}" bindtap="onSubmit">
        {{submitting ? '提交中...' : '提交申请'}}
      </view>
    </view>
  </block>

  <!-- 联盟优势 -->
  <view class="benefits-section">
    <view class="section-title">团长特权</view>
    <view class="benefits-list">
      <view class="benefit-item">
        <view class="benefit-icon team-icon">👑</view>
        <view class="benefit-content">
          <view class="benefit-title">团队管理</view>
          <view class="benefit-desc">组建自己的团队，获得团队业绩提成</view>
        </view>
      </view>
      <view class="benefit-item">
        <view class="benefit-icon profit-icon">💰</view>
        <view class="benefit-content">
          <view class="benefit-title">多重收益</view>
          <view class="benefit-desc">WiFi码、商品销售、广告分成多重收益</view>
        </view>
      </view>
      <view class="benefit-item">
        <view class="benefit-icon level-icon">🔝</view>
        <view class="benefit-content">
          <view class="benefit-title">等级特权</view>
          <view class="benefit-desc">团队规模越大，享受更高分润比例</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 广告位 -->
  <view class="ad-container">
    <ad unit-id="adunit-id" ad-type="banner" ad-theme="white" bindload="onAdLoad" binderror="onAdError"></ad>
  </view>
</view> 