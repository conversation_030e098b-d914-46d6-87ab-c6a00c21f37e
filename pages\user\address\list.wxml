<!--pages/user/address/list.wxml-->
<view class="address-container">
  <!-- 地址列表 -->
  <view class="address-list" wx:if="{{!isEmpty && !loading}}">
    <view class="address-item" wx:for="{{addressList}}" wx:key="id" bindtap="onSelectAddress" data-id="{{item.id}}">
      <view class="address-info">
        <view class="address-header">
          <view class="contact-info">
            <text class="name">{{item.name}}</text>
            <text class="phone">{{item.phone}}</text>
          </view>
          <view class="default-tag" wx:if="{{item.is_default}}">默认</view>
        </view>
        <view class="address-detail">{{item.province}}{{item.city}}{{item.district}}{{item.address}}</view>
      </view>
      <view class="address-actions">
        <view class="action-btn" catchtap="onSetDefault" data-id="{{item.id}}" wx:if="{{!item.is_default}}">
          <text class="action-icon">☆</text>
          <text class="action-text">设为默认</text>
        </view>
        <view class="action-btn" catchtap="onEditAddress" data-id="{{item.id}}">
          <text class="action-icon">✎</text>
          <text class="action-text">编辑</text>
        </view>
        <view class="action-btn" catchtap="onDeleteAddress" data-id="{{item.id}}">
          <text class="action-icon">🗑</text>
          <text class="action-text">删除</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{isEmpty && !loading}}">
    <image class="empty-icon" src="/assets/images/empty-address.png" mode="aspectFit"></image>
    <text class="empty-text">暂无收货地址</text>
    <text class="empty-tip">添加地址，轻松购物</text>
  </view>

  <!-- 加载中 -->
  <view class="loading-state" wx:if="{{loading}}">
    <view class="loading-icon"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 添加地址按钮 -->
  <view class="add-address-btn" bindtap="onAddAddress">
    <text class="add-icon">+</text>
    <text class="add-text">新增收货地址</text>
  </view>
</view> 