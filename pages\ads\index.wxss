/* pages/ads/index.wxss */
.ads-container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 收益统计 */
.stats-section {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  border-bottom: 1rpx solid #eee;
  padding-bottom: 15rpx;
}

.stats-content {
  display: flex;
  justify-content: space-between;
  margin-bottom: 30rpx;
}

.stats-item {
  flex: 1;
  text-align: center;
}

.stats-value {
  font-size: 36rpx;
  color: #07c160;
  font-weight: 500;
  margin-bottom: 10rpx;
}

.stats-label {
  font-size: 24rpx;
  color: #999;
}

.stats-chart {
  height: 200rpx;
  border-top: 1rpx solid #eee;
  padding-top: 20rpx;
}

.chart-placeholder {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
  font-size: 28rpx;
  background-color: #f9f9f9;
  border-radius: 8rpx;
}

/* 广告位列表 */
.ad-spaces-section {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.ad-spaces-list {
  
}

.ad-space-item {
  padding: 20rpx 0;
  border-bottom: 1rpx solid #eee;
}

.ad-space-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.ad-space-stats {
  display: flex;
  justify-content: space-between;
  font-size: 24rpx;
  color: #999;
}

.view-more {
  text-align: center;
  color: #07c160;
  font-size: 28rpx;
  padding: 20rpx 0 0;
}

.empty-ad-spaces {
  text-align: center;
  padding: 40rpx 0;
  color: #999;
  font-size: 28rpx;
}

/* 功能按钮 */
.function-buttons {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.function-button {
  width: 48%;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  background-color: #07c160;
  color: #fff;
  border-radius: 40rpx;
  font-size: 28rpx;
  box-shadow: 0 4rpx 12rpx rgba(7, 193, 96, 0.2);
}

/* 广告容器 */
.ad-container {
  border-radius: 16rpx;
  overflow: hidden;
  margin-bottom: 20rpx;
}

/* 加载状态 */
.loading-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999;
}

.loading-text {
  color: #999;
  font-size: 28rpx;
} 