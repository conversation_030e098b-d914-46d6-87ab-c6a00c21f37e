// pages/wifi/create/create.js
// WiFi码创建页面

const request = require('../../../utils/request.js')
const util = require('../../../utils/util.js')

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 表单数据
    formData: {
      title: '',        // WiFi标题
      ssid: '',         // WiFi名称
      password: '',     // WiFi密码
      merchantName: ''  // 商户名称
    },
    
    // 表单验证状态
    formErrors: {
      title: '',
      ssid: '',
      password: '',
      merchantName: ''
    },
    
    // 提交状态
    isSubmitting: false,
    
    // 广告数据
    adData: {
      title: '推广位招租',
      subtitle: '点击了解详情',
      image: '/assets/images/ad-placeholder.jpg'
    }
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    console.log('WiFi创建页面加载')
    this.checkLoginStatus()
  },

  /**
   * 检查登录状态
   */
  checkLoginStatus() {
    const app = getApp()
    if (!app.globalData.isLogin) {
      wx.showModal({
        title: '提示',
        content: '请先登录后再创建WiFi码',
        showCancel: false,
        success: () => {
          wx.switchTab({
            url: '/pages/index/index'
          })
        }
      })
    }
  },

  /**
   * 表单输入处理
   */
  onInputChange(e) {
    const { field } = e.currentTarget.dataset
    const { value } = e.detail
    
    this.setData({
      [`formData.${field}`]: value,
      [`formErrors.${field}`]: '' // 清除错误信息
    })
  },

  /**
   * 表单验证
   */
  validateForm() {
    const { formData } = this.data
    const errors = {}
    let isValid = true

    // WiFi标题验证
    if (!formData.title.trim()) {
      errors.title = '请输入WiFi标题'
      isValid = false
    } else if (formData.title.length > 20) {
      errors.title = '标题不能超过20个字符'
      isValid = false
    }

    // WiFi名称验证
    if (!formData.ssid.trim()) {
      errors.ssid = '请输入WiFi名称'
      isValid = false
    } else if (formData.ssid.length > 32) {
      errors.ssid = 'WiFi名称不能超过32个字符'
      isValid = false
    }

    // WiFi密码验证
    if (!formData.password.trim()) {
      errors.password = '请输入WiFi密码'
      isValid = false
    } else if (formData.password.length < 8) {
      errors.password = '密码至少8个字符'
      isValid = false
    } else if (formData.password.length > 63) {
      errors.password = '密码不能超过63个字符'
      isValid = false
    }

    // 商户名称验证
    if (!formData.merchantName.trim()) {
      errors.merchantName = '请输入商户名称'
      isValid = false
    } else if (formData.merchantName.length > 20) {
      errors.merchantName = '商户名称不能超过20个字符'
      isValid = false
    }

    this.setData({
      formErrors: errors
    })

    return isValid
  },

  /**
   * 创建WiFi码
   */
  async onCreateWifiCode() {
    if (this.data.isSubmitting) return

    // 表单验证
    if (!this.validateForm()) {
      wx.showToast({
        title: '请检查输入信息',
        icon: 'none'
      })
      return
    }

    this.setData({ isSubmitting: true })

    try {
      wx.showLoading({
        title: '创建中...'
      })

      const { formData } = this.data
      
      // 调用WiFi服务创建接口
      const result = await request.post('/wifi/create', {
        title: formData.title.trim(),
        name: formData.ssid.trim(),  // 前端发送字段名与后端接收字段名保持一致
        password: formData.password.trim(),
        merchant_name: formData.merchantName.trim()  // 前端发送字段名与后端接收字段名保持一致
      })

      wx.hideLoading()

      if (result.success) {
        wx.showToast({
          title: '创建成功',
          icon: 'success'
        })

        // 延迟跳转到WiFi码详情页
        setTimeout(() => {
          wx.redirectTo({
            url: `/pages/wifi/detail/detail?id=${result.data.id}`
          })
        }, 1500)
      } else {
        wx.showToast({
          title: result.message || '创建失败',
          icon: 'none'
        })
      }
    } catch (error) {
      wx.hideLoading()
      console.error('创建WiFi码失败:', error)
      wx.showToast({
        title: '网络错误，请重试',
        icon: 'none'
      })
    } finally {
      this.setData({ isSubmitting: false })
    }
  },

  /**
   * 重置表单
   */
  onResetForm() {
    wx.showModal({
      title: '确认重置',
      content: '确定要重置所有输入内容吗？',
      success: (res) => {
        if (res.confirm) {
          this.setData({
            formData: {
              title: '',
              ssid: '',
              password: '',
              merchantName: ''
            },
            formErrors: {
              title: '',
              ssid: '',
              password: '',
              merchantName: ''
            }
          })
          wx.showToast({
            title: '已重置',
            icon: 'success'
          })
        }
      }
    })
  },

  /**
   * 广告点击事件
   */
  onAdTap() {
    wx.showToast({
      title: '广告功能开发中',
      icon: 'none'
    })
  },

  /**
   * 帮助说明
   */
  onHelpTap() {
    wx.showModal({
      title: '使用说明',
      content: '1. 填写完整的WiFi信息\n2. 商户名称将显示在二维码上\n3. 创建后可生成二维码供他人扫码连接\n4. 支持打印、分享和统计功能',
      showCancel: false
    })
  },

  /**
   * 页面分享
   */
  onShareAppMessage() {
    return {
      title: 'WiFi共享商城 - 创建WiFi码',
      path: '/pages/wifi/create/create',
      imageUrl: '/assets/images/share-wifi-create.jpg'
    }
  }
}) 