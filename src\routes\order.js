﻿const express = require('express');
const router = express.Router();
const { verifyToken } = require('../middlewares/auth');
const { success, error } = require('../utils/response');
const db = require('../database');

// 检查订单表是否存在，如果不存在则创建
const initOrderTable = async () => {
  try {
    // 检查表是否存在
    const tables = await db.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = DATABASE() 
      AND table_name = 'orders'
    `);
    
    if (!tables || tables.length === 0) {
      console.log('创建orders表...');
      await db.query(`
        CREATE TABLE orders (
          id INT PRIMARY KEY AUTO_INCREMENT,
          order_no VARCHAR(50) NOT NULL UNIQUE,
          user_id INT NOT NULL,
          receiver_name VARCHAR(50) NOT NULL,
          receiver_phone VARCHAR(20) NOT NULL,
          receiver_address TEXT NOT NULL,
          goods_amount DECIMAL(10,2) NOT NULL,
          shipping_fee DECIMAL(10,2) NOT NULL DEFAULT 0,
          discount_amount DECIMAL(10,2) NOT NULL DEFAULT 0,
          total_amount DECIMAL(10,2) NOT NULL,
          payment_method TINYINT,
          payment_time DATETIME,
          shipping_time DATETIME,
          completion_time DATETIME,
          logistics_company VARCHAR(50),
          logistics_no VARCHAR(50),
          status TINYINT NOT NULL DEFAULT 0 COMMENT '0:待支付 1:待发货 2:待收货 3:已完成 4:已取消',
          remark TEXT,
          created_at DATETIME NOT NULL,
          updated_at DATETIME NOT NULL
        )
      `);
      
      // 创建订单商品表
      await db.query(`
        CREATE TABLE order_goods (
          id INT PRIMARY KEY AUTO_INCREMENT,
          order_id INT NOT NULL,
          goods_id INT NOT NULL,
          goods_title VARCHAR(100) NOT NULL,
          goods_cover VARCHAR(255),
          price DECIMAL(10,2) NOT NULL,
          quantity INT NOT NULL,
          subtotal DECIMAL(10,2) NOT NULL,
          created_at DATETIME NOT NULL,
          INDEX (order_id),
          INDEX (goods_id)
        )
      `);
      
      // 插入示例数据
      const now = new Date().toISOString().slice(0, 19).replace('T', ' ');
      
      // 插入5条订单数据
      for (let i = 1; i <= 5; i++) {
        const orderNo = `ORDER${Date.now().toString().slice(-10)}${i}`;
        const goodsAmount = Math.floor(Math.random() * 1000) + 100;
        const shippingFee = 10;
        const discountAmount = Math.floor(Math.random() * 50);
        const totalAmount = goodsAmount + shippingFee - discountAmount;
        const status = i % 5; // 0:待支付 1:待发货 2:待收货 3:已完成 4:已取消
        
        const result = await db.query(`
          INSERT INTO orders (
            order_no, user_id, receiver_name, receiver_phone, receiver_address,
            goods_amount, shipping_fee, discount_amount, total_amount,
            payment_method, status, created_at, updated_at
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `, [
          orderNo, 1, `收货人${i}`, `1380013800${i}`, `北京市朝阳区某某街道某某小区${i}号楼`,
          goodsAmount, shippingFee, discountAmount, totalAmount,
          1, status, now, now
        ]);
        
        const orderId = result.insertId;
        
        // 为每个订单添加1-3个商品
        const goodsCount = Math.floor(Math.random() * 3) + 1;
        for (let j = 1; j <= goodsCount; j++) {
          const price = Math.floor(Math.random() * 200) + 50;
          const quantity = Math.floor(Math.random() * 3) + 1;
          const subtotal = price * quantity;
          
          await db.query(`
            INSERT INTO order_goods (
              order_id, goods_id, goods_title, goods_cover,
              price, quantity, subtotal, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
          `, [
            orderId, j, `商品${j}`, `/assets/images/goods${j}.jpg`,
            price, quantity, subtotal, now
          ]);
        }
      }
      
      console.log('orders表和order_goods表创建完成并插入示例数据');
    } else {
      console.log('orders表已存在');
    }
  } catch (err) {
    console.error('初始化orders表失败:', err);
  }
};

// 初始化表
initOrderTable();

/**
 * 获取订单列表（管理端）
 * GET /api/order/list
 */
router.get('/list', verifyToken, async (req, res) => {
  try {
  const { page = 1, limit = 10, status, keyword } = req.query;
    const pageNum = parseInt(page);
    const limitNum = parseInt(limit);
    const offset = (pageNum - 1) * limitNum;
    
    // 构建查询条件
    const conditions = [];
    const params = [];
    
    if (status !== undefined && status !== '') {
      conditions.push('o.status = ?');
      params.push(parseInt(status));
    }
    
    if (keyword) {
      conditions.push('(o.order_no LIKE ? OR o.receiver_name LIKE ? OR o.receiver_phone LIKE ?)');
      params.push(`%${keyword}%`, `%${keyword}%`, `%${keyword}%`);
    }
    
    const whereClause = conditions.length > 0 ? 'WHERE ' + conditions.join(' AND ') : '';
    
    // 获取总数
    const countResult = await db.query(
      `SELECT COUNT(*) as total FROM orders o ${whereClause}`,
      params
    );
    
    // 确保安全地获取total值
    let total = 0;
    if (countResult && countResult.length > 0) {
      if (countResult[0].total !== undefined) {
        total = countResult[0].total;
      } else if (countResult[0]['COUNT(*)'] !== undefined) {
        total = countResult[0]['COUNT(*)'];
      }
    }
    
    // 获取订单列表
    const orders = await db.query(
      `SELECT 
        o.*,
        u.nickname as user_name,
        u.phone as user_phone,
        CASE 
          WHEN o.status = 0 THEN '待支付'
          WHEN o.status = 1 THEN '待发货'
          WHEN o.status = 2 THEN '待收货'
          WHEN o.status = 3 THEN '已完成'
          WHEN o.status = 4 THEN '已取消'
          ELSE '未知状态'
        END as status_name
      FROM orders o
      LEFT JOIN user u ON o.user_id = u.id
      ${whereClause}
      ORDER BY o.created_at DESC
      LIMIT ${limitNum} OFFSET ${offset}`,
      params
    );
  
  return success(res, {
      list: orders || [],
      total,
      page: pageNum,
      limit: limitNum
  }, '获取订单列表成功');
  } catch (err) {
    console.error('获取订单列表失败:', err);
    return error(res, '获取订单列表失败: ' + err.message, 500);
  }
});

/**
 * 获取订单详情（管理端）
 * GET /api/order/detail/:id
 */
router.get('/detail/:id', verifyToken, async (req, res) => {
  try {
  const { id } = req.params;
  
    // 获取订单基本信息
    const orders = await db.query(
      `SELECT 
        o.*,
        u.nickname as user_name,
        u.phone as user_phone,
        CASE 
          WHEN o.status = 0 THEN '待支付'
          WHEN o.status = 1 THEN '待发货'
          WHEN o.status = 2 THEN '待收货'
          WHEN o.status = 3 THEN '已完成'
          WHEN o.status = 4 THEN '已取消'
          ELSE '未知状态'
        END as status_name,
        CASE
          WHEN o.payment_method = 1 THEN '微信支付'
          WHEN o.payment_method = 2 THEN '支付宝'
          ELSE '其他支付方式'
        END as payment_method_name
      FROM orders o
      LEFT JOIN user u ON o.user_id = u.id
      WHERE o.id = ?`,
      [id]
    );
    
    if (!orders || orders.length === 0) {
      return error(res, '订单不存在', 404);
    }
    
    const orderDetail = orders[0];
    
    // 获取订单商品信息
    const goods = await db.query(
      `SELECT
        id,
        order_id,
        goods_id,
        goods_title as name,
        goods_cover as image,
        price,
        quantity,
        subtotal,
        created_at
      FROM order_goods WHERE order_id = ?`,
      [id]
    );

    orderDetail.goods_list = goods || [];
  
  return success(res, orderDetail, '获取订单详情成功');
  } catch (err) {
    console.error('获取订单详情失败:', err);
    return error(res, '获取订单详情失败: ' + err.message, 500);
  }
});

/**
 * 更新订单状态（管理端）
 * PUT /api/order/status/:id
 */
router.put('/status/:id', verifyToken, async (req, res) => {
  try {
  const { id } = req.params;
  const { status } = req.body;
    
    // 检查订单是否存在
    const orders = await db.query('SELECT * FROM orders WHERE id = ?', [id]);
    
    if (!orders || orders.length === 0) {
      return error(res, '订单不存在', 404);
    }
    
    // 更新状态
    await db.query(
      'UPDATE orders SET status = ?, updated_at = NOW() WHERE id = ?',
      [status, id]
    );
  
  const statusMessages = {
    1: '订单已确认',
    2: '订单已发货',
    3: '订单已完成',
    4: '订单已取消'
  };
  
  return success(res, null, statusMessages[status] || '订单状态已更新');
  } catch (err) {
    console.error('更新订单状态失败:', err);
    return error(res, '更新订单状态失败: ' + err.message, 500);
  }
});

/**
 * 更新物流信息（管理端）
 * PUT /api/order/logistics/:id
 */
router.put('/logistics/:id', verifyToken, async (req, res) => {
  try {
  const { id } = req.params;
  const { logistics_company, logistics_no } = req.body;
    
    // 检查订单是否存在
    const orders = await db.query('SELECT * FROM orders WHERE id = ?', [id]);
    
    if (!orders || orders.length === 0) {
      return error(res, '订单不存在', 404);
    }
    
    // 更新物流信息
    await db.query(
      'UPDATE orders SET logistics_company = ?, logistics_no = ?, shipping_time = NOW(), status = IF(status = 1, 2, status), updated_at = NOW() WHERE id = ?',
      [logistics_company, logistics_no, id]
    );
  
  return success(res, null, '物流信息已更新');
  } catch (err) {
    console.error('更新物流信息失败:', err);
    return error(res, '更新物流信息失败: ' + err.message, 500);
  }
});

/**
 * 创建订单（客户端）
 * POST /create
 */
router.post('/create', verifyToken, async (req, res) => {
  try {
    console.log('收到创建订单请求，参数:', req.body);
    
    const userId = req.user.id;
    const { 
      addressId, 
      goods, 
      remark = '', 
      paymentMethod = 'wechat',
      couponId = 0,
      fromCart = false
    } = req.body;
    
    // 验证必要参数
    if (!addressId) {
      console.log('参数验证失败: 缺少地址ID');
      return error(res, '缺少收货地址', 400);
    }

    let finalGoods = goods;

    // 如果是从购物车下单，获取购物车中的商品
    if (fromCart) {
      console.log('🛒 从购物车下单，获取购物车商品...');
      console.log('🔍 查询用户ID:', userId);

      const cartItems = await db.query(
        'SELECT c.*, g.title, g.price, g.stock FROM cart c LEFT JOIN goods g ON c.goods_id = g.id WHERE c.user_id = ? AND c.selected = 1',
        [userId]
      );

      console.log('🛒 购物车查询结果:', cartItems);

      if (!cartItems || cartItems.length === 0) {
        console.log('❌ 购物车中没有选中的商品');
        return error(res, '购物车中没有选中的商品', 400);
      }

      finalGoods = cartItems.map(item => ({
        goodsId: item.goods_id,
        quantity: item.quantity,
        specificationId: item.specs_id || 0
      }));

      console.log('🛒 从购物车获取的商品:', finalGoods);
      console.log('🛒 商品数量:', finalGoods.length);
    } else {
      // 直接购买，验证商品参数
      if (!goods || !Array.isArray(goods) || goods.length === 0) {
        console.log('参数验证失败:', { goods });
        return error(res, '商品参数错误', 400);
      }
    }

    // 获取地址信息
    const addresses = await db.query('SELECT * FROM user_address WHERE id = ? AND user_id = ?', [addressId, userId]);
    if (!addresses || addresses.length === 0) {
      console.log('地址查询失败:', { addressId, userId, addresses });
      return error(res, '收货地址不存在', 400);
    }
    const address = addresses[0];
    console.log('找到地址信息:', address);
    
    // 生成订单号
    const orderNo = `ORDER${Date.now().toString().slice(-10)}${Math.floor(Math.random() * 1000)}`;
    const now = new Date().toISOString().slice(0, 19).replace('T', ' ');
    
    // 获取商品详情并计算金额
    const goodsIds = finalGoods.map(item => item.goodsId);
    console.log('查询商品ID列表:', goodsIds);

    // 构建IN查询的占位符
    const placeholders = goodsIds.map(() => '?').join(',');
    const goodsDetails = await db.query(`SELECT * FROM goods WHERE id IN (${placeholders})`, goodsIds);
    console.log('查询到的商品详情:', goodsDetails);

    if (!goodsDetails || goodsDetails.length === 0) {
      console.log('商品查询失败:', { goodsIds, goodsDetails });
      return error(res, '商品不存在', 400);
    }

    // 构建商品列表并计算金额
    const goodsList = [];
    let goodsAmount = 0;
    let insufficientStock = false;

    for (const item of finalGoods) {
      const goodsDetail = goodsDetails.find(g => g.id == item.goodsId);
      if (!goodsDetail) {
        console.log('商品不存在:', item.goodsId);
        continue;
      }

      console.log('检查商品库存:', {
        goodsId: item.goodsId,
        title: goodsDetail.title,
        stock: goodsDetail.stock,
        requestQuantity: item.quantity
      });

      // 检查库存
      if (goodsDetail.stock < item.quantity) {
        console.log('库存不足:', {
          goodsId: item.goodsId,
          title: goodsDetail.title,
          stock: goodsDetail.stock,
          requestQuantity: item.quantity
        });
        insufficientStock = true;
        break;
      }
      
      const price = parseFloat(goodsDetail.price);
      const subtotal = price * parseInt(item.quantity);
      goodsAmount += subtotal;
      
      goodsList.push({
        goodsId: item.goodsId,
        title: goodsDetail.title,
        cover: goodsDetail.cover,
        price: price,
        quantity: parseInt(item.quantity),
        subtotal: subtotal,
        specificationId: item.specificationId || 0
      });
    }
    
    if (insufficientStock) {
      return error(res, '商品库存不足', 400);
    }
    
    if (goodsList.length === 0) {
      return error(res, '无有效商品', 400);
    }
    
    // 计算运费和最终金额
    const freight = goodsAmount >= 99 ? 0 : 10;
    const discountAmount = 0; // 暂不处理优惠券
    const finalAmount = goodsAmount + freight - discountAmount;
    
    console.log('订单金额计算:', {
      goodsAmount,
      freight,
      discountAmount,
      finalAmount
    });
    
    // 创建订单
    const orderResult = await db.query(
      `INSERT INTO orders (
        order_no, user_id, receiver_name, receiver_phone, receiver_address,
        goods_amount, shipping_fee, discount_amount, total_amount,
        payment_method, status, remark, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        orderNo, 
        userId, 
        address.name, 
        address.phone, 
        `${address.province}${address.city}${address.district}${address.address}`,
        goodsAmount.toFixed(2), 
        parseFloat(freight).toFixed(2), 
        parseFloat(discountAmount).toFixed(2), 
        parseFloat(finalAmount).toFixed(2),
        paymentMethod === 'wechat' ? 1 : (paymentMethod === 'alipay' ? 2 : 0), 
        0, // 待支付状态
        remark, 
        now, 
        now
      ]
    );
    
    if (!orderResult || !orderResult.insertId) {
      return error(res, '创建订单失败', 500);
    }
    
    const orderId = orderResult.insertId;
    
    // 添加订单商品
    for (const item of goodsList) {
      await db.query(
        `INSERT INTO order_goods (
          order_id, goods_id, goods_title, goods_cover,
          price, quantity, subtotal, created_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          orderId, 
          item.goodsId, 
          item.title, 
          item.cover,
          parseFloat(item.price).toFixed(2), 
          parseInt(item.quantity), 
          item.subtotal.toFixed(2), 
          now
        ]
      );
      
      // 更新商品库存
      await db.query(
        `UPDATE goods SET stock = stock - ? WHERE id = ? AND stock >= ?`,
        [parseInt(item.quantity), item.goodsId, parseInt(item.quantity)]
      );
    }
    
    // 如果是从购物车下单，清空购物车中已购买的商品
    if (fromCart) {
      const goodsIds = goodsList.map(item => item.goodsId);
      const placeholders = goodsIds.map(() => '?').join(',');
      await db.query(
        `DELETE FROM cart WHERE user_id = ? AND goods_id IN (${placeholders})`,
        [userId, ...goodsIds]
      );
    }
    
    return success(res, {
      orderId,
      orderNo
    }, '订单创建成功');
  } catch (err) {
    console.error('创建订单失败:', err);
    return error(res, '创建订单失败: ' + err.message, 500);
  }
});

/**
 * 获取订单列表（客户端）
 * GET /list
 */
router.get('/list', verifyToken, async (req, res) => {
  try {
    const userId = req.user.id;
    const { page = 1, limit = 10, status } = req.query;
    const pageNum = parseInt(page);
    const limitNum = parseInt(limit);
    const offset = (pageNum - 1) * limitNum;
    
    // 构建查询条件
    const conditions = ['o.user_id = ?'];
    const params = [userId];
    
    if (status !== undefined && status !== '') {
      conditions.push('o.status = ?');
      params.push(parseInt(status));
    }
    
    const whereClause = conditions.length > 0 ? 'WHERE ' + conditions.join(' AND ') : '';
    
    // 获取总数
    const countResult = await db.query(
      `SELECT COUNT(*) as total FROM orders o ${whereClause}`,
      params
    );
    
    // 确保安全地获取total值
    let total = 0;
    if (countResult && countResult.length > 0) {
      if (countResult[0].total !== undefined) {
        total = countResult[0].total;
      } else if (countResult[0]['COUNT(*)'] !== undefined) {
        total = countResult[0]['COUNT(*)'];
      }
    }
    
    // 获取订单列表
    const orders = await db.query(
      `SELECT 
        o.*,
        CASE 
          WHEN o.status = 0 THEN '待支付'
          WHEN o.status = 1 THEN '待发货'
          WHEN o.status = 2 THEN '待收货'
          WHEN o.status = 3 THEN '已完成'
          WHEN o.status = 4 THEN '已取消'
          ELSE '未知状态'
        END as status_name
      FROM orders o
      ${whereClause}
      ORDER BY o.created_at DESC
      LIMIT ${limitNum} OFFSET ${offset}`,
      params
    );
    
    // 获取每个订单的商品信息
    const result = [];
    for (const order of orders) {
      const goods = await db.query(
        `SELECT * FROM order_goods WHERE order_id = ?`,
        [order.id]
      );
      
      result.push({
        ...order,
        goods_list: goods || []
      });
    }
  
    return success(res, {
      list: result || [],
      total,
      page: pageNum,
      limit: limitNum
    }, '获取订单列表成功');
  } catch (err) {
    console.error('获取订单列表失败:', err);
    return error(res, '获取订单列表失败: ' + err.message, 500);
  }
});

/**
 * 获取订单详情（客户端）
 * GET /client/detail/:id
 */
router.get('/client/detail/:id', verifyToken, async (req, res) => {
  try {
    const userId = req.user.id;
    const { id } = req.params;
    
    // 获取订单基本信息
    const orders = await db.query(
      `SELECT 
        o.*,
        CASE 
          WHEN o.status = 0 THEN '待支付'
          WHEN o.status = 1 THEN '待发货'
          WHEN o.status = 2 THEN '待收货'
          WHEN o.status = 3 THEN '已完成'
          WHEN o.status = 4 THEN '已取消'
          ELSE '未知状态'
        END as status_name,
        CASE
          WHEN o.payment_method = 1 THEN '微信支付'
          WHEN o.payment_method = 2 THEN '支付宝'
          ELSE '其他支付方式'
        END as payment_method_name
      FROM orders o
      WHERE o.id = ? AND o.user_id = ?`,
      [id, userId]
    );
    
    if (!orders || orders.length === 0) {
      return error(res, '订单不存在', 404);
    }
    
    const orderDetail = orders[0];
    
    // 获取订单商品信息
    const goods = await db.query(
      `SELECT
        id,
        order_id,
        goods_id,
        goods_title as name,
        goods_cover as image,
        price,
        quantity,
        subtotal,
        created_at
      FROM order_goods WHERE order_id = ?`,
      [id]
    );

    orderDetail.goods_list = goods || [];
  
    return success(res, orderDetail, '获取订单详情成功');
  } catch (err) {
    console.error('获取订单详情失败:', err);
    return error(res, '获取订单详情失败: ' + err.message, 500);
  }
});

/**
 * 取消订单（客户端）
 * POST /cancel/:id
 */
router.post('/cancel/:id', verifyToken, async (req, res) => {
  try {
    const userId = req.user.id;
    const { id } = req.params;
    
    // 检查订单是否存在且属于当前用户
    const orders = await db.query(
      'SELECT * FROM orders WHERE id = ? AND user_id = ?', 
      [id, userId]
    );
    
    if (!orders || orders.length === 0) {
      return error(res, '订单不存在', 404);
    }
    
    const order = orders[0];
    
    // 只有待支付或待发货的订单可以取消
    if (order.status !== 0 && order.status !== 1) {
      return error(res, '当前订单状态不可取消', 400);
    }
    
    // 更新订单状态为已取消
    await db.query(
      'UPDATE orders SET status = 4, updated_at = NOW() WHERE id = ?',
      [id]
    );
    
    // 恢复商品库存
    const orderGoods = await db.query(
      'SELECT * FROM order_goods WHERE order_id = ?',
      [id]
    );
    
    for (const item of orderGoods) {
      await db.query(
        'UPDATE goods SET stock = stock + ? WHERE id = ?',
        [item.quantity, item.goods_id]
      );
    }
  
    return success(res, null, '订单已取消');
  } catch (err) {
    console.error('取消订单失败:', err);
    return error(res, '取消订单失败: ' + err.message, 500);
  }
});

/**
 * 支付成功回调（客户端）
 * POST /payment-success/:id
 */
router.post('/payment-success/:id', verifyToken, async (req, res) => {
  try {
    const userId = req.user.id;
    const { id } = req.params;

    console.log('💰 支付成功回调请求:', { userId, orderId: id });

    // 检查订单是否存在且属于当前用户
    const orders = await db.query(
      'SELECT * FROM orders WHERE id = ? AND user_id = ?',
      [id, userId]
    );

    if (!orders || orders.length === 0) {
      console.log('💰 订单不存在或不属于当前用户:', { orderId: id, userId });
      return error(res, '订单不存在', 404);
    }

    const order = orders[0];

    // 检查订单状态是否为待支付
    if (order.status !== 0) {
      console.log('💰 订单状态不是待支付:', { orderId: id, status: order.status });
      return error(res, '订单状态不正确', 400);
    }

    // 更新订单状态为待发货
    await db.query(
      'UPDATE orders SET status = 1, payment_time = NOW(), updated_at = NOW() WHERE id = ?',
      [id]
    );

    console.log('💰 订单状态更新成功:', { orderId: id, newStatus: 1 });

    return success(res, { orderId: id, status: 1 }, '支付成功，订单状态已更新');
  } catch (err) {
    console.error('💰 支付成功回调失败:', err);
    return error(res, '支付回调处理失败: ' + err.message, 500);
  }
});

/**
 * 删除订单（客户端）
 * DELETE /delete/:id
 */
router.delete('/delete/:id', verifyToken, async (req, res) => {
  try {
    const userId = req.user.id;
    const { id } = req.params;

    console.log('🗑️ 删除订单请求:', { userId, orderId: id });

    // 检查订单是否存在且属于当前用户
    const orders = await db.query(
      'SELECT * FROM orders WHERE id = ? AND user_id = ?',
      [id, userId]
    );

    if (!orders || orders.length === 0) {
      console.log('🗑️ 订单不存在或不属于当前用户:', { orderId: id, userId });
      return error(res, '订单不存在', 404);
    }

    const order = orders[0];

    // 只允许删除已取消或已完成的订单
    if (order.status !== 3 && order.status !== 4) {
      console.log('🗑️ 订单状态不允许删除:', { orderId: id, status: order.status });
      return error(res, '只能删除已完成或已取消的订单', 400);
    }

    // 删除订单
    await db.query('DELETE FROM orders WHERE id = ?', [id]);

    console.log('🗑️ 订单删除成功:', { orderId: id });

    return success(res, { orderId: id }, '订单删除成功');
  } catch (err) {
    console.error('🗑️ 删除订单失败:', err);
    return error(res, '删除订单失败: ' + err.message, 500);
  }
});

/**
 * 确认收货（客户端）
 * POST /confirm/:id
 */
router.post('/confirm/:id', verifyToken, async (req, res) => {
  try {
    const userId = req.user.id;
    const { id } = req.params;
    
    // 检查订单是否存在且属于当前用户
    const orders = await db.query(
      'SELECT * FROM orders WHERE id = ? AND user_id = ?', 
      [id, userId]
    );
    
    if (!orders || orders.length === 0) {
      return error(res, '订单不存在', 404);
    }
    
    const order = orders[0];
    
    // 只有待收货的订单可以确认收货
    if (order.status !== 2) {
      return error(res, '当前订单状态不可确认收货', 400);
    }
    
    // 更新订单状态为已完成
    await db.query(
      'UPDATE orders SET status = 3, completion_time = NOW(), updated_at = NOW() WHERE id = ?',
      [id]
    );
  
    return success(res, null, '已确认收货');
  } catch (err) {
    console.error('确认收货失败:', err);
    return error(res, '确认收货失败: ' + err.message, 500);
  }
});

module.exports = router;
