-- 实时数据统计系统数据库迁移脚本
-- 创建时间: 2025-07-15

-- 1. 创建平台收益统计表
CREATE TABLE IF NOT EXISTS `platform_revenue` (
  `id` int NOT NULL AUTO_INCREMENT,
  `date` date NOT NULL COMMENT '统计日期',
  `profit_type` varchar(20) NOT NULL COMMENT '收益类型：wifi_share,goods_sale,advertisement',
  `region_id` int DEFAULT NULL COMMENT '地区ID（地区隔离）',
  `total_amount` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '总交易金额',
  `platform_amount` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '平台分润金额',
  `leader_amount` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '团长分润金额',
  `member_amount` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '成员分润金额',
  `transaction_count` int NOT NULL DEFAULT '0' COMMENT '交易笔数',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_platform_revenue_date` (`date`, `profit_type`, `region_id`),
  KEY `idx_platform_revenue_date` (`date`),
  KEY `idx_platform_revenue_type` (`profit_type`),
  KEY `idx_platform_revenue_region` (`region_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='平台收益统计表';

-- 2. 创建团队业绩缓存表
CREATE TABLE IF NOT EXISTS `team_performance_cache` (
  `id` int NOT NULL AUTO_INCREMENT,
  `team_id` int NOT NULL COMMENT '团队ID',
  `region_id` int DEFAULT NULL COMMENT '地区ID',
  `date` date NOT NULL COMMENT '统计日期',
  `total_income` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '总收益',
  `today_income` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '今日收益',
  `week_income` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '本周收益',
  `month_income` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '本月收益',
  `wifi_count` int NOT NULL DEFAULT '0' COMMENT 'WiFi数量',
  `member_count` int NOT NULL DEFAULT '0' COMMENT '成员数量',
  `active_member_count` int NOT NULL DEFAULT '0' COMMENT '活跃成员数',
  `transaction_count` int NOT NULL DEFAULT '0' COMMENT '交易笔数',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_team_performance_date` (`team_id`, `date`),
  KEY `idx_team_performance_team` (`team_id`),
  KEY `idx_team_performance_region` (`region_id`),
  KEY `idx_team_performance_date` (`date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='团队业绩缓存表';

-- 3. 创建用户余额变动日志表
CREATE TABLE IF NOT EXISTS `balance_log` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL COMMENT '用户ID',
  `amount` decimal(10,2) NOT NULL COMMENT '变动金额',
  `type` varchar(20) NOT NULL COMMENT '变动类型：profit,withdraw,recharge,adjust',
  `before_balance` decimal(10,2) NOT NULL COMMENT '变动前余额',
  `after_balance` decimal(10,2) NOT NULL COMMENT '变动后余额',
  `source_type` varchar(20) DEFAULT NULL COMMENT '来源类型：wifi_share,goods_sale,advertisement',
  `source_id` int DEFAULT NULL COMMENT '来源ID',
  `profit_log_id` int DEFAULT NULL COMMENT '关联的分润记录ID',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_balance_log_user` (`user_id`),
  KEY `idx_balance_log_type` (`type`),
  KEY `idx_balance_log_source` (`source_type`, `source_id`),
  KEY `idx_balance_log_profit` (`profit_log_id`),
  KEY `idx_balance_log_date` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户余额变动日志表';

-- 4. 创建平台收益实时统计表
CREATE TABLE IF NOT EXISTS `platform_stats_realtime` (
  `id` int NOT NULL AUTO_INCREMENT,
  `stat_key` varchar(50) NOT NULL COMMENT '统计键：total_revenue,today_revenue,month_revenue等',
  `region_id` int DEFAULT NULL COMMENT '地区ID（NULL表示全平台）',
  `profit_type` varchar(20) DEFAULT NULL COMMENT '收益类型（NULL表示所有类型）',
  `stat_value` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '统计值',
  `count_value` int NOT NULL DEFAULT '0' COMMENT '计数值',
  `last_updated` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_platform_stats_key` (`stat_key`, `region_id`, `profit_type`),
  KEY `idx_platform_stats_region` (`region_id`),
  KEY `idx_platform_stats_type` (`profit_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='平台收益实时统计表';

-- 5. 初始化平台统计数据
INSERT INTO `platform_stats_realtime` (`stat_key`, `region_id`, `profit_type`, `stat_value`, `count_value`) VALUES
-- 全平台统计
('total_revenue', NULL, NULL, 0.00, 0),
('today_revenue', NULL, NULL, 0.00, 0),
('week_revenue', NULL, NULL, 0.00, 0),
('month_revenue', NULL, NULL, 0.00, 0),
('total_platform_profit', NULL, NULL, 0.00, 0),
('total_leader_profit', NULL, NULL, 0.00, 0),
('total_member_profit', NULL, NULL, 0.00, 0),
-- 按类型统计
('total_revenue', NULL, 'wifi_share', 0.00, 0),
('total_revenue', NULL, 'goods_sale', 0.00, 0),
('total_revenue', NULL, 'advertisement', 0.00, 0),
('today_revenue', NULL, 'wifi_share', 0.00, 0),
('today_revenue', NULL, 'goods_sale', 0.00, 0),
('today_revenue', NULL, 'advertisement', 0.00, 0);

-- 6. 为现有分润记录补充余额变动日志
INSERT INTO `balance_log` (
  `user_id`, `amount`, `type`, `before_balance`, `after_balance`, 
  `source_type`, `source_id`, `profit_log_id`, `remark`, `created_at`
)
SELECT 
  pl.user_id,
  pl.amount,
  'profit',
  0.00, -- 历史数据无法获取变动前余额
  pl.amount, -- 简化处理
  pl.source_type,
  pl.source_id,
  pl.id,
  CONCAT('历史分润记录补录: ', pl.remark),
  pl.created_at
FROM profit_log pl
WHERE NOT EXISTS (
  SELECT 1 FROM balance_log bl WHERE bl.profit_log_id = pl.id
);

-- 7. 创建触发器：分润记录插入时自动更新统计
DELIMITER $$

CREATE TRIGGER `tr_profit_log_insert` 
AFTER INSERT ON `profit_log`
FOR EACH ROW
BEGIN
  DECLARE platform_rate DECIMAL(5,2) DEFAULT 0;
  DECLARE leader_rate DECIMAL(5,2) DEFAULT 0;
  DECLARE member_rate DECIMAL(5,2) DEFAULT 0;
  DECLARE total_amount DECIMAL(10,2) DEFAULT 0;
  DECLARE platform_amount DECIMAL(10,2) DEFAULT 0;
  DECLARE user_region_id INT DEFAULT NULL;
  
  -- 获取分润规则
  SELECT pr.platform_rate, pr.leader_rate, pr.user_rate
  INTO platform_rate, leader_rate, member_rate
  FROM profit_rule pr
  WHERE pr.type = NEW.source_type
  LIMIT 1;
  
  -- 计算总交易金额（反推）
  SET total_amount = NEW.amount * 100 / CASE 
    WHEN NEW.role = 'platform' THEN platform_rate
    WHEN NEW.role = 'leader' THEN leader_rate  
    WHEN NEW.role = 'member' THEN member_rate
    ELSE 100
  END;
  
  SET platform_amount = total_amount * platform_rate / 100;
  
  -- 获取用户地区
  SELECT region_id INTO user_region_id FROM user WHERE id = NEW.user_id LIMIT 1;
  
  -- 更新平台收益统计
  INSERT INTO platform_revenue (
    date, profit_type, region_id, total_amount, platform_amount,
    leader_amount, member_amount, transaction_count
  ) VALUES (
    DATE(NEW.created_at), NEW.source_type, user_region_id, 
    total_amount, platform_amount, 0, 0, 1
  ) ON DUPLICATE KEY UPDATE
    total_amount = total_amount + VALUES(total_amount),
    platform_amount = platform_amount + VALUES(platform_amount),
    transaction_count = transaction_count + 1,
    updated_at = NOW();
    
  -- 更新实时统计
  INSERT INTO platform_stats_realtime (stat_key, region_id, profit_type, stat_value, count_value)
  VALUES ('total_revenue', NULL, NEW.source_type, total_amount, 1)
  ON DUPLICATE KEY UPDATE 
    stat_value = stat_value + VALUES(stat_value),
    count_value = count_value + 1;
    
  INSERT INTO platform_stats_realtime (stat_key, region_id, profit_type, stat_value, count_value)
  VALUES ('today_revenue', NULL, NEW.source_type, total_amount, 1)
  ON DUPLICATE KEY UPDATE 
    stat_value = stat_value + VALUES(stat_value),
    count_value = count_value + 1;
END$$

DELIMITER ;

-- 8. 创建存储过程：刷新团队业绩缓存
DELIMITER $$

CREATE PROCEDURE `RefreshTeamPerformanceCache`(IN team_id_param INT)
BEGIN
  DECLARE team_region_id INT DEFAULT NULL;
  
  -- 获取团队地区
  SELECT region_id INTO team_region_id FROM team WHERE id = team_id_param LIMIT 1;
  
  -- 刷新今日缓存
  INSERT INTO team_performance_cache (
    team_id, region_id, date, total_income, today_income, 
    week_income, month_income, member_count, transaction_count
  )
  SELECT 
    team_id_param,
    team_region_id,
    CURDATE(),
    COALESCE(total_stats.total_income, 0),
    COALESCE(today_stats.today_income, 0),
    COALESCE(week_stats.week_income, 0),
    COALESCE(month_stats.month_income, 0),
    COALESCE(member_stats.member_count, 0),
    COALESCE(total_stats.transaction_count, 0)
  FROM (SELECT 1) dummy
  LEFT JOIN (
    SELECT 
      SUM(pl.amount) as total_income,
      COUNT(*) as transaction_count
    FROM profit_log pl
    WHERE pl.user_id IN (SELECT user_id FROM team_member WHERE team_id = team_id_param)
  ) total_stats ON 1=1
  LEFT JOIN (
    SELECT SUM(pl.amount) as today_income
    FROM profit_log pl
    WHERE pl.user_id IN (SELECT user_id FROM team_member WHERE team_id = team_id_param)
    AND DATE(pl.created_at) = CURDATE()
  ) today_stats ON 1=1
  LEFT JOIN (
    SELECT SUM(pl.amount) as week_income
    FROM profit_log pl
    WHERE pl.user_id IN (SELECT user_id FROM team_member WHERE team_id = team_id_param)
    AND YEARWEEK(pl.created_at) = YEARWEEK(NOW())
  ) week_stats ON 1=1
  LEFT JOIN (
    SELECT SUM(pl.amount) as month_income
    FROM profit_log pl
    WHERE pl.user_id IN (SELECT user_id FROM team_member WHERE team_id = team_id_param)
    AND YEAR(pl.created_at) = YEAR(NOW()) AND MONTH(pl.created_at) = MONTH(NOW())
  ) month_stats ON 1=1
  LEFT JOIN (
    SELECT COUNT(*) as member_count
    FROM team_member 
    WHERE team_id = team_id_param
  ) member_stats ON 1=1
  ON DUPLICATE KEY UPDATE
    total_income = VALUES(total_income),
    today_income = VALUES(today_income),
    week_income = VALUES(week_income),
    month_income = VALUES(month_income),
    member_count = VALUES(member_count),
    transaction_count = VALUES(transaction_count),
    updated_at = NOW();
END$$

DELIMITER ;
