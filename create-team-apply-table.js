const db = require('./src/database');

async function createTeamApplyTable() {
  try {
    console.log('正在检查team_apply表是否存在...');
    const [tables] = await db.query(
      "SELECT table_name FROM information_schema.tables WHERE table_schema = 'mall' AND table_name = 'team_apply'"
    );
    
    if (tables && tables.length > 0) {
      console.log('team_apply表已存在，无需创建');
      return;
    }
    
    console.log('创建team_apply表...');
    await db.query(`
      CREATE TABLE IF NOT EXISTS \`team_apply\` (
        \`id\` int(11) NOT NULL AUTO_INCREMENT COMMENT '申请ID',
        \`user_id\` int(11) NOT NULL COMMENT '申请用户ID',
        \`name\` varchar(50) NOT NULL COMMENT '姓名',
        \`phone\` varchar(20) NOT NULL COMMENT '手机号',
        \`area\` varchar(100) DEFAULT NULL COMMENT '区域',
        \`description\` text COMMENT '简介',
        \`status\` tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态：0待审核，1已通过，2已拒绝',
        \`remark\` varchar(255) DEFAULT NULL COMMENT '备注',
        \`created_at\` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '申请时间',
        \`updated_at\` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
        PRIMARY KEY (\`id\`),
        KEY \`user_id\` (\`user_id\`)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='团队申请表';
    `);
    
    console.log('team_apply表创建成功');
  } catch (err) {
    console.error('创建team_apply表时出错:', err);
  } finally {
    process.exit(0);
  }
}

createTeamApplyTable(); 