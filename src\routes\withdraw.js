/**
 * 提现相关路由
 */

const express = require('express');
const router = express.Router();
const { body, param, query } = require('express-validator');
const withdrawController = require('../controllers/withdrawController');
const authMiddleware = require('../middlewares/auth');

// 应用认证中间件
router.use(authMiddleware);

/**
 * 获取提现配置
 * GET /api/v1/client/withdraw/config
 */
router.get('/config', withdrawController.getConfig);

/**
 * 获取提现方式列表（兼容前端）
 * GET /api/v1/client/withdraw/methods
 */
router.get('/methods', withdrawController.getConfig);

/**
 * 获取用户提现账户
 * GET /api/v1/client/withdraw/accounts
 */
router.get('/accounts', [
  query('method_code').optional().isString().withMessage('提现方式代码必须是字符串')
], withdrawController.getAccounts);

/**
 * 计算提现手续费
 * POST /api/v1/client/withdraw/calculate-fee
 */
router.post('/calculate-fee', [
  body('method_code')
    .notEmpty()
    .withMessage('提现方式代码不能为空')
    .isString()
    .withMessage('提现方式代码必须是字符串')
    .isIn(['wechat', 'alipay', 'bank_card'])
    .withMessage('提现方式代码无效'),
  body('amount')
    .notEmpty()
    .withMessage('提现金额不能为空')
    .isFloat({ min: 0.01 })
    .withMessage('提现金额必须大于0.01')
    .custom((value) => {
      if (value > 50000) {
        throw new Error('提现金额不能超过50000元');
      }
      return true;
    })
], withdrawController.calculateFee);

/**
 * 提交提现申请
 * POST /api/v1/client/withdraw/apply
 */
router.post('/apply', [
  body('method_code')
    .notEmpty()
    .withMessage('提现方式代码不能为空')
    .isString()
    .withMessage('提现方式代码必须是字符串')
    .isIn(['wechat', 'alipay', 'bank_card'])
    .withMessage('提现方式代码无效'),
  body('amount')
    .notEmpty()
    .withMessage('提现金额不能为空')
    .isFloat({ min: 0.01 })
    .withMessage('提现金额必须大于0.01')
    .custom((value) => {
      if (value > 50000) {
        throw new Error('提现金额不能超过50000元');
      }
      return true;
    }),
  body('account_id')
    .notEmpty()
    .withMessage('提现账户ID不能为空')
    .isInt({ min: 1 })
    .withMessage('提现账户ID必须是正整数'),
  body('remark')
    .optional()
    .isString()
    .withMessage('备注必须是字符串')
    .isLength({ max: 255 })
    .withMessage('备注长度不能超过255个字符')
], withdrawController.apply);

/**
 * 获取提现记录
 * GET /api/v1/client/withdraw/records
 */
router.get('/records', [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('页码必须是正整数'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('每页数量必须是1-100之间的整数'),
  query('status')
    .optional()
    .isInt({ min: 0, max: 5 })
    .withMessage('状态必须是0-5之间的整数'),
  query('method_code')
    .optional()
    .isString()
    .withMessage('提现方式代码必须是字符串')
    .isIn(['wechat', 'alipay', 'bank_card'])
    .withMessage('提现方式代码无效')
], withdrawController.getRecords);

/**
 * 获取提现详情
 * GET /api/v1/client/withdraw/detail/:id
 */
router.get('/detail/:id', [
  param('id')
    .notEmpty()
    .withMessage('提现记录ID不能为空')
    .isInt({ min: 1 })
    .withMessage('提现记录ID必须是正整数')
], withdrawController.getDetail);

/**
 * 取消提现申请
 * POST /api/v1/client/withdraw/cancel/:id
 */
router.post('/cancel/:id', [
  param('id')
    .notEmpty()
    .withMessage('提现记录ID不能为空')
    .isInt({ min: 1 })
    .withMessage('提现记录ID必须是正整数'),
  body('reason')
    .optional()
    .isString()
    .withMessage('取消原因必须是字符串')
    .isLength({ max: 255 })
    .withMessage('取消原因长度不能超过255个字符')
], withdrawController.cancel);

/**
 * 添加提现账户
 * POST /api/v1/client/withdraw/account/add
 */
router.post('/account/add', [
  body('method_code')
    .notEmpty()
    .withMessage('提现方式代码不能为空')
    .isString()
    .withMessage('提现方式代码必须是字符串')
    .isIn(['wechat', 'alipay', 'bank_card'])
    .withMessage('提现方式代码无效'),
  body('account_name')
    .notEmpty()
    .withMessage('账户名称不能为空')
    .isString()
    .withMessage('账户名称必须是字符串')
    .isLength({ min: 1, max: 100 })
    .withMessage('账户名称长度必须在1-100个字符之间'),
  body('account_number')
    .notEmpty()
    .withMessage('账户号码不能为空')
    .isString()
    .withMessage('账户号码必须是字符串')
    .isLength({ min: 1, max: 100 })
    .withMessage('账户号码长度必须在1-100个字符之间'),
  body('account_holder')
    .notEmpty()
    .withMessage('账户持有人不能为空')
    .isString()
    .withMessage('账户持有人必须是字符串')
    .isLength({ min: 1, max: 50 })
    .withMessage('账户持有人长度必须在1-50个字符之间'),
  body('bank_name')
    .optional()
    .isString()
    .withMessage('银行名称必须是字符串')
    .isLength({ max: 100 })
    .withMessage('银行名称长度不能超过100个字符'),
  body('bank_branch')
    .optional()
    .isString()
    .withMessage('开户行必须是字符串')
    .isLength({ max: 100 })
    .withMessage('开户行长度不能超过100个字符'),
  body('id_card')
    .optional()
    .isString()
    .withMessage('身份证号必须是字符串')
    .matches(/^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/)
    .withMessage('身份证号格式不正确'),
  body('phone')
    .optional()
    .isString()
    .withMessage('手机号必须是字符串')
    .matches(/^1[3-9]\d{9}$/)
    .withMessage('手机号格式不正确')
], withdrawController.addAccount);

/**
 * 删除提现账户
 * DELETE /api/v1/client/withdraw/account/:id
 */
router.delete('/account/:id', [
  param('id')
    .notEmpty()
    .withMessage('账户ID不能为空')
    .isInt({ min: 1 })
    .withMessage('账户ID必须是正整数')
], withdrawController.deleteAccount);

module.exports = router;
