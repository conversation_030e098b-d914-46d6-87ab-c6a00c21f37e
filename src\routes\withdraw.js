const express = require('express');
const router = express.Router();
const { verifyToken, adminAuth } = require('../middlewares/auth');
const logger = require('../utils/logger');
const { success, error } = require('../utils/response');
const mysql = require('mysql2/promise');

// 数据库连接配置
const dbConfig = {
  host: 'localhost',
  user: 'root',
  password: 'wo587129955',
  database: 'mall'
};

// 模拟数据 - 提现申请列表
const withdrawList = [
  {
    id: 1,
    withdraw_no: 'W2025070800001',
    user_id: 1001,
    user_name: '张三',
    user_phone: '138****1001',
    amount: 200.00,
    status: 0,  // 0待审核，1审核通过待打款，2审核拒绝，3已打款
    type: 1,  // 1支付宝，2银行卡
    account_type: '支付宝',
    account_name: '张三',
    account_no: 'z<PERSON><PERSON>@example.com',
    bank_name: null,
    created_at: '2025-07-08 10:12:33',
    audit_time: null,
    audit_user: null,
    audit_remark: null,
    pay_time: null,
    pay_remark: null
  },
  {
    id: 2,
    withdraw_no: 'W2025070800002',
    user_id: 1002,
    user_name: '李四',
    user_phone: '138****1002',
    amount: 500.00,
    status: 1,
    type: 2,
    account_type: '银行卡',
    account_name: '李四',
    account_no: '6222xxxxxxx',
    bank_name: '工商银行',
    created_at: '2025-07-08 09:45:21',
    audit_time: '2025-07-08 11:23:45',
    audit_user: 'admin',
    audit_remark: '审核通过',
    pay_time: null,
    pay_remark: null
  },
  {
    id: 3,
    withdraw_no: 'W2025070800003',
    user_id: 1003,
    user_name: '王五',
    user_phone: '138****1003',
    amount: 100.00,
    status: 2,
    type: 1,
    account_type: '支付宝',
    account_name: '王五',
    account_no: '<EMAIL>',
    bank_name: null,
    created_at: '2025-07-07 16:32:18',
    audit_time: '2025-07-08 09:15:30',
    audit_user: 'admin',
    audit_remark: '金额不足，请重新提交',
    pay_time: null,
    pay_remark: null
  },
  {
    id: 4,
    withdraw_no: 'W2025070800004',
    user_id: 1004,
    user_name: '赵六',
    user_phone: '138****1004',
    amount: 300.00,
    status: 3,
    type: 2,
    account_type: '银行卡',
    account_name: '赵六',
    account_no: '6217xxxxxxx',
    bank_name: '招商银行',
    created_at: '2025-07-07 14:56:42',
    audit_time: '2025-07-07 16:28:35',
    audit_user: 'admin',
    audit_remark: '审核通过',
    pay_time: '2025-07-08 10:35:12',
    pay_remark: '打款成功'
  }
];

// 获取提现申请列表
router.get('/list', verifyToken, adminAuth, (req, res) => {
  try {
    const { page = 1, limit = 10, keyword, status, start_date, end_date } = req.query;
    
    // 过滤提现申请
    let filteredWithdraws = [...withdrawList];
    
    // 按关键词搜索
    if (keyword) {
      filteredWithdraws = filteredWithdraws.filter(withdraw => 
        withdraw.user_name.includes(keyword) ||
        withdraw.user_phone.includes(keyword) ||
        withdraw.withdraw_no.includes(keyword)
      );
    }
    
    // 按状态筛选
    if (status !== undefined && status !== '') {
      filteredWithdraws = filteredWithdraws.filter(withdraw => withdraw.status === parseInt(status));
    }
    
    // 按日期筛选
    if (start_date && end_date) {
      filteredWithdraws = filteredWithdraws.filter(withdraw => {
        const withdrawDate = new Date(withdraw.created_at);
        const startDate = new Date(start_date);
        const endDate = new Date(end_date);
        endDate.setHours(23, 59, 59, 999);
        return withdrawDate >= startDate && withdrawDate <= endDate;
      });
    }
    
    // 计算分页
    const total = filteredWithdraws.length;
    const pageNum = parseInt(page);
    const pageSize = parseInt(limit);
    const start = (pageNum - 1) * pageSize;
    const end = start + pageSize;
    const list = filteredWithdraws.slice(start, end);
    
    logger.info('获取提现申请列表成功');
    return success(res, {
      list,
      total,
      page: pageNum,
      limit: pageSize
    }, '获取提现申请列表成功');
  } catch (err) {
    logger.error('获取提现申请列表失败:', err);
    return error(res, '获取提现申请列表失败');
  }
});

// 获取提现申请详情
router.get('/detail/:id', verifyToken, adminAuth, (req, res) => {
  try {
    const { id } = req.params;
    const withdraw = withdrawList.find(w => w.id === parseInt(id));
    
    if (!withdraw) {
      return error(res, '提现申请不存在', 404);
    }
    
    // 构造返回数据
    const detailData = {
      detail: {
        ...withdraw,
        card_holder: withdraw.account_name,
        bank_name: withdraw.bank_name || '支付宝',
        card_number: withdraw.account_no,
        bank_branch: withdraw.bank_name === '工商银行' ? '北京市朝阳区支行' : null,
        transfer_time: withdraw.pay_time,
        transaction_id: withdraw.status === 3 ? 'TRX202507080001' : null,
        remark: withdraw.audit_remark || withdraw.pay_remark || null
      },
      user_info: {
        id: withdraw.user_id,
        nickname: withdraw.user_name,
        phone: withdraw.user_phone,
        avatar: '/img/default-avatar.png',
        balance: (Math.random() * 1000 + 500).toFixed(2),
        is_leader: withdraw.user_id % 2 === 0 ? 0 : 1
      }
    };
    
    logger.info('获取提现申请详情成功');
    return success(res, detailData, '获取提现申请详情成功');
  } catch (err) {
    logger.error('获取提现申请详情失败:', err);
    return error(res, '获取提现申请详情失败');
  }
});

// 审核提现申请
router.post('/audit/:id', verifyToken, adminAuth, (req, res) => {
  try {
    const { id } = req.params;
    const { status, remark } = req.body;
    
    const withdrawIndex = withdrawList.findIndex(w => w.id === parseInt(id));
    
    if (withdrawIndex === -1) {
      return error(res, '提现申请不存在', 404);
    }
    
    // 检查提现申请状态
    if (withdrawList[withdrawIndex].status !== 0) {
      return error(res, '只有待审核的提现申请可以进行审核操作', 400);
    }
    
    // 检查状态参数
    if (status !== 1 && status !== 2) {
      return error(res, '状态参数错误，只能是1(通过)或2(拒绝)', 400);
    }
    
    // 更新提现申请状态
    withdrawList[withdrawIndex].status = status;
    withdrawList[withdrawIndex].audit_time = new Date().toISOString().replace('T', ' ').slice(0, 19);
    withdrawList[withdrawIndex].audit_user = 'admin';
    withdrawList[withdrawIndex].audit_remark = remark || (status === 1 ? '审核通过' : '审核拒绝');
    
    logger.info('审核提现申请成功');
    return success(res, {}, '审核成功');
  } catch (err) {
    logger.error('审核提现申请失败:', err);
    return error(res, '审核提现申请失败');
  }
});

// 确认提现打款
router.post('/confirm/:id', verifyToken, adminAuth, (req, res) => {
  try {
    const { id } = req.params;
    const { transaction_id, remark } = req.body;
    
    const withdrawIndex = withdrawList.findIndex(w => w.id === parseInt(id));
    
    if (withdrawIndex === -1) {
      return error(res, '提现申请不存在', 404);
    }
    
    // 检查提现申请状态
    if (withdrawList[withdrawIndex].status !== 1) {
      return error(res, '只有审核通过的提现申请可以进行打款操作', 400);
    }
    
    // 更新提现申请状态
    withdrawList[withdrawIndex].status = 3;
    withdrawList[withdrawIndex].pay_time = new Date().toISOString().replace('T', ' ').slice(0, 19);
    withdrawList[withdrawIndex].pay_remark = remark || '已打款';
    withdrawList[withdrawIndex].transaction_id = transaction_id || `TRX${Date.now().toString().substr(-8)}`;
    
    logger.info('确认提现打款成功');
    return success(res, {}, '打款成功');
  } catch (err) {
    logger.error('确认提现打款失败:', err);
    return error(res, '确认提现打款失败');
  }
});

// ==================== 客户端提现API ====================

// 获取提现配置
router.get('/config', verifyToken, async (req, res) => {
  let connection;

  try {
    connection = await mysql.createConnection(dbConfig);

    console.log('⚙️ 获取提现配置');

    // 查询提现配置
    const [configs] = await connection.execute(`
      SELECT config_key, config_value, config_type
      FROM withdraw_config
      WHERE status = 1
    `);

    // 转换配置格式
    const configMap = {};
    configs.forEach(config => {
      let value = config.config_value;

      // 根据类型转换值
      if (config.config_type === 'number') {
        value = parseFloat(value);
      } else if (config.config_type === 'boolean') {
        value = value === 'true';
      } else if (config.config_type === 'json') {
        try {
          value = JSON.parse(value);
        } catch (e) {
          console.error('JSON解析失败:', config.config_key, value);
        }
      }

      configMap[config.config_key] = value;
    });

    return success(res, {
      min_amount: configMap.min_withdraw_amount || 10,
      max_amount: configMap.max_withdraw_amount || 50000,
      daily_limit: configMap.daily_withdraw_limit || 100000,
      wechat_fee_rate: configMap.wechat_fee_rate || 0.006,
      bank_fee_rate: configMap.bank_fee_rate || 0.001,
      wechat_min_fee: configMap.wechat_min_fee || 0.1,
      bank_min_fee: configMap.bank_min_fee || 2,
      auto_audit_enabled: configMap.auto_audit_enabled || false,
      auto_audit_limit: configMap.auto_audit_limit || 1000,
      working_hours: configMap.working_hours || { start: "09:00", end: "18:00" },
      weekend_process: configMap.weekend_process || false,
      notification_enabled: configMap.notification_enabled || true
    }, '获取提现配置成功');

  } catch (err) {
    console.error('⚙️ 获取提现配置失败:', err);
    return error(res, '获取提现配置失败', 500);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
});

// 获取提现方式列表
router.get('/methods', verifyToken, async (req, res) => {
  let connection;

  try {
    connection = await mysql.createConnection(dbConfig);

    const userId = req.user.id;
    console.log('🏦 获取用户提现方式:', userId);

    // 查询微信账户
    const [wechatAccounts] = await connection.execute(`
      SELECT id, openid, nickname, real_name, is_verified, is_default, status
      FROM wechat_account
      WHERE user_id = ? AND status = 1
    `, [userId]);

    // 查询银行卡
    const [bankCards] = await connection.execute(`
      SELECT id, bank_name, bank_code, card_number_mask, card_holder,
             card_type, is_default, is_verified, status
      FROM bank_card
      WHERE user_id = ? AND status = 1
    `, [userId]);

    return success(res, {
      wechat_accounts: wechatAccounts,
      bank_cards: bankCards
    }, '获取提现方式成功');

  } catch (err) {
    console.error('🏦 获取提现方式失败:', err);
    return error(res, '获取提现方式失败', 500);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
});

// 计算提现手续费
router.post('/calculate-fee', verifyToken, async (req, res) => {
  let connection;

  try {
    const { amount, withdraw_type } = req.body;

    if (!amount || amount <= 0) {
      return error(res, '提现金额无效', 400);
    }

    if (!withdraw_type || !['wechat', 'bank_card'].includes(withdraw_type)) {
      return error(res, '提现方式无效', 400);
    }

    connection = await mysql.createConnection(dbConfig);

    console.log('💰 计算提现手续费:', amount, withdraw_type);

    // 获取费率配置
    const [configs] = await connection.execute(`
      SELECT config_key, config_value
      FROM withdraw_config
      WHERE config_key IN ('wechat_fee_rate', 'bank_fee_rate', 'wechat_min_fee', 'bank_min_fee')
      AND status = 1
    `);

    const configMap = {};
    configs.forEach(config => {
      configMap[config.config_key] = parseFloat(config.config_value);
    });

    let fee = 0;
    let feeRate = 0;

    if (withdraw_type === 'wechat') {
      feeRate = configMap.wechat_fee_rate || 0.006;
      const minFee = configMap.wechat_min_fee || 0.1;
      fee = Math.max(amount * feeRate, minFee);
    } else if (withdraw_type === 'bank_card') {
      feeRate = configMap.bank_fee_rate || 0.001;
      const minFee = configMap.bank_min_fee || 2;
      fee = Math.max(amount * feeRate, minFee);
    }

    const actualAmount = amount - fee;

    return success(res, {
      amount: parseFloat(amount.toFixed(2)),
      fee: parseFloat(fee.toFixed(2)),
      actual_amount: parseFloat(actualAmount.toFixed(2)),
      fee_rate: feeRate
    }, '计算手续费成功');

  } catch (err) {
    console.error('💰 计算手续费失败:', err);
    return error(res, '计算手续费失败', 500);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
});

// 申请提现
router.post('/apply', verifyToken, async (req, res) => {
  let connection;

  try {
    const userId = req.user.id;
    const { amount, withdraw_type, account_id, remark } = req.body;

    // 验证参数
    if (!amount || amount <= 0) {
      return error(res, '提现金额无效', 400);
    }

    if (!withdraw_type || !['wechat', 'bank_card'].includes(withdraw_type)) {
      return error(res, '提现方式无效', 400);
    }

    if (!account_id) {
      return error(res, '请选择提现账户', 400);
    }

    connection = await mysql.createConnection(dbConfig);
    await connection.beginTransaction();

    console.log('💰 申请提现:', userId, amount, withdraw_type, account_id);

    // 检查用户余额
    const [users] = await connection.execute(`
      SELECT balance FROM user WHERE id = ?
    `, [userId]);

    if (users.length === 0) {
      await connection.rollback();
      return error(res, '用户不存在', 404);
    }

    const userBalance = parseFloat(users[0].balance);
    if (userBalance < amount) {
      await connection.rollback();
      return error(res, '余额不足', 400);
    }

    // 计算手续费
    const feeResult = await calculateWithdrawFee(connection, amount, withdraw_type);
    const fee = feeResult.fee;
    const actualAmount = amount - fee;

    // 生成提现单号
    const withdrawNo = generateWithdrawNo();

    // 获取账户信息
    let accountInfo = {};
    if (withdraw_type === 'wechat') {
      const [wechatAccounts] = await connection.execute(`
        SELECT openid, nickname, real_name FROM wechat_account WHERE id = ? AND user_id = ?
      `, [account_id, userId]);

      if (wechatAccounts.length === 0) {
        await connection.rollback();
        return error(res, '微信账户不存在', 400);
      }

      accountInfo = {
        type: 'wechat',
        openid: wechatAccounts[0].openid,
        nickname: wechatAccounts[0].nickname,
        real_name: wechatAccounts[0].real_name
      };
    } else if (withdraw_type === 'bank_card') {
      const [bankCards] = await connection.execute(`
        SELECT bank_name, card_number_mask, card_holder FROM bank_card WHERE id = ? AND user_id = ?
      `, [account_id, userId]);

      if (bankCards.length === 0) {
        await connection.rollback();
        return error(res, '银行卡不存在', 400);
      }

      accountInfo = {
        type: 'bank_card',
        bank_name: bankCards[0].bank_name,
        card_number_mask: bankCards[0].card_number_mask,
        card_holder: bankCards[0].card_holder
      };
    }

    // 创建提现记录
    const [withdrawResult] = await connection.execute(`
      INSERT INTO withdraw (
        user_id, withdraw_no, amount, fee, actual_amount,
        withdraw_type, account_id, account_info, status, remark
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 0, ?)
    `, [
      userId, withdrawNo, amount, fee, actualAmount,
      withdraw_type, account_id, JSON.stringify(accountInfo), remark
    ]);

    // 扣减用户余额
    await connection.execute(`
      UPDATE user SET balance = balance - ? WHERE id = ?
    `, [amount, userId]);

    // 记录钱包交易
    await connection.execute(`
      INSERT INTO wallet_transaction (
        user_id, type, amount, balance_before, balance_after,
        related_type, related_id, title, description, transaction_no
      ) VALUES (?, 'withdraw', ?, ?, ?, 'withdraw', ?, '申请提现', ?, ?)
    `, [
      userId, -amount, userBalance, userBalance - amount,
      withdrawResult.insertId, `提现到${withdraw_type === 'wechat' ? '微信' : '银行卡'}`, withdrawNo
    ]);

    await connection.commit();

    return success(res, {
      withdraw_no: withdrawNo,
      status: 'pending_audit',
      estimated_arrival: getEstimatedArrival()
    }, '提现申请已提交');

  } catch (err) {
    if (connection) {
      await connection.rollback();
    }
    console.error('💰 申请提现失败:', err);
    return error(res, '申请提现失败', 500);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
});

// 获取提现记录
router.get('/records', verifyToken, async (req, res) => {
  let connection;

  try {
    const userId = req.user.id;
    const { page = 1, limit = 10, status } = req.query;

    connection = await mysql.createConnection(dbConfig);

    console.log('📋 获取提现记录:', userId, page, limit, status);

    const offset = (parseInt(page) - 1) * parseInt(limit);

    // 构建查询条件
    let whereClause = 'WHERE user_id = ?';
    let queryParams = [userId];

    if (status !== undefined && status !== '') {
      whereClause += ' AND status = ?';
      queryParams.push(status);
    }

    // 查询总数
    const [countResult] = await connection.execute(`
      SELECT COUNT(*) as total FROM withdraw ${whereClause}
    `, queryParams);

    // 查询记录
    const [records] = await connection.execute(`
      SELECT id, withdraw_no, amount, fee, actual_amount, withdraw_type,
             account_info, status, apply_time, complete_time, failure_reason, remark,
             created_at, updated_at
      FROM withdraw
      ${whereClause}
      ORDER BY created_at DESC
      LIMIT ? OFFSET ?
    `, [...queryParams, parseInt(limit), parseInt(offset)]);

    // 格式化记录
    const formattedRecords = records.map(record => ({
      ...record,
      account_info: JSON.parse(record.account_info || '{}'),
      apply_time: record.apply_time || record.created_at,
      amount: parseFloat(record.amount),
      fee: parseFloat(record.fee),
      actual_amount: parseFloat(record.actual_amount)
    }));

    return success(res, {
      list: formattedRecords,
      total: countResult[0].total,
      page: parseInt(page),
      limit: parseInt(limit)
    }, '获取提现记录成功');

  } catch (err) {
    console.error('📋 获取提现记录失败:', err);
    return error(res, '获取提现记录失败', 500);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
});

// 辅助函数：计算提现手续费
async function calculateWithdrawFee(connection, amount, withdrawType) {
  const [configs] = await connection.execute(`
    SELECT config_key, config_value
    FROM withdraw_config
    WHERE config_key IN ('wechat_fee_rate', 'bank_fee_rate', 'wechat_min_fee', 'bank_min_fee')
    AND status = 1
  `);

  const configMap = {};
  configs.forEach(config => {
    configMap[config.config_key] = parseFloat(config.config_value);
  });

  let fee = 0;

  if (withdrawType === 'wechat') {
    const feeRate = configMap.wechat_fee_rate || 0.006;
    const minFee = configMap.wechat_min_fee || 0.1;
    fee = Math.max(amount * feeRate, minFee);
  } else if (withdrawType === 'bank_card') {
    const feeRate = configMap.bank_fee_rate || 0.001;
    const minFee = configMap.bank_min_fee || 2;
    fee = Math.max(amount * feeRate, minFee);
  }

  return { fee };
}

// 辅助函数：生成提现单号
function generateWithdrawNo() {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const day = String(now.getDate()).padStart(2, '0');
  const hour = String(now.getHours()).padStart(2, '0');
  const minute = String(now.getMinutes()).padStart(2, '0');
  const second = String(now.getSeconds()).padStart(2, '0');
  const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');

  return `WD${year}${month}${day}${hour}${minute}${second}${random}`;
}

// 辅助函数：获取预计到账时间
function getEstimatedArrival() {
  const now = new Date();
  const tomorrow = new Date(now.getTime() + 24 * 60 * 60 * 1000);

  // 如果是周五，延迟到下周一
  if (now.getDay() === 5) {
    tomorrow.setTime(tomorrow.getTime() + 2 * 24 * 60 * 60 * 1000);
  }
  // 如果是周六，延迟到下周一
  else if (now.getDay() === 6) {
    tomorrow.setTime(tomorrow.getTime() + 1 * 24 * 60 * 60 * 1000);
  }

  // 设置到账时间为下午6点
  tomorrow.setHours(18, 0, 0, 0);

  return tomorrow.toISOString().slice(0, 19).replace('T', ' ');
}

module.exports = router;