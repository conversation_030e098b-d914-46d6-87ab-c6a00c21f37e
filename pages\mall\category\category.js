// pages/mall/category/category.js
const app = getApp()
const API = require('../../../config/api')
const { request } = require('../../../utils/request')
const { showToast, formatImageUrl } = require('../../../utils/util')
const ImageService = require('../../../utils/imageService')

Page({
  /**
   * 页面的初始数据
   */
  data: {
    categories: [],
    currentCategory: null,
    categoryGoods: [],
    loading: true,
    searchValue: '',
    page: 1,
    limit: 10,
    hasMore: true,
    showAd: false  // 控制广告显示，默认不显示
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    this.fetchCategories()
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    // 如果有当前分类，刷新商品列表
    if (this.data.currentCategory) {
      this.fetchCategoryGoods(this.data.currentCategory.id)
    }
  },

  /**
   * 获取商品分类列表
   */
  fetchCategories: function () {
    console.log('fetchCategories调用');
    this.setData({ loading: true })

    const promise = request({
      url: API.goods.categories,
      method: 'GET'
    }).then(res => {
      console.log('分类API响应:', res);

      if ((res.code === 0 || res.status === 'success') && res.data) {
        // 支持多种数据格式
        let categories = [];
        if (Array.isArray(res.data)) {
          categories = res.data;
        } else if (res.data.list && Array.isArray(res.data.list)) {
          categories = res.data.list;
        } else if (res.data.categories && Array.isArray(res.data.categories)) {
          categories = res.data.categories;
        }

        console.log('解析到的分类数据:', categories);
        this.setData({ categories, loading: false })

        // 如果有分类，默认选中第一个
        if (categories.length > 0) {
          this.setData({ currentCategory: categories[0] })
          this.fetchCategoryGoods(categories[0].id)
        } else {
          console.log('没有分类数据');
        }
      } else {
        console.error('分类API响应格式错误:', res);
        showToast('获取分类失败')
        this.setData({ loading: false })
      }
    }).catch(err => {
      console.error('获取分类失败', err)
      showToast('网络错误，请检查网络连接')
      this.setData({
        categories: [],
        loading: false,
        currentCategory: null
      });
    });

    console.log('fetchCategories返回Promise:', promise);
    return promise;
  },

  /**
   * 获取分类商品列表
   */
  fetchCategoryGoods: function (categoryId, isRefresh = true) {
    console.log('fetchCategoryGoods调用，参数:', { categoryId, isRefresh, hasMore: this.data.hasMore });

    if (isRefresh) {
      this.setData({
        loading: true,
        page: 1,
        hasMore: true
      })
    }

    if (!this.data.hasMore) {
      console.log('没有更多数据，返回resolved Promise');
      return Promise.resolve()
    }

    const promise = request({
      url: API.goods.list,
      method: 'GET',
      data: {
        categoryId: categoryId,
        keyword: this.data.searchValue,
        page: this.data.page,
        limit: this.data.limit
      }
    }).then(res => {
      console.log('商品API响应:', res);

      if ((res.code === 0 || res.status === 'success') && res.data) {
        // 支持多种数据格式
        let newGoods = [];
        if (Array.isArray(res.data)) {
          newGoods = res.data;
        } else if (res.data.list && Array.isArray(res.data.list)) {
          newGoods = res.data.list;
        } else if (res.data.goods && Array.isArray(res.data.goods)) {
          newGoods = res.data.goods;
        }

        console.log('解析到的商品数据:', newGoods);

        // 处理图片URL，使用标准的格式化函数
        const processedGoods = newGoods.map((item, index) => {
          console.log(`商品${index} 原始图片URL:`, item.cover);
          if (item.cover) {
            const originalUrl = item.cover;
            item.cover = formatImageUrl(item.cover);
            console.log(`商品${index} 格式化后图片URL:`, originalUrl, '->', item.cover);
          } else {
            console.log(`商品${index} 没有图片URL，使用默认占位图`);
            item.cover = '/assets/images/goods-placeholder.svg';
          }
          return item;
        });

        const categoryGoods = isRefresh
          ? processedGoods
          : [...this.data.categoryGoods, ...processedGoods]

        this.setData({
          categoryGoods,
          hasMore: newGoods.length === this.data.limit,
          page: this.data.page + 1,
          loading: false
        })
      } else {
        console.error('商品API响应格式错误:', res);
        showToast('获取商品失败')
        this.setData({ loading: false })
      }
    }).catch(err => {
      console.error('获取商品失败', err)
      showToast('网络错误，请检查网络连接')
      this.setData({
        loading: false,
        hasMore: false
      });
    });

    console.log('fetchCategoryGoods返回Promise:', promise);
    return promise;
  },

  /**
   * 切换分类
   */
  onCategoryTap: function (e) {
    const categoryId = e.currentTarget.dataset.id
    const category = this.data.categories.find(item => item.id === categoryId)
    
    if (category && this.data.currentCategory?.id !== categoryId) {
      this.setData({ currentCategory: category })
      this.fetchCategoryGoods(categoryId)
    }
  },

  /**
   * 商品搜索
   */
  onSearch: function (e) {
    this.setData({ searchValue: e.detail.value })
    if (this.data.currentCategory) {
      this.fetchCategoryGoods(this.data.currentCategory.id)
    }
  },

  /**
   * 点击商品项
   */
  onGoodsTap: function (e) {
    const goodsId = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/mall/goods/goods?id=${goodsId}`
    })
  },

  /**
   * 加载更多商品
   */
  loadMoreGoods: function () {
    if (this.data.hasMore && this.data.currentCategory) {
      this.fetchCategoryGoods(this.data.currentCategory.id, false)
    }
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {
    try {
      if (this.data.currentCategory) {
        const promise = this.fetchCategoryGoods(this.data.currentCategory.id);
        if (promise && typeof promise.finally === 'function') {
          promise.finally(() => {
            wx.stopPullDownRefresh()
          })
        } else {
          console.error('fetchCategoryGoods没有返回Promise对象');
          wx.stopPullDownRefresh()
        }
      } else {
        const promise = this.fetchCategories();
        if (promise && typeof promise.finally === 'function') {
          promise.finally(() => {
            wx.stopPullDownRefresh()
          })
        } else {
          console.error('fetchCategories没有返回Promise对象');
          wx.stopPullDownRefresh()
        }
      }
    } catch (error) {
      console.error('下拉刷新出错:', error);
      wx.stopPullDownRefresh()
    }
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {
    this.loadMoreGoods()
  },

  /**
   * 广告加载成功
   */
  onAdLoad: function () {
    console.log('广告加载成功')
  },

  /**
   * 广告加载失败
   */
  onAdError: function (err) {
    console.error('广告加载失败', err)
    // 广告加载失败时隐藏广告容器
    const adContainer = wx.createSelectorQuery().select('.ad-container')
    if (adContainer) {
      adContainer.boundingClientRect().exec((res) => {
        if (res[0]) {
          // 隐藏广告容器
          this.setData({
            showAd: false
          })
        }
      })
    }
  },

  /**
   * 图片加载失败处理
   */
  onImageError: function (e) {
    const index = e.currentTarget.dataset.index
    console.error('图片加载失败，索引:', index, '错误:', e.detail)

    // 使用默认占位图替换失败的图片
    const categoryGoods = [...this.data.categoryGoods]  // 创建副本避免直接修改
    if (categoryGoods && categoryGoods[index]) {
      const originalUrl = categoryGoods[index].cover
      console.log(`图片加载失败: ${originalUrl}，使用占位图替换`)

      // 记录图片错误到统计中
      ImageService.recordImageError(originalUrl)

      // 避免无限循环，如果已经是占位图了就不再替换
      if (categoryGoods[index].imageError) {
        console.log('占位图也加载失败，使用base64占位图')
        // 使用base64编码的简单占位图
        categoryGoods[index].cover = ImageService.placeholders.base64
        categoryGoods[index].useFallback = true
      } else {
        // 首次失败，尝试使用SVG占位图
        categoryGoods[index].cover = ImageService.placeholders.svg
        categoryGoods[index].imageError = true  // 标记图片加载失败
      }

      this.setData({
        categoryGoods: categoryGoods
      })

      // 显示用户友好的提示（可选）
      if (originalUrl && originalUrl.includes('/uploads/')) {
        console.warn('服务器图片资源不可用，建议检查服务器配置')
      }
    }
  },

  /**
   * 获取base64占位图
   */
  getBase64PlaceholderImage: function() {
    // 返回一个简单的灰色占位图的base64编码
    return 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjVmNWY1Ii8+CiAgPHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPuWVhuWTgeWbvueJhzwvdGV4dD4KPC9zdmc+'
  }
}) 