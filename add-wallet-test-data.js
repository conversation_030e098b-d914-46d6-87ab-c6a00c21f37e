const mysql = require('mysql2/promise');

async function addWalletTestData() {
  const connection = await mysql.createConnection({
    host: 'localhost',
    user: 'root',
    password: 'wo587129955',
    database: 'mall'
  });
  
  try {
    console.log('🔧 添加钱包测试数据...\n');
    
    // 添加更多测试用户
    console.log('1️⃣ 添加测试用户...');
    await connection.execute(`
      INSERT IGNORE INTO users (nickname, phone, balance, total_income, role, status) VALUES
      ('张三', '13800138001', 1500.50, 2000.00, 'user', 1),
      ('李四', '13800138002', 2300.75, 3500.00, 'user', 1),
      ('王五', '13800138003', 500.00, 800.00, 'user', 1),
      ('赵六', '13800138004', 0.00, 0.00, 'user', 1),
      ('团队长A', '13800138005', 5000.00, 8000.00, 'team_leader', 1),
      ('团队长B', '13800138006', 3200.00, 4500.00, 'team_leader', 1),
      ('普通用户A', '13800138007', 800.00, 1200.00, 'user', 1),
      ('普通用户B', '13800138008', 1200.00, 1800.00, 'user', 1),
      ('普通用户C', '13800138009', 300.00, 600.00, 'user', 1),
      ('VIP用户', '13800138010', 10000.00, 15000.00, 'user', 1)
    `);
    
    console.log('✅ 测试用户添加成功');
    
    // 获取所有用户
    const [users] = await connection.execute('SELECT id, nickname, balance, total_income FROM users WHERE balance > 0');
    console.log(`📊 找到 ${users.length} 个有余额的用户`);
    
    // 为每个用户创建交易记录
    console.log('\n2️⃣ 创建交易记录...');
    
    for (const user of users) {
      const userId = user.id;
      const currentBalance = parseFloat(user.balance);
      const totalIncome = parseFloat(user.total_income);
      
      if (currentBalance > 0) {
        // 创建收入记录
        const incomeAmount1 = Math.round(currentBalance * 0.6 * 100) / 100;
        const incomeAmount2 = Math.round(currentBalance * 0.4 * 100) / 100;
        
        await connection.execute(`
          INSERT INTO wallet_transactions (user_id, type, amount, balance_before, balance_after, business_type, description, created_at) VALUES
          (?, 'income', ?, 0.00, ?, 'wifi_share', '分享WiFi收益', DATE_SUB(NOW(), INTERVAL 7 DAY)),
          (?, 'income', ?, ?, ?, 'referral', '推荐奖励', DATE_SUB(NOW(), INTERVAL 3 DAY))
        `, [
          userId, incomeAmount1, incomeAmount1,
          userId, incomeAmount2, incomeAmount1, currentBalance
        ]);
        
        // 如果用户有支出（总收入大于当前余额）
        if (totalIncome > currentBalance) {
          const expenseAmount = Math.round((totalIncome - currentBalance) * 100) / 100;
          await connection.execute(`
            INSERT INTO wallet_transactions (user_id, type, amount, balance_before, balance_after, business_type, description, created_at) VALUES
            (?, 'expense', ?, ?, ?, 'withdraw', '提现', DATE_SUB(NOW(), INTERVAL 1 DAY))
          `, [userId, expenseAmount, totalIncome, currentBalance]);
        }
        
        console.log(`  - 为用户 ${user.nickname} 创建交易记录`);
      }
    }
    
    console.log('✅ 交易记录创建成功');
    
    // 显示最终统计
    console.log('\n3️⃣ 最终统计...');
    
    const [finalUsers] = await connection.execute(`
      SELECT 
        id, nickname, phone, balance, total_income, role, status,
        created_at
      FROM users 
      ORDER BY balance DESC
      LIMIT 10
    `);
    
    console.log('📋 用户钱包列表（按余额排序）:');
    finalUsers.forEach(user => {
      console.log(`  - ${user.nickname}: ¥${user.balance} (总收入: ¥${user.total_income}) [${user.role}]`);
    });
    
    const [transactionCount] = await connection.execute('SELECT COUNT(*) as count FROM wallet_transactions');
    console.log(`\n💰 总交易记录数: ${transactionCount[0].count}`);
    
    const [totalBalance] = await connection.execute('SELECT SUM(balance) as total FROM users');
    console.log(`💵 平台总余额: ¥${totalBalance[0].total || 0}`);
    
    console.log('\n✅ 钱包测试数据添加完成！');
    
  } catch (error) {
    console.error('❌ 错误:', error.message);
  } finally {
    await connection.end();
  }
}

addWalletTestData();
