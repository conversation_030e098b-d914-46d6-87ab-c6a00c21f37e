{"miniprogramRoot": "./", "projectname": "wifi-share-miniapp", "description": "WiFi共享小程序", "appid": "wxd8a4b4c5e6f7g8h9", "setting": {"urlCheck": false, "es6": true, "enhance": true, "postcss": true, "preloadBackgroundData": false, "minified": true, "newFeature": true, "coverView": true, "nodeModules": false, "autoAudits": false, "showShadowRootInWxmlPanel": true, "scopeDataCheck": false, "uglifyFileName": false, "checkInvalidKey": true, "checkSiteMap": true, "uploadWithSourceMap": true, "compileHotReLoad": false, "lazyloadPlaceholderEnable": false, "useMultiFrameRuntime": true, "useApiHook": true, "useApiHostProcess": true, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "enableEngineNative": false, "useIsolateContext": true, "userConfirmedBundleSwitch": false, "packNpmManually": false, "packNpmRelationList": [], "minifyWXSS": true, "disableUseStrict": false, "minifyWXML": true, "showES6CompileOption": false, "useCompilerPlugins": false}, "compileType": "miniprogram", "libVersion": "3.8.11", "srcMiniprogramRoot": "./", "packOptions": {"ignore": [{"type": "file", "value": "README.md"}, {"type": "file", "value": "*.md"}, {"type": "folder", "value": "node_modules"}, {"type": "folder", "value": ".git"}, {"type": "folder", "value": "wifi-share-server"}, {"type": "folder", "value": "src"}]}, "condition": {"search": {"list": []}, "conversation": {"list": []}, "game": {"list": []}, "plugin": {"list": []}, "gamePlugin": {"list": []}, "miniprogram": {"list": []}}}