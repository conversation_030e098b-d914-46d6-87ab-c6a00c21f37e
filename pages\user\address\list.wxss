/* pages/user/address/list.wxss */
.address-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 120rpx;
}

/* 地址列表样式 */
.address-list {
  padding: 20rpx;
}

.address-item {
  background-color: #ffffff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.address-info {
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 20rpx;
}

.address-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}

.contact-info {
  display: flex;
  align-items: center;
}

.name {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-right: 20rpx;
}

.phone {
  font-size: 28rpx;
  color: #666;
}

.default-tag {
  font-size: 22rpx;
  color: #ff6b00;
  border: 1px solid #ff6b00;
  padding: 2rpx 12rpx;
  border-radius: 20rpx;
}

.address-detail {
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
  margin-top: 10rpx;
}

.address-actions {
  display: flex;
  justify-content: flex-end;
  padding-top: 20rpx;
}

.action-btn {
  display: flex;
  align-items: center;
  margin-left: 30rpx;
}

.action-icon {
  font-size: 28rpx;
  margin-right: 6rpx;
  color: #666;
}

.action-text {
  font-size: 26rpx;
  color: #666;
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding-top: 100rpx;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}

.empty-text {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.empty-tip {
  font-size: 26rpx;
  color: #999;
}

/* 加载状态样式 */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding-top: 100rpx;
}

.loading-icon {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

/* 添加地址按钮样式 */
.add-address-btn {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100rpx;
  background-color: #ff6b00;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  font-weight: 500;
}

.add-icon {
  font-size: 36rpx;
  margin-right: 10rpx;
}

.add-text {
  font-size: 32rpx;
} 