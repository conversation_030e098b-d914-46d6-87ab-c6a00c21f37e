const db = require('../database');
const logger = require('../utils/logger');

/**
 * 根据ID查找用户
 * @param {number} id - 用户ID
 * @returns {Promise<Object|null>} - 用户对象或null
 */
async function findUserById(id) {
  try {
    // 检查数据库中是否存在users表或user表
    const tableCheck = await db.getOne(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = DATABASE() AND table_name IN ('users', 'user')
    `);
    
    const tableName = tableCheck ? tableCheck.table_name : 'users';
    
    logger.info(`使用表名 ${tableName} 查询用户`);
    
    const sql = `SELECT * FROM ${tableName} WHERE id = ? AND status = 1`;
    const user = await db.getOne(sql, [id]);
    
    if (user) {
      // 移除敏感信息
      delete user.password;
      logger.info(`找到用户: ID=${id}`);
    } else {
      logger.warn(`未找到用户: ID=${id}`);
    }
    
    return user || null;
  } catch (error) {
    logger.error(`查找用户失败: ${error.message}`, error);
    throw error;
  }
}

/**
 * 根据用户名查找用户
 * @param {string} username - 用户名
 * @returns {Promise<Object|null>} - 用户对象或null
 */
async function findUserByUsername(username) {
  try {
    // 检查数据库中是否存在users表或user表
    const tableCheck = await db.getOne(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = DATABASE() AND table_name IN ('users', 'user')
    `);
    
    const tableName = tableCheck ? tableCheck.table_name : 'users';
    
    const sql = `SELECT * FROM ${tableName} WHERE username = ?`;
    return await db.getOne(sql, [username]);
  } catch (error) {
    logger.error('根据用户名查找用户失败:', error);
    throw error;
  }
}

/**
 * 根据OpenID查找用户
 * @param {string} openid - 微信OpenID
 * @returns {Promise<Object|null>} - 用户对象或null
 */
async function findUserByOpenid(openid) {
  try {
    // 检查数据库中是否存在users表或user表
    const tableCheck = await db.getOne(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = DATABASE() AND table_name IN ('users', 'user')
    `);
    
    const tableName = tableCheck ? tableCheck.table_name : 'users';
    
    const sql = `SELECT * FROM ${tableName} WHERE openid = ?`;
    return await db.getOne(sql, [openid]);
  } catch (error) {
    logger.error('根据OpenID查找用户失败:', error);
    throw error;
  }
}

module.exports = {
  findUserById,
  findUserByUsername,
  findUserByOpenid
}; 