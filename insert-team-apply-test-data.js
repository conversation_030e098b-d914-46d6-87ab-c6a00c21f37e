const db = require('./src/database');
const config = require('./config/index');

async function insertTestData() {
  try {
    console.log('开始插入联盟申请测试数据...');
    
    // 首先获取一个用户ID作为申请人
    const users = await db.query('SELECT id FROM user LIMIT 1');
    
    if (!users || users.length === 0) {
      console.log('没有找到用户，请先确保有用户数据');
      return;
    }
    
    const userId = users[0].id;
    console.log(`使用用户ID: ${userId} 创建测试申请`);
    
    // 插入测试数据
    const result = await db.query(
      `INSERT INTO team_apply 
        (user_id, name, phone, area, description, status, created_at) 
       VALUES 
        (?, '测试联盟', '13800138000', '测试地区', '这是一个测试申请', 0, NOW())`,
      [userId]
    );
    
    console.log('插入结果:', result);
    console.log('测试数据插入成功，申请ID:', result.insertId);
    
  } catch (error) {
    console.error('插入测试数据失败:', error);
  } finally {
    process.exit();
  }
}

insertTestData(); 