// 测试钱包数据显示
// 这个文件用于调试钱包页面数据显示问题

const testWalletDisplay = {
  
  // 测试数据设置
  testDataSetting: function() {
    console.log('🧪 测试钱包数据设置...');
    
    // 模拟钱包页面的setData
    const mockWalletPage = {
      data: {
        balance: '0.00',
        incomeStats: {
          wifi: '0.00',
          team: '0.00', 
          ads: '0.00',
          mall: '0.00'
        }
      },
      
      setData: function(newData) {
        console.log('📝 设置新数据:', newData);
        Object.assign(this.data, newData);
        console.log('📊 当前数据状态:', this.data);
      }
    };
    
    // 测试设置示例数据
    const testData = {
      balance: '66.80',
      incomeStats: {
        wifi: '28.30',
        team: '25.00',
        ads: '13.50', 
        mall: '0.00'
      }
    };
    
    console.log('设置前的数据:', mockWalletPage.data);
    mockWalletPage.setData(testData);
    console.log('设置后的数据:', mockWalletPage.data);
    
    return mockWalletPage.data;
  },
  
  // 测试API响应解析
  testAPIResponseParsing: function() {
    console.log('🧪 测试API响应解析...');
    
    // 模拟不同格式的API响应
    const mockResponses = [
      // 格式1: 标准格式
      {
        success: true,
        data: {
          balance: '66.80',
          wifi_income: '28.30',
          team_income: '25.00',
          ad_income: '13.50',
          goods_income: '0.00'
        }
      },
      
      // 格式2: 嵌套total格式
      {
        code: 0,
        data: {
          total: {
            balance: '66.80',
            wifi_income: '28.30',
            team_income: '25.00',
            ad_income: '13.50',
            goods_income: '0.00'
          }
        }
      },
      
      // 格式3: 混合格式
      {
        code: 200,
        data: {
          balance: '66.80',
          total: {
            wifi_income: '28.30',
            team_income: '25.00'
          },
          ad_income: '13.50',
          goods_income: '0.00'
        }
      }
    ];
    
    mockResponses.forEach((res, index) => {
      console.log(`\n测试响应格式 ${index + 1}:`, res);
      
      if ((res.code === 0 || res.code === 200 || res.success === true) && res.data) {
        const data = res.data;
        const parsedData = {
          balance: data.balance || data.total?.balance || '0.00',
          incomeStats: {
            wifi: data.wifi_income || data.total?.wifi_income || '0.00',
            team: data.team_income || data.referral_income || data.total?.team_income || '0.00',
            ads: data.ad_income || data.advertisement_income || data.total?.ad_income || '0.00',
            mall: data.goods_income || data.mall_income || data.total?.goods_income || '0.00'
          }
        };
        
        console.log(`解析结果 ${index + 1}:`, parsedData);
      } else {
        console.log(`响应格式 ${index + 1} 解析失败`);
      }
    });
  },
  
  // 测试WXML数据绑定
  testWXMLDataBinding: function() {
    console.log('🧪 测试WXML数据绑定...');
    
    const mockData = {
      balance: '66.80',
      incomeStats: {
        wifi: '28.30',
        team: '25.00',
        ads: '13.50',
        mall: '0.00'
      }
    };
    
    console.log('模拟WXML数据绑定:');
    console.log(`账户余额: ¥${mockData.balance}`);
    console.log(`WiFi分享收益: ¥${mockData.incomeStats.wifi}`);
    console.log(`团队收益: ¥${mockData.incomeStats.team}`);
    console.log(`广告流量收益: ¥${mockData.incomeStats.ads}`);
    console.log(`商城订单收益: ¥${mockData.incomeStats.mall}`);
    
    // 检查数据类型
    console.log('\n数据类型检查:');
    console.log(`balance类型: ${typeof mockData.balance}`);
    console.log(`incomeStats类型: ${typeof mockData.incomeStats}`);
    console.log(`wifi类型: ${typeof mockData.incomeStats.wifi}`);
    
    return mockData;
  },
  
  // 生成钱包页面调试代码
  generateDebugCode: function() {
    console.log('🔧 生成钱包页面调试代码...');
    
    const debugCode = `
// 在钱包页面的onLoad方法中添加以下调试代码:

onLoad: function(options) {
  console.log('🔧 钱包页面加载，开始调试...');
  
  // 1. 检查初始数据
  console.log('初始数据:', this.data);
  
  // 2. 强制设置测试数据
  this.setData({
    balance: '66.80',
    incomeStats: {
      wifi: '28.30',
      team: '25.00', 
      ads: '13.50',
      mall: '0.00'
    }
  });
  
  console.log('设置测试数据后:', this.data);
  
  // 3. 检查登录状态
  this.checkLoginStatus();
},

// 在onShow方法中添加:
onShow: function() {
  console.log('🔧 页面显示，当前数据:', this.data);
  
  // 如果数据为空，强制设置测试数据
  if (this.data.balance === '0.00') {
    console.log('数据为空，设置测试数据...');
    this.setData({
      balance: '66.80',
      incomeStats: {
        wifi: '28.30',
        team: '25.00',
        ads: '13.50', 
        mall: '0.00'
      }
    });
  }
}
`;
    
    console.log(debugCode);
    return debugCode;
  },
  
  // 运行所有测试
  runAllTests: function() {
    console.log('🚀 开始运行所有钱包显示测试...\n');
    
    this.testDataSetting();
    console.log('\n' + '='.repeat(50) + '\n');
    
    this.testAPIResponseParsing();
    console.log('\n' + '='.repeat(50) + '\n');
    
    this.testWXMLDataBinding();
    console.log('\n' + '='.repeat(50) + '\n');
    
    this.generateDebugCode();
    
    console.log('\n🎉 所有测试完成！');
  }
};

// 运行测试
testWalletDisplay.runAllTests();

module.exports = testWalletDisplay;
