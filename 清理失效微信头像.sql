-- 清理失效的微信头像URL
-- 这些URL现在已经无法访问，需要清理掉

-- 查看当前有多少用户使用了失效的微信头像
SELECT 
    id, 
    nickname, 
    avatar,
    CASE 
        WHEN avatar LIKE '%thirdwx.qlogo.cn%' THEN '微信头像URL'
        WHEN avatar LIKE '%wx.qlogo.cn%' THEN '微信头像URL'
        WHEN avatar LIKE '%mmopen%' THEN '微信头像URL'
        ELSE '其他头像'
    END as avatar_type
FROM user 
WHERE avatar IS NOT NULL 
  AND avatar != ''
  AND (avatar LIKE '%thirdwx.qlogo.cn%' 
       OR avatar LIKE '%wx.qlogo.cn%' 
       OR avatar LIKE '%mmopen%');

-- 清理失效的微信头像URL
UPDATE user 
SET avatar = '', updated_at = NOW()
WHERE avatar IS NOT NULL 
  AND avatar != ''
  AND (avatar LIKE '%thirdwx.qlogo.cn%' 
       OR avatar LIKE '%wx.qlogo.cn%' 
       OR avatar LIKE '%mmopen%');

-- 查看清理后的结果
SELECT 
    COUNT(*) as total_users,
    COUNT(CASE WHEN avatar IS NOT NULL AND avatar != '' THEN 1 END) as users_with_avatar,
    COUNT(CASE WHEN avatar IS NULL OR avatar = '' THEN 1 END) as users_without_avatar
FROM user;
