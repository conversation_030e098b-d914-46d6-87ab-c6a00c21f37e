<!-- 提现页面 -->
<view class="withdraw-container">
  <!-- 可提现余额卡片 -->
  <view class="balance-card">
    <view class="balance-title">可提现余额</view>
    <view class="balance-amount">¥{{balance}}</view>
    <view class="balance-tip">提现金额将在1个工作日内到账</view>
  </view>
  
  <!-- 提现金额输入 -->
  <view class="amount-section">
    <view class="section-title">提现金额</view>
    <view class="amount-input-wrapper">
      <text class="currency-symbol">¥</text>
      <input 
        class="amount-input" 
        type="digit" 
        placeholder="请输入提现金额"
        value="{{withdrawAmount}}"
        bindinput="onAmountInput"
        maxlength="8"
      />
    </view>
    <view class="amount-tips">
      <text>最小提现金额：¥{{config.min_amount || 10}}</text>
      <text>最大提现金额：¥{{config.max_amount || 50000}}</text>
    </view>
    <view wx:if="{{feeInfo.fee > 0}}" class="fee-info">
      <text>手续费：¥{{feeInfo.fee}}</text>
      <text>实际到账：¥{{feeInfo.actual_amount}}</text>
    </view>
  </view>
  
  <!-- 提现方式选择 -->
  <view class="method-section">
    <view class="section-title">选择提现方式</view>
    
    <!-- 微信提现 -->
    <view 
      wx:if="{{wechatAccount.id}}"
      class="method-item {{selectedMethod === 'wechat' ? 'selected' : ''}}"
      bindtap="selectMethod"
      data-type="wechat"
      data-id="{{wechatAccount.id}}"
    >
      <view class="method-icon">
        <image src="/assets/icons/user.png" mode="aspectFit" />
      </view>
      <view class="method-info">
        <view class="method-name">微信</view>
        <view class="method-desc">{{wechatAccount.nickname}} ({{wechatAccount.real_name}})</view>
        <view class="method-fee">手续费：{{config.wechat_fee_rate * 100 || 0.6}}%，最低¥{{config.wechat_min_fee || 0.1}}</view>
      </view>
      <view class="method-check">
        <image wx:if="{{selectedMethod === 'wechat'}}" src="/assets/icons/user-active.png" mode="aspectFit" />
        <image wx:else src="/assets/icons/user.png" mode="aspectFit" />
      </view>
    </view>
    
    <!-- 银行卡提现 -->
    <view 
      wx:for="{{bankCards}}" 
      wx:key="id"
      class="method-item {{selectedMethod === 'bank_card' && selectedAccountId === item.id ? 'selected' : ''}}"
      bindtap="selectMethod"
      data-type="bank_card"
      data-id="{{item.id}}"
    >
      <view class="method-icon">
        <image src="/assets/icons/cart.png" mode="aspectFit" />
      </view>
      <view class="method-info">
        <view class="method-name">{{item.bank_name}}</view>
        <view class="method-desc">{{item.card_number_mask}} ({{item.card_holder}})</view>
        <view class="method-fee">手续费：{{config.bank_fee_rate * 100 || 0.1}}%，最低¥{{config.bank_min_fee || 2}}</view>
      </view>
      <view class="method-check">
        <image wx:if="{{selectedMethod === 'bank_card' && selectedAccountId === item.id}}" src="/assets/icons/cart-active.png" mode="aspectFit" />
        <image wx:else src="/assets/icons/cart.png" mode="aspectFit" />
      </view>
    </view>
    
    <!-- 添加银行卡 -->
    <view class="add-card-item" bindtap="addBankCard">
      <view class="add-icon">+</view>
      <view class="add-text">添加银行卡</view>
    </view>
  </view>
  
  <!-- 快捷金额选择 -->
  <view class="quick-amount-section">
    <view class="section-title">快捷金额</view>
    <view class="quick-amounts">
      <view 
        wx:for="{{quickAmounts}}" 
        wx:key="*this"
        class="quick-amount-item"
        bindtap="selectQuickAmount"
        data-amount="{{item}}"
      >
        ¥{{item}}
      </view>
      <view 
        class="quick-amount-item"
        bindtap="selectAllBalance"
      >
        全部
      </view>
    </view>
  </view>
  
  <!-- 提现说明 -->
  <view class="notice-section">
    <view class="notice-title">提现说明</view>
    <view class="notice-list">
      <text>• 提现申请提交后，将在1个工作日内处理</text>
      <text>• 微信提现手续费为{{config.wechat_fee_rate * 100 || 0.6}}%，最低¥{{config.wechat_min_fee || 0.1}}</text>
      <text>• 银行卡提现手续费为{{config.bank_fee_rate * 100 || 0.1}}%，最低¥{{config.bank_min_fee || 2}}</text>
      <text>• 工作日9:00-18:00处理提现申请</text>
      <text>• 节假日顺延至下一工作日处理</text>
      <text>• 提现金额不能超过当前可用余额</text>
    </view>
  </view>
  
  <!-- 提现按钮 -->
  <view class="submit-section">
    <button 
      class="submit-btn {{canSubmit ? 'active' : 'disabled'}}"
      bindtap="submitWithdraw"
      disabled="{{!canSubmit}}"
      loading="{{submitting}}"
    >
      {{submitting ? '提交中...' : '立即提现'}}
    </button>
  </view>
</view>

<!-- 确认提现弹窗 -->
<view wx:if="{{showConfirmModal}}" class="modal-overlay" bindtap="hideConfirmModal">
  <view class="confirm-modal" catchtap="">
    <view class="modal-title">确认提现</view>
    <view class="modal-content">
      <view class="confirm-item">
        <text class="label">提现方式：</text>
        <text class="value">{{selectedMethod === 'wechat' ? '微信' : selectedBankCard.bank_name}}</text>
      </view>
      <view class="confirm-item">
        <text class="label">提现金额：</text>
        <text class="value">¥{{withdrawAmount}}</text>
      </view>
      <view class="confirm-item">
        <text class="label">手续费：</text>
        <text class="value">¥{{feeInfo.fee}}</text>
      </view>
      <view class="confirm-item">
        <text class="label">实际到账：</text>
        <text class="value highlight">¥{{feeInfo.actual_amount}}</text>
      </view>
    </view>
    <view class="modal-buttons">
      <button class="cancel-btn" bindtap="hideConfirmModal">取消</button>
      <button class="confirm-btn" bindtap="confirmWithdraw">确认提现</button>
    </view>
  </view>
</view>
