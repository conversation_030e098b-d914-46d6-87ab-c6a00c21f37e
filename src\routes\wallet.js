const express = require('express');
const router = express.Router();
const { verifyToken } = require('../middlewares/auth');
const { success, error } = require('../utils/response');
const db = require('../database');


/**
 * 钱包管理路由
 */

// 获取用户钱包列表
router.get('/list', verifyToken, async (req, res) => {
  try {
    // 验证管理员权限
    if (req.user.role !== 'admin') {
      return error(res, '无权限访问', 403);
    }

    const { page = 1, limit = 20, nickname = '', phone = '', min_balance = '', max_balance = '' } = req.query;
    const pageNum = parseInt(page);
    const limitNum = parseInt(limit);
    const offset = (pageNum - 1) * limitNum;

    let whereClause = '1=1';
    const params = [];

    if (nickname) {
      whereClause += ' AND u.nickname LIKE ?';
      params.push(`%${nickname}%`);
    }

    if (phone) {
      whereClause += ' AND u.phone LIKE ?';
      params.push(`%${phone}%`);
    }

    if (min_balance !== '') {
      whereClause += ' AND u.balance >= ?';
      params.push(parseFloat(min_balance));
    }

    if (max_balance !== '') {
      whereClause += ' AND u.balance <= ?';
      params.push(parseFloat(max_balance));
    }

    // 查询用户钱包信息
    const query = `
      SELECT
        u.id, u.nickname, u.phone, u.avatar, u.balance,
        u.status, u.created_at, u.updated_at,
        COALESCE(wt_stats.transaction_count, 0) as transaction_count,
        COALESCE(wt_stats.total_income_amount, 0) as total_income_amount,
        COALESCE(wt_stats.total_expense_amount, 0) as total_expense_amount
      FROM user u
      LEFT JOIN (
        SELECT
          user_id,
          COUNT(*) as transaction_count,
          SUM(amount) as total_income_amount,
          0 as total_expense_amount
        FROM profit_log
        GROUP BY user_id
      ) wt_stats ON u.id = wt_stats.user_id
      WHERE ${whereClause}
      ORDER BY u.balance DESC
      LIMIT ${limitNum} OFFSET ${offset}
    `;

    // 查询总数
    const countQuery = `
      SELECT COUNT(DISTINCT u.id) as total
      FROM user u
      WHERE ${whereClause}
    `;

    const [wallets] = await db.query(query, params);
    const [countResult] = await db.query(countQuery, params);
    const total = countResult && countResult[0] ? countResult[0].total : 0;

    success(res, '获取钱包列表成功', {
      list: wallets,
      total,
      page: pageNum,
      limit: limitNum
    });
  } catch (err) {
    console.error('获取钱包列表失败:', err);
    error(res, '获取钱包列表失败');
  }
});

// 获取用户钱包详情
router.get('/detail/:userId', verifyToken, async (req, res) => {
  try {
    // 验证管理员权限
    if (req.user.role !== 'admin') {
      return error(res, '无权限访问', 403);
    }

    const { userId } = req.params;
    
    if (!userId || isNaN(parseInt(userId))) {
      return error(res, '无效的用户ID', 400);
    }

    // 查询用户基本信息
    const userQuery = `
      SELECT
        id, nickname, phone, avatar, balance,
        status, created_at, updated_at
      FROM user
      WHERE id = ?
    `;

    const [users] = await db.query(userQuery, [userId]);
    
    if (users.length === 0) {
      return error(res, '用户不存在', 404);
    }

    // 查询交易统计
    const statsQuery = `
      SELECT 
        COUNT(*) as total_transactions,
        SUM(CASE WHEN type = 'income' THEN amount ELSE 0 END) as total_income,
        SUM(CASE WHEN type = 'expense' THEN amount ELSE 0 END) as total_expense,
        COUNT(CASE WHEN type = 'income' THEN 1 END) as income_count,
        COUNT(CASE WHEN type = 'expense' THEN 1 END) as expense_count
      FROM balance_transactions
      WHERE user_id = ?
    `;

    const [stats] = await db.query(statsQuery, [userId]);

    // 查询最近交易记录
    const transactionsQuery = `
      SELECT 
        id, type, amount, balance_before, balance_after, description, source_type,
        created_at
      FROM balance_transactions
      WHERE user_id = ?
      ORDER BY created_at DESC
      LIMIT 10
    `;

    const [recentTransactions] = await db.query(transactionsQuery, [userId]);

    success(res, '获取钱包详情成功', {
      user: users[0],
      stats: stats[0],
      recent_transactions: recentTransactions
    });
  } catch (err) {
    console.error('获取钱包详情失败:', err);
    error(res, '获取钱包详情失败');
  }
});

// 调整用户余额
router.post('/adjust/:userId', verifyToken, async (req, res) => {
  try {
    // 验证管理员权限
    if (req.user.role !== 'admin') {
      return error(res, '无权限访问', 403);
    }

    const { userId } = req.params;
    const { type, amount, description = '' } = req.body;
    
    if (!userId || isNaN(parseInt(userId))) {
      return error(res, '无效的用户ID', 400);
    }

    if (!type || !['increase', 'decrease'].includes(type)) {
      return error(res, '无效的调整类型', 400);
    }

    if (!amount || isNaN(parseFloat(amount)) || parseFloat(amount) <= 0) {
      return error(res, '无效的调整金额', 400);
    }

    const adjustAmount = parseFloat(amount);
    const adminId = req.user.userId;

    // 开始事务
    await db.query('START TRANSACTION');

    try {
      // 查询用户当前余额
      const userQuery = 'SELECT balance FROM user WHERE id = ?';
      const [users] = await db.query(userQuery, [userId]);
      
      if (users.length === 0) {
        await db.query('ROLLBACK');
        return error(res, '用户不存在', 404);
      }

      const currentBalance = parseFloat(users[0].balance);
      let newBalance;
      let transactionType;
      let transactionDescription;

      if (type === 'increase') {
        newBalance = currentBalance + adjustAmount;
        transactionType = 'income';
        transactionDescription = description || `管理员增加余额 ${adjustAmount} 元`;
      } else {
        if (currentBalance < adjustAmount) {
          await db.query('ROLLBACK');
          return error(res, '用户余额不足', 400);
        }
        newBalance = currentBalance - adjustAmount;
        transactionType = 'expense';
        transactionDescription = description || `管理员减少余额 ${adjustAmount} 元`;
      }

      // 更新用户余额
      const updateQuery = 'UPDATE user SET balance = ?, updated_at = NOW() WHERE id = ?';
      await db.query(updateQuery, [newBalance, userId]);

      // 记录交易
      const transactionQuery = `
        INSERT INTO balance_transactions
        (user_id, type, amount, balance_before, balance_after, description, source_type, created_at)
        VALUES (?, ?, ?, ?, ?, ?, 'admin_adjust', NOW())
      `;
      
      await db.query(transactionQuery, [
        userId,
        transactionType,
        adjustAmount,
        currentBalance,
        newBalance,
        transactionDescription
      ]);

      // 提交事务
      await db.query('COMMIT');

      success(res, '余额调整成功', {
        old_balance: currentBalance,
        new_balance: newBalance,
        adjust_amount: adjustAmount,
        adjust_type: type
      });
    } catch (err) {
      await db.query('ROLLBACK');
      throw err;
    }
  } catch (err) {
    console.error('调整余额失败:', err);
    error(res, '调整余额失败');
  }
});

// 获取交易记录
router.get('/transactions/:userId', verifyToken, async (req, res) => {
  try {
    // 验证管理员权限
    if (req.user.role !== 'admin') {
      return error(res, '无权限访问', 403);
    }

    const { userId } = req.params;
    const { page = 1, limit = 20, type = '', business_type = '', source_type = '', start_date = '', end_date = '' } = req.query;
    
    if (!userId || isNaN(parseInt(userId))) {
      return error(res, '无效的用户ID', 400);
    }

    const pageNum = parseInt(page);
    const limitNum = parseInt(limit);
    const offset = (pageNum - 1) * limitNum;

    let whereClause = 'pl.user_id = ?';
    const params = [userId];

    if (type) {
      // 由于实际表中没有status字段，所有记录都视为income
      if (type === 'expense') {
        // 如果查询支出，返回空结果
        whereClause += ' AND 1 = 0';
      }
    }

    // 兼容前端传递的business_type参数，映射到source_type
    const sourceTypeValue = business_type || source_type;
    if (sourceTypeValue) {
      // 映射source_type值
      let sourceTypeNum = 0;
      if (sourceTypeValue === 'wifi_share') sourceTypeNum = 1;
      else if (sourceTypeValue === 'goods_sale') sourceTypeNum = 2;
      else if (sourceTypeValue === 'advertisement') sourceTypeNum = 3;

      if (sourceTypeNum > 0) {
        whereClause += ' AND pl.source_type = ?';
        params.push(sourceTypeNum);
      }
    }

    if (start_date) {
      whereClause += ' AND DATE(pl.created_at) >= ?';
      params.push(start_date);
    }

    if (end_date) {
      whereClause += ' AND DATE(pl.created_at) <= ?';
      params.push(end_date);
    }

    // 查询交易记录 - 基于分润记录表
    const query = `
      SELECT
        pl.id,
        'income' as type,
        pl.amount,
        0 as balance_before,
        0 as balance_after,
        CONCAT('分润收益 - 来源类型:', pl.source_type, ' - ', COALESCE(pl.remark, '无备注')) as description,
        CASE
          WHEN pl.source_type = 1 THEN 'wifi_share'
          WHEN pl.source_type = 2 THEN 'goods_sale'
          WHEN pl.source_type = 3 THEN 'advertisement'
          ELSE 'other'
        END as source_type,
        pl.created_at,
        'profit_log' as table_source
      FROM profit_log pl
      LEFT JOIN user u ON pl.user_id = u.id
      WHERE ${whereClause}
      ORDER BY pl.created_at DESC
      LIMIT ? OFFSET ?
    `;

    // 查询总数 - 基于分润记录表
    const countQuery = `
      SELECT COUNT(*) as total
      FROM profit_log pl
      LEFT JOIN user u ON pl.user_id = u.id
      WHERE ${whereClause}
    `;

    const [transactions] = await db.query(query, [...params, limitNum, offset]);
    const [countResult] = await db.query(countQuery, params);
    const total = countResult[0].total;

    success(res, '获取交易记录成功', {
      list: transactions,
      total,
      page: pageNum,
      limit: limitNum
    });
  } catch (err) {
    console.error('获取交易记录失败:', err);
    error(res, '获取交易记录失败');
  }
});

// 获取钱包统计
router.get('/stats', verifyToken, async (req, res) => {
  try {
    // 验证管理员权限
    if (req.user.role !== 'admin') {
      return error(res, '无权限访问', 403);
    }

    // 用户余额统计
    const balanceStatsQuery = `
      SELECT 
        COUNT(*) as total_users,
        SUM(balance) as total_balance,
        AVG(balance) as avg_balance,
        MAX(balance) as max_balance,
        COUNT(CASE WHEN balance > 0 THEN 1 END) as users_with_balance
      FROM user
    `;

    // 交易统计 - 基于分润记录表
    const transactionStatsQuery = `
      SELECT
        COUNT(*) as total_transactions,
        SUM(amount) as total_income,
        0 as total_expense,
        COUNT(*) as income_count,
        0 as expense_count,
        COUNT(CASE WHEN DATE(created_at) = CURDATE() THEN 1 END) as today_transactions
      FROM profit_log
    `;

    // 业务类型统计 - 基于分润记录表
    const businessStatsQuery = `
      SELECT
        CASE
          WHEN source_type = 1 THEN 'wifi_share'
          WHEN source_type = 2 THEN 'goods_sale'
          WHEN source_type = 3 THEN 'advertisement'
          ELSE 'other'
        END as source_type,
        COUNT(*) as transaction_count,
        SUM(amount) as total_amount
      FROM profit_log
      WHERE source_type IS NOT NULL
      GROUP BY source_type
      ORDER BY total_amount DESC
    `;

    const [balanceStats] = await db.query(balanceStatsQuery);
    const [transactionStats] = await db.query(transactionStatsQuery);
    const [businessStats] = await db.query(businessStatsQuery);

    success(res, '获取钱包统计成功', {
      balance: balanceStats[0],
      transactions: transactionStats[0],
      business_types: businessStats
    });
  } catch (err) {
    console.error('获取钱包统计失败:', err);
    error(res, '获取钱包统计失败');
  }
});

module.exports = router;
