<!--pages/mall/order/payment/payment.wxml-->
<view class="payment-container">
  <!-- 支付金额 -->
  <view class="amount-section">
    <view class="amount-title">支付金额</view>
    <view class="amount-value">¥{{paymentAmount}}</view>
  </view>

  <!-- 订单信息 -->
  <view class="order-section">
    <view class="section-title">订单信息</view>
    <view class="order-info">
      <view class="info-item">
        <text class="info-label">订单编号:</text>
        <text class="info-value">{{orderInfo.orderNo || orderId}}</text>
      </view>
      <view class="info-item">
        <text class="info-label">商品名称:</text>
        <text class="info-value">{{orderInfo.goodsNames || '商品购买'}}</text>
      </view>
      <view class="info-item">
        <text class="info-label">下单时间:</text>
        <text class="info-value">{{orderInfo.createTime || ''}}</text>
      </view>
    </view>
  </view>

  <!-- 支付方式 -->
  <view class="payment-method-section">
    <view class="section-title">支付方式</view>
    <view class="payment-methods">
      <view class="payment-method {{paymentMethod === 'wechat' ? 'active' : ''}}" bindtap="onSelectPayment" data-method="wechat">
        <view class="method-icon">💰</view>
        <view class="method-info">
          <view class="method-name">微信支付</view>
        </view>
        <view class="method-check" wx:if="{{paymentMethod === 'wechat'}}">
          <text class="check-icon">✓</text>
        </view>
      </view>
      
      <view class="payment-method {{paymentMethod === 'balance' ? 'active' : ''}}" bindtap="onSelectPayment" data-method="balance">
        <view class="method-icon">💳</view>
        <view class="method-info">
          <view class="method-name">余额支付</view>
          <view class="method-desc">可用: ¥{{orderInfo.userBalance || '0.00'}}</view>
        </view>
        <view class="method-check" wx:if="{{paymentMethod === 'balance'}}">
          <text class="check-icon">✓</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 广告位 -->
  <view class="ad-container">
    <ad unit-id="adunit-id" ad-type="banner" ad-theme="white" bindload="onAdLoad" binderror="onAdError"></ad>
  </view>

  <!-- 底部操作栏 -->
  <view class="bottom-bar">
    <view class="cancel-btn" bindtap="onCancelPayment">取消支付</view>
    <view class="confirm-btn {{paying ? 'disabled' : ''}}" bindtap="onConfirmPayment">
      确认支付 ¥{{paymentAmount}}
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{loading}}">
    <text class="loading-text">加载中...</text>
  </view>
</view> 