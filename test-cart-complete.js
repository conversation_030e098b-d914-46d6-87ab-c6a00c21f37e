// 完整的购物车API测试脚本
const http = require('http');

// 测试用的token（需要替换为真实的token）
const TEST_TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8';

function makeRequest(method, path, data = null) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 4000,
      path: path,
      method: method,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${TEST_TOKEN}`,
        'User-Agent': 'Test-Client/1.0'
      }
    };

    if (data) {
      const postData = JSON.stringify(data);
      options.headers['Content-Length'] = Buffer.byteLength(postData);
    }

    const req = http.request(options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        try {
          const parsedData = JSON.parse(responseData);
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            data: parsedData
          });
        } catch (err) {
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            data: responseData
          });
        }
      });
    });

    req.on('error', (err) => {
      reject(err);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

async function testCartAPIs() {
  console.log('🛒 开始测试购物车API...\n');

  try {
    // 1. 测试获取购物车列表
    console.log('1️⃣ 测试获取购物车列表');
    const listResult = await makeRequest('GET', '/api/v1/client/cart/list');
    console.log(`状态码: ${listResult.statusCode}`);
    console.log(`响应: ${JSON.stringify(listResult.data, null, 2)}\n`);

    // 2. 测试添加商品到购物车
    console.log('2️⃣ 测试添加商品到购物车');
    const addResult = await makeRequest('POST', '/api/v1/client/cart/add', {
      goodsId: 1,
      quantity: 2,
      specificationId: null
    });
    console.log(`状态码: ${addResult.statusCode}`);
    console.log(`响应: ${JSON.stringify(addResult.data, null, 2)}\n`);

    let cartItemId = null;
    if (addResult.statusCode === 200 && addResult.data.data) {
      cartItemId = addResult.data.data.id;
    }

    // 3. 测试更新购物车商品数量
    if (cartItemId) {
      console.log('3️⃣ 测试更新购物车商品数量');
      const updateResult = await makeRequest('POST', '/api/v1/client/cart/update', {
        id: cartItemId,
        quantity: 3,
        selected: true
      });
      console.log(`状态码: ${updateResult.statusCode}`);
      console.log(`响应: ${JSON.stringify(updateResult.data, null, 2)}\n`);
    } else {
      console.log('3️⃣ 跳过更新测试（没有购物车项ID）\n');
    }

    // 4. 测试删除购物车商品（POST方法）
    if (cartItemId) {
      console.log('4️⃣ 测试删除购物车商品（POST方法）');
      const removeResult = await makeRequest('POST', '/api/v1/client/cart/remove', {
        ids: [cartItemId]
      });
      console.log(`状态码: ${removeResult.statusCode}`);
      console.log(`响应: ${JSON.stringify(removeResult.data, null, 2)}\n`);
    } else {
      console.log('4️⃣ 跳过删除测试（没有购物车项ID）\n');
    }

    // 5. 重新添加商品用于DELETE方法测试
    console.log('5️⃣ 重新添加商品用于DELETE方法测试');
    const addResult2 = await makeRequest('POST', '/api/v1/client/cart/add', {
      goodsId: 1,
      quantity: 1,
      specificationId: null
    });
    console.log(`状态码: ${addResult2.statusCode}`);
    console.log(`响应: ${JSON.stringify(addResult2.data, null, 2)}\n`);

    let cartItemId2 = null;
    if (addResult2.statusCode === 200 && addResult2.data.data) {
      cartItemId2 = addResult2.data.data.id;
    }

    // 6. 测试删除购物车商品（DELETE方法）
    if (cartItemId2) {
      console.log('6️⃣ 测试删除购物车商品（DELETE方法）');
      const deleteResult = await makeRequest('DELETE', '/api/v1/client/cart/remove', {
        ids: [cartItemId2]
      });
      console.log(`状态码: ${deleteResult.statusCode}`);
      console.log(`响应: ${JSON.stringify(deleteResult.data, null, 2)}\n`);
    } else {
      console.log('6️⃣ 跳过DELETE删除测试（没有购物车项ID）\n');
    }

    // 7. 添加多个商品用于清空测试
    console.log('7️⃣ 添加多个商品用于清空测试');
    await makeRequest('POST', '/api/v1/client/cart/add', {
      goodsId: 1,
      quantity: 1,
      specificationId: null
    });
    await makeRequest('POST', '/api/v1/client/cart/add', {
      goodsId: 2,
      quantity: 2,
      specificationId: null
    });
    console.log('已添加测试商品\n');

    // 8. 测试清空购物车
    console.log('8️⃣ 测试清空购物车');
    const clearResult = await makeRequest('POST', '/api/v1/client/cart/clear');
    console.log(`状态码: ${clearResult.statusCode}`);
    console.log(`响应: ${JSON.stringify(clearResult.data, null, 2)}\n`);

    // 9. 最终检查购物车是否为空
    console.log('9️⃣ 最终检查购物车是否为空');
    const finalListResult = await makeRequest('GET', '/api/v1/client/cart/list');
    console.log(`状态码: ${finalListResult.statusCode}`);
    console.log(`响应: ${JSON.stringify(finalListResult.data, null, 2)}\n`);

    console.log('🎉 购物车API测试完成！');

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
  }
}

// 等待服务器启动后开始测试
setTimeout(() => {
  testCartAPIs();
}, 2000);
