// pages/wifi/ad-view/ad-view.js
// 广告观看页面，看完广告后连接WiFi

Page({
  /**
   * 页面的初始数据
   */
  data: {
    ssid: '', // WiFi名称
    password: '', // WiFi密码
    adType: 'video', // 广告类型：video(视频广告)、reward(激励视频)、banner(横幅广告)
    countdown: 5, // 倒计时秒数
    showCountdown: true, // 是否显示倒计时
    adLoaded: false, // 广告是否加载成功
    adError: false, // 广告是否加载失败
    connectReady: false, // 是否准备好连接WiFi
    isConnecting: false, // 是否正在连接WiFi
    connectSuccess: false, // 是否连接成功
    connectFailed: false, // 是否连接失败
    errorMsg: '' // 错误信息
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    console.log('广告页面参数:', options);
    
    const { ssid, pwd, type = 'video' } = options;
    
    if (!ssid || !pwd) {
      this.setData({
        adError: true,
        errorMsg: 'WiFi信息不完整，无法连接'
      });
      return;
    }
    
    this.setData({
      ssid: decodeURIComponent(ssid),
      password: decodeURIComponent(pwd),
      adType: type
    });
    
    // 加载广告
    this.loadAd();
  },
  
  /**
   * 加载广告
   */
  loadAd() {
    const { adType } = this.data;
    
    // 根据广告类型加载不同的广告
    switch (adType) {
      case 'video':
        this.loadVideoAd();
        break;
      case 'reward':
        this.loadRewardAd();
        break;
      case 'banner':
        this.loadBannerAd();
        break;
      default:
        this.loadVideoAd();
    }
  },
  
  /**
   * 加载视频广告
   */
  loadVideoAd() {
    // 模拟广告加载
    setTimeout(() => {
      this.setData({
        adLoaded: true
      });
      
      // 开始倒计时
      this.startCountdown();
    }, 1000);
  },
  
  /**
   * 加载激励视频广告
   */
  loadRewardAd() {
    // 尝试创建微信激励视频广告
    if (wx.createRewardedVideoAd) {
      const rewardedVideoAd = wx.createRewardedVideoAd({
        adUnitId: 'your-ad-unit-id' // 替换为实际的广告单元ID
      });
      
      rewardedVideoAd.onLoad(() => {
        console.log('激励视频广告加载成功');
        this.setData({ adLoaded: true });
        
        // 显示广告
        rewardedVideoAd.show().catch(() => {
          // 失败重试
          rewardedVideoAd.load()
            .then(() => rewardedVideoAd.show())
            .catch(err => {
              console.log('激励视频广告显示失败', err);
              this.setData({
                adError: true,
                errorMsg: '广告加载失败，请重试'
              });
            });
        });
      });
      
      rewardedVideoAd.onError(err => {
        console.log('激励视频广告加载失败', err);
        this.setData({
          adError: true,
          errorMsg: '广告加载失败，请重试'
        });
      });
      
      rewardedVideoAd.onClose(res => {
        // 用户点击了【关闭广告】按钮
        if (res && res.isEnded) {
          // 正常播放结束，可以下发游戏奖励
          this.prepareWifiConnect();
        } else {
          // 播放中途退出，不下发游戏奖励
          wx.showModal({
            title: '提示',
            content: '观看完整广告才能连接WiFi',
            showCancel: false
          });
        }
      });
    } else {
      // 如果不支持激励视频，回退到普通视频
      this.loadVideoAd();
    }
  },
  
  /**
   * 加载Banner广告
   */
  loadBannerAd() {
    // 模拟广告加载
    setTimeout(() => {
      this.setData({
        adLoaded: true
      });
      
      // 开始倒计时
      this.startCountdown();
    }, 1000);
  },
  
  /**
   * 开始倒计时
   */
  startCountdown() {
    this.countdownTimer = setInterval(() => {
      const { countdown } = this.data;
      
      if (countdown <= 1) {
        clearInterval(this.countdownTimer);
        this.prepareWifiConnect();
      } else {
        this.setData({
          countdown: countdown - 1
        });
      }
    }, 1000);
  },
  
  /**
   * 准备连接WiFi
   */
  prepareWifiConnect() {
    this.setData({
      showCountdown: false,
      connectReady: true
    });
  },
  
  /**
   * 连接WiFi
   */
  connectWifi() {
    const { ssid, password } = this.data;
    
    this.setData({
      isConnecting: true
    });
    
    // 检查是否支持WiFi连接API
    if (wx.connectWifi) {
      wx.connectWifi({
        SSID: ssid,
        password: password,
        success: () => {
          console.log('WiFi连接成功');
          this.setData({
            isConnecting: false,
            connectSuccess: true
          });
        },
        fail: (err) => {
          console.error('WiFi连接失败:', err);
          this.setData({
            isConnecting: false,
            connectFailed: true,
            errorMsg: '连接失败，请手动连接'
          });
        }
      });
    } else {
      // 不支持自动连接，提示手动连接
      setTimeout(() => {
        this.setData({
          isConnecting: false,
          connectSuccess: true,
          errorMsg: '请手动连接WiFi'
        });
      }, 1500);
    }
  },
  
  /**
   * 手动连接WiFi
   */
  manualConnect() {
    const { ssid, password } = this.data;
    
    wx.setClipboardData({
      data: password,
      success: () => {
        wx.showToast({
          title: '密码已复制',
          icon: 'success'
        });
      }
    });
  },
  
  /**
   * 返回上一页
   */
  goBack() {
    wx.navigateBack();
  }
}) 