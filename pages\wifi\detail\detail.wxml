<!--pages/wifi/detail/detail.wxml-->
<!--WiFi码详情页面模板-->

<view class="container">
  <!-- 加载状态 -->
  <view class="loading-wrapper" wx:if="{{loading}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 主要内容 -->
  <view class="content" wx:if="{{!loading}}">
    <!-- WiFi码信息卡片 -->
    <view class="wifi-card">
      <view class="wifi-header">
        <text class="wifi-title">{{wifiInfo.title}}</text>
        <view class="wifi-status {{wifiInfo.status === 'active' ? 'active' : 'inactive'}}">
          {{wifiInfo.status === 'active' ? '活跃' : '未激活'}}
        </view>
      </view>

      <!-- 广告模式开关 -->
      <view class="ad-mode-toggle" bindtap="toggleAdMode">
        <view class="toggle-switch {{adModeEnabled ? 'on' : 'off'}}">
          <view class="toggle-button"></view>
        </view>
        <text class="toggle-label">{{adModeEnabled ? '广告模式已开启' : '广告模式已关闭'}}</text>
      </view>

      <!-- 二维码区域 -->
      <view class="qrcode-section">
        <view class="qrcode-wrapper">
          <!-- 如果有后端生成的二维码，优先使用 -->
          <block wx:if="{{wifiInfo.qrCodeUrl}}">
            <image 
              src="{{wifiInfo.qrCodeUrl}}" 
              class="server-qrcode" 
              mode="aspectFit"
              bindtap="onQrCodeTap"
              bindlongpress="onPrintWifi"
              style="width: 500rpx; height: 500rpx;"
            />
            <!-- 广告标签 -->
            <view class="ad-label" wx:if="{{adModeEnabled}}">
              <text>看广告连WiFi</text>
            </view>
          </block>
          
          <!-- 如果没有后端生成的二维码，使用组件生成 -->
          <qrcode
            wx:else
            ssid="{{wifiInfo.ssid}}"
            password="{{wifiInfo.password}}"
            merchantName="{{wifiInfo.merchantName}}"
            size="500"
            adEnabled="{{adModeEnabled}}"
            adType="{{wifiInfo.ad_type || 'video'}}"
            bind:generated="onQrCodeGenerated"
            bind:tap="onQrCodeTap"
            bind:longpress="onPrintWifi"
          />
        </view>
      </view>

      <!-- WiFi信息 -->
      <view class="wifi-info">
        <view class="wifi-info-item" bindlongpress="onCopyWifiInfo" data-type="ssid">
          <text class="info-label">WiFi名称:</text>
          <text class="info-value">{{wifiInfo.ssid}}</text>
          <view class="copy-hint">长按复制</view>
        </view>
        
        <view class="wifi-info-item" bindlongpress="onCopyWifiInfo" data-type="password">
          <text class="info-label">WiFi密码:</text>
          <text class="info-value">{{showPassword ? wifiInfo.password : '********'}}</text>
          <view class="password-toggle" bindtap="onTogglePassword">
            <text class="toggle-icon">{{showPassword ? '👁️' : '👁️‍🗨️'}}</text>
          </view>
        </view>
        
        <view class="wifi-info-item">
          <text class="info-label">商户名称:</text>
          <text class="info-value">{{wifiInfo.merchantName}}</text>
        </view>
        
        <!-- 广告模式说明 -->
        <view class="wifi-info-item" wx:if="{{adModeEnabled}}">
          <text class="info-label">连接方式:</text>
          <text class="info-value ad-mode-text">扫码看广告后连接</text>
        </view>
      </view>
    </view>

    <!-- 使用统计 -->
    <view class="stats-section">
      <view class="section-title">
        <text>使用统计</text>
      </view>
      <view class="stats-grid">
        <view class="stat-item">
          <text class="stat-value">{{statsData.scanCount || 0}}</text>
          <text class="stat-label">扫码次数</text>
        </view>
        <view class="stat-item">
          <text class="stat-value">{{statsData.connectCount || 0}}</text>
          <text class="stat-label">连接次数</text>
        </view>
        <view class="stat-item">
          <text class="stat-value">{{statsData.todayCount || 0}}</text>
          <text class="stat-label">今日连接</text>
        </view>
        <view class="stat-item">
          <text class="stat-value">¥{{statsData.totalEarnings || '0.00'}}</text>
          <text class="stat-label">总收益</text>
        </view>
      </view>
      
      <!-- 详细统计信息 -->
      <view class="stats-details" wx:if="{{showStatsDetails}}">
        <view class="stats-detail-item">
          <view class="detail-label">日均连接数</view>
          <view class="detail-value">{{statsData.dailyAverage || 0}}</view>
        </view>
        <view class="stats-detail-item">
          <view class="detail-label">本周连接</view>
          <view class="detail-value">{{statsData.weeklyConnections || 0}}</view>
        </view>
        <view class="stats-detail-item">
          <view class="detail-label">本月连接</view>
          <view class="detail-value">{{statsData.monthlyConnections || 0}}</view>
        </view>
        <view class="stats-detail-item">
          <view class="detail-label">本月收益</view>
          <view class="detail-value">¥{{statsData.monthlyEarnings || '0.00'}}</view>
        </view>
      </view>
      
      <view class="toggle-stats-btn" bindtap="toggleStatsDetails">
        <text>{{showStatsDetails ? '收起详情' : '查看更多'}}</text>
        <text class="toggle-icon">{{showStatsDetails ? '▲' : '▼'}}</text>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="action-section">
      <view class="action-buttons">
        <button class="action-btn print-btn" bindtap="onPrintWifi">
          <text class="btn-icon">🖨️</text>
          <text class="btn-text">打印</text>
        </button>
        <button class="action-btn edit-btn" bindtap="onEditWifi">
          <text class="btn-icon">✏️</text>
          <text class="btn-text">修改</text>
        </button>
        <button class="action-btn delete-btn" bindtap="onDeleteWifi">
          <text class="btn-icon">🗑️</text>
          <text class="btn-text">删除</text>
        </button>
      </view>
      
      <view class="share-button">
        <button class="btn btn-primary" bindtap="onShareWifi">
          <text class="btn-icon">📤</text>
          <text class="btn-text">分享</text>
        </button>
      </view>
    </view>

    <!-- 广告区域 -->
    <view class="ad-section">
      <view class="section-title">
        <text>广告推广</text>
      </view>
      <view class="ad-banner" bindtap="onAdTap">
        <image src="{{adData.image}}" class="ad-image" mode="aspectFill"></image>
        <view class="ad-content">
          <text class="ad-title">{{adData.title}}</text>
          <text class="ad-subtitle">{{adData.subtitle}}</text>
        </view>
      </view>
    </view>

    <!-- 推荐商品 -->
    <view class="recommend-section" wx:if="{{recommendGoods.length > 0}}">
      <view class="section-title">
        <text>推荐商品</text>
      </view>
      <view class="goods-grid">
        <view 
          class="goods-item" 
          wx:for="{{recommendGoods}}" 
          wx:key="id"
          bindtap="onGoodsTap"
          data-id="{{item.id}}"
        >
          <image src="{{item.image}}" class="goods-image" mode="aspectFill"></image>
          <view class="goods-info">
            <text class="goods-name">{{item.name}}</text>
            <text class="goods-price">¥{{item.price}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 创建时间 -->
    <view class="meta-section">
      <text class="meta-text">创建时间: {{wifiInfo.createTime}}</text>
    </view>

    <!-- 底部间距 -->
    <view class="bottom-spacing"></view>
  </view>
</view> 