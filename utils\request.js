// utils/request.js
// 网络请求封装

const config = require('../config/config.js')

/**
 * 封装wx.request
 * @param {Object} options 请求参数
 * @returns {Promise} 请求结果
 */
const request = (options) => {
  return new Promise((resolve, reject) => {
    // 获取token
    const token = wx.getStorageSync(config.storageKeys.token)
    
    // 构建请求头
    const header = {
      'Content-Type': 'application/json',
      ...options.header
    }
    
    // 添加token到请求头
    if (token) {
      header.Authorization = `Bearer ${token}`
      console.log(`请求 ${options.url} 添加Authorization: Bearer ${token.substring(0, 10)}...`)
    } else {
      console.warn(`请求 ${options.url} 未找到token`)
    }
    
    // 显示loading
    if (options.showLoading !== false) {
      wx.showLoading({
        title: options.loadingText || '加载中...',
        mask: true
      })
    }
    
    // 特殊处理微信登录请求
    const isWechatLogin = options.url && options.url.includes('/auth/wechat/login');
    if (isWechatLogin) {
      console.log(`微信登录请求: ${options.method || 'GET'} ${options.url}`);
      console.log(`请求数据:`, JSON.stringify(options.data || {}));
    } else {
      console.log(`开始请求: ${options.method || 'GET'} ${options.url}`, options.data || {});
    }
    
    wx.request({
      url: options.url,
      method: options.method || 'GET',
      data: options.data || {},
      header: header,
      timeout: options.timeout || config.api.timeout,
      success: (res) => {
        // 隐藏loading
        if (options.showLoading !== false) {
          wx.hideLoading()
        }
        
        if (isWechatLogin) {
          console.log(`微信登录请求成功: ${options.url}, 状态码: ${res.statusCode}`);
          console.log(`响应数据:`, JSON.stringify(res.data));
        } else {
          console.log(`请求成功: ${options.url}, 状态码: ${res.statusCode}`, res.data);
        }
        
        // 处理响应
        if (res.statusCode === 200) {
          const data = res.data
          
          // 检查业务状态码 - 支持多种成功响应格式
          if (data.code === 200 || data.success === true || data.status === 'success' || 
              (data.code === undefined && data.data !== undefined) || 
              (Array.isArray(data)) || (data.message && data.message.includes('成功'))) {
            
            // 统一数据格式
            let responseData = data
            if (Array.isArray(data)) {
              // 如果直接返回数组，包装成标准格式
              responseData = {
                code: 200,
                message: '获取数据成功',
                data: data,
                success: true
              }
            } else if (data.code === undefined && data.data !== undefined) {
              // 如果没有code但有data，添加标准字段
              responseData = {
                ...data,
                code: 200,
                success: true
              }
            } else if (data.status === 'success' && data.code === undefined) {
              // 后端使用status='success'格式，添加code字段
              responseData = {
                ...data,
                code: 200,
                success: true
              }
            }
            
            if (isWechatLogin) {
              console.log(`微信登录请求处理成功，返回数据:`, JSON.stringify(responseData));
            } else {
              console.log(`请求 ${options.url} 处理成功`, responseData);
            }
            
            resolve(responseData)
          } else {
            // 业务错误
            const errorMsg = data.message || data.msg || data.error || '请求失败'
            console.error(`请求 ${options.url} 业务错误: ${errorMsg}`, data)
            
            // 登录失效处理
            if (data.code === 401 || data.status === 401) {
              console.warn(`请求 ${options.url} 登录已过期`)
              handleAuthError()
              reject(new Error('登录已过期，请重新登录'))
              return
            }
            
            // 显示错误信息
            if (options.showError !== false) {
              wx.showToast({
                title: errorMsg,
                icon: 'none',
                duration: 2000
              })
            }
            
            reject(new Error(errorMsg))
          }
        } else {
          // HTTP错误
          const errorMsg = `请求失败(${res.statusCode})`
          console.error(`请求 ${options.url} HTTP错误: ${res.statusCode}`, res.data)
          
          // 处理401未授权错误
          if (res.statusCode === 401) {
            console.warn(`请求 ${options.url} 未授权(401)`)
            // 检查token是否存在
            if (token) {
              console.log(`token存在但无效: ${token.substring(0, 10)}...`)
            }
            handleAuthError()
          }
          
          if (options.showError !== false) {
            wx.showToast({
              title: errorMsg,
              icon: 'none',
              duration: 2000
            })
          }
          
          // 创建一个包含HTTP状态码和服务器响应数据的错误对象
          const error = new Error(errorMsg)
          error.statusCode = res.statusCode
          error.data = res.data
          
          reject(error)
        }
      },
      fail: (error) => {
        // 隐藏loading
        if (options.showLoading !== false) {
          wx.hideLoading()
        }
        
        let errorMsg = '网络请求失败'
        console.error(`请求 ${options.url} 失败:`, error)
        
        // 网络错误处理
        if (error.errMsg) {
          if (error.errMsg.includes('timeout')) {
            errorMsg = '请求超时，请检查网络连接'
          } else if (error.errMsg.includes('fail')) {
            errorMsg = '网络连接失败，请检查网络设置'
          }
        }
        
        if (options.showError !== false) {
          wx.showToast({
            title: errorMsg,
            icon: 'none',
            duration: 2000
          })
        }
        
        reject(new Error(errorMsg))
      }
    })
  })
}

/**
 * 处理认证错误
 */
const handleAuthError = () => {
  // 清除本地存储的用户信息
  wx.removeStorageSync(config.storageKeys.token)
  wx.removeStorageSync(config.storageKeys.userInfo)
  
  // 尝试更新全局登录状态（如果App实例已初始化）
  try {
  const app = getApp()
    if (app && app.globalData) {
  app.globalData.isLogin = false
  app.globalData.userInfo = null
  app.globalData.token = null
    }
  } catch (e) {
    console.error('更新全局状态失败:', e)
  }
  
  // 跳转到登录页面
  wx.showModal({
    title: '提示',
    content: '登录已过期，请重新登录',
    showCancel: false,
    success: () => {
      wx.reLaunch({
        url: '/pages/index/index'
      })
    }
  })
}

/**
 * 构建完整的API URL
 * @param {String} url 相对URL
 * @returns {String} 完整的API URL
 */
const buildApiUrl = (url) => {
  // 如果URL已经是完整地址，直接返回
  if (url.startsWith('http://') || url.startsWith('https://')) {
    return url
  }
  
  // 如果URL不以/开头，添加/
  if (!url.startsWith('/')) {
    url = '/' + url
  }
  
  // 如果URL已经包含完整的API路径，直接拼接baseUrl
  if (url.startsWith('/api/v1/client/')) {
    return config.api.baseUrl + url
  }
  
  // 否则拼接客户端API路径
  return config.api.baseUrl + config.api.clientPath + url
}

/**
 * GET请求
 * @param {String} url 请求地址
 * @param {Object} data 请求参数
 * @param {Object} options 其他选项
 * @returns {Promise} 请求结果
 */
const get = (url, data = {}, options = {}) => {
  return request({
    url: buildApiUrl(url),
    method: 'GET',
    data,
    ...options
  })
}

/**
 * POST请求
 * @param {String} url 请求地址
 * @param {Object} data 请求参数
 * @param {Object} options 其他选项
 * @returns {Promise} 请求结果
 */
const post = (url, data = {}, options = {}) => {
  return request({
    url: buildApiUrl(url),
    method: 'POST',
    data,
    ...options
  })
}

/**
 * PUT请求
 * @param {String} url 请求地址
 * @param {Object} data 请求参数
 * @param {Object} options 其他选项
 * @returns {Promise} 请求结果
 */
const put = (url, data = {}, options = {}) => {
  return request({
    url: buildApiUrl(url),
    method: 'PUT',
    data,
    ...options
  })
}

/**
 * DELETE请求
 * @param {String} url 请求地址
 * @param {Object} data 请求参数
 * @param {Object} options 其他选项
 * @returns {Promise} 请求结果
 */
const del = (url, data = {}, options = {}) => {
  return request({
    url: buildApiUrl(url),
    method: 'DELETE',
    data,
    ...options
  })
}

/**
 * 文件上传
 * @param {String} url 上传地址
 * @param {String} filePath 文件路径
 * @param {Object} formData 额外表单数据
 * @param {Object} options 其他选项
 * @returns {Promise} 上传结果
 */
const upload = (url, filePath, formData = {}, options = {}) => {
  return new Promise((resolve, reject) => {
    const token = wx.getStorageSync(config.storageKeys.token)
    
    const header = {
      ...options.header
    }
    
    if (token) {
      header.Authorization = `Bearer ${token}`
    }
    
    if (options.showLoading !== false) {
      wx.showLoading({
        title: '上传中...',
        mask: true
      })
    }
    
    wx.uploadFile({
      url: buildApiUrl(url),
      filePath,
      name: options.name || 'file',
      formData,
      header,
      success: (res) => {
        if (options.showLoading !== false) {
          wx.hideLoading()
        }
        
        try {
          const data = JSON.parse(res.data)
          if (data.code === 200 || data.success === true) {
            resolve(data)
          } else {
            const errorMsg = data.message || data.msg || '上传失败'
            wx.showToast({
              title: errorMsg,
              icon: 'none'
            })
            reject(new Error(errorMsg))
          }
        } catch (error) {
          wx.showToast({
            title: '上传失败',
            icon: 'none'
          })
          reject(error)
        }
      },
      fail: (error) => {
        if (options.showLoading !== false) {
          wx.hideLoading()
        }
        
        wx.showToast({
          title: '上传失败',
          icon: 'none'
        })
        reject(error)
      }
    })
  })
}

module.exports = {
  request,
  get,
  post,
  put,
  delete: del,
  upload
} 