/**
 * 联盟审核接口测试脚本
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:4000/api/v1/admin/alliance';

// 测试用的管理员token（从后台管理系统获取）
const ADMIN_TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6MSwidXNlcm5hbWUiOiJtcngwOTI3Iiwicm9sZSI6ImFkbWluIiwiaWF0IjoxNzUyMzczODM0LCJleHAiOjE3NTI5Nzg2MzR9.uFW9QYuueQwMgf7KBd_CGExk_321QNSCvOz-lmqwAug';

// 通用API请求函数
async function apiRequest(url, method = 'GET', data = null) {
  try {
    const config = {
      method: method.toLowerCase(),
      url: url,
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${ADMIN_TOKEN}`
      }
    };
    
    if (data) {
      config.data = data;
    }
    
    const response = await axios(config);
    
    console.log(`✅ ${method} ${url}`);
    console.log(`📄 状态码: ${response.status}`);
    console.log(`📄 响应数据:`, JSON.stringify(response.data, null, 2));
    
    return response.data;
  } catch (error) {
    if (error.response) {
      console.log(`❌ ${method} ${url}`);
      console.log(`📄 错误状态码: ${error.response.status}`);
      console.log(`📄 错误响应:`, JSON.stringify(error.response.data, null, 2));
    } else if (error.request) {
      console.log(`❌ 网络错误: 无法连接到服务器`);
    } else {
      console.log(`❌ 请求错误:`, error.message);
    }
    return null;
  }
}

async function testAllianceAudit() {
  console.log('🚀 开始测试联盟审核接口...\n');
  
  // 1. 先获取联盟申请列表，找到可以审核的申请
  console.log('1️⃣ 获取联盟申请列表');
  const listResult = await apiRequest(`${BASE_URL}/list?page=1&limit=10`);
  
  if (!listResult || !listResult.data || !listResult.data.list) {
    console.log('❌ 无法获取联盟申请列表');
    return;
  }
  
  const applications = listResult.data.list;
  console.log(`📋 找到 ${applications.length} 个联盟申请`);
  
  // 找到一个待审核的申请
  const pendingApp = applications.find(app => app.status === 0);
  
  if (!pendingApp) {
    console.log('⚠️ 没有找到待审核的申请，创建一个测试申请...');
    
    // 创建测试申请
    const mysql = require('mysql2/promise');
    const connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: 'wo587129955',
      database: 'mall'
    });
    
    try {
      const [result] = await connection.execute(`
        INSERT INTO team_apply (user_id, name, contact, phone, email, area, description, status)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
      `, [1, '测试联盟申请', '测试联系人', '13800138000', '<EMAIL>', '测试地区', '这是一个测试申请', 0]);
      
      console.log(`✅ 创建测试申请成功，ID: ${result.insertId}`);
      
      // 重新获取列表
      const newListResult = await apiRequest(`${BASE_URL}/list?page=1&limit=10`);
      const newApplications = newListResult.data.list;
      const testApp = newApplications.find(app => app.id === result.insertId);
      
      if (testApp) {
        await testAuditProcess(testApp);
      }
    } catch (err) {
      console.log('❌ 创建测试申请失败:', err.message);
    } finally {
      await connection.end();
    }
  } else {
    console.log(`📋 找到待审核申请: ID=${pendingApp.id}, 名称=${pendingApp.name}`);
    await testAuditProcess(pendingApp);
  }
}

async function testAuditProcess(application) {
  console.log(`\n2️⃣ 测试审核申请 ID: ${application.id}`);
  
  // 获取申请详情
  console.log('\n📋 获取申请详情:');
  const detailResult = await apiRequest(`${BASE_URL}/detail/${application.id}`);
  
  if (detailResult && detailResult.data) {
    console.log(`✅ 申请详情: ${detailResult.data.name} - ${detailResult.data.status_text}`);
  }
  
  // 测试审核通过
  console.log('\n✅ 测试审核通过:');
  const approveResult = await apiRequest(`${BASE_URL}/audit/${application.id}`, 'POST', {
    status: 1,
    remark: '申请材料齐全，审核通过'
  });
  
  if (approveResult && approveResult.success) {
    console.log('🎉 审核通过成功！');
    
    // 验证是否创建了团队
    console.log('\n🔍 验证团队创建:');
    const mysql = require('mysql2/promise');
    const connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: 'wo587129955',
      database: 'mall'
    });
    
    try {
      const [teams] = await connection.execute(`
        SELECT * FROM team WHERE leader_id = ? ORDER BY created_at DESC LIMIT 1
      `, [application.user_id]);
      
      if (teams.length > 0) {
        console.log(`✅ 团队创建成功: ID=${teams[0].id}, 名称=${teams[0].name}`);
        
        // 检查团队成员
        const [members] = await connection.execute(`
          SELECT * FROM team_member WHERE team_id = ?
        `, [teams[0].id]);
        
        console.log(`✅ 团队成员数: ${members.length}`);
        
        // 检查用户角色更新
        const [users] = await connection.execute(`
          SELECT * FROM user WHERE id = ?
        `, [application.user_id]);
        
        if (users.length > 0) {
          console.log(`✅ 用户角色: ${users[0].role}, 团队ID: ${users[0].team_id}`);
        }
      } else {
        console.log('❌ 团队创建失败');
      }
    } catch (err) {
      console.log('❌ 验证团队创建失败:', err.message);
    } finally {
      await connection.end();
    }
  } else {
    console.log('❌ 审核失败');
  }
  
  // 获取统计信息
  console.log('\n📊 获取联盟统计:');
  const statsResult = await apiRequest(`${BASE_URL}/stats`);
  
  if (statsResult && statsResult.data) {
    console.log('✅ 统计信息:');
    console.log(`   - 总申请数: ${statsResult.data.applications.total}`);
    console.log(`   - 待审核: ${statsResult.data.applications.pending}`);
    console.log(`   - 已通过: ${statsResult.data.applications.approved}`);
    console.log(`   - 已拒绝: ${statsResult.data.applications.rejected}`);
    console.log(`   - 今日申请: ${statsResult.data.applications.todayCount}`);
    console.log(`   - 总团队数: ${statsResult.data.teams.total}`);
  }
}

// 运行测试
if (require.main === module) {
  testAllianceAudit().then(() => {
    console.log('\n🎉 联盟审核接口测试完成！');
    process.exit(0);
  }).catch(error => {
    console.error('❌ 测试失败:', error.message);
    process.exit(1);
  });
}

module.exports = { testAllianceAudit };
