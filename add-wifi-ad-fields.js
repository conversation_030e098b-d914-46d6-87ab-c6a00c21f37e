// 为WiFi表添加广告相关字段
const db = require('./src/database');

async function addWifiAdFields() {
  try {
    // 初始化数据库连接
    await db.init();
    console.log('数据库连接已建立');
    
    // 首先检查现有字段
    const [existingColumns] = await db.query(`
      SELECT COLUMN_NAME 
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = DATABASE() 
      AND TABLE_NAME = 'wifi'
    `);
    
    const existingColumnNames = existingColumns.map(col => col.COLUMN_NAME);
    console.log('现有字段:', existingColumnNames);
    
    // 定义要添加的字段
    const fieldsToAdd = [
      { name: 'ad_enabled', definition: 'BOOLEAN DEFAULT FALSE COMMENT \'是否启用广告\'' },
      { name: 'ad_type', definition: 'VARCHAR(20) DEFAULT NULL COMMENT \'广告类型\'' },
      { name: 'ad_content', definition: 'TEXT DEFAULT NULL COMMENT \'广告内容\'' },
      { name: 'ad_duration', definition: 'INT DEFAULT 5 COMMENT \'广告持续时间（秒）\'' },
      { name: 'ad_title', definition: 'VARCHAR(200) DEFAULT NULL COMMENT \'广告标题\'' },
      { name: 'ad_link', definition: 'VARCHAR(500) DEFAULT NULL COMMENT \'广告跳转链接\'' },
      { name: 'ad_view_count', definition: 'INT DEFAULT 0 COMMENT \'广告展示次数\'' },
      { name: 'ad_click_count', definition: 'INT DEFAULT 0 COMMENT \'广告点击次数\'' },
      { name: 'ad_revenue_per_view', definition: 'DECIMAL(10,4) DEFAULT 0.0000 COMMENT \'每次展示收益\'' },
      { name: 'ad_total_revenue', definition: 'DECIMAL(10,2) DEFAULT 0.00 COMMENT \'广告总收益\'' }
    ];
    
    console.log('开始添加广告相关字段...');
    
    for (const field of fieldsToAdd) {
      if (!existingColumnNames.includes(field.name)) {
        try {
          const sql = `ALTER TABLE wifi ADD COLUMN ${field.name} ${field.definition}`;
          await db.query(sql);
          console.log('✓ 成功添加字段:', field.name);
        } catch (err) {
          console.error(`✗ 添加字段 ${field.name} 失败:`, err.message);
        }
      } else {
        console.log(`- 字段 ${field.name} 已存在，跳过`);
      }
    }
    
    // 查询表结构，确认字段已添加
    const [columns] = await db.query(`
      SELECT COLUMN_NAME, DATA_TYPE, COLUMN_COMMENT 
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = DATABASE() 
      AND TABLE_NAME = 'wifi' 
      AND COLUMN_NAME LIKE 'ad_%'
      ORDER BY ORDINAL_POSITION
    `);
    
    console.log('\n当前WiFi表的广告相关字段:');
    if (columns && columns.length > 0) {
      columns.forEach(col => {
        console.log(`- ${col.COLUMN_NAME} (${col.DATA_TYPE}): ${col.COLUMN_COMMENT}`);
      });
    } else {
      console.log('没有找到广告相关字段');
    }
    
    // 为ID=2的WiFi记录添加示例广告数据
    console.log('\n为ID=2的WiFi记录添加示例广告数据...');
    
    // 先检查是否有ad_enabled字段
    const [checkColumns] = await db.query(`
      SELECT COLUMN_NAME 
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = DATABASE() 
      AND TABLE_NAME = 'wifi' 
      AND COLUMN_NAME = 'ad_enabled'
    `);
    
    if (checkColumns && checkColumns.length > 0) {
      const result = await db.query(`
        UPDATE wifi 
        SET 
          ad_enabled = TRUE,
          ad_type = 'video',
          ad_title = '星巴克新品上市',
          ad_content = 'https://example.com/starbucks-ad-video.mp4',
          ad_duration = 10,
          ad_link = 'https://www.starbucks.com/menu/new-arrivals',
          ad_revenue_per_view = 0.0100
        WHERE id = 2
      `);
      
      console.log('更新影响行数:', result.affectedRows);
      
      // 查询更新后的记录
      const [updatedWifi] = await db.query('SELECT * FROM wifi WHERE id = 2');
      if (updatedWifi && updatedWifi.length > 0) {
        console.log('\n更新后的WiFi记录:', JSON.stringify(updatedWifi[0], null, 2));
      }
    } else {
      console.log('广告字段未成功添加，跳过数据更新');
    }
    
  } catch (err) {
    console.error('执行过程中出错:', err);
    console.error('错误详情:', err.stack);
  } finally {
    process.exit(0);
  }
}

addWifiAdFields(); 