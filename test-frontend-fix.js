const axios = require('axios');
const jwt = require('jsonwebtoken');

async function testAvatarFix() {
  try {
    console.log('🔧 测试头像修复效果...');

    // 生成JWT token
    const config = require('./config');
    const userId = 1;

    const token = jwt.sign(
      { id: userId, openid: 'oW20C7mVlW8e3W2AgUGtDTJeAbQU', role: 'user', session_key: 'gEow49sNFy+UU8J7ojrEPQ==' },
      config.jwt.secret,
      { expiresIn: config.jwt.expiresIn }
    );

    console.log('🔑 Token生成成功');

    // 测试获取用户信息
    console.log('\n👤 测试获取用户信息...');

    const userInfoResponse = await axios.get('http://localhost:4000/api/v1/client/user/info', {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      timeout: 10000
    });

    console.log('✅ 用户信息获取成功:', userInfoResponse.data);

    // 检查头像字段
    if (userInfoResponse.data && userInfoResponse.data.data) {
      const userData = userInfoResponse.data.data;
      console.log('\n🖼️ 头像信息分析:');
      console.log('- 用户昵称:', userData.nickname);
      console.log('- 头像URL:', userData.avatar || '(空)');
      console.log('- 头像类型:', userData.avatar ?
        (userData.avatar.includes('thirdwx.qlogo.cn') ? '失效的微信头像' :
         userData.avatar.includes('http') ? '网络头像' :
         userData.avatar.startsWith('/') ? '本地头像' : '其他') : '无头像');

      if (!userData.avatar || userData.avatar === '') {
        console.log('✅ 头像为空，前端将显示默认头像');
      } else if (userData.avatar.includes('thirdwx.qlogo.cn') ||
                 userData.avatar.includes('wx.qlogo.cn') ||
                 userData.avatar.includes('mmopen')) {
        console.log('❌ 仍然包含失效的微信头像URL');
      } else {
        console.log('✅ 头像URL有效');
      }
    }

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    if (error.response) {
      console.error('响应状态:', error.response.status);
      console.error('响应数据:', error.response.data);
    } else {
      console.error('其他错误:', error.code || error.message);
    }
  }
}

// 运行测试
testAvatarFix();
