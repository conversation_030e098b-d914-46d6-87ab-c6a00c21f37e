const axios = require('axios');
const jwt = require('jsonwebtoken');

async function testFrontendFix() {
  try {
    console.log('🔧 测试前端修复后的订单创建...');
    
    // 生成JWT token
    const config = require('./config');
    const userId = 1;
    
    const token = jwt.sign(
      { id: userId, openid: 'oW20C7mVlW8e3W2AgUGtDTJeAbQU', role: 'user', session_key: 'gEow49sNFy+UU8J7ojrEPQ==' },
      config.jwt.secret,
      { expiresIn: config.jwt.expiresIn }
    );
    
    console.log('🔑 Token生成成功');
    
    // 模拟修复后的前端请求（正确传递商品ID）
    console.log('\n📝 测试修复后的订单创建...');
    const orderData = {
      addressId: 2,
      goods: [
        { goodsId: 8, quantity: 1, specificationId: 0 } // 正确传递商品ID 8，而不是购物车项ID 16
      ],
      remark: '测试前端修复',
      paymentMethod: 'wechat',
      couponId: 0,
      fromCart: false,
      deliveryMethodId: 1,
      freight: 10
    };
    
    console.log('修复后的订单数据:', orderData);
    
    const createOrderResponse = await axios.post('http://localhost:4000/api/v1/client/order/create', orderData, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      timeout: 10000
    });
    
    console.log('✅ 订单创建成功:', createOrderResponse.data);
    
    // 检查订单详情
    if (createOrderResponse.data && createOrderResponse.data.orderId) {
      console.log('\n📋 检查订单详情...');
      const orderId = createOrderResponse.data.orderId;
      
      // 查询订单详情
      const orderDetailResponse = await axios.get(`http://localhost:4000/api/v1/client/order/client/detail/${orderId}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      console.log('📋 订单详情:', orderDetailResponse.data);
    }
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    if (error.response) {
      console.error('响应状态:', error.response.status);
      console.error('响应数据:', error.response.data);
    } else {
      console.error('其他错误:', error.code || error.message);
    }
  }
}

// 运行测试
testFrontendFix();
