// 测试修复后的视频音频处理器
const { main } = require('./video-audio-processor.ts');

// 测试用例1：正常数据
async function testNormalData() {
  console.log('=== 测试正常数据 ===');
  
  const params = {
    audio_list_data: [
      { data: { link: 'https://example.com/audio1.mp3' } },
      { data: { link: 'https://example.com/audio2.mp3' } }
    ],
    texts: ['这是第一段文本', '这是第二段文本'],
    duration_data: [
      { duration: 3.5 },
      { duration: 4.2 }
    ],
    bg_big_text: '未来',
    bg_audio_url: 'https://example.com/bg_audio.mp3',
    bg_video_url: 'https://example.com/bg_video.mp4'
  };

  try {
    const result = await main({ params });
    console.log('✅ 正常数据测试通过');
    console.log('结果:', JSON.stringify(result, null, 2));
  } catch (error) {
    console.error('❌ 正常数据测试失败:', error.message);
  }
}

// 测试用例2：空数据（应该抛出错误）
async function testEmptyData() {
  console.log('\n=== 测试空数据 ===');
  
  const params = {
    audio_list_data: [],
    texts: null,
    duration_data: [],
    bg_big_text: '未来',
    bg_audio_url: 'https://example.com/bg_audio.mp3',
    bg_video_url: 'https://example.com/bg_video.mp4'
  };

  try {
    const result = await main({ params });
    console.error('❌ 空数据测试失败：应该抛出错误但没有');
  } catch (error) {
    console.log('✅ 空数据测试通过，正确抛出错误:', error.message);
  }
}

// 测试用例3：数组长度不匹配（应该抛出错误）
async function testMismatchedArrays() {
  console.log('\n=== 测试数组长度不匹配 ===');
  
  const params = {
    audio_list_data: [
      { data: { link: 'https://example.com/audio1.mp3' } }
    ],
    texts: ['文本1', '文本2'], // 长度不匹配
    duration_data: [
      { duration: 3.5 }
    ],
    bg_big_text: '未来',
    bg_audio_url: 'https://example.com/bg_audio.mp3',
    bg_video_url: 'https://example.com/bg_video.mp4'
  };

  try {
    const result = await main({ params });
    console.error('❌ 数组长度不匹配测试失败：应该抛出错误但没有');
  } catch (error) {
    console.log('✅ 数组长度不匹配测试通过，正确抛出错误:', error.message);
  }
}

// 测试用例4：无效数据结构（应该抛出错误）
async function testInvalidStructure() {
  console.log('\n=== 测试无效数据结构 ===');
  
  const params = {
    audio_list_data: [
      { data: { link: 'https://example.com/audio1.mp3' } },
      { invalid: 'structure' } // 无效结构
    ],
    texts: ['文本1', '文本2'],
    duration_data: [
      { duration: 3.5 },
      { duration: 4.2 }
    ],
    bg_big_text: '未来',
    bg_audio_url: 'https://example.com/bg_audio.mp3',
    bg_video_url: 'https://example.com/bg_video.mp4'
  };

  try {
    const result = await main({ params });
    console.error('❌ 无效数据结构测试失败：应该抛出错误但没有');
  } catch (error) {
    console.log('✅ 无效数据结构测试通过，正确抛出错误:', error.message);
  }
}

// 测试用例5：模拟你遇到的问题
async function testYourProblem() {
  console.log('\n=== 测试你遇到的问题场景 ===');
  
  const params = {
    audio_list_data: {}, // 你的错误显示为 {0}
    texts: null, // 你的错误显示为 null
    duration_data: {}, // 你的错误显示为 {0}
    bg_big_text: '未来',
    bg_audio_url: 'https://p26-bot-workflow-sign.byteimg.com/tos-cn-i-mdko3gqilj/13218cb9486e4cb5a39fc04e53e3a9e3.MP3~tplv-mdko3gqilj-image.image?rk3s=81d4c505&x-expires=1784211521&x-signature=htZuRTNtdecW3OQR4w8PvkRqh%2Bc%3D&x-wf-file_name=7%E6%9C%8821%E6%97%A5%E7%B4%A0%E6%9D%90%E9%9F%B3%E4%B9%90.MP3',
    bg_video_url: 'https://p9-bot-workflow-sign.byteimg.com/tos-cn-i-mdko3gqilj/ff6da3cde9ad41d5aa19e80b76ad21fd.mp4~tplv-mdko3gqilj-image.image?rk3s=81d4c505&x-expires=1784211775&x-signature=49CpTpu6sg4j8qxY7ZuzShFQtyc%3D&x-wf-file_name=7%E6%9C%8821%E6%97%A5%E7%B4%A0%E6%9D%90%E8%A7%86%E9%A2%91.mp4'
  };

  try {
    const result = await main({ params });
    console.error('❌ 问题场景测试失败：应该抛出错误但没有');
  } catch (error) {
    console.log('✅ 问题场景测试通过，正确抛出错误:', error.message);
  }
}

// 运行所有测试
async function runAllTests() {
  await testNormalData();
  await testEmptyData();
  await testMismatchedArrays();
  await testInvalidStructure();
  await testYourProblem();
  
  console.log('\n=== 测试完成 ===');
  console.log('修复建议:');
  console.log('1. 确保 audio_list_data 是一个数组，且每个元素都有 data.link 属性');
  console.log('2. 确保 texts 是一个非空字符串数组');
  console.log('3. 确保 duration_data 是一个数组，且每个元素都有 duration 属性（正数）');
  console.log('4. 确保三个数组的长度相同');
  console.log('5. 在调用函数前检查参数的有效性');
}

// 如果直接运行此文件
if (require.main === module) {
  runAllTests().catch(console.error);
}

module.exports = {
  testNormalData,
  testEmptyData,
  testMismatchedArrays,
  testInvalidStructure,
  testYourProblem,
  runAllTests
};
