const express = require('express');
const router = express.Router();
const { body } = require('express-validator');
const { verifyToken, checkRole } = require('../middlewares/auth');
const db = require('../database');
const logger = require('../utils/logger');

/**
 * 获取系统配置
 * GET /api/v1/admin/system/config
 */
router.get('/config', verifyToken, checkRole('admin'), async (req, res, next) => {
  try {
    // 模拟系统配置数据
    const config = {
      basic: {
        siteName: '华红WIFI共享商业系统',
        siteLogo: 'data:image/svg+xml,%3Csvg width="200" height="60" xmlns="http://www.w3.org/2000/svg"%3E%3Crect width="200" height="60" fill="%234A90E2"/%3E%3Ctext x="50%25" y="50%25" text-anchor="middle" dy=".3em" fill="white" font-size="24" font-weight="bold"%3EWIFI共享%3C/text%3E%3C/svg%3E',
        copyright: '© 2024 华红科技有限公司',
        icp: '京ICP备**********号'
      },
      payment: {
        enableWechat: 1,
        wechatMchId: '**********',
        wechatAppId: 'wx**********abcdef',
        wechatApiKey: '',
        enableAlipay: 1,
        alipayAppId: '202100**********',
        alipayPublicKey: '',
        alipayPrivateKey: ''
      },
      logistics: {
        apiProvider: 'kdniao',
        appId: 'test123456',
        appKey: '',
        feeType: 1,
        defaultFee: 10
      },
      sms: {
        provider: 'aliyun',
        accessKeyId: 'LTAI4xxxxxxxxxxxx',
        accessKeySecret: '',
        signName: '华红科技',
        templates: {
          verifyCode: 'SMS_123456789',
          orderNotify: 'SMS_123456790',
          deliveryNotify: 'SMS_123456791'
        }
      },
      updated_at: new Date().toISOString().replace('T', ' ').slice(0, 19)
    };

    res.json({
      status: 'success',
      data: config
    });
  } catch (error) {
    next(error);
  }
});

/**
 * 更新系统配置
 * PUT /api/v1/admin/system/config/update
 */
router.put('/config/update', verifyToken, checkRole('admin'), async (req, res, next) => {
  try {
    const { basic, payment, logistics, sms } = req.body;
    
    // 这里应该保存到数据库，暂时模拟成功响应
    logger.info('系统配置更新', { 
      userId: req.user.id,
      updates: req.body
    });

    res.json({
      status: 'success',
      message: '系统配置更新成功'
    });
  } catch (error) {
    next(error);
  }
});

/**
 * 获取角色列表
 * GET /api/v1/admin/system/role/list
 */
router.get('/role/list', verifyToken, checkRole('admin'), async (req, res, next) => {
  try {
    // 模拟角色数据
    const roles = [
      {
        id: 1,
        name: '超级管理员',
        code: 'super_admin',
        description: '拥有系统所有权限',
        permissions: ['*'],
        status: 1,
        created_at: '2024-01-01 00:00:00',
        updated_at: '2024-01-01 00:00:00'
      },
      {
        id: 2,
        name: '运营管理员',
        code: 'operation_admin',
        description: '负责日常运营管理',
        permissions: ['wifi:*', 'user:*', 'order:*', 'ad:*'],
        status: 1,
        created_at: '2024-01-15 10:00:00',
        updated_at: '2024-12-20 15:00:00'
      }
    ];

    res.json({
      status: 'success',
      data: {
        list: roles,
        total: roles.length
      }
    });
  } catch (error) {
    next(error);
  }
});

/**
 * 创建角色
 * POST /api/v1/admin/system/role/create
 */
router.post('/role/create', verifyToken, checkRole('admin'), [
  body('name').notEmpty().withMessage('角色名称不能为空'),
  body('code').notEmpty().withMessage('角色代码不能为空'),
  body('description').optional().isLength({ max: 255 }).withMessage('描述长度不能超过255字符')
], async (req, res, next) => {
  try {
    const { name, code, description, permissions, status } = req.body;
    
    // 模拟创建角色
    const newRole = {
      id: Date.now(),
      name,
      code,
      description: description || '',
      permissions: permissions || [],
      status: status || 1,
      created_at: new Date().toISOString().replace('T', ' ').slice(0, 19),
      updated_at: new Date().toISOString().replace('T', ' ').slice(0, 19)
    };

    res.json({
      status: 'success',
      data: newRole,
      message: '角色创建成功'
    });
  } catch (error) {
    next(error);
  }
});

/**
 * 更新角色
 * PUT /api/v1/admin/system/role/update/:id
 */
router.put('/role/update/:id', verifyToken, checkRole('admin'), async (req, res, next) => {
  try {
    const { id } = req.params;
    const { name, code, description, permissions, status } = req.body;
    
    // 不允许修改超级管理员角色
    if (parseInt(id) === 1) {
      return res.status(400).json({
        status: 'error',
        message: '不能修改超级管理员角色'
      });
    }

    res.json({
      status: 'success',
      message: '角色更新成功'
    });
  } catch (error) {
    next(error);
  }
});

/**
 * 删除角色
 * DELETE /api/v1/admin/system/role/delete/:id
 */
router.delete('/role/delete/:id', verifyToken, checkRole('admin'), async (req, res, next) => {
  try {
    const { id } = req.params;
    
    // 不允许删除超级管理员角色
    if (parseInt(id) === 1) {
      return res.status(400).json({
        status: 'error',
        message: '超级管理员角色不能删除'
      });
    }

    res.json({
      status: 'success',
      message: '角色删除成功'
    });
  } catch (error) {
    next(error);
  }
});

/**
 * 获取账号列表
 * GET /api/v1/admin/system/account/list
 */
router.get('/account/list', verifyToken, checkRole('admin'), async (req, res, next) => {
  try {
    const { username, nickname, role_id, status } = req.query;
    
    // 模拟账号数据
    let accounts = [
      {
        id: 1,
        username: 'admin',
        nickname: '超级管理员',
        email: '<EMAIL>',
        phone: '***********',
        avatar: 'data:image/svg+xml,%3Csvg width="100" height="100" xmlns="http://www.w3.org/2000/svg"%3E%3Ccircle cx="50" cy="50" r="50" fill="%234A90E2"/%3E%3Ctext x="50%25" y="50%25" text-anchor="middle" dy=".3em" fill="white" font-size="40"%3EA%3C/text%3E%3C/svg%3E',
        role_id: 1,
        role_name: '超级管理员',
        status: 1,
        last_login_time: '2025-01-07 14:00:00',
        last_login_ip: '127.0.0.1',
        created_at: '2024-01-01 00:00:00',
        updated_at: '2025-01-07 14:00:00'
      },
      {
        id: 2,
        username: 'operation',
        nickname: '运营小王',
        email: '<EMAIL>',
        phone: '***********',
        avatar: '',
        role_id: 2,
        role_name: '运营管理员',
        status: 1,
        last_login_time: '2025-01-07 09:00:00',
        last_login_ip: '*************',
        created_at: '2024-01-15 10:00:00',
        updated_at: '2025-01-07 09:00:00'
      }
    ];

    // 根据查询条件过滤
    if (username) {
      accounts = accounts.filter(item => item.username.includes(username));
    }
    if (nickname) {
      accounts = accounts.filter(item => item.nickname.includes(nickname));
    }
    if (role_id) {
      accounts = accounts.filter(item => item.role_id === parseInt(role_id));
    }
    if (status !== undefined && status !== '') {
      accounts = accounts.filter(item => item.status === parseInt(status));
    }

    res.json({
      status: 'success',
      data: {
        total: accounts.length,
        list: accounts
      }
    });
  } catch (error) {
    next(error);
  }
});

/**
 * 创建账号
 * POST /api/v1/admin/system/account/create
 */
router.post('/account/create', verifyToken, checkRole('admin'), [
  body('username').notEmpty().withMessage('用户名不能为空'),
  body('password').isLength({ min: 6 }).withMessage('密码长度至少6位'),
  body('role_id').isInt().withMessage('角色ID必须是整数')
], async (req, res, next) => {
  try {
    const { username, password, nickname, email, phone, role_id, status } = req.body;
    
    // 模拟创建账号
    const newAccount = {
      id: Date.now(),
      username,
      nickname: nickname || username,
      email: email || '',
      phone: phone || '',
      avatar: '',
      role_id: parseInt(role_id),
      role_name: '运营管理员', // 根据role_id获取角色名
      status: status || 1,
      last_login_time: '',
      last_login_ip: '',
      created_at: new Date().toISOString().replace('T', ' ').slice(0, 19),
      updated_at: new Date().toISOString().replace('T', ' ').slice(0, 19)
    };

    res.json({
      status: 'success',
      data: newAccount,
      message: '账号创建成功'
    });
  } catch (error) {
    next(error);
  }
});

/**
 * 更新账号
 * PUT /api/v1/admin/system/account/update/:id
 */
router.put('/account/update/:id', verifyToken, checkRole('admin'), async (req, res, next) => {
  try {
    const { id } = req.params;
    const { nickname, email, phone, role_id, status } = req.body;
    
    res.json({
      status: 'success',
      message: '账号更新成功'
    });
  } catch (error) {
    next(error);
  }
});

/**
 * 删除账号
 * DELETE /api/v1/admin/system/account/delete/:id
 */
router.delete('/account/delete/:id', verifyToken, checkRole('admin'), async (req, res, next) => {
  try {
    const { id } = req.params;
    
    // 不允许删除超级管理员账号
    if (parseInt(id) === 1) {
      return res.status(400).json({
        status: 'error',
        message: '超级管理员账号不能删除'
      });
    }

    res.json({
      status: 'success',
      message: '账号删除成功'
    });
  } catch (error) {
    next(error);
  }
});

/**
 * 获取操作日志
 * GET /api/v1/admin/system/log/operation
 */
router.get('/log/operation', verifyToken, checkRole('admin'), async (req, res, next) => {
  try {
    const { page = 1, limit = 20, username, module, start_time, end_time } = req.query;
    
    // 模拟操作日志数据
    const logs = [
      {
        id: 1,
        user_id: 1,
        username: 'admin',
        module: '系统管理',
        action: '更新系统配置',
        method: 'PUT',
        url: '/api/v1/admin/system/config/update',
        ip: '127.0.0.1',
        user_agent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)',
        params: '{"site_name":"华红WIFI共享商业系统"}',
        result: 'success',
        created_at: '2025-01-07 15:30:00'
      },
      {
        id: 2,
        user_id: 2,
        username: 'operation',
        module: 'WiFi管理',
        action: '创建WiFi热点',
        method: 'POST',
        url: '/api/v1/admin/wifi/create',
        ip: '*************',
        user_agent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)',
        params: '{"name":"星巴克-朝阳店","password":"starbucks2024"}',
        result: 'success',
        created_at: '2025-01-07 14:20:00'
      }
    ];

    res.json({
      status: 'success',
      data: {
        list: logs,
        total: logs.length,
        page: parseInt(page),
        limit: parseInt(limit)
      }
    });
  } catch (error) {
    next(error);
  }
});

/**
 * 获取登录日志
 * GET /api/v1/admin/system/log/login
 */
router.get('/log/login', verifyToken, checkRole('admin'), async (req, res, next) => {
  try {
    const { page = 1, limit = 20, username, ip, start_time, end_time } = req.query;
    
    // 模拟登录日志数据
    const logs = [
      {
        id: 1,
        user_id: 1,
        username: 'admin',
        ip: '127.0.0.1',
        user_agent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)',
        result: 'success',
        created_at: '2025-01-07 15:30:00'
      },
      {
        id: 2,
        user_id: 2,
        username: 'operation',
        ip: '*************',
        user_agent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)',
        result: 'success',
        created_at: '2025-01-07 14:20:00'
      }
    ];

    res.json({
      status: 'success',
      data: {
        list: logs,
        total: logs.length,
        page: parseInt(page),
        limit: parseInt(limit)
      }
    });
  } catch (error) {
    next(error);
  }
});

/**
 * 获取错误日志
 * GET /api/v1/admin/system/log/error
 */
router.get('/log/error', verifyToken, checkRole('admin'), async (req, res, next) => {
  try {
    const { page = 1, limit = 20, level, start_time, end_time } = req.query;
    
    // 模拟错误日志数据
    const logs = [
      {
        id: 1,
        level: 'error',
        message: '数据库连接失败',
        stack: 'Error: connect ECONNREFUSED 127.0.0.1:3306',
        url: '/api/v1/admin/user/list',
        method: 'GET',
        ip: '127.0.0.1',
        user_agent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)',
        created_at: '2025-01-07 15:30:00'
      }
    ];

    res.json({
      status: 'success',
      data: {
        list: logs,
        total: logs.length,
        page: parseInt(page),
        limit: parseInt(limit)
      }
    });
  } catch (error) {
    next(error);
  }
});

module.exports = router; 