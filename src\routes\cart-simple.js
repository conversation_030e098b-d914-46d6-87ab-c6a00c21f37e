const express = require('express');
const router = express.Router();
const { body } = require('express-validator');
const { verifyToken } = require('../middlewares/auth');
const { success, error } = require('../utils/response');
const db = require('../database');

/**
 * 获取购物车商品列表
 * GET /api/v1/client/cart/list
 */
router.get('/list', verifyToken, async (req, res) => {
  try {
    const userId = req.user.id;
    console.log('获取用户购物车数据，用户ID:', userId);
    
    // 查询购物车数据
    const cartItems = await db.query(`
      SELECT c.id, c.goods_id as goodsId, c.quantity, c.specs_id as specificationId, c.selected,
             g.title as name, g.price, g.cover, g.stock
      FROM cart c
      JOIN goods g ON c.goods_id = g.id
      WHERE c.user_id = ?
      ORDER BY c.created_at DESC
    `, [userId]);
    
    console.log('查询到的购物车数据:', cartItems);
    
    // 如果购物车为空，返回空数组
    if (!cartItems || cartItems.length === 0) {
      return success(res, {
        list: [],
        totalPrice: 0,
        totalQuantity: 0
      }, '购物车为空');
    }
  
    // 计算总价和总数量
    const totalPrice = cartItems.reduce((sum, item) => 
        item.selected ? sum + (parseFloat(item.price) * item.quantity) : sum, 0);
    
    const totalQuantity = cartItems.reduce((sum, item) => 
      item.selected ? sum + item.quantity : sum, 0);
  
    return success(res, {
      list: cartItems,
      totalPrice,
      totalQuantity
    }, '获取购物车列表成功');
  } catch (err) {
    console.error('获取购物车数据失败:', err);
    return error(res, '获取购物车数据失败', 500);
  }
});

/**
 * 添加商品到购物车
 * POST /api/v1/client/cart/add
 */
router.post('/add', [
  body('goodsId').notEmpty().withMessage('商品ID不能为空'),
  body('quantity').notEmpty().isInt({ min: 1 }).withMessage('数量必须是大于0的整数')
], verifyToken, async (req, res) => {
  try {
    const userId = req.user.id;
    const { goodsId, quantity, specificationId = 0 } = req.body;
    
    console.log('添加商品到购物车，用户ID:', userId, '商品ID:', goodsId, '数量:', quantity);
    
    // 查询商品是否存在
    const goodsExists = await db.query('SELECT id, title, price, stock FROM goods WHERE id = ? AND status = 1', [goodsId]);
    
    if (!goodsExists || goodsExists.length === 0) {
      return error(res, '商品不存在或已下架', 404);
    }
    
    const goods = goodsExists[0];
    const quantityNum = parseInt(quantity);
    
    // 检查库存
    if (quantityNum > goods.stock) {
      return error(res, `库存不足，当前库存：${goods.stock}`, 400);
    }
    
    // 检查购物车中是否已有该商品
    const existingCartItem = await db.query(
      'SELECT id, quantity FROM cart WHERE user_id = ? AND goods_id = ? AND specs_id = ?',
      [userId, goodsId, specificationId]
    );
    
    let cartItem;
    
    // 如果购物车中已有该商品，更新数量
    if (existingCartItem && existingCartItem.length > 0) {
      const newQuantity = existingCartItem[0].quantity + quantityNum;
      
      // 检查新数量是否超过库存
      if (newQuantity > goods.stock) {
        return error(res, `库存不足，当前库存：${goods.stock}，购物车中已有：${existingCartItem[0].quantity}`, 400);
      }
      
      await db.query(
        'UPDATE cart SET quantity = ?, updated_at = NOW() WHERE id = ?',
        [newQuantity, existingCartItem[0].id]
      );
      
      cartItem = {
        id: existingCartItem[0].id,
        goodsId,
        quantity: newQuantity,
        specificationId,
        selected: 1
      };
    }
    // 如果购物车中没有该商品，添加新记录
    else {
      const result = await db.query(
        'INSERT INTO cart (user_id, goods_id, quantity, specs_id, selected) VALUES (?, ?, ?, ?, 1)',
        [userId, goodsId, quantityNum, specificationId]
      );
      
      if (!result || !result.insertId) {
        return error(res, '添加到购物车失败', 500);
      }
      
      cartItem = {
        id: result.insertId,
        goodsId,
        quantity: quantityNum,
        specificationId,
        selected: 1
      };
    }
  
    return success(res, cartItem, '商品已成功添加到购物车');
  } catch (err) {
    console.error('添加购物车失败:', err);
    return error(res, '添加到购物车失败', 500);
  }
});

module.exports = router;
