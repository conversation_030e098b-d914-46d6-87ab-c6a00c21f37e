const axios = require('axios');

const BASE_URL = 'http://localhost:4000';

async function testRoutes() {
  try {
    console.log('🧪 测试路由状态...\n');
    
    // 1. 测试服务器是否运行
    console.log('1️⃣ 测试服务器根路径');
    try {
      const rootResponse = await axios.get(`${BASE_URL}/`);
      console.log('✅ 服务器运行正常，状态:', rootResponse.status);
    } catch (error) {
      console.log('❌ 服务器根路径访问失败:', error.message);
      console.log('请确保服务器正在运行：npm start');
      return;
    }
    
    // 2. 测试API根路径
    console.log('\n2️⃣ 测试API根路径');
    try {
      const apiResponse = await axios.get(`${BASE_URL}/api`);
      console.log('✅ API根路径访问成功，状态:', apiResponse.status);
    } catch (error) {
      console.log('❌ API根路径访问失败:', error.response?.status, error.response?.data);
    }
    
    // 3. 测试v1路径
    console.log('\n3️⃣ 测试v1路径');
    try {
      const v1Response = await axios.get(`${BASE_URL}/api/v1`);
      console.log('✅ v1路径访问成功，状态:', v1Response.status);
    } catch (error) {
      console.log('❌ v1路径访问失败:', error.response?.status, error.response?.data);
    }
    
    // 4. 测试auth路径
    console.log('\n4️⃣ 测试auth路径');
    try {
      const authResponse = await axios.get(`${BASE_URL}/api/v1/auth`);
      console.log('✅ auth路径访问成功，状态:', authResponse.status);
    } catch (error) {
      console.log('❌ auth路径访问失败:', error.response?.status, error.response?.data);
    }
    
    // 5. 测试admin-login路径（GET请求，应该返回405）
    console.log('\n5️⃣ 测试admin-login路径（GET请求）');
    try {
      const loginGetResponse = await axios.get(`${BASE_URL}/api/v1/auth/admin-login`);
      console.log('✅ admin-login GET访问成功，状态:', loginGetResponse.status);
    } catch (error) {
      if (error.response?.status === 405) {
        console.log('✅ admin-login路径存在，但不支持GET请求（正常）');
      } else {
        console.log('❌ admin-login GET访问失败:', error.response?.status, error.response?.data);
      }
    }
    
    // 6. 测试POST请求但不带参数
    console.log('\n6️⃣ 测试admin-login路径（POST请求，无参数）');
    try {
      const loginPostResponse = await axios.post(`${BASE_URL}/api/v1/auth/admin-login`, {});
      console.log('✅ admin-login POST访问成功，状态:', loginPostResponse.status);
    } catch (error) {
      if (error.response?.status === 400) {
        console.log('✅ admin-login路径存在，参数验证正常');
        console.log('响应:', error.response?.data);
      } else {
        console.log('❌ admin-login POST访问失败:', error.response?.status, error.response?.data);
      }
    }
    
    // 7. 测试正确的登录
    console.log('\n7️⃣ 测试正确的管理员登录');
    try {
      const loginResponse = await axios.post(`${BASE_URL}/api/v1/auth/admin-login`, {
        username: 'admin',
        password: 'admin123'
      });
      console.log('✅ 管理员登录成功，状态:', loginResponse.status);
      console.log('响应数据:', JSON.stringify(loginResponse.data, null, 2));
      
      // 如果登录成功，测试团队列表
      if (loginResponse.data && loginResponse.data.data && loginResponse.data.data.token) {
        const token = loginResponse.data.data.token;
        console.log('\n8️⃣ 测试团队列表（使用token）');
        try {
          const teamResponse = await axios.get(`${BASE_URL}/api/v1/admin/team/list`, {
            headers: {
              'Authorization': `Bearer ${token}`
            }
          });
          console.log('✅ 团队列表访问成功，状态:', teamResponse.status);
          console.log('团队数据:', JSON.stringify(teamResponse.data, null, 2));
        } catch (teamError) {
          console.log('❌ 团队列表访问失败:', teamError.response?.status, teamError.response?.data);
        }
      }
      
    } catch (error) {
      console.log('❌ 管理员登录失败:', error.response?.status, error.response?.data);
    }
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  }
}

testRoutes();
