<!--pages/user/profile/profile.wxml-->
<view class="profile-container">
  <!-- 顶部广告区域 -->
  <view class="ad-banner" bindtap="onAdClick">
    <image class="ad-image" src="/assets/images/user-ad-banner.jpg" mode="aspectFill"></image>
    <view class="ad-overlay">
      <text class="ad-text">广告区域</text>
    </view>
  </view>

  <!-- 用户信息区域 -->
  <view class="user-section">
    <view class="user-info">
      <view class="user-left" bindtap="{{!isLoggedIn ? 'onLogin' : ''}}">
        <!-- 已登录且未完善资料时显示头像选择按钮 -->
        <block wx:if="{{isLoggedIn && (!isProfileCompleted || isDemote)}}">
          <view class="avatar-button" bindtap="forceGetWechatInfo">
            <image class="user-avatar" src="{{userInfo.avatar || '/assets/images/default-avatar.png'}}" mode="aspectFill" binderror="onAvatarError"></image>
            <view class="avatar-edit-icon">点击设置</view>
          </view>
        </block>
        <!-- 已登录且已完善资料时显示普通头像 -->
        <block wx:elif="{{isLoggedIn}}">
          <image class="user-avatar" src="{{userInfo.avatar || '/assets/images/default-avatar.png'}}" mode="aspectFill" binderror="onAvatarError"></image>
        </block>
        <!-- 未登录时显示默认头像 -->
        <block wx:else>
          <image class="user-avatar" src="/assets/icons/user-placeholder.png" mode="aspectFill"></image>
        </block>
        
        <view class="user-detail">
          <!-- 已登录且未完善资料时显示昵称输入框 -->
          <block wx:if="{{isLoggedIn && (!isProfileCompleted || isDemote)}}">
            <input class="nickname-input" type="nickname" placeholder="请输入昵称" value="{{userInfo.nickname !== '微信用户' ? userInfo.nickname : ''}}" bindchange="onInputNickname" />
          </block>
          <!-- 已登录且已完善资料时显示昵称 -->
          <block wx:elif="{{isLoggedIn}}">
            <text class="user-nickname">{{userInfo.nickname || '微信用户'}}</text>
            <text class="user-id" wx:if="{{isLoggedIn}}">ID: {{userInfo.id || 'xxxxxxxx'}}</text>
          </block>
          <!-- 未登录时显示提示文字 -->
          <block wx:else>
            <text class="user-nickname">点击登录</text>
          </block>
        </view>
      </view>
      <view class="user-right">
        <button class="logout-btn" bindtap="{{isLoggedIn ? 'onLogout' : 'onLogin'}}">
          {{isLoggedIn ? '退出' : '登录'}}
        </button>
        <!-- 已登录且未完善资料时显示保存按钮 -->
        <button wx:if="{{isLoggedIn && (!isProfileCompleted || isDemote)}}" class="save-btn" bindtap="onSaveUserInfo">
          保存
        </button>
        <!-- 获取真实信息按钮 -->
        <button wx:if="{{isLoggedIn && isDemote}}" class="real-info-btn" bindtap="getRealUserInfo">
          获取真实信息
        </button>
        <!-- 获取微信头像按钮 -->
        <button wx:if="{{isLoggedIn}}" class="get-avatar-btn" bindtap="forceGetWechatInfo">
          获取微信头像
        </button>
      </view>
    </view>
  </view>

  <!-- 我的订单区域 -->
  <view class="order-section">
    <view class="section-header" bindtap="onViewAllOrders">
      <text class="section-title">我的订单</text>
      <view class="section-action">
        <text class="action-text">全部订单</text>
        <text class="arrow-icon">></text>
      </view>
    </view>
    <view class="order-status-list">
      <view class="order-status-item" bindtap="onViewOrdersByStatus" data-status="1">
        <view class="status-icon-wrapper">
          <text class="status-icon">💰</text>
          <text class="status-badge" wx:if="{{orderStats.pending > 0}}">{{orderStats.pending}}</text>
        </view>
        <text class="status-text">待付款</text>
      </view>
      <view class="order-status-item" bindtap="onViewOrdersByStatus" data-status="2">
        <view class="status-icon-wrapper">
          <text class="status-icon">📦</text>
          <text class="status-badge" wx:if="{{orderStats.shipped > 0}}">{{orderStats.shipped}}</text>
        </view>
        <text class="status-text">待发货</text>
      </view>
      <view class="order-status-item" bindtap="onViewOrdersByStatus" data-status="3">
        <view class="status-icon-wrapper">
          <text class="status-icon">🚚</text>
          <text class="status-badge" wx:if="{{orderStats.delivering > 0}}">{{orderStats.delivering}}</text>
        </view>
        <text class="status-text">待收货</text>
      </view>
      <view class="order-status-item" bindtap="onViewOrdersByStatus" data-status="4">
        <view class="status-icon-wrapper">
          <text class="status-icon">✅</text>
        </view>
        <text class="status-text">已完成</text>
      </view>
    </view>
  </view>

  <!-- 功能菜单区域 -->
  <view class="menu-section">
    <!-- 第一行：我的钱包 | 我的团队 -->
    <view class="menu-row">
      <view class="menu-item" bindtap="onNavigateToWallet">
        <text class="menu-icon">💳</text>
        <text class="menu-text">我的钱包</text>
        <text class="menu-arrow">></text>
      </view>
      <view class="menu-divider"></view>
      <view class="menu-item" bindtap="onNavigateToTeam">
        <text class="menu-icon">👥</text>
        <text class="menu-text">我的团队</text>
        <text class="menu-arrow">></text>
      </view>
    </view>

    <!-- 第二行：我的客服 | 帮助中心 -->
    <view class="menu-row">
      <view class="menu-item">
        <button class="contact-button" open-type="contact" bindcontact="onContactService">
        <text class="menu-icon">🎧</text>
        <text class="menu-text">我的客服</text>
        <text class="menu-arrow">></text>
        </button>
      </view>
      <view class="menu-divider"></view>
      <view class="menu-item" bindtap="onNavigateToHelp">
        <text class="menu-icon">❓</text>
        <text class="menu-text">帮助中心</text>
        <text class="menu-arrow">></text>
      </view>
    </view>

    <!-- 第三行：消息中心 | 邀请好友 -->
    <view class="menu-row">
      <view class="menu-item" bindtap="onNavigateToMessages">
        <text class="menu-icon">📧</text>
        <text class="menu-text">消息中心</text>
        <text class="menu-badge" wx:if="{{messageCount > 0}}">{{messageCount}}</text>
        <text class="menu-arrow">></text>
      </view>
      <view class="menu-divider"></view>
      <view class="menu-item" bindtap="onInviteFriends">
        <text class="menu-icon">🎁</text>
        <text class="menu-text">邀请好友</text>
        <text class="menu-arrow">></text>
      </view>
    </view>

    <!-- 第四行：收货地址管理 -->
    <view class="menu-single-row">
      <view class="menu-item-full" bindtap="onNavigateToAddress">
        <text class="menu-icon">📍</text>
        <text class="menu-text">收货地址管理</text>
        <text class="menu-arrow">></text>
      </view>
    </view>

    <!-- 腾讯广告区域 -->
    <view class="ad-container">
      <ad unit-id="{{adUnitId}}" ad-type="banner" ad-theme="white" bindload="onAdLoad" binderror="onAdError" bindtap="onAdClick"></ad>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{loading}}">
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 空状态 -->
  <view class="empty-container" wx:if="{{!isLoggedIn && !loading}}">
    <text class="empty-text">请先登录查看更多信息</text>
    <button class="login-btn" bindtap="onLogin">立即登录</button>
  </view>

  <!-- 头像昵称编辑弹窗 -->
  <view class="avatar-edit-modal" wx:if="{{showAvatarEdit}}">
    <view class="modal-mask" bindtap="cancelAvatarEdit"></view>
    <view class="modal-content">
      <view class="modal-header">
        <text class="modal-title">设置头像和昵称</text>
        <view class="modal-close" bindtap="cancelAvatarEdit">×</view>
      </view>
      <view class="modal-body">
        <view class="avatar-section">
          <text class="section-title">选择头像</text>
          <!-- 主要头像选择按钮 - 使用相册选择替代微信头像选择 -->
          <button class="avatar-choose-btn {{isChoosingAvatar ? 'choosing' : ''}}"
                  bindtap="chooseFromAlbum"
                  disabled="{{isChoosingAvatar}}">
            <image class="preview-avatar" src="{{tempUserInfo.avatar || '/assets/images/default-avatar.png'}}" mode="aspectFill"></image>
            <view class="choose-text">
              {{isChoosingAvatar ? '选择中...' : (tempUserInfo.avatar ? '重新选择头像' : '点击选择头像')}}
            </view>
          </button>
          <!-- 开发者工具提示 -->
          <view class="dev-tip">
            <text class="tip-text">💡 开发者工具中微信头像选择可能有限制，建议使用下方按钮或真机测试</text>
          </view>
          <!-- 头像选择选项 -->
          <view class="avatar-options">
            <button class="option-btn primary" bindtap="chooseFromAlbum" disabled="{{isChoosingAvatar}}">📷 从相册选择</button>
            <button class="option-btn secondary"
                    open-type="chooseAvatar"
                    bind:chooseavatar="onChooseAvatar"
                    disabled="{{isChoosingAvatar}}">👤 微信头像</button>
            <button class="option-btn default" bindtap="useDefaultAvatar" disabled="{{isChoosingAvatar}}">🎭 默认头像</button>
          </view>
          <!-- 提示信息 -->
          <view class="avatar-tip">
            <text class="tip-text">💡 推荐使用"从相册选择"，微信头像在开发者工具中可能不稳定</text>
          </view>
        </view>
        <view class="nickname-section">
          <text class="section-title">输入昵称</text>
          <form bindsubmit="onNicknameSubmit">
            <input class="nickname-input-modal"
                   type="nickname"
                   placeholder="请输入昵称"
                   value="{{tempUserInfo.nickname}}"
                   bindinput="onNicknameInput"
                   bindblur="onNicknameBlur"
                   maxlength="20"
                   name="nickname" />
          </form>
          <view class="nickname-tip">
            <text class="tip-text">💡 输入时键盘上方会显示微信昵称建议</text>
          </view>
        </view>
      </view>
      <view class="modal-footer">
        <button class="cancel-btn" bindtap="cancelAvatarEdit">取消</button>
        <button class="save-btn" bindtap="saveNewUserInfo">保存</button>
      </view>
    </view>
  </view>
</view>