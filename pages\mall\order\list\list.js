// pages/mall/order/list/list.js
const { request } = require('../../../../utils/request')
const { showToast, showLoading, hideLoading } = require('../../../../utils/util')
const API = require('../../../../config/api')

Page({
  data: {
    currentTab: 0, // 当前选中的标签页
    tabs: [
      { name: '全部', status: -1, count: 0 },
      { name: '待付款', status: 0, count: 0 },
      { name: '待发货', status: 1, count: 0 },
      { name: '待收货', status: 2, count: 0 },
      { name: '已完成', status: 3, count: 0 }
    ],
    orders: [], // 订单列表
    page: 1,
    limit: 10,
    hasMore: true,
    loading: false,
    refreshing: false,

    // 搜索相关
    showSearch: false,
    searchKeyword: '',

    // 筛选相关
    showFilterPopup: false,
    filterStatus: -1,
    filterTime: '',
    filterAmount: '',
    filterStatusOptions: [
      { name: '全部状态', status: -1 },
      { name: '待付款', status: 0 },
      { name: '待发货', status: 1 },
      { name: '待收货', status: 2 },
      { name: '已完成', status: 3 },
      { name: '已取消', status: 4 }
    ],
    filterTimeOptions: [
      { name: '全部时间', value: '' },
      { name: '最近一周', value: '7d' },
      { name: '最近一月', value: '30d' },
      { name: '最近三月', value: '90d' }
    ],
    filterAmountOptions: [
      { name: '全部金额', value: '' },
      { name: '0-50元', value: '0-50' },
      { name: '50-100元', value: '50-100' },
      { name: '100-500元', value: '100-500' },
      { name: '500元以上', value: '500+' }
    ]
  },

  onLoad: function (options) {
    console.log('📦 订单列表页面加载', options)

    // 清空之前的订单数据，强制重新加载
    this.setData({
      orders: [],
      page: 1,
      hasMore: true
    })

    // 如果有传入状态参数，设置对应的tab
    if (options.status !== undefined) {
      const tabIndex = this.data.tabs.findIndex(tab => tab.status == options.status)
      if (tabIndex !== -1) {
        this.setData({ currentTab: tabIndex })
      }
    }

    this.fetchOrderList()
  },

  onShow: function () {
    // 页面显示时刷新数据
    this.refreshOrderList()
  },

  /**
   * 切换标签页
   */
  onTabChange: function (e) {
    const index = e.currentTarget.dataset.index
    this.setData({
      currentTab: index,
      page: 1,
      hasMore: true
    })
    this.fetchOrderList()
  },

  /**
   * 获取订单列表
   */
  fetchOrderList: function () {
    if (this.data.loading || !this.data.hasMore) return

    this.setData({ loading: true })

    const currentTab = this.data.tabs[this.data.currentTab]
    const params = {
      page: this.data.page,
      limit: this.data.limit
    }

    // 如果不是全部订单，添加状态筛选
    if (currentTab.status !== -1) {
      params.status = currentTab.status
    }

    // 添加搜索参数
    if (this.data.searchKeyword) {
      params.keyword = this.data.searchKeyword
    }

    // 添加筛选参数
    if (this.data.filterStatus !== -1 && this.data.filterStatus !== currentTab.status) {
      params.status = this.data.filterStatus
    }

    if (this.data.filterTime) {
      params.timeRange = this.data.filterTime
    }

    if (this.data.filterAmount) {
      params.amountRange = this.data.filterAmount
    }

    request({
      url: API.order.list,
      method: 'GET',
      data: params
    }).then(res => {
      console.log('📦 订单列表响应:', res)
      // 兼容不同的响应格式
      if ((res.code === 0 || res.code === 200 || res.success === true) && res.data) {
        const newOrders = res.data.list || []
        const orders = this.data.page === 1
          ? newOrders
          : [...this.data.orders, ...newOrders]

        this.setData({
          orders,
          hasMore: newOrders.length === this.data.limit,
          page: this.data.page + 1,
          loading: false,
          refreshing: false
        })
        console.log('✅ 订单列表更新成功，共', orders.length, '条记录')
        console.log('📋 订单详细信息:', orders.map(order => ({
          id: order.id,
          status: order.status,
          status_name: order.status_name
        })))
      } else {
        console.error('❌ 订单列表响应格式错误:', res)
        showToast('获取订单列表失败')
        this.setData({ loading: false, refreshing: false })
      }
    }).catch(err => {
      console.error('❌ 获取订单列表失败:', err)
      showToast('获取订单列表失败')
      this.setData({ loading: false, refreshing: false })
    })
  },

  /**
   * 刷新订单列表
   */
  refreshOrderList: function () {
    this.setData({
      page: 1,
      hasMore: true,
      refreshing: true
    })
    this.fetchOrderList()
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh: function () {
    this.refreshOrderList()
    setTimeout(() => {
      wx.stopPullDownRefresh()
    }, 1000)
  },

  /**
   * 上拉加载更多
   */
  onReachBottom: function () {
    this.fetchOrderList()
  },

  /**
   * 查看订单详情
   */
  onViewDetail: function (e) {
    const orderId = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/mall/order/detail/detail?id=${orderId}`
    })
  },

  /**
   * 取消订单
   */
  onCancelOrder: function (e) {
    const orderId = e.currentTarget.dataset.id
    const orderNo = e.currentTarget.dataset.orderNo

    wx.showModal({
      title: '确认取消',
      content: '确定要取消这个订单吗？',
      success: (res) => {
        if (res.confirm) {
          this.cancelOrder(orderId, orderNo)
        }
      }
    })
  },

  /**
   * 执行取消订单
   */
  cancelOrder: function (orderId, orderNo) {
    wx.showLoading({ title: '取消中...' })

    request({
      url: `${API.order.cancel}/${orderId}`,
      method: 'POST'
    }).then(res => {
      wx.hideLoading()
      if ((res.code === 0 || res.code === 200 || res.success === true)) {
        showToast('订单已取消')
        this.refreshOrderList()
      } else {
        showToast(res.message || '取消订单失败')
      }
    }).catch(err => {
      wx.hideLoading()
      console.error('取消订单失败:', err)
      showToast('取消订单失败')
    })
  },

  /**
   * 确认收货
   */
  onConfirmReceive: function (e) {
    const orderId = e.currentTarget.dataset.id

    wx.showModal({
      title: '确认收货',
      content: '确定已收到商品吗？',
      success: (res) => {
        if (res.confirm) {
          this.confirmReceive(orderId)
        }
      }
    })
  },

  /**
   * 执行确认收货
   */
  confirmReceive: function (orderId) {
    wx.showLoading({ title: '确认中...' })

    request({
      url: `${API.order.confirm}/${orderId}`,
      method: 'POST'
    }).then(res => {
      wx.hideLoading()
      if ((res.code === 0 || res.code === 200 || res.success === true)) {
        showToast('确认收货成功')
        this.refreshOrderList()
      } else {
        showToast(res.message || '确认收货失败')
      }
    }).catch(err => {
      wx.hideLoading()
      console.error('确认收货失败:', err)
      showToast('确认收货失败')
    })
  },

  /**
   * 立即支付
   */
  onPayNow: function (e) {
    const orderId = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/mall/order/payment/payment?orderId=${orderId}`
    })
  },

  /**
   * 查看物流
   */
  onViewLogistics: function (e) {
    const orderId = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/mall/order/logistics/logistics?orderId=${orderId}`
    })
  },

  /**
   * 删除订单
   */
  onDeleteOrder: function(e) {
    const orderId = e.currentTarget.dataset.id
    const orderNo = e.currentTarget.dataset.orderNo

    wx.showModal({
      title: '确认删除',
      content: `确定要删除订单 ${orderNo} 吗？删除后无法恢复。`,
      confirmText: '删除',
      confirmColor: '#ff4757',
      success: (res) => {
        if (res.confirm) {
          this.deleteOrder(orderId)
        }
      }
    })
  },

  /**
   * 执行删除订单
   */
  deleteOrder: async function(orderId) {
    try {
      showLoading('删除中...')

      const res = await request({
        url: `${API.order.delete}/${orderId}`,
        method: 'DELETE'
      })

      hideLoading()

      if (res.code === 0 || res.success) {
        showToast('订单删除成功')
        // 刷新订单列表
        this.setData({
          orders: [],
          page: 1,
          hasMore: true
        })
        this.fetchOrderList()
      } else {
        showToast(res.message || '删除失败')
      }
    } catch (err) {
      hideLoading()
      console.error('删除订单失败:', err)
      showToast('删除失败，请稍后重试')
    }
  },

  /**
   * 去逛逛
   */
  onGoShopping: function () {
    wx.switchTab({
      url: '/pages/mall/index/index'
    })
  },

  /**
   * 切换搜索栏显示
   */
  onToggleSearch: function () {
    this.setData({
      showSearch: !this.data.showSearch
    })
  },

  /**
   * 搜索输入
   */
  onSearchInput: function (e) {
    this.setData({
      searchKeyword: e.detail.value
    })
  },

  /**
   * 搜索确认
   */
  onSearchConfirm: function () {
    this.refreshOrderList()
  },

  /**
   * 清除搜索
   */
  onClearSearch: function () {
    this.setData({
      searchKeyword: ''
    })
    this.refreshOrderList()
  },

  /**
   * 显示筛选弹窗
   */
  onShowFilter: function () {
    this.setData({
      showFilterPopup: true
    })
  },

  /**
   * 隐藏筛选弹窗
   */
  onHideFilter: function () {
    this.setData({
      showFilterPopup: false
    })
  },

  /**
   * 选择筛选状态
   */
  onSelectFilterStatus: function (e) {
    this.setData({
      filterStatus: e.currentTarget.dataset.status
    })
  },

  /**
   * 选择筛选时间
   */
  onSelectFilterTime: function (e) {
    this.setData({
      filterTime: e.currentTarget.dataset.time
    })
  },

  /**
   * 选择筛选金额
   */
  onSelectFilterAmount: function (e) {
    this.setData({
      filterAmount: e.currentTarget.dataset.amount
    })
  },

  /**
   * 重置筛选
   */
  onResetFilter: function () {
    this.setData({
      filterStatus: -1,
      filterTime: '',
      filterAmount: ''
    })
  },

  /**
   * 确认筛选
   */
  onConfirmFilter: function () {
    this.setData({
      showFilterPopup: false
    })
    this.refreshOrderList()
  },

  /**
   * 阻止弹窗滚动穿透
   */
  preventTouchMove: function () {
    return false
  }
})