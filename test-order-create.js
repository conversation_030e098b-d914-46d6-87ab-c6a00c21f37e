const axios = require('axios');
const jwt = require('jsonwebtoken');
const mysql = require('mysql2/promise');

async function testOrderCreate() {
  let connection;
  try {
    console.log('开始测试订单创建功能...');
    
    // 连接数据库检查数据
    console.log('1. 连接数据库检查数据...');
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: 'wo587129955',
      database: 'mall'
    });
    
    // 检查用户是否存在
    const [users] = await connection.execute('SELECT id, openid, nickname FROM user WHERE id = 2');
    console.log('用户信息:', users);
    
    if (users.length === 0) {
      console.error('用户ID 2 不存在');
      return;
    }
    
    // 检查地址是否存在
    const [addresses] = await connection.execute('SELECT * FROM user_address WHERE user_id = 2 LIMIT 1');
    console.log('地址信息:', addresses);
    
    if (addresses.length === 0) {
      console.error('用户没有地址');
      return;
    }
    
    // 检查商品是否存在
    const [goods] = await connection.execute('SELECT id, title, price, stock FROM goods WHERE id = 1');
    console.log('商品信息:', goods);
    
    if (goods.length === 0) {
      console.error('商品ID 1 不存在');
      return;
    }
    
    // 直接生成JWT token
    console.log('\n2. 生成测试token...');
    const config = require('./config');
    const userId = 2;
    
    const token = jwt.sign(
      { id: userId, openid: users[0].openid },
      config.jwt.secret,
      { expiresIn: config.jwt.expiresIn }
    );
    
    console.log('生成的token:', token);
    
    // 测试创建订单
    console.log('\n3. 测试创建订单...');
    const orderData = {
      addressId: addresses[0].id,
      goods: [
        {
          goodsId: 1,
          quantity: 2
        }
      ],
      remark: '测试订单备注',
      paymentMethod: 'wechat',
      couponId: 0,
      fromCart: false
    };
    
    console.log('订单数据:', orderData);
    
    const createOrderResponse = await axios.post('http://localhost:4000/api/v1/client/order/create', orderData, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      timeout: 10000
    });
    
    console.log('订单创建响应:', createOrderResponse.data);
    
    // 检查订单数据
    console.log('\n4. 检查订单数据...');
    const [orderItems] = await connection.execute('SELECT * FROM orders WHERE user_id = 2 ORDER BY id DESC LIMIT 1');
    console.log('订单数据:', orderItems);
    
    if (orderItems.length > 0) {
      const orderId = orderItems[0].id;
      const [orderGoods] = await connection.execute('SELECT * FROM order_goods WHERE order_id = ?', [orderId]);
      console.log('订单商品数据:', orderGoods);
    }
    
  } catch (error) {
    console.error('测试失败:', error.message);
    if (error.response) {
      console.error('响应状态:', error.response.status);
      console.error('响应数据:', error.response.data);
    } else if (error.code === 'ECONNREFUSED') {
      console.error('连接被拒绝，请确保服务器正在运行');
    } else {
      console.error('其他错误:', error.code || error.message);
      console.error('错误堆栈:', error.stack);
    }
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// 运行测试
testOrderCreate();
