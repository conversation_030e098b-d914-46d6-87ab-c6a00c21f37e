/* pages/user/income-details/income-details.wxss */
.income-details-container {
  min-height: 100vh;
  background-color: #f8f9fa;
  padding-bottom: 120rpx;
}

/* 统计信息头部 - 简洁专业风格 */
.stats-header {
  background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%);
  padding: 50rpx 30rpx 40rpx;
  color: white;
  position: relative;
}

.stats-card {
  background: rgba(255, 255, 255, 0.15);
  border-radius: 12rpx;
  padding: 40rpx 30rpx;
  backdrop-filter: blur(20rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}

.stats-title {
  font-size: 28rpx;
  font-weight: 600;
  text-align: center;
  margin-bottom: 40rpx;
  opacity: 0.95;
}

.stats-grid {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stats-item {
  text-align: center;
  flex: 1;
  position: relative;
}

.stats-item:not(:last-child)::after {
  content: '';
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 1rpx;
  height: 60rpx;
  background: rgba(255, 255, 255, 0.2);
}

.stats-value {
  font-size: 32rpx;
  font-weight: 700;
  margin-bottom: 8rpx;
  letter-spacing: 0.5rpx;
}

.stats-label {
  font-size: 22rpx;
  opacity: 0.85;
  font-weight: 400;
}

/* 筛选器 - 简洁设计 */
.filter-section {
  background: white;
  padding: 24rpx 30rpx;
  margin: 16rpx 30rpx;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

.filter-label {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
}

.filter-picker {
  flex: 1;
  max-width: 180rpx;
}

.picker-text {
  font-size: 26rpx;
  color: #4a90e2;
  text-align: right;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  font-weight: 500;
}

.picker-arrow {
  margin-left: 8rpx;
  font-size: 18rpx;
  opacity: 0.7;
}

/* 收入列表 - 卡片式设计 */
.income-list {
  padding: 0 30rpx;
}

.income-item {
  background: white;
  border-radius: 12rpx;
  padding: 32rpx;
  margin-bottom: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
  transition: all 0.2s ease;
  border: 1rpx solid #f0f0f0;
}

.income-item:active {
  transform: translateY(2rpx);
  box-shadow: 0 1rpx 6rpx rgba(0, 0, 0, 0.08);
}

/* 收入头部 - 优化布局 */
.income-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.income-type {
  display: flex;
  align-items: center;
}

.type-icon {
  font-size: 28rpx;
  margin-right: 10rpx;
  opacity: 0.8;
}

.type-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #2c3e50;
}

.income-amount {
  font-size: 30rpx;
  font-weight: 600;
  color: #27ae60;
  letter-spacing: 0.5rpx;
}

/* 收入内容 - 简化信息层次 */
.income-content {
  margin-bottom: 20rpx;
}

.income-title {
  font-size: 26rpx;
  color: #34495e;
  margin-bottom: 6rpx;
  font-weight: 500;
  line-height: 1.3;
}

.income-desc {
  font-size: 24rpx;
  color: #7f8c8d;
  line-height: 1.4;
}

/* 收入底部 - 精简设计 */
.income-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.income-time {
  font-size: 22rpx;
  color: #95a5a6;
}

.income-status {
  display: flex;
  align-items: center;
}

.status-text {
  font-size: 20rpx;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  color: white;
  font-weight: 500;
}

.status-text.confirmed {
  background-color: #27ae60;
}

.status-text.pending {
  background-color: #f39c12;
}

/* 来源信息 - 淡化处理 */
.income-source {
  border-top: 1rpx solid #ecf0f1;
  padding-top: 16rpx;
  display: flex;
  align-items: center;
}

.source-label {
  font-size: 22rpx;
  color: #bdc3c7;
  margin-right: 8rpx;
}

.source-value {
  font-size: 22rpx;
  color: #95a5a6;
}

/* 空状态 - 简洁设计 */
.empty-state {
  text-align: center;
  padding: 80rpx 30rpx;
  background: white;
  border-radius: 12rpx;
  margin: 20rpx 0;
}

.empty-icon {
  width: 100rpx;
  height: 100rpx;
  margin-bottom: 24rpx;
  opacity: 0.4;
}

.empty-text {
  font-size: 26rpx;
  color: #95a5a6;
  margin-bottom: 32rpx;
}

.empty-action {
  background-color: #4a90e2;
  color: white;
  border-radius: 24rpx;
  padding: 16rpx 32rpx;
  font-size: 26rpx;
  border: none;
  font-weight: 500;
}

/* 加载更多 */
.load-more {
  text-align: center;
  padding: 30rpx;
  color: #999;
  font-size: 26rpx;
}

.load-more-text {
  color: #999;
}

/* 没有更多 */
.no-more {
  text-align: center;
  padding: 30rpx;
  color: #ccc;
  font-size: 24rpx;
}

/* 底部统计 */
.bottom-stats {
  text-align: center;
  padding: 30rpx;
  background: white;
  margin: 20rpx 0;
  border-radius: 16rpx;
}

.stats-text {
  font-size: 26rpx;
  color: #666;
}

/* 加载状态 */
.loading-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.loading-content {
  background: white;
  border-radius: 16rpx;
  padding: 60rpx;
  text-align: center;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #e5e5e5;
  border-top: 4rpx solid #007aff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20rpx;
}

/* 刷新状态 */
.refresh-indicator {
  position: fixed;
  top: 100rpx;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 20rpx 40rpx;
  border-radius: 50rpx;
  font-size: 26rpx;
  display: flex;
  align-items: center;
  z-index: 1000;
}

.refresh-spinner {
  width: 30rpx;
  height: 30rpx;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  border-top: 2rpx solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 16rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
