const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');
const path = require('path');

async function testCompleteAvatarFlow() {
  try {
    console.log('🧪 测试完整的头像上传流程...\n');
    
    // 1. 创建一个测试图片文件（模拟）
    console.log('1️⃣ 准备测试图片...');
    const testImageData = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==';
    
    // 2. 测试文件上传接口
    console.log('2️⃣ 测试文件上传接口...');
    
    try {
      const uploadResponse = await axios.post('http://localhost:4000/api/v1/client/upload', {
        imageData: testImageData
      }, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer test-token' // 这里需要真实的token
        },
        timeout: 10000
      });
      
      console.log('✅ 文件上传成功:');
      console.log('响应数据:', JSON.stringify(uploadResponse.data, null, 2));
      
      const uploadedUrl = uploadResponse.data.data.url;
      console.log('上传的文件URL:', uploadedUrl);
      
      // 3. 构建完整的头像URL
      let fullAvatarUrl = uploadedUrl;
      if (uploadedUrl.startsWith('/uploads/')) {
        fullAvatarUrl = `http://localhost:4000${uploadedUrl}`;
      }
      console.log('完整的头像URL:', fullAvatarUrl);
      
      // 4. 测试用户信息更新接口
      console.log('\n3️⃣ 测试用户信息更新接口...');
      
      const updateData = {
        nickname: '润生',
        avatar: fullAvatarUrl
      };
      
      console.log('发送的更新数据:', JSON.stringify(updateData, null, 2));
      
      try {
        const updateResponse = await axios.post('http://localhost:4000/api/v1/client/user/update', updateData, {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer test-token' // 这里需要真实的token
          },
          timeout: 10000
        });
        
        console.log('✅ 用户信息更新成功:');
        console.log('响应数据:', JSON.stringify(updateResponse.data, null, 2));
        
        // 5. 验证数据库中的数据
        console.log('\n4️⃣ 验证数据库中的数据...');
        await verifyDatabaseData(fullAvatarUrl);
        
      } catch (updateError) {
        console.log('❌ 用户信息更新失败:');
        if (updateError.response) {
          console.log('状态码:', updateError.response.status);
          console.log('错误信息:', JSON.stringify(updateError.response.data, null, 2));
        } else {
          console.log('错误:', updateError.message);
        }
      }
      
    } catch (uploadError) {
      console.log('❌ 文件上传失败:');
      if (uploadError.response) {
        console.log('状态码:', uploadError.response.status);
        console.log('错误信息:', JSON.stringify(uploadError.response.data, null, 2));
      } else if (uploadError.code === 'ECONNREFUSED') {
        console.log('无法连接到服务器，请确保后端服务正在运行');
      } else {
        console.log('错误:', uploadError.message);
      }
    }
    
  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error.message);
  }
}

// 验证数据库数据
async function verifyDatabaseData(expectedAvatarUrl) {
  const mysql = require('mysql2/promise');
  
  try {
    const connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: 'wo587129955',
      database: 'mall'
    });
    
    const [users] = await connection.execute('SELECT id, nickname, avatar FROM user WHERE id = 1');
    
    console.log('数据库中的用户数据:');
    console.table(users);
    
    if (users.length > 0) {
      const user = users[0];
      if (user.avatar === expectedAvatarUrl) {
        console.log('✅ 数据库验证成功：头像URL匹配');
      } else {
        console.log('❌ 数据库验证失败：头像URL不匹配');
        console.log('期望的URL:', expectedAvatarUrl);
        console.log('实际的URL:', user.avatar);
      }
    }
    
    await connection.end();
    
  } catch (error) {
    console.error('❌ 数据库验证失败:', error.message);
  }
}

// 运行测试
testCompleteAvatarFlow();
