// pages/user/withdraw/withdraw.js
// 提现页面

const request = require('../../../utils/request.js')

Page({
  data: {
    balance: 0,
    withdrawAmount: '',
    selectedMethod: '',
    selectedAccountId: null,
    wechatAccount: {},
    bankCards: [],
    config: {},
    feeInfo: {
      fee: 0,
      actual_amount: 0
    },
    quickAmounts: [50, 100, 200, 500],
    canSubmit: false,
    submitting: false,
    showConfirmModal: false,
    selectedBankCard: {}
  },

  onLoad(options) {
    console.log('💰 提现页面加载')
    this.loadUserBalance()
    this.loadWithdrawConfig()
    this.loadWithdrawMethods()
  },

  onShow() {
    // 页面显示时刷新提现方式（可能添加了新银行卡）
    this.loadWithdrawMethods()
  },

  // 加载用户余额
  async loadUserBalance() {
    try {
      console.log('💰 加载用户余额')
      
      const result = await request.get('/api/v1/client/income/stats')
      
      if (result.success) {
        this.setData({
          balance: parseFloat(result.data.balance || 0)
        })
        console.log('💰 用户余额:', this.data.balance)
      }
    } catch (error) {
      console.error('💰 加载用户余额失败:', error)
    }
  },

  // 加载提现配置
  async loadWithdrawConfig() {
    try {
      console.log('⚙️ 加载提现配置')
      
      const result = await request.get('/api/v1/client/withdraw/config')
      
      if (result.success) {
        this.setData({ config: result.data })
        console.log('⚙️ 提现配置:', this.data.config)
      }
    } catch (error) {
      console.error('⚙️ 加载提现配置失败:', error)
      // 使用默认配置
      this.setData({
        config: {
          min_amount: 10,
          max_amount: 50000,
          wechat_fee_rate: 0.006,
          bank_fee_rate: 0.001,
          wechat_min_fee: 0.1,
          bank_min_fee: 2
        }
      })
    }
  },

  // 加载提现方式
  async loadWithdrawMethods() {
    try {
      console.log('🏦 加载提现方式')
      
      const result = await request.get('/api/v1/client/withdraw/methods')
      
      if (result.success) {
        this.setData({
          wechatAccount: result.data.wechat_accounts[0] || {},
          bankCards: result.data.bank_cards || []
        })
        
        // 默认选择微信提现（如果有）
        if (result.data.wechat_accounts.length > 0) {
          this.selectMethod({
            currentTarget: {
              dataset: {
                type: 'wechat',
                id: result.data.wechat_accounts[0].id
              }
            }
          })
        } else if (result.data.bank_cards.length > 0) {
          // 如果没有微信账户，选择第一张银行卡
          this.selectMethod({
            currentTarget: {
              dataset: {
                type: 'bank_card',
                id: result.data.bank_cards[0].id
              }
            }
          })
        }
        
        console.log('🏦 提现方式加载成功')
      }
    } catch (error) {
      console.error('🏦 加载提现方式失败:', error)
      wx.showToast({
        title: '加载提现方式失败',
        icon: 'none'
      })
    }
  },

  // 输入提现金额
  onAmountInput(e) {
    const amount = e.detail.value
    this.setData({ withdrawAmount: amount })
    
    // 实时计算手续费
    this.calculateFee(parseFloat(amount) || 0)
    this.checkCanSubmit()
  },

  // 选择提现方式
  selectMethod(e) {
    const { type, id } = e.currentTarget.dataset
    console.log('🏦 选择提现方式:', type, id)
    
    this.setData({
      selectedMethod: type,
      selectedAccountId: parseInt(id)
    })
    
    // 找到选中的银行卡信息
    if (type === 'bank_card') {
      const selectedCard = this.data.bankCards.find(card => card.id === parseInt(id))
      this.setData({ selectedBankCard: selectedCard || {} })
    }
    
    // 重新计算手续费
    const amount = parseFloat(this.data.withdrawAmount) || 0
    this.calculateFee(amount)
    this.checkCanSubmit()
  },

  // 计算手续费
  async calculateFee(amount) {
    if (!amount || !this.data.selectedMethod) {
      this.setData({
        feeInfo: { fee: 0, actual_amount: 0 }
      })
      return
    }

    try {
      const result = await request.post('/api/v1/client/withdraw/calculate-fee', {
        amount,
        withdraw_type: this.data.selectedMethod
      })
      
      if (result.success) {
        this.setData({
          feeInfo: {
            fee: parseFloat(result.data.fee).toFixed(2),
            actual_amount: parseFloat(result.data.actual_amount).toFixed(2)
          }
        })
      }
    } catch (error) {
      console.error('💰 计算手续费失败:', error)
      // 本地计算手续费
      this.calculateFeeLocally(amount)
    }
  },

  // 本地计算手续费
  calculateFeeLocally(amount) {
    const { config, selectedMethod } = this.data
    let fee = 0
    
    if (selectedMethod === 'wechat') {
      fee = Math.max(amount * config.wechat_fee_rate, config.wechat_min_fee)
    } else if (selectedMethod === 'bank_card') {
      fee = Math.max(amount * config.bank_fee_rate, config.bank_min_fee)
    }
    
    const actualAmount = amount - fee
    
    this.setData({
      feeInfo: {
        fee: fee.toFixed(2),
        actual_amount: actualAmount.toFixed(2)
      }
    })
  },

  // 选择快捷金额
  selectQuickAmount(e) {
    const amount = e.currentTarget.dataset.amount
    this.setData({ withdrawAmount: amount.toString() })
    this.calculateFee(amount)
    this.checkCanSubmit()
  },

  // 选择全部余额
  selectAllBalance() {
    const balance = this.data.balance
    this.setData({ withdrawAmount: balance.toString() })
    this.calculateFee(balance)
    this.checkCanSubmit()
  },

  // 检查是否可以提交
  checkCanSubmit() {
    const { withdrawAmount, selectedMethod, balance, config } = this.data
    const amount = parseFloat(withdrawAmount) || 0
    
    const canSubmit = amount >= config.min_amount && 
                     amount <= config.max_amount && 
                     amount <= balance && 
                     selectedMethod &&
                     !this.data.submitting
    
    this.setData({ canSubmit })
  },

  // 提交提现申请
  submitWithdraw() {
    if (!this.data.canSubmit) return
    
    // 显示确认弹窗
    this.setData({ showConfirmModal: true })
  },

  // 隐藏确认弹窗
  hideConfirmModal() {
    this.setData({ showConfirmModal: false })
  },

  // 确认提现
  async confirmWithdraw() {
    this.setData({ 
      submitting: true,
      showConfirmModal: false
    })
    
    wx.showLoading({ title: '提交中...' })
    
    try {
      const result = await request.post('/api/v1/client/withdraw/apply', {
        amount: parseFloat(this.data.withdrawAmount),
        withdraw_type: this.data.selectedMethod,
        account_id: this.data.selectedAccountId,
        remark: '用户提现申请'
      })
      
      wx.hideLoading()
      
      if (result.success) {
        wx.showToast({
          title: '提现申请已提交',
          icon: 'success'
        })
        
        // 跳转到提现记录页面
        setTimeout(() => {
          wx.navigateTo({
            url: '/pages/user/withdraw-records/withdraw-records'
          })
        }, 1500)
      } else {
        wx.showToast({
          title: result.message || '提现申请失败',
          icon: 'none'
        })
      }
    } catch (error) {
      wx.hideLoading()
      console.error('💰 提现申请失败:', error)
      wx.showToast({
        title: '网络错误，请重试',
        icon: 'none'
      })
    } finally {
      this.setData({ submitting: false })
    }
  },

  // 添加银行卡
  addBankCard() {
    wx.showToast({
      title: '添加银行卡功能开发中',
      icon: 'none'
    })
    
    // TODO: 跳转到添加银行卡页面
    // wx.navigateTo({
    //   url: '/pages/user/add-bank-card/add-bank-card'
    // })
  },

  // 分享页面
  onShareAppMessage() {
    return {
      title: 'WiFi共享商城 - 提现',
      path: '/pages/user/withdraw/withdraw'
    }
  }
})
