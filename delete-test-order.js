const mysql = require('mysql2/promise');

async function deleteTestOrder() {
  let connection;

  try {
    console.log('开始连接数据库...');

    // 创建数据库连接
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: 'wo587129955',
      database: 'mall'
    });

    console.log('数据库连接成功');

    // 查找金额为1065.00的订单（从截图中看到的金额）
    const [orders] = await connection.execute(
      'SELECT id, order_no, user_id, status, total_amount, created_at FROM orders WHERE total_amount = ? ORDER BY created_at DESC',
      [1065.00]
    );

    console.log('找到的订单:', orders);

    if (orders.length > 0) {
      for (const order of orders) {
        console.log(`准备删除订单: ID=${order.id}, 订单号=${order.order_no}, 金额=${order.total_amount}`);
        
        // 删除订单商品
        await connection.execute('DELETE FROM order_goods WHERE order_id = ?', [order.id]);
        console.log(`已删除订单商品，订单ID: ${order.id}`);
        
        // 删除订单
        await connection.execute('DELETE FROM orders WHERE id = ?', [order.id]);
        console.log(`已删除订单，订单ID: ${order.id}`);
      }
    } else {
      console.log('未找到金额为1065.00的订单，查看所有最近的订单:');
      
      // 查看最近的订单
      const [recentOrders] = await connection.execute(
        'SELECT id, order_no, user_id, status, total_amount, created_at FROM orders ORDER BY created_at DESC LIMIT 10'
      );
      
      console.log('最近的订单:', recentOrders);
    }

  } catch (error) {
    console.error('操作失败:', error);
  } finally {
    if (connection) {
      await connection.end();
      console.log('数据库连接已关闭');
    }
  }
}

deleteTestOrder();
