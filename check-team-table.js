const mysql = require('mysql2');

const connection = mysql.createConnection({
  host: 'localhost',
  user: 'root',
  password: 'wo587129955',
  database: 'mall'
});

connection.query('DESCRIBE team', (error, results) => {
  if (error) {
    console.error('查询失败:', error);
  } else {
    console.log('团队表结构:');
    console.table(results);
  }
  
  // 检查是否有邀请码字段
  const hasInviteCode = results.some(field => field.Field === 'invite_code');
  
  if (!hasInviteCode) {
    console.log('\n需要添加邀请码字段，执行以下SQL:');
    console.log('ALTER TABLE team ADD COLUMN invite_code VARCHAR(20) UNIQUE;');
    console.log('ALTER TABLE team ADD COLUMN level INT DEFAULT 1;');
    
    // 自动添加字段
    connection.query('ALTER TABLE team ADD COLUMN invite_code VARCHAR(20) UNIQUE', (error) => {
      if (error && !error.message.includes('Duplicate column')) {
        console.error('添加invite_code字段失败:', error);
      } else {
        console.log('✅ invite_code字段添加成功');
      }
      
      connection.query('ALTER TABLE team ADD COLUMN level INT DEFAULT 1', (error) => {
        if (error && !error.message.includes('Duplicate column')) {
          console.error('添加level字段失败:', error);
        } else {
          console.log('✅ level字段添加成功');
        }
        
        // 为现有团队生成邀请码
        connection.query('UPDATE team SET invite_code = CONCAT("TEAM", LPAD(id, 6, "0")) WHERE invite_code IS NULL', (error) => {
          if (error) {
            console.error('生成邀请码失败:', error);
          } else {
            console.log('✅ 邀请码生成成功');
          }
          connection.end();
        });
      });
    });
  } else {
    console.log('✅ 团队表结构完整');
    connection.end();
  }
});
