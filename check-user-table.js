const db = require('./src/database');

async function checkUserTable() {
  try {
    console.log('🔍 检查用户表结构...\n');
    
    // 查询用户表结构
    const structure = await db.query('DESCRIBE user');
    console.log('📋 用户表结构:');
    console.table(structure);
    
    // 查询特定用户的信息
    console.log('\n👤 查询用户ID 1的信息:');
    const user1 = await db.query('SELECT * FROM user WHERE id = 1');
    console.table(user1);
    
    // 查询用户ID 3的信息
    console.log('\n👤 查询用户ID 3的信息:');
    const user3 = await db.query('SELECT * FROM user WHERE id = 3');
    console.table(user3);
    
    // 手动更新用户ID 1的团队信息
    console.log('\n🔧 手动更新用户ID 1的团队信息...');
    await db.query('UPDATE user SET team_id = 10, is_leader = 1 WHERE id = 1');
    
    // 再次查询用户ID 1的信息
    console.log('\n✅ 更新后的用户ID 1信息:');
    const updatedUser1 = await db.query('SELECT * FROM user WHERE id = 1');
    console.table(updatedUser1);
    
    process.exit(0);
  } catch (error) {
    console.error('❌ 检查失败:', error);
    process.exit(1);
  }
}

checkUserTable();
