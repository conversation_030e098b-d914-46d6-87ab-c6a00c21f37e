<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>创建提现相关图标</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .icon-container {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin-top: 20px;
        }
        .icon-item {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            text-align: center;
            width: 200px;
        }
        .icon-svg {
            width: 64px;
            height: 64px;
            margin-bottom: 10px;
        }
        .icon-name {
            font-weight: bold;
            margin-bottom: 5px;
        }
        .icon-desc {
            color: #666;
            font-size: 12px;
        }
        button {
            background: #007aff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-top: 10px;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <h1>提现功能图标生成器</h1>
    <p>点击下载按钮可以将SVG图标保存为PNG文件</p>
    
    <div class="icon-container">
        <!-- 微信支付图标 -->
        <div class="icon-item">
            <svg class="icon-svg" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
                <rect x="8" y="16" width="48" height="32" rx="8" fill="#07C160" stroke="#05A050" stroke-width="2"/>
                <circle cx="20" cy="28" r="3" fill="white"/>
                <circle cx="32" cy="28" r="3" fill="white"/>
                <circle cx="44" cy="28" r="3" fill="white"/>
                <path d="M16 36C16 36 24 40 32 36C32 36 40 40 48 36" stroke="white" stroke-width="2" fill="none"/>
            </svg>
            <div class="icon-name">微信支付</div>
            <div class="icon-desc">wechat-pay.png</div>
            <button onclick="downloadIcon(this, 'wechat-pay')">下载PNG</button>
        </div>
        
        <!-- 银行卡图标 -->
        <div class="icon-item">
            <svg class="icon-svg" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
                <rect x="8" y="20" width="48" height="28" rx="4" fill="#4A90E2" stroke="#357ABD" stroke-width="2"/>
                <rect x="8" y="26" width="48" height="6" fill="#2C5282"/>
                <rect x="12" y="36" width="16" height="4" rx="2" fill="white"/>
                <rect x="32" y="36" width="8" height="4" rx="2" fill="white"/>
                <rect x="44" y="36" width="8" height="4" rx="2" fill="white"/>
            </svg>
            <div class="icon-name">银行卡</div>
            <div class="icon-desc">bank-card.png</div>
            <button onclick="downloadIcon(this, 'bank-card')">下载PNG</button>
        </div>
        
        <!-- 空记录图标 -->
        <div class="icon-item">
            <svg class="icon-svg" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
                <rect x="16" y="12" width="32" height="40" rx="4" stroke="#CCC" stroke-width="2" fill="none"/>
                <line x1="24" y1="20" x2="40" y2="20" stroke="#CCC" stroke-width="2"/>
                <line x1="24" y1="28" x2="40" y2="28" stroke="#CCC" stroke-width="2"/>
                <line x1="24" y1="36" x2="32" y2="36" stroke="#CCC" stroke-width="2"/>
                <circle cx="32" cy="32" r="20" fill="none" stroke="#E0E0E0" stroke-width="1"/>
                <path d="M26 26L38 38M38 26L26 38" stroke="#E0E0E0" stroke-width="2"/>
            </svg>
            <div class="icon-name">空记录</div>
            <div class="icon-desc">empty-records.png</div>
            <button onclick="downloadIcon(this, 'empty-records')">下载PNG</button>
        </div>
        
        <!-- 刷新图标 -->
        <div class="icon-item">
            <svg class="icon-svg" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M32 8C40.8 8 48 15.2 48 24" stroke="#007AFF" stroke-width="3" fill="none"/>
                <path d="M40 16L48 24L40 32" stroke="#007AFF" stroke-width="3" fill="none"/>
                <path d="M32 56C23.2 56 16 48.8 16 40" stroke="#007AFF" stroke-width="3" fill="none"/>
                <path d="M24 48L16 40L24 32" stroke="#007AFF" stroke-width="3" fill="none"/>
            </svg>
            <div class="icon-name">刷新</div>
            <div class="icon-desc">refresh.png</div>
            <button onclick="downloadIcon(this, 'refresh')">下载PNG</button>
        </div>
        
        <!-- 选中图标 -->
        <div class="icon-item">
            <svg class="icon-svg" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
                <circle cx="32" cy="32" r="24" fill="#007AFF"/>
                <path d="M20 32L28 40L44 24" stroke="white" stroke-width="3" fill="none"/>
            </svg>
            <div class="icon-name">选中</div>
            <div class="icon-desc">check-selected.png</div>
            <button onclick="downloadIcon(this, 'check-selected')">下载PNG</button>
        </div>
        
        <!-- 未选中图标 -->
        <div class="icon-item">
            <svg class="icon-svg" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
                <circle cx="32" cy="32" r="24" fill="none" stroke="#CCC" stroke-width="2"/>
            </svg>
            <div class="icon-name">未选中</div>
            <div class="icon-desc">check-unselected.png</div>
            <button onclick="downloadIcon(this, 'check-unselected')">下载PNG</button>
        </div>
        
        <!-- 提现图标 -->
        <div class="icon-item">
            <svg class="icon-svg" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
                <rect x="12" y="20" width="40" height="24" rx="4" stroke="#FF6B35" stroke-width="2" fill="none"/>
                <path d="M32 12L32 32M24 20L32 12L40 20" stroke="#FF6B35" stroke-width="2" fill="none"/>
                <circle cx="20" cy="28" r="2" fill="#FF6B35"/>
                <circle cx="44" cy="28" r="2" fill="#FF6B35"/>
                <path d="M16 48C16 48 24 52 32 48C32 48 40 52 48 48" stroke="#FF6B35" stroke-width="2" fill="none"/>
            </svg>
            <div class="icon-name">提现</div>
            <div class="icon-desc">withdraw.png</div>
            <button onclick="downloadIcon(this, 'withdraw')">下载PNG</button>
        </div>
        
        <!-- 充值图标 -->
        <div class="icon-item">
            <svg class="icon-svg" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
                <rect x="12" y="20" width="40" height="24" rx="4" stroke="#34C759" stroke-width="2" fill="none"/>
                <path d="M32 52L32 32M24 44L32 52L40 44" stroke="#34C759" stroke-width="2" fill="none"/>
                <circle cx="20" cy="28" r="2" fill="#34C759"/>
                <circle cx="44" cy="28" r="2" fill="#34C759"/>
                <path d="M16 16C16 16 24 12 32 16C32 16 40 12 48 16" stroke="#34C759" stroke-width="2" fill="none"/>
            </svg>
            <div class="icon-name">充值</div>
            <div class="icon-desc">recharge.png</div>
            <button onclick="downloadIcon(this, 'recharge')">下载PNG</button>
        </div>
        
        <!-- 明细图标 -->
        <div class="icon-item">
            <svg class="icon-svg" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
                <rect x="16" y="8" width="32" height="48" rx="4" stroke="#5856D6" stroke-width="2" fill="none"/>
                <line x1="24" y1="20" x2="40" y2="20" stroke="#5856D6" stroke-width="2"/>
                <line x1="24" y1="28" x2="40" y2="28" stroke="#5856D6" stroke-width="2"/>
                <line x1="24" y1="36" x2="36" y2="36" stroke="#5856D6" stroke-width="2"/>
                <line x1="24" y1="44" x2="40" y2="44" stroke="#5856D6" stroke-width="2"/>
                <circle cx="20" cy="20" r="2" fill="#5856D6"/>
                <circle cx="20" cy="28" r="2" fill="#5856D6"/>
                <circle cx="20" cy="36" r="2" fill="#5856D6"/>
                <circle cx="20" cy="44" r="2" fill="#5856D6"/>
            </svg>
            <div class="icon-name">明细</div>
            <div class="icon-desc">details.png</div>
            <button onclick="downloadIcon(this, 'details')">下载PNG</button>
        </div>
    </div>

    <script>
        function downloadIcon(button, filename) {
            const iconItem = button.parentElement;
            const svg = iconItem.querySelector('.icon-svg');
            
            // 创建canvas
            const canvas = document.createElement('canvas');
            canvas.width = 64;
            canvas.height = 64;
            const ctx = canvas.getContext('2d');
            
            // 设置白色背景
            ctx.fillStyle = 'white';
            ctx.fillRect(0, 0, 64, 64);
            
            // 将SVG转换为图片
            const svgData = new XMLSerializer().serializeToString(svg);
            const img = new Image();
            const svgBlob = new Blob([svgData], {type: 'image/svg+xml;charset=utf-8'});
            const url = URL.createObjectURL(svgBlob);
            
            img.onload = function() {
                ctx.drawImage(img, 0, 0, 64, 64);
                
                // 下载PNG
                canvas.toBlob(function(blob) {
                    const link = document.createElement('a');
                    link.download = filename + '.png';
                    link.href = URL.createObjectURL(blob);
                    link.click();
                    URL.revokeObjectURL(url);
                }, 'image/png');
            };
            
            img.src = url;
        }
    </script>
</body>
</html>
