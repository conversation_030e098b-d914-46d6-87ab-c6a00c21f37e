// pages/user/address/list.js
const app = getApp();
const { STORAGE_KEYS } = require('../../../utils/constants');

Page({
  /**
   * 页面的初始数据
   */
  data: {
    addressList: [],
    loading: true,
    isLoggedIn: false,
    isEmpty: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    // 检查登录状态
    this.checkLoginStatus();

    // 如果是从订单确认页跳转来的，记录返回标记
    // 支持两种参数：from=order 或 select=true
    if (options.from === 'order' || options.select === 'true') {
      this.setData({
        fromOrder: true
      });
      console.log('从订单确认页面跳转到地址列表，启用选择模式');
    }
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    if (this.data.isLoggedIn) {
      this.loadAddressList();
    }
  },

  /**
   * 检查登录状态
   */
  checkLoginStatus: function() {
    const app = getApp();
    
    if (!app) {
      console.error('获取app实例失败');
      return;
    }
    
    // 获取全局登录状态
    const isLoggedIn = app.globalData.isLogin;
    
    this.setData({
      isLoggedIn
    });
    
    if (!isLoggedIn) {
      wx.showToast({
        title: '请先登录',
        icon: 'none',
        duration: 2000
      });
      
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  /**
   * 加载地址列表
   */
  loadAddressList: function() {
    this.setData({
      loading: true
    });
    
    // 添加时间戳和随机数防止缓存
    const timestamp = new Date().getTime();
    const random = Math.floor(Math.random() * 10000);
    
    // 调用获取地址列表接口
    wx.request({
      url: `${app.globalData.apiBase}/user/address/list?_t=${timestamp}&_r=${random}`,
      method: 'GET',
      header: {
        'Authorization': `Bearer ${wx.getStorageSync(STORAGE_KEYS.TOKEN)}`,
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache'
      },
      success: (res) => {
        console.log('获取地址列表结果:', res);
        if (res.data && res.data.status === 'success') {
          const addressList = res.data.data || [];
          
          this.setData({
            addressList,
            isEmpty: addressList.length === 0,
            loading: false
          });
        } else {
          this.setData({
            addressList: [],
            isEmpty: true,
            loading: false
          });
          
          wx.showToast({
            title: '获取地址失败',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        console.error('获取地址列表失败:', err);
        this.setData({
          addressList: [],
          isEmpty: true,
          loading: false
        });
        
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 添加新地址
   */
  onAddAddress: function() {
    wx.navigateTo({
      url: './edit?type=add'
    });
  },

  /**
   * 编辑地址
   */
  onEditAddress: function(e) {
    const addressId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `./edit?type=edit&id=${addressId}`
    });
  },

  /**
   * 删除地址
   */
  onDeleteAddress: function(e) {
    const addressId = e.currentTarget.dataset.id;
    
    wx.showModal({
      title: '提示',
      content: '确定要删除这个地址吗？',
      success: (res) => {
        if (res.confirm) {
          // 调用删除地址接口
          wx.request({
            url: `${app.globalData.apiBase}/user/address/delete`,
            method: 'POST',
            header: {
              'Authorization': `Bearer ${wx.getStorageSync(STORAGE_KEYS.TOKEN)}`
            },
            data: {
              id: addressId
            },
            success: (res) => {
              if (res.data && res.data.status === 'success') {
                wx.showToast({
                  title: '删除成功',
                  icon: 'success'
                });
                
                // 从本地列表中移除已删除的地址
                const updatedList = this.data.addressList.filter(item => item.id !== addressId);
                this.setData({
                  addressList: updatedList,
                  isEmpty: updatedList.length === 0
                });
                
                // 重新加载地址列表确保数据同步
                setTimeout(() => {
                  this.loadAddressList();
                }, 500);
              } else {
                wx.showToast({
                  title: '删除失败',
                  icon: 'none'
                });
              }
            },
            fail: (err) => {
              console.error('删除地址失败:', err);
              wx.showToast({
                title: '网络错误，请重试',
                icon: 'none'
              });
            }
          });
        }
      }
    });
  },

  /**
   * 设为默认地址
   */
  onSetDefault: function(e) {
    const addressId = e.currentTarget.dataset.id;
    
    // 调用设置默认地址接口
    wx.request({
      url: `${app.globalData.apiBase}/user/address/set-default`,
      method: 'POST',
      header: {
        'Authorization': `Bearer ${wx.getStorageSync(STORAGE_KEYS.TOKEN)}`
      },
      data: {
        id: addressId
      },
      success: (res) => {
        if (res.data && res.data.status === 'success') {
          wx.showToast({
            title: '设置成功',
            icon: 'success'
          });
          
          // 先在本地更新默认状态
          const updatedList = this.data.addressList.map(item => {
            return {
              ...item,
              is_default: item.id === addressId ? 1 : 0
            };
          });
          
          this.setData({
            addressList: updatedList
          });
          
          // 重新加载地址列表确保数据同步
          setTimeout(() => {
            this.loadAddressList();
          }, 500);
        } else {
          wx.showToast({
            title: '设置失败',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        console.error('设置默认地址失败:', err);
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 选择地址（从订单确认页进入时）
   */
  onSelectAddress: function(e) {
    if (!this.data.fromOrder) return;
    
    const addressId = e.currentTarget.dataset.id;
    const address = this.data.addressList.find(item => item.id === addressId);
    
    if (address) {
      // 将选中的地址信息传回订单确认页
      const pages = getCurrentPages();
      const prevPage = pages[pages.length - 2];
      
      // 调用上一个页面的方法，更新地址信息
      if (prevPage && prevPage.setAddress) {
        prevPage.setAddress(address);
      }
      
      wx.navigateBack();
    }
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh: function() {
    if (this.data.isLoggedIn) {
      this.loadAddressList();
      setTimeout(() => {
        wx.stopPullDownRefresh();
      }, 1000);
    } else {
      wx.stopPullDownRefresh();
    }
  }
}); 