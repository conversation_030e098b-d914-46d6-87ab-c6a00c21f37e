// 提现相关API接口
const express = require('express');
const router = express.Router();
const mysql = require('mysql2/promise');
const jwt = require('jsonwebtoken');

// 数据库连接配置
const dbConfig = {
  host: 'localhost',
  user: 'root',
  password: 'wo587129955',
  database: 'mall'
};

// JWT验证中间件
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({
      success: false,
      message: '未提供访问令牌'
    });
  }

  jwt.verify(token, 'wifi_share_secret_key', (err, user) => {
    if (err) {
      return res.status(403).json({
        success: false,
        message: '访问令牌无效'
      });
    }
    req.user = user;
    next();
  });
};

// 获取提现配置
router.get('/config', authenticateToken, async (req, res) => {
  let connection;
  
  try {
    connection = await mysql.createConnection(dbConfig);
    
    console.log('⚙️ 获取提现配置');
    
    // 查询提现配置
    const [configs] = await connection.execute(`
      SELECT config_key, config_value, config_type 
      FROM withdraw_config 
      WHERE status = 1
    `);
    
    // 转换配置格式
    const configMap = {};
    configs.forEach(config => {
      let value = config.config_value;
      
      // 根据类型转换值
      if (config.config_type === 'number') {
        value = parseFloat(value);
      } else if (config.config_type === 'boolean') {
        value = value === 'true';
      } else if (config.config_type === 'json') {
        try {
          value = JSON.parse(value);
        } catch (e) {
          console.error('JSON解析失败:', config.config_key, value);
        }
      }
      
      configMap[config.config_key] = value;
    });
    
    res.json({
      success: true,
      data: {
        min_amount: configMap.min_withdraw_amount || 10,
        max_amount: configMap.max_withdraw_amount || 50000,
        daily_limit: configMap.daily_withdraw_limit || 100000,
        wechat_fee_rate: configMap.wechat_fee_rate || 0.006,
        bank_fee_rate: configMap.bank_fee_rate || 0.001,
        wechat_min_fee: configMap.wechat_min_fee || 0.1,
        bank_min_fee: configMap.bank_min_fee || 2,
        auto_audit_enabled: configMap.auto_audit_enabled || false,
        auto_audit_limit: configMap.auto_audit_limit || 1000,
        working_hours: configMap.working_hours || { start: "09:00", end: "18:00" },
        weekend_process: configMap.weekend_process || false,
        notification_enabled: configMap.notification_enabled || true
      },
      message: '获取提现配置成功'
    });
    
  } catch (error) {
    console.error('⚙️ 获取提现配置失败:', error);
    res.status(500).json({
      success: false,
      message: '获取提现配置失败'
    });
  } finally {
    if (connection) {
      await connection.end();
    }
  }
});

// 获取提现方式列表
router.get('/methods', authenticateToken, async (req, res) => {
  let connection;
  
  try {
    connection = await mysql.createConnection(dbConfig);
    
    const userId = req.user.id;
    console.log('🏦 获取用户提现方式:', userId);
    
    // 查询微信账户
    const [wechatAccounts] = await connection.execute(`
      SELECT id, openid, nickname, real_name, is_verified, is_default, status
      FROM wechat_account 
      WHERE user_id = ? AND status = 1
    `, [userId]);
    
    // 查询银行卡
    const [bankCards] = await connection.execute(`
      SELECT id, bank_name, bank_code, card_number_mask, card_holder, 
             card_type, is_default, is_verified, status
      FROM bank_card 
      WHERE user_id = ? AND status = 1
    `, [userId]);
    
    res.json({
      success: true,
      data: {
        wechat_accounts: wechatAccounts,
        bank_cards: bankCards
      },
      message: '获取提现方式成功'
    });
    
  } catch (error) {
    console.error('🏦 获取提现方式失败:', error);
    res.status(500).json({
      success: false,
      message: '获取提现方式失败'
    });
  } finally {
    if (connection) {
      await connection.end();
    }
  }
});

// 计算提现手续费
router.post('/calculate-fee', authenticateToken, async (req, res) => {
  let connection;
  
  try {
    const { amount, withdraw_type } = req.body;
    
    if (!amount || amount <= 0) {
      return res.status(400).json({
        success: false,
        message: '提现金额无效'
      });
    }
    
    if (!withdraw_type || !['wechat', 'bank_card'].includes(withdraw_type)) {
      return res.status(400).json({
        success: false,
        message: '提现方式无效'
      });
    }
    
    connection = await mysql.createConnection(dbConfig);
    
    console.log('💰 计算提现手续费:', amount, withdraw_type);
    
    // 获取费率配置
    const [configs] = await connection.execute(`
      SELECT config_key, config_value 
      FROM withdraw_config 
      WHERE config_key IN ('wechat_fee_rate', 'bank_fee_rate', 'wechat_min_fee', 'bank_min_fee')
      AND status = 1
    `);
    
    const configMap = {};
    configs.forEach(config => {
      configMap[config.config_key] = parseFloat(config.config_value);
    });
    
    let fee = 0;
    let feeRate = 0;
    
    if (withdraw_type === 'wechat') {
      feeRate = configMap.wechat_fee_rate || 0.006;
      const minFee = configMap.wechat_min_fee || 0.1;
      fee = Math.max(amount * feeRate, minFee);
    } else if (withdraw_type === 'bank_card') {
      feeRate = configMap.bank_fee_rate || 0.001;
      const minFee = configMap.bank_min_fee || 2;
      fee = Math.max(amount * feeRate, minFee);
    }
    
    const actualAmount = amount - fee;
    
    res.json({
      success: true,
      data: {
        amount: parseFloat(amount.toFixed(2)),
        fee: parseFloat(fee.toFixed(2)),
        actual_amount: parseFloat(actualAmount.toFixed(2)),
        fee_rate: feeRate
      },
      message: '计算手续费成功'
    });
    
  } catch (error) {
    console.error('💰 计算手续费失败:', error);
    res.status(500).json({
      success: false,
      message: '计算手续费失败'
    });
  } finally {
    if (connection) {
      await connection.end();
    }
  }
});

// 申请提现
router.post('/apply', authenticateToken, async (req, res) => {
  let connection;
  
  try {
    const userId = req.user.id;
    const { amount, withdraw_type, account_id, remark } = req.body;
    
    // 验证参数
    if (!amount || amount <= 0) {
      return res.status(400).json({
        success: false,
        message: '提现金额无效'
      });
    }
    
    if (!withdraw_type || !['wechat', 'bank_card'].includes(withdraw_type)) {
      return res.status(400).json({
        success: false,
        message: '提现方式无效'
      });
    }
    
    if (!account_id) {
      return res.status(400).json({
        success: false,
        message: '请选择提现账户'
      });
    }
    
    connection = await mysql.createConnection(dbConfig);
    await connection.beginTransaction();
    
    console.log('💰 申请提现:', userId, amount, withdraw_type, account_id);
    
    // 检查用户余额
    const [users] = await connection.execute(`
      SELECT balance FROM user WHERE id = ?
    `, [userId]);
    
    if (users.length === 0) {
      await connection.rollback();
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }
    
    const userBalance = parseFloat(users[0].balance);
    if (userBalance < amount) {
      await connection.rollback();
      return res.status(400).json({
        success: false,
        message: '余额不足'
      });
    }
    
    // 计算手续费
    const feeResult = await calculateWithdrawFee(connection, amount, withdraw_type);
    const fee = feeResult.fee;
    const actualAmount = amount - fee;
    
    // 生成提现单号
    const withdrawNo = generateWithdrawNo();
    
    // 获取账户信息
    let accountInfo = {};
    if (withdraw_type === 'wechat') {
      const [wechatAccounts] = await connection.execute(`
        SELECT openid, nickname, real_name FROM wechat_account WHERE id = ? AND user_id = ?
      `, [account_id, userId]);
      
      if (wechatAccounts.length === 0) {
        await connection.rollback();
        return res.status(400).json({
          success: false,
          message: '微信账户不存在'
        });
      }
      
      accountInfo = {
        type: 'wechat',
        openid: wechatAccounts[0].openid,
        nickname: wechatAccounts[0].nickname,
        real_name: wechatAccounts[0].real_name
      };
    } else if (withdraw_type === 'bank_card') {
      const [bankCards] = await connection.execute(`
        SELECT bank_name, card_number_mask, card_holder FROM bank_card WHERE id = ? AND user_id = ?
      `, [account_id, userId]);
      
      if (bankCards.length === 0) {
        await connection.rollback();
        return res.status(400).json({
          success: false,
          message: '银行卡不存在'
        });
      }
      
      accountInfo = {
        type: 'bank_card',
        bank_name: bankCards[0].bank_name,
        card_number_mask: bankCards[0].card_number_mask,
        card_holder: bankCards[0].card_holder
      };
    }
    
    // 创建提现记录
    const [withdrawResult] = await connection.execute(`
      INSERT INTO withdraw (
        user_id, withdraw_no, amount, fee, actual_amount, 
        withdraw_type, account_id, account_info, status, remark
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 0, ?)
    `, [
      userId, withdrawNo, amount, fee, actualAmount,
      withdraw_type, account_id, JSON.stringify(accountInfo), remark
    ]);
    
    // 扣减用户余额
    await connection.execute(`
      UPDATE user SET balance = balance - ? WHERE id = ?
    `, [amount, userId]);
    
    // 记录钱包交易
    await connection.execute(`
      INSERT INTO wallet_transaction (
        user_id, type, amount, balance_before, balance_after,
        related_type, related_id, title, description, transaction_no
      ) VALUES (?, 'withdraw', ?, ?, ?, 'withdraw', ?, '申请提现', ?, ?)
    `, [
      userId, -amount, userBalance, userBalance - amount,
      withdrawResult.insertId, `提现到${withdraw_type === 'wechat' ? '微信' : '银行卡'}`, withdrawNo
    ]);
    
    await connection.commit();
    
    res.json({
      success: true,
      data: {
        withdraw_no: withdrawNo,
        status: 'pending_audit',
        estimated_arrival: getEstimatedArrival()
      },
      message: '提现申请已提交'
    });
    
  } catch (error) {
    if (connection) {
      await connection.rollback();
    }
    console.error('💰 申请提现失败:', error);
    res.status(500).json({
      success: false,
      message: '申请提现失败'
    });
  } finally {
    if (connection) {
      await connection.end();
    }
  }
});

// 辅助函数：计算提现手续费
async function calculateWithdrawFee(connection, amount, withdrawType) {
  const [configs] = await connection.execute(`
    SELECT config_key, config_value 
    FROM withdraw_config 
    WHERE config_key IN ('wechat_fee_rate', 'bank_fee_rate', 'wechat_min_fee', 'bank_min_fee')
    AND status = 1
  `);
  
  const configMap = {};
  configs.forEach(config => {
    configMap[config.config_key] = parseFloat(config.config_value);
  });
  
  let fee = 0;
  
  if (withdrawType === 'wechat') {
    const feeRate = configMap.wechat_fee_rate || 0.006;
    const minFee = configMap.wechat_min_fee || 0.1;
    fee = Math.max(amount * feeRate, minFee);
  } else if (withdrawType === 'bank_card') {
    const feeRate = configMap.bank_fee_rate || 0.001;
    const minFee = configMap.bank_min_fee || 2;
    fee = Math.max(amount * feeRate, minFee);
  }
  
  return { fee };
}

// 辅助函数：生成提现单号
function generateWithdrawNo() {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const day = String(now.getDate()).padStart(2, '0');
  const hour = String(now.getHours()).padStart(2, '0');
  const minute = String(now.getMinutes()).padStart(2, '0');
  const second = String(now.getSeconds()).padStart(2, '0');
  const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
  
  return `WD${year}${month}${day}${hour}${minute}${second}${random}`;
}

// 辅助函数：获取预计到账时间
function getEstimatedArrival() {
  const now = new Date();
  const tomorrow = new Date(now.getTime() + 24 * 60 * 60 * 1000);
  
  // 如果是周五，延迟到下周一
  if (now.getDay() === 5) {
    tomorrow.setTime(tomorrow.getTime() + 2 * 24 * 60 * 60 * 1000);
  }
  // 如果是周六，延迟到下周一
  else if (now.getDay() === 6) {
    tomorrow.setTime(tomorrow.getTime() + 1 * 24 * 60 * 60 * 1000);
  }
  
  // 设置到账时间为下午6点
  tomorrow.setHours(18, 0, 0, 0);
  
  return tomorrow.toISOString().slice(0, 19).replace('T', ' ');
}

module.exports = router;
