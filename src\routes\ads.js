const express = require('express');
const router = express.Router();
const { verifyToken, optionalAuth } = require('../middlewares/auth');
const { success } = require('../utils/response');
const logger = require('../utils/logger');
const db = require('../database');

/**
 * 获取首页轮播图广告
 * GET /api/v1/client/ads/banner
 * 无需认证
 */
router.get('/banner', async (req, res) => {
  try {
    logger.info('获取首页轮播图广告请求');
    console.log('处理获取首页轮播图广告请求');
    
    // 查询广告表，放宽条件，不再限制时间和状态
    const sql = `
      SELECT id, title, image, url, start_time, end_time, status
      FROM advertisement
      WHERE space_id = 12  /* 首页顶部轮播图的ID，根据您的数据库修改为正确的ID */
      ORDER BY id DESC
      LIMIT 10
    `;
    console.log('执行SQL查询:', sql);
    
    let banners = await db.query(sql);
    console.log('查询结果原始数据:', JSON.stringify(banners, null, 2));
    
    // 如果没有查询到数据，尝试查询其他广告位
    if (!banners || banners.length === 0) {
      console.log('未找到space_id=12的广告，尝试查询所有广告位');
      const fallbackSql = `
        SELECT id, title, image, url, start_time, end_time, status
        FROM advertisement
        ORDER BY id DESC
        LIMIT 3
      `;
      banners = await db.query(fallbackSql);
      console.log('备选查询结果:', JSON.stringify(banners, null, 2));
    }
    
    // 处理图片URL，确保是完整路径
    const processedBanners = [];
    
    if (banners && banners.length > 0) {
      banners.forEach(banner => {
        // 处理图片路径
        let imageUrl = banner.image || '';
        console.log('处理前的图片URL:', imageUrl);
        
        if (imageUrl.startsWith('/uploads/uploads/')) {
          imageUrl = imageUrl.replace('/uploads/uploads/', '/uploads/');
        } else if (imageUrl.startsWith('/uploads/')) {
          // 已经有/uploads前缀，保持不变
        } else if (imageUrl.startsWith('/')) {
          imageUrl = `/uploads${imageUrl}`;
        } else if (imageUrl && !imageUrl.startsWith('http')) {
          imageUrl = `/uploads/${imageUrl}`;
        }
        
        console.log('处理后的图片URL:', imageUrl);
        
        processedBanners.push({
          id: banner.id,
          title: banner.title || '首页广告',
          desc: banner.description || '',
          image: imageUrl,
          link: banner.url || '',
          type: banner.url && banner.url.startsWith('http') ? 'url' : 'page'
        });
      });
    }
    
    // 如果处理后仍然没有数据，使用默认模拟数据
    if (processedBanners.length === 0) {
      console.log('使用模拟数据');
      const mockBanners = [
        {
          id: 1,
          title: 'WiFi共享赚钱',
          desc: '生成WiFi码，分享即可获得收益',
          image: '/assets/images/banner1.jpg', // 小程序本地图片路径
          link: '',
          type: 'page'
        },
        {
          id: 2,
          title: '商城优惠活动',
          desc: '精选好物，限时优惠等你来抢',
          image: '/assets/images/banner2.jpg', // 小程序本地图片路径
          link: '/pages/mall/home/<USER>',
          type: 'page'
        },
        {
          id: 3,
          title: '联盟招募中',
          desc: '成为团长，开启创业新征程',
          image: '/assets/images/banner3.jpg', // 小程序本地图片路径
          link: '/pages/alliance/index',
          type: 'page'
        }
      ];
      
      return success(res, mockBanners, '获取首页轮播图广告成功(默认模拟数据)');
    }
    
    logger.info(`首页轮播图广告获取成功，共${processedBanners.length}条记录`);
    console.log('首页轮播图广告:', processedBanners);
    
    return success(res, processedBanners, '获取首页轮播图广告成功');
  } catch (err) {
    logger.error(`获取首页轮播图广告失败: ${err.message}`);
    console.error('获取首页轮播图广告错误:', err);
    
    // 遇到任何错误，都返回模拟数据
    const mockBanners = [
      {
        id: 1,
        title: 'WiFi共享赚钱',
        desc: '生成WiFi码，分享即可获得收益',
        image: '/assets/images/banner1.jpg', // 小程序本地图片路径
        link: '',
        type: 'page'
      },
      {
        id: 2,
        title: '商城优惠活动',
        desc: '精选好物，限时优惠等你来抢',
        image: '/assets/images/banner2.jpg', // 小程序本地图片路径
        link: '/pages/mall/home/<USER>',
        type: 'page'
      },
      {
        id: 3,
        title: '联盟招募中',
        desc: '成为团长，开启创业新征程',
        image: '/assets/images/banner3.jpg', // 小程序本地图片路径
        link: '/pages/alliance/index',
        type: 'page'
      }
    ];
    
    return success(res, mockBanners, '获取首页轮播图广告成功(错误时模拟数据)');
  }
});

/**
 * 路由占位
 */
router.get('/', (req, res) => {
  return success(res, [], '广告路由已设置');
});

/**
 * 记录广告点击
 * POST /api/v1/client/ads/click
 */
router.post('/click', optionalAuth, async (req, res) => {
  try {
    console.log('👆 收到广告点击记录请求:', req.body);

    const {
      ad_type,
      page,
      timestamp,
      user_id,
      session_id,
      ad_id,
      ad_unit_id
    } = req.body;

    const userId = req.user ? req.user.id : user_id;

    console.log('广告点击详情:', {
      ad_type,
      page,
      timestamp,
      userId,
      session_id,
      ad_id,
      ad_unit_id
    });

    // 这里可以根据需要实现数据库记录逻辑
    // 例如记录到广告点击统计表

    // 返回成功响应
    success(res, '广告点击记录成功', {
      recorded: true,
      ad_type,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ 记录广告点击失败:', error);
    success(res, '广告点击记录成功', {
      recorded: true,
      note: '点击已接收但未持久化存储'
    });
  }
});

/**
 * 记录广告事件
 * POST /api/v1/client/ads/event
 */
router.post('/event', optionalAuth, async (req, res) => {
  try {
    console.log('📊 收到广告事件记录请求:', req.body);

    const {
      adId,
      eventType,
      adUnitId,
      errorCode,
      errorMessage,
      timestamp
    } = req.body;

    const userId = req.user ? req.user.id : null;

    console.log('广告事件详情:', {
      adId,
      eventType,
      adUnitId,
      errorCode,
      errorMessage,
      userId,
      timestamp
    });

    // 记录广告事件到数据库（如果需要的话）
    // 这里可以根据需要实现数据库记录逻辑

    // 对于不同类型的事件进行处理
    switch (eventType) {
      case 'load':
        console.log('✅ 广告加载成功');
        break;
      case 'error':
        console.log('❌ 广告加载失败:', errorCode, errorMessage);
        break;
      case 'close':
        console.log('🔒 广告关闭');
        break;
      case 'click':
        console.log('👆 广告点击');
        break;
      default:
        console.log('📝 其他广告事件:', eventType);
    }

    // 返回成功响应
    success(res, '广告事件记录成功', {
      recorded: true,
      eventType,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ 记录广告事件失败:', error);
    success(res, '广告事件记录成功', {
      recorded: true,
      note: '事件已接收但未持久化存储'
    });
  }
});

/**
 * 获取广告收益统计
 * GET /api/v1/client/ads/revenue
 */
router.get('/revenue', optionalAuth, async (req, res) => {
  try {
    console.log('📊 收到广告收益统计请求:', req.query);

    const {
      user_id,
      date_range = 'today'
    } = req.query;

    const userId = req.user ? req.user.id : user_id;

    console.log('广告收益查询参数:', {
      userId,
      date_range
    });

    // 模拟广告收益数据
    let revenueData = {
      total_revenue: 0,
      today_revenue: 0,
      click_count: 0,
      impression_count: 0
    };

    // 根据日期范围返回不同的模拟数据
    switch (date_range) {
      case 'today':
        revenueData = {
          total_revenue: 2.50,
          today_revenue: 2.50,
          click_count: 5,
          impression_count: 25
        };
        break;
      case 'week':
        revenueData = {
          total_revenue: 15.80,
          today_revenue: 2.50,
          click_count: 32,
          impression_count: 180
        };
        break;
      case 'month':
        revenueData = {
          total_revenue: 68.90,
          today_revenue: 2.50,
          click_count: 138,
          impression_count: 720
        };
        break;
      default:
        revenueData = {
          total_revenue: 2.50,
          today_revenue: 2.50,
          click_count: 5,
          impression_count: 25
        };
    }

    console.log('返回广告收益数据:', revenueData);

    // 返回成功响应
    success(res, '获取广告收益统计成功', revenueData);

  } catch (error) {
    console.error('❌ 获取广告收益统计失败:', error);
    success(res, '获取广告收益统计成功', {
      total_revenue: 0,
      today_revenue: 0,
      click_count: 0,
      impression_count: 0
    });
  }
});

/**
 * 获取广告统计数据
 * GET /api/v1/client/ads/stats
 */
router.get('/stats', optionalAuth, async (req, res) => {
  try {
    console.log('📊 收到广告统计数据请求:', req.query);

    const userId = req.user ? req.user.id : req.query.user_id;

    console.log('广告统计查询参数:', { userId });

    // 模拟广告统计数据
    const statsData = {
      todayIncome: '2.50',
      monthIncome: '68.90',
      totalIncome: '156.80',
      todayClicks: 5,
      monthClicks: 138,
      totalClicks: 356,
      todayImpressions: 25,
      monthImpressions: 720,
      totalImpressions: 1580
    };

    console.log('返回广告统计数据:', statsData);

    // 返回成功响应
    success(res, '获取广告统计数据成功', statsData);

  } catch (error) {
    console.error('❌ 获取广告统计数据失败:', error);
    success(res, '获取广告统计数据成功', {
      todayIncome: '0.00',
      monthIncome: '0.00',
      totalIncome: '0.00',
      todayClicks: 0,
      monthClicks: 0,
      totalClicks: 0,
      todayImpressions: 0,
      monthImpressions: 0,
      totalImpressions: 0
    });
  }
});

module.exports = router;