const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const path = require('path');
const fs = require('fs');

// 引入配置
const config = require('./config');

// 引入数据库连接
const db = require('./config/database');

// 引入路由
const apiRouter = require('./src/routes/api');

// 引入中间件
const { errorMiddleware } = require('./src/middlewares/error');
const { verifyToken } = require('./src/middlewares/auth');
const loggerMiddleware = require('./src/middlewares/logger');

// 加载环境变量
require('dotenv').config();

// 创建Express应用
const app = express();

// 确保日志目录存在
if (!fs.existsSync(config.logger.logDir)) {
  fs.mkdirSync(config.logger.logDir, { recursive: true });
}

// 配置中间件
app.use(helmet()); // 安全HTTP头
app.use(cors(config.cors)); // 跨域资源共享
app.use(express.json({ limit: '50mb' })); // 解析JSON请求体，增加大小限制
app.use(express.urlencoded({ extended: true, limit: '50mb' })); // 解析URL编码请求体，增加大小限制

// 添加全局请求日志中间件
app.use((req, res, next) => {
  if (req.method === 'DELETE' && req.path.includes('/cart/remove')) {
    console.log('🚨 DELETE请求到达服务器！');
    console.log('🚨 请求方法:', req.method);
    console.log('🚨 请求路径:', req.path);
    console.log('🚨 请求URL:', req.url);
    console.log('🚨 请求体:', req.body);
    console.log('🚨 请求头:', req.headers);
  }

  // 添加订单创建请求的调试信息
  if (req.method === 'POST' && req.path.includes('/order/create')) {
    console.log('📦 订单创建请求到达服务器！');
    console.log('📦 请求方法:', req.method);
    console.log('📦 请求路径:', req.path);
    console.log('📦 请求URL:', req.url);
    console.log('📦 请求体:', req.body);
    console.log('📦 Authorization头:', req.headers.authorization);
  }

  next();
});
app.use(morgan('combined', { // 请求日志
  stream: fs.createWriteStream(
    path.join(config.logger.logDir, 'access.log'),
    { flags: 'a' }
  )
}));
app.use(loggerMiddleware); // 自定义日志中间件

// 基本API路由
app.get('/', (req, res) => {
  res.json({
    message: 'WiFi共享商业系统API服务正在运行',
    version: '1.0.0',
    timestamp: new Date()
  });
});

// 加载v1版本API路由
const v1Router = require('./src/routes/v1');
app.use('/api/v1', v1Router);
console.log('✅ 已加载v1版本API路由: /api/v1');

// 输出所有已加载的路由
function printLoadedRoutes() {
  console.log('\n===== 已加载的路由 =====');
  // v1路由及其子路由
  function print(path, layer) {
    if (layer.route) {
      layer.route.stack.forEach(print.bind(null, path.concat(layer.route.path)));
    } else if (layer.name === 'router' && layer.handle.stack) {
      layer.handle.stack.forEach(print.bind(null, path.concat(layer.regexp.toString().replace('/^\\', '').replace('\\/?(?=\\/|$)/i', ''))));
    } else if (layer.method) {
      console.log('%s %s', layer.method.toUpperCase().padEnd(6), path.filter(p => p).join(''));
    }
  }
  
  app._router.stack.forEach(print.bind(null, []));
  console.log('======================\n');
}

// 静态文件服务
app.use('/public', express.static(path.join(__dirname, 'public')));

// 确保uploads目录存在（兼容旧版本）
const uploadsDir = path.join(__dirname, 'uploads');
const publicUploadsDir = path.join(__dirname, 'public', 'uploads');

// 创建两个目录以确保兼容性
if (!fs.existsSync(uploadsDir)) {
  fs.mkdirSync(uploadsDir, { recursive: true });
  fs.mkdirSync(path.join(uploadsDir, 'images'), { recursive: true });
  console.log('✅ 已创建上传目录:', uploadsDir);
}

if (!fs.existsSync(publicUploadsDir)) {
  fs.mkdirSync(publicUploadsDir, { recursive: true });
  fs.mkdirSync(path.join(publicUploadsDir, 'images'), { recursive: true });
  console.log('✅ 已创建公共上传目录:', publicUploadsDir);
}

// 为静态文件添加CORS头，解决图片跨域问题
app.use('/uploads', (req, res, next) => {
  res.header('Access-Control-Allow-Origin', '*');
  res.header('Access-Control-Allow-Methods', 'GET, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');
  res.header('Cross-Origin-Resource-Policy', 'cross-origin');
  res.header('Access-Control-Expose-Headers', 'Content-Length, Content-Range');

  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  // 记录图片请求
  console.log('请求图片路径:', req.path);

  // 首先检查public/uploads目录（新的上传文件）
  const publicFilePath = path.join(publicUploadsDir, req.path);
  console.log('检查公共上传路径:', publicFilePath);

  if (fs.existsSync(publicFilePath)) {
    console.log('✅ 在public/uploads中找到文件');
    return express.static(publicUploadsDir)(req, res, next);
  }

  // 如果public/uploads中没有，则检查uploads目录（兼容旧文件）
  const uploadsFilePath = path.join(uploadsDir, req.path);
  console.log('检查上传路径:', uploadsFilePath);

  if (fs.existsSync(uploadsFilePath)) {
    console.log('✅ 在uploads中找到文件');
    return express.static(uploadsDir)(req, res, next);
  }

  // 如果都没有找到，返回404
  console.log('❌ 文件未找到:', req.path);
  return res.status(404).json({ error: '文件未找到' });
});

// 健康检查端点
app.get('/health', (req, res) => {
  res.status(200).json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: config.server.nodeEnv
  });
});

// API路由 - 注释掉避免路由冲突
// app.use('/api', apiRouter);

// 错误处理中间件
app.use(errorMiddleware);

// 处理未找到的路由
app.use((req, res) => {
  res.status(404).json({
    status: 'error',
    message: '未找到请求的资源'
  });
});

// 启动服务器
const PORT = config.server.port;

if (require.main === module) {
  // 直接运行此文件时启动服务器
  app.listen(PORT, () => {
    console.log(`服务器运行在 http://localhost:${PORT}`);
    console.log(`当前环境: ${config.server.nodeEnv}`);
    console.log(`静态文件路径: ${path.join(__dirname, 'public')}`);
    console.log(`上传文件路径: ${path.join(__dirname, 'uploads')}`);
    
    // 打印已加载的路由
    printLoadedRoutes();
    
    // 数据库连接已在模块加载时自动建立
    console.log('数据库连接成功');
  });
} else {
  // 通过require引入时作为模块导出
  module.exports = app;
} 