const axios = require('axios');

async function testLogin() {
  try {
    console.log('🧪 测试管理员登录API...\n');
    
    const loginData = {
      username: 'mrx0927',
      password: 'hh20250701'
    };
    
    console.log('📤 发送登录请求:');
    console.log('URL:', 'http://localhost:4000/api/v1/admin/auth/admin-login');
    console.log('数据:', loginData);
    
    const response = await axios.post('http://localhost:4000/api/v1/admin/auth/admin-login', loginData);
    
    console.log('\n✅ 登录成功!');
    console.log('状态码:', response.status);
    console.log('响应数据:', JSON.stringify(response.data, null, 2));
    
  } catch (error) {
    console.log('\n❌ 登录失败!');
    console.log('错误状态码:', error.response?.status);
    console.log('错误信息:', error.response?.data);
    console.log('完整错误:', error.message);
  }
}

testLogin();
