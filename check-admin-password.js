const crypto = require('crypto');

// MD5加密函数
function md5(text) {
  return crypto.createHash('md5').update(text).digest('hex');
}

console.log('🔐 检查管理员密码...\n');

// 检查已知密码的MD5值
const passwords = [
  'admin123',
  'hh20250701',
  '123456',
  'admin',
  'password'
];

console.log('已知密码的MD5值:');
passwords.forEach(pwd => {
  console.log(`${pwd} -> ${md5(pwd)}`);
});

console.log('\n数据库中的密码哈希:');
console.log('mrx0927: 5eb688175b8e2581bab3e2c657880533');
console.log('admin: 0192023a7bbd73250516f069df18b500');

// 检查哪个密码匹配
console.log('\n匹配结果:');
passwords.forEach(pwd => {
  const hash = md5(pwd);
  if (hash === '5eb688175b8e2581bab3e2c657880533') {
    console.log(`✅ mrx0927的密码是: ${pwd}`);
  }
  if (hash === '0192023a7bbd73250516f069df18b500') {
    console.log(`✅ admin的密码是: ${pwd}`);
  }
});

// 尝试更多常见密码
const morePasswords = [
  '12345678',
  'qwerty',
  'abc123',
  'admin888',
  'root',
  'test',
  '000000',
  '111111',
  '888888',
  '666666'
];

console.log('\n尝试更多密码:');
morePasswords.forEach(pwd => {
  const hash = md5(pwd);
  if (hash === '5eb688175b8e2581bab3e2c657880533') {
    console.log(`✅ mrx0927的密码是: ${pwd}`);
  }
  if (hash === '0192023a7bbd73250516f069df18b500') {
    console.log(`✅ admin的密码是: ${pwd}`);
  }
});
