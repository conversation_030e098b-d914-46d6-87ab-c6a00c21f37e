const ProfitModel = require('../models/profit');
const UserModel = require('../models/user');
const logger = require('../utils/logger');
const { success, error } = require('../utils/response');

/**
 * 获取用户收益列表
 */
const getProfitList = async (req, res) => {
  try {
    const userId = req.user.id;
    const { sourceType, status, startDate, endDate, page = 1, limit = 10 } = req.query;
    
    const result = await ProfitModel.list({
      userId,
      sourceType,
      status: status !== undefined ? parseInt(status) : undefined,
      startDate,
      endDate,
      page: parseInt(page),
      limit: parseInt(limit)
    });
    
    // 获取用户余额
    const user = await UserModel.findById(userId);
    
    if (user) {
      result.balance = user.balance || 0;
    }
    
    return success(res, result, '获取收益列表成功');
  } catch (err) {
    logger.error(`获取收益列表失败: ${err.message}`);
    return error(res, '获取收益列表失败', 500);
  }
};

/**
 * 获取收益详情
 */
const getProfitDetail = async (req, res) => {
  try {
    const userId = req.user.id;
    const { id } = req.params;
    
    const profit = await ProfitModel.getById(id);
    
    if (!profit) {
      return error(res, '收益记录不存在', 404);
    }
    
    // 验证是否是该用户的收益
    if (profit.user_id !== userId) {
      return error(res, '无权查看此收益记录', 403);
    }
    
    return success(res, {
      id: profit.id,
      userId: profit.user_id,
      amount: profit.amount,
      sourceType: profit.source_type,
      sourceId: profit.source_id,
      description: profit.description,
      orderNo: profit.order_no,
      status: profit.status,
      createdAt: profit.created_at,
      updatedAt: profit.updated_at
    }, '获取收益详情成功');
  } catch (err) {
    logger.error(`获取收益详情失败: ${err.message}`);
    return error(res, '获取收益详情失败', 500);
  }
};

/**
 * 提交提现申请
 */
const applyWithdraw = async (req, res) => {
  try {
    const userId = req.user.id;
    const { amount, bankName, bankAccount, accountName } = req.body;
    
    // 验证必填字段
    if (!amount || !bankName || !bankAccount || !accountName) {
      return error(res, '缺少必填字段', 400);
    }
    
    // 验证金额
    if (parseFloat(amount) <= 0) {
      return error(res, '提现金额必须大于0', 400);
    }
    
    // 获取用户余额
    const user = await UserModel.findById(userId);
    
    if (!user) {
      return error(res, '用户不存在', 404);
    }
    
    if (parseFloat(user.balance || 0) < parseFloat(amount)) {
      return error(res, '余额不足', 400);
    }
    
    // 提交提现申请
    const withdraw = await ProfitModel.createWithdraw({
      userId,
      amount: parseFloat(amount),
      bankName,
      bankAccount,
      accountName
    });
    
    return success(res, withdraw, '提现申请提交成功，请等待审核');
  } catch (err) {
    logger.error(`提交提现申请失败: ${err.message}`);
    
    if (err.message.includes('余额不足')) {
      return error(res, '余额不足', 400);
    }
    
    return error(res, '提交提现申请失败', 500);
  }
};

/**
 * 获取提现列表
 */
const getWithdrawList = async (req, res) => {
  try {
    const userId = req.user.id;
    const { status, page = 1, limit = 10 } = req.query;
    
    const result = await ProfitModel.getWithdraws({
      userId,
      status: status !== undefined ? parseInt(status) : undefined,
      page: parseInt(page),
      limit: parseInt(limit)
    });
    
    return success(res, result, '获取提现列表成功');
  } catch (err) {
    logger.error(`获取提现列表失败: ${err.message}`);
    return error(res, '获取提现列表失败', 500);
  }
};

/**
 * 获取分润规则
 */
const getProfitRules = async (req, res) => {
  try {
    const rules = await ProfitModel.getProfitRules();
    
    return success(res, rules, '获取分润规则成功');
  } catch (err) {
    logger.error(`获取分润规则失败: ${err.message}`);
    return error(res, '获取分润规则失败', 500);
  }
};

/**
 * 更新分润规则（管理端）
 */
const updateProfitRules = async (req, res) => {
  try {
    const { type, rules } = req.body;
    
    // 验证参数
    if (!type || !rules || typeof rules !== 'object') {
      return error(res, '参数错误', 400);
    }
    
    // 更新规则
    await ProfitModel.updateProfitRules(type, rules);
    
    return success(res, null, '更新分润规则成功');
  } catch (err) {
    logger.error(`更新分润规则失败: ${err.message}`);
    return error(res, '更新分润规则失败', 500);
  }
};

module.exports = {
  getProfitList,
  getProfitDetail,
  applyWithdraw,
  getWithdrawList,
  getProfitRules,
  updateProfitRules
}; 