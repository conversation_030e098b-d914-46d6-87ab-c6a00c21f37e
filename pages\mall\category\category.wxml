<!--pages/mall/category/category.wxml-->
<view class="category-container">
  <!-- 搜索框 -->
  <view class="search-box">
    <input class="search-input" placeholder="搜索商品..." bindinput="onSearch" value="{{searchValue}}" />
  </view>

  <!-- 分类和商品列表 -->
  <view class="category-content">
    <!-- 左侧分类导航 -->
    <view class="category-nav">
      <view 
        class="category-nav-item {{currentCategory.id === item.id ? 'active' : ''}}" 
        wx:for="{{categories}}" 
        wx:key="id" 
        bindtap="onCategoryTap" 
        data-id="{{item.id}}"
      >
        {{item.name}}
      </view>
    </view>

    <!-- 右侧商品列表 -->
    <view class="goods-list-container">
      <block wx:if="{{categoryGoods.length > 0}}">
        <view class="goods-grid">
          <view class="goods-item" wx:for="{{categoryGoods}}" wx:key="id" bindtap="onGoodsTap" data-id="{{item.id}}">
            <image
              class="goods-image"
              src="{{item.cover || '/assets/images/goods-placeholder.svg'}}"
              mode="aspectFill"
              lazy-load
              binderror="onImageError"
              data-index="{{index}}"
            ></image>
            <view class="goods-info">
              <view class="goods-name">{{item.name}}</view>
              <view class="goods-price">¥{{item.price}}</view>
            </view>
          </view>
        </view>
        <view class="load-more" wx:if="{{hasMore}}">上拉加载更多</view>
        <view class="no-more" wx:else>没有更多了</view>
      </block>
      <view class="empty-goods" wx:elif="{{!loading}}">
        <image class="empty-image" src="/assets/images/empty-goods.svg" mode="aspectFit"></image>
        <view class="empty-text">暂无商品</view>
      </view>
    </view>
  </view>

  <!-- 广告位 - 暂时隐藏，避免加载错误 -->
  <view class="ad-container" style="display: none;">
    <!-- 广告组件暂时禁用，需要配置有效的广告位ID -->
    <!-- <ad unit-id="adunit-id" ad-type="banner" ad-theme="white" bindload="onAdLoad" binderror="onAdError"></ad> -->
  </view>

  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{loading}}">
    <text class="loading-text">加载中...</text>
  </view>
</view> 