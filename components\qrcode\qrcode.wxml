<!--components/qrcode/qrcode.wxml-->
<!--二维码组件模板-->

<view class="qrcode-container">
  <!-- 二维码内容 - 始终渲染，确保Canvas可以被查询到 -->
  <view class="qrcode-content">
    <view class="qrcode-wrapper">
      <!-- 标题 -->
      <view class="qrcode-title">
        <text>扫描连接WiFi</text>
      </view>

      <!-- 加载状态覆盖层 -->
      <view class="qrcode-loading-overlay" wx:if="{{loading}}">
        <view class="loading-spinner"></view>
        <text class="loading-text">生成中...</text>
      </view>

      <!-- Canvas 用于绘制二维码 - 强制显示用于调试 -->
      <canvas
        wx:if="{{canvasId}}"
        type="2d"
        id="{{canvasId}}"
        class="qrcode-canvas"
        style="width: {{size}}rpx; height: {{size}}rpx; display: block; opacity: {{loading ? 0.3 : 1}}; border: 2px solid red;"
      ></canvas>

      <!-- 调试信息 -->
      <view class="debug-info" style="font-size: 24rpx; color: #666; margin-top: 10rpx;">
        <text>Canvas ID: {{canvasId}}</text><br/>
        <text>qrCodeImageUrl: {{qrCodeImageUrl}}</text><br/>
        <text>loading: {{loading}}</text>
      </view>
      
      <!-- 显示生成的二维码图片 -->
      <image
        wx:if="{{qrCodeImageUrl}}"
        class="qrcode-image"
        src="{{qrCodeImageUrl}}"
        mode="aspectFit"
        bindtap="onQRCodeTap"
        bindlongpress="onQRCodeLongPress"
        binderror="onImageLoadError"
        style="width: {{size}}rpx; height: {{size}}rpx;"
      />

      <!-- 备用显示：当二维码生成失败时显示WiFi信息 -->
      <view
        wx:if="{{!qrCodeImageUrl && fallbackInfo}}"
        class="qrcode-fallback"
        style="width: {{size}}rpx; height: {{size}}rpx;"
      >
        <view class="fallback-content">
          <view class="fallback-title">WiFi连接信息</view>
          <view class="fallback-info">
            <text class="fallback-label">网络名称：</text>
            <text class="fallback-value">{{fallbackInfo.ssid}}</text>
          </view>
          <view class="fallback-info">
            <text class="fallback-label">密码：</text>
            <text class="fallback-value">{{fallbackInfo.password}}</text>
          </view>
          <view wx:if="{{fallbackInfo.merchantName}}" class="fallback-info">
            <text class="fallback-label">提供方：</text>
            <text class="fallback-value">{{fallbackInfo.merchantName}}</text>
          </view>
          <view class="fallback-tip">请手动连接WiFi</view>
        </view>
      </view>
      
      <!-- WiFi信息展示 -->
      <view class="wifi-info">
        <view class="wifi-info-item">
          <text class="wifi-label">SSID:</text>
          <text class="wifi-value">{{ssid}}</text>
        </view>
        <view class="wifi-info-item">
          <text class="wifi-label">密码:</text>
          <text class="wifi-value">{{password}}</text>
        </view>
        <view class="wifi-info-item" wx:if="{{merchantName}}">
          <text class="wifi-label">提供方:</text>
          <text class="wifi-value">{{merchantName}}</text>
      </view>
    </view>
    
    <!-- 提示信息 -->
    <view class="qrcode-tips">
        <text>长按二维码可保存或分享</text>
      </view>
    </view>
  </view>

  <!-- 生成失败 -->
  <view class="qrcode-error" wx:if="{{!loading && !qrCodeData}}">
    <text class="error-text">二维码生成失败</text>
    <button class="retry-btn" bindtap="generateQRCode">重试</button>
  </view>
</view> 