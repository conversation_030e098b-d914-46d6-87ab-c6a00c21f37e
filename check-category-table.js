const db = require('./src/database');

async function checkCategoryTable() {
  try {
    console.log('检查category表是否存在...');
    
    // 直接使用不带参数的查询
    const result = await db.query('SHOW TABLES LIKE "category"');
    console.log('查询结果:', JSON.stringify(result));
    
    if (result && result.length > 0) {
      console.log('category表存在');
      
      // 查询表结构
      const structure = await db.query('DESCRIBE category');
      console.log('表结构:', JSON.stringify(structure));
      
      // 查询数据
      const data = await db.query('SELECT * FROM category LIMIT 5');
      console.log('表数据:', JSON.stringify(data));
      
    } else {
      console.log('category表不存在，准备创建...');
      
      // 创建表
      await db.query(`
        CREATE TABLE IF NOT EXISTS category (
          id INT AUTO_INCREMENT PRIMARY KEY,
          name VARCHAR(100) NOT NULL,
          icon VARCHAR(255),
          sort_order INT DEFAULT 0,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
      `);
      
      console.log('category表创建成功');
      
      // 插入默认数据
      await db.query(`
        INSERT INTO category (name, icon, sort_order) VALUES
        ('电子产品', '/uploads/icons/electronics.png', 1),
        ('服装', '/uploads/icons/clothing.png', 2),
        ('食品', '/uploads/icons/food.png', 3),
        ('家居', '/uploads/icons/home.png', 4),
        ('美妆', '/uploads/icons/beauty.png', 5)
      `);
      
      console.log('默认分类数据插入成功');
    }
    
  } catch (err) {
    console.error('检查或创建category表时出错:', err);
  } finally {
    process.exit();
  }
}

checkCategoryTable(); 