const mysql = require('mysql2/promise');

async function setupWithdrawTables() {
  let connection;
  
  try {
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: 'wo587129955',
      database: 'mall'
    });
    
    console.log('🔧 开始设置提现功能数据库表...');
    
    // 1. 创建微信支付账户表
    console.log('1️⃣ 创建微信支付账户表...');
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS wechat_account (
        id int(11) NOT NULL AUTO_INCREMENT COMMENT '微信账户ID',
        user_id int(11) NOT NULL COMMENT '用户ID',
        openid varchar(100) NOT NULL COMMENT '微信openid',
        nickname varchar(100) DEFAULT NULL COMMENT '微信昵称',
        real_name varchar(50) DEFAULT NULL COMMENT '真实姓名',
        is_verified tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否实名认证：0否，1是',
        is_default tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否默认提现方式：0否，1是',
        status tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0禁用，1启用',
        created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        updated_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
        PRIMARY KEY (id),
        UNIQUE KEY user_openid (user_id, openid),
        KEY user_id (user_id)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='微信支付账户表'
    `);
    
    // 2. 完善银行卡表
    console.log('2️⃣ 完善银行卡表...');
    
    // 检查字段是否存在，如果不存在则添加
    const [columns] = await connection.execute(`
      SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = 'mall' AND TABLE_NAME = 'bank_card'
    `);
    
    const existingColumns = columns.map(col => col.COLUMN_NAME);
    
    if (!existingColumns.includes('bank_code')) {
      await connection.execute('ALTER TABLE bank_card ADD COLUMN bank_code varchar(20) DEFAULT NULL COMMENT "银行代码" AFTER bank_name');
    }
    if (!existingColumns.includes('card_number_mask')) {
      await connection.execute('ALTER TABLE bank_card ADD COLUMN card_number_mask varchar(50) DEFAULT NULL COMMENT "卡号掩码显示" AFTER card_number');
    }
    if (!existingColumns.includes('card_type')) {
      await connection.execute('ALTER TABLE bank_card ADD COLUMN card_type tinyint(1) NOT NULL DEFAULT "1" COMMENT "卡类型：1储蓄卡，2信用卡" AFTER card_holder');
    }
    if (!existingColumns.includes('phone')) {
      await connection.execute('ALTER TABLE bank_card ADD COLUMN phone varchar(20) DEFAULT NULL COMMENT "预留手机号" AFTER card_type');
    }
    if (!existingColumns.includes('is_verified')) {
      await connection.execute('ALTER TABLE bank_card ADD COLUMN is_verified tinyint(1) NOT NULL DEFAULT "0" COMMENT "是否已验证：0否，1是" AFTER is_default');
    }
    if (!existingColumns.includes('status')) {
      await connection.execute('ALTER TABLE bank_card ADD COLUMN status tinyint(1) NOT NULL DEFAULT "1" COMMENT "状态：0禁用，1启用" AFTER is_verified');
    }
    
    // 3. 完善提现申请表
    console.log('3️⃣ 完善提现申请表...');
    
    const [withdrawColumns] = await connection.execute(`
      SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = 'mall' AND TABLE_NAME = 'withdraw'
    `);
    
    const existingWithdrawColumns = withdrawColumns.map(col => col.COLUMN_NAME);
    
    if (!existingWithdrawColumns.includes('withdraw_no')) {
      await connection.execute('ALTER TABLE withdraw ADD COLUMN withdraw_no varchar(50) DEFAULT NULL COMMENT "提现单号" AFTER user_id');
    }
    if (!existingWithdrawColumns.includes('fee')) {
      await connection.execute('ALTER TABLE withdraw ADD COLUMN fee decimal(10,2) NOT NULL DEFAULT "0.00" COMMENT "手续费" AFTER amount');
    }
    if (!existingWithdrawColumns.includes('actual_amount')) {
      await connection.execute('ALTER TABLE withdraw ADD COLUMN actual_amount decimal(10,2) NOT NULL DEFAULT "0.00" COMMENT "实际到账金额" AFTER fee');
    }
    if (!existingWithdrawColumns.includes('withdraw_type')) {
      await connection.execute('ALTER TABLE withdraw ADD COLUMN withdraw_type varchar(20) NOT NULL DEFAULT "bank_card" COMMENT "提现方式：wechat,bank_card" AFTER actual_amount');
    }
    if (!existingWithdrawColumns.includes('account_id')) {
      await connection.execute('ALTER TABLE withdraw ADD COLUMN account_id int(11) DEFAULT NULL COMMENT "账户ID（银行卡ID或微信账户ID）" AFTER withdraw_type');
    }
    if (!existingWithdrawColumns.includes('account_info')) {
      await connection.execute('ALTER TABLE withdraw ADD COLUMN account_info text DEFAULT NULL COMMENT "账户信息快照（JSON格式）" AFTER account_id');
    }
    if (!existingWithdrawColumns.includes('apply_time')) {
      await connection.execute('ALTER TABLE withdraw ADD COLUMN apply_time datetime DEFAULT CURRENT_TIMESTAMP COMMENT "申请时间" AFTER account_info');
    }
    if (!existingWithdrawColumns.includes('complete_time')) {
      await connection.execute('ALTER TABLE withdraw ADD COLUMN complete_time datetime DEFAULT NULL COMMENT "完成时间" AFTER apply_time');
    }
    if (!existingWithdrawColumns.includes('failure_reason')) {
      await connection.execute('ALTER TABLE withdraw ADD COLUMN failure_reason varchar(255) DEFAULT NULL COMMENT "失败原因" AFTER complete_time');
    }
    
    // 更新状态字段注释
    await connection.execute('ALTER TABLE withdraw MODIFY COLUMN status tinyint(1) NOT NULL DEFAULT "0" COMMENT "状态：0待审核，1审核通过，2审核拒绝，3处理中，4已完成，5已取消"');
    
    // 4. 创建提现配置表
    console.log('4️⃣ 创建提现配置表...');
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS withdraw_config (
        id int(11) NOT NULL AUTO_INCREMENT COMMENT '配置ID',
        config_key varchar(50) NOT NULL COMMENT '配置键',
        config_value text NOT NULL COMMENT '配置值',
        config_type varchar(20) NOT NULL DEFAULT 'string' COMMENT '配置类型：string,number,boolean,json',
        description varchar(255) DEFAULT NULL COMMENT '配置描述',
        status tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0禁用，1启用',
        created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        updated_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
        PRIMARY KEY (id),
        UNIQUE KEY config_key (config_key)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='提现配置表'
    `);
    
    // 5. 插入提现配置数据
    console.log('5️⃣ 插入提现配置数据...');
    await connection.execute(`
      INSERT INTO withdraw_config (config_key, config_value, config_type, description) VALUES
      ('min_withdraw_amount', '10', 'number', '最小提现金额（元）'),
      ('max_withdraw_amount', '50000', 'number', '最大提现金额（元）'),
      ('daily_withdraw_limit', '100000', 'number', '每日提现限额（元）'),
      ('wechat_fee_rate', '0.006', 'number', '微信提现手续费率（0.6%）'),
      ('bank_fee_rate', '0.001', 'number', '银行卡提现手续费率（0.1%）'),
      ('wechat_min_fee', '0.1', 'number', '微信提现最小手续费（元）'),
      ('bank_min_fee', '2', 'number', '银行卡提现最小手续费（元）'),
      ('auto_audit_enabled', 'true', 'boolean', '是否启用自动审核'),
      ('auto_audit_limit', '1000', 'number', '自动审核金额限制（元）')
      ON DUPLICATE KEY UPDATE 
        config_value = VALUES(config_value),
        description = VALUES(description),
        updated_at = CURRENT_TIMESTAMP
    `);
    
    // 6. 为测试用户插入微信账户
    console.log('6️⃣ 插入测试用户微信账户...');
    await connection.execute(`
      INSERT INTO wechat_account (user_id, openid, nickname, real_name, is_verified, is_default) VALUES
      (1, 'oW20C7mVlW8e3W2AgUGtDTJeAbQU', '润生', '张润生', 1, 1)
      ON DUPLICATE KEY UPDATE 
        nickname = VALUES(nickname),
        real_name = VALUES(real_name),
        updated_at = CURRENT_TIMESTAMP
    `);
    
    // 7. 更新测试用户银行卡
    console.log('7️⃣ 更新测试用户银行卡...');
    await connection.execute(`
      UPDATE bank_card SET 
        bank_code = 'CMB',
        card_number_mask = '**** **** **** 6789',
        card_type = 1,
        phone = '***********',
        is_verified = 1,
        status = 1
      WHERE user_id = 1 AND bank_name = '招商银行'
    `);
    
    // 如果不存在则插入
    await connection.execute(`
      INSERT IGNORE INTO bank_card (user_id, bank_name, bank_code, card_number, card_number_mask, card_holder, card_type, phone, is_verified, status) VALUES
      (1, '招商银行', 'CMB', 'encrypted_6217000123456789', '**** **** **** 6789', '张润生', 1, '***********', 1, 1)
    `);
    
    // 8. 插入测试提现记录
    console.log('8️⃣ 插入测试提现记录...');
    await connection.execute(`
      INSERT INTO withdraw (user_id, withdraw_no, amount, fee, actual_amount, withdraw_type, account_id, account_info, status, apply_time, complete_time, remark) VALUES
      (1, 'WD202501290001', 100.00, 0.60, 99.40, 'wechat', 1, '{"type":"wechat","nickname":"润生","real_name":"张润生"}', 4, '2025-01-29 10:00:00', '2025-01-29 11:30:00', '测试微信提现'),
      (1, 'WD202501290002', 200.00, 2.00, 198.00, 'bank_card', 1, '{"type":"bank_card","bank_name":"招商银行","card_mask":"**** **** **** 6789"}', 4, '2025-01-29 14:00:00', '2025-01-29 16:00:00', '测试银行卡提现'),
      (1, 'WD202501290003', 50.00, 0.30, 49.70, 'wechat', 1, '{"type":"wechat","nickname":"润生","real_name":"张润生"}', 0, '2025-01-29 18:00:00', NULL, '待审核提现申请')
      ON DUPLICATE KEY UPDATE updated_at = CURRENT_TIMESTAMP
    `);
    
    console.log('✅ 提现功能数据库表设置完成！');
    
    // 验证设置结果
    console.log('\n📊 验证设置结果:');
    const [configCount] = await connection.execute('SELECT COUNT(*) as count FROM withdraw_config');
    console.log(`✅ 提现配置: ${configCount[0].count} 条`);
    
    const [wechatCount] = await connection.execute('SELECT COUNT(*) as count FROM wechat_account');
    console.log(`✅ 微信账户: ${wechatCount[0].count} 个`);
    
    const [bankCount] = await connection.execute('SELECT COUNT(*) as count FROM bank_card');
    console.log(`✅ 银行卡: ${bankCount[0].count} 张`);
    
    const [withdrawCount] = await connection.execute('SELECT COUNT(*) as count FROM withdraw');
    console.log(`✅ 提现记录: ${withdrawCount[0].count} 条`);
    
    const [userBalance] = await connection.execute('SELECT balance FROM user WHERE id = 1');
    if (userBalance.length > 0) {
      console.log(`✅ 用户余额: ${userBalance[0].balance} 元`);
    }
    
  } catch (error) {
    console.error('❌ 设置失败:', error.message);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

setupWithdrawTables();
