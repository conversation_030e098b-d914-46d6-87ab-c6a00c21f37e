// pages/mall/order/detail/detail.js
const { request } = require('../../../../utils/request')
const { showToast, formatImageUrl } = require('../../../../utils/util')
const API = require('../../../../config/api')

Page({
  data: {
    orderId: '',
    orderInfo: null,
    loading: true
  },

  onLoad: function (options) {
    console.log('📦 订单详情页面加载', options)
    
    if (options.id) {
      this.setData({ orderId: options.id })
      this.fetchOrderDetail()
    } else {
      showToast('订单ID不能为空')
      wx.navigateBack()
    }
  },

  /**
   * 获取订单详情
   */
  fetchOrderDetail: function () {
    this.setData({ loading: true })
    
    request({
      url: `${API.order.detail}/${this.data.orderId}`,
      method: 'GET'
    }).then(res => {
      console.log('📦 订单详情响应:', res)
      // 兼容不同的响应格式
      if ((res.code === 0 || res.code === 200 || res.success === true) && res.data) {
        // 处理商品图片URL
        if (res.data.goods_list && Array.isArray(res.data.goods_list)) {
          res.data.goods_list.forEach(item => {
            if (item.image) {
              item.image = formatImageUrl(item.image)
              console.log('处理后的商品图片URL:', item.image)
            }
          })
        }

        this.setData({
          orderInfo: res.data,
          loading: false
        })
        console.log('✅ 订单详情获取成功')
      } else {
        console.error('❌ 订单详情响应格式错误:', res)
        showToast('获取订单详情失败')
        this.setData({ loading: false })
      }
    }).catch(err => {
      console.error('❌ 获取订单详情失败:', err)
      showToast('获取订单详情失败')
      this.setData({ loading: false })
    })
  },

  /**
   * 取消订单
   */
  onCancelOrder: function () {
    wx.showModal({
      title: '确认取消',
      content: '确定要取消这个订单吗？',
      success: (res) => {
        if (res.confirm) {
          this.cancelOrder()
        }
      }
    })
  },

  /**
   * 执行取消订单
   */
  cancelOrder: function () {
    wx.showLoading({ title: '取消中...' })
    
    request({
      url: `${API.order.cancel}/${this.data.orderId}`,
      method: 'POST'
    }).then(res => {
      wx.hideLoading()
      if ((res.code === 0 || res.code === 200 || res.success === true)) {
        showToast('订单已取消')
        this.fetchOrderDetail() // 刷新订单详情
      } else {
        showToast(res.message || '取消订单失败')
      }
    }).catch(err => {
      wx.hideLoading()
      console.error('取消订单失败:', err)
      showToast('取消订单失败')
    })
  },

  /**
   * 确认收货
   */
  onConfirmReceive: function () {
    wx.showModal({
      title: '确认收货',
      content: '确定已收到商品吗？',
      success: (res) => {
        if (res.confirm) {
          this.confirmReceive()
        }
      }
    })
  },

  /**
   * 执行确认收货
   */
  confirmReceive: function () {
    wx.showLoading({ title: '确认中...' })
    
    request({
      url: `${API.order.confirm}/${this.data.orderId}`,
      method: 'POST'
    }).then(res => {
      wx.hideLoading()
      if ((res.code === 0 || res.code === 200 || res.success === true)) {
        showToast('确认收货成功')
        this.fetchOrderDetail() // 刷新订单详情
      } else {
        showToast(res.message || '确认收货失败')
      }
    }).catch(err => {
      wx.hideLoading()
      console.error('确认收货失败:', err)
      showToast('确认收货失败')
    })
  },

  /**
   * 立即支付
   */
  onPayNow: function () {
    wx.navigateTo({
      url: `/pages/mall/order/payment/payment?orderId=${this.data.orderId}`
    })
  },

  /**
   * 查看物流
   */
  onViewLogistics: function () {
    wx.navigateTo({
      url: `/pages/mall/order/logistics/logistics?orderId=${this.data.orderId}`
    })
  },

  /**
   * 复制订单号
   */
  onCopyOrderNo: function () {
    if (this.data.orderInfo && this.data.orderInfo.order_no) {
      wx.setClipboardData({
        data: this.data.orderInfo.order_no,
        success: () => {
          showToast('订单号已复制')
        }
      })
    }
  },

  /**
   * 联系客服
   */
  onContactService: function () {
    wx.makePhoneCall({
      phoneNumber: '************'
    })
  },

  /**
   * 返回上一页
   */
  onGoBack: function () {
    wx.navigateBack()
  }
})
