const axios = require('axios');

const BASE_URL = 'http://localhost:4000/api/v1';

// 测试联盟申请API
async function testAllianceApply() {
  console.log('🧪 开始测试联盟申请API...\n');
  
  try {
    // 1. 使用一个测试token（假设用户ID为1的用户）
    console.log('1. 使用测试token...');

    // 生成一个简单的测试token（实际项目中不应该这样做）
    const jwt = require('jsonwebtoken');
    const token = jwt.sign(
      { id: 1, phone: '13800138000' },
      'wifi_share_secret_key', // 与后端配置的密钥一致
      { expiresIn: '24h' }
    );

    console.log('✅ 使用测试token:', token.substring(0, 20) + '...\n');
    
    // 2. 测试联盟申请
    console.log('2. 测试联盟申请...');
    const applyData = {
      name: '测试联盟团队',
      contact: '测试联系人',
      phone: '13800138000',
      email: '<EMAIL>',
      area: '华东地区',
      description: '这是一个测试联盟申请'
    };
    
    const applyResponse = await axios.post(`${BASE_URL}/client/alliance/apply`, applyData, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    console.log('申请响应:', applyResponse.data);
    
    if (applyResponse.data.code === 0) {
      console.log('✅ 联盟申请提交成功');
      const applicationId = applyResponse.data.data.id;
      console.log('申请ID:', applicationId);
      
      // 3. 测试查询申请状态
      console.log('\n3. 测试查询申请状态...');
      const statusResponse = await axios.get(`${BASE_URL}/client/alliance/status`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      console.log('状态查询响应:', statusResponse.data);
      
      if (statusResponse.data.code === 0) {
        console.log('✅ 申请状态查询成功');
        console.log('申请状态:', statusResponse.data.data);
      } else {
        console.log('❌ 申请状态查询失败');
      }
      
      // 4. 测试管理端查看申请列表
      console.log('\n4. 测试管理端查看申请列表...');
      const listResponse = await axios.get(`${BASE_URL}/admin/alliance/list?page=1&limit=10`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      console.log('申请列表响应:', listResponse.data);
      
      if (listResponse.data.code === 0) {
        console.log('✅ 申请列表查询成功');
        const applications = listResponse.data.data.list;
        const testApp = applications.find(app => app.id === applicationId);
        if (testApp) {
          console.log('✅ 在管理端找到了刚提交的申请:', testApp);
        } else {
          console.log('❌ 在管理端未找到刚提交的申请');
        }
      } else {
        console.log('❌ 申请列表查询失败');
      }
      
    } else {
      console.log('❌ 联盟申请提交失败:', applyResponse.data.message);
    }
    
  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error.message);
    if (error.response) {
      console.error('错误响应:', error.response.data);
    }
  }
}

// 运行测试
testAllianceApply().then(() => {
  console.log('\n🏁 测试完成');
}).catch(err => {
  console.error('测试失败:', err);
});
