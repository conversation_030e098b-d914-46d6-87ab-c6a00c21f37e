#!/usr/bin/env node

/**
 * 微信支付配置测试脚本
 * 用于验证微信支付配置是否正确
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 开始检查微信支付配置...\n');

// 检查配置文件
function checkConfigFile() {
  console.log('📁 检查配置文件...');
  
  const configPath = path.join(__dirname, '../src/config/wechat.js');
  const examplePath = path.join(__dirname, '../src/config/wechat.example.js');
  
  if (!fs.existsSync(configPath)) {
    console.log('❌ 配置文件不存在: src/config/wechat.js');
    if (fs.existsSync(examplePath)) {
      console.log('💡 请复制示例文件: cp src/config/wechat.example.js src/config/wechat.js');
    }
    return false;
  }
  
  console.log('✅ 配置文件存在');
  
  try {
    const config = require(configPath);
    
    if (!config.payment) {
      console.log('❌ 配置文件中缺少 payment 配置');
      return false;
    }
    
    const payment = config.payment;
    const requiredFields = ['mchId', 'appId', 'serialNo', 'apiV3Key', 'notifyUrl'];
    const missingFields = [];
    
    requiredFields.forEach(field => {
      if (!payment[field] || payment[field].includes('your_') || payment[field].includes('1234')) {
        missingFields.push(field);
      }
    });
    
    if (missingFields.length > 0) {
      console.log('❌ 以下配置字段需要填写真实值:');
      missingFields.forEach(field => {
        console.log(`   - ${field}: ${payment[field] || '未设置'}`);
      });
      return false;
    }
    
    console.log('✅ 配置文件字段完整');
    console.log(`   - 支付状态: ${payment.enabled ? '启用' : '禁用'}`);
    console.log(`   - 商户号: ${payment.mchId}`);
    console.log(`   - AppID: ${payment.appId}`);
    console.log(`   - 回调地址: ${payment.notifyUrl}`);
    
    return true;
  } catch (err) {
    console.log('❌ 配置文件格式错误:', err.message);
    return false;
  }
}

// 检查私钥文件
function checkPrivateKey() {
  console.log('\n🔑 检查私钥文件...');
  
  const configPath = path.join(__dirname, '../src/config/wechat.js');
  
  if (!fs.existsSync(configPath)) {
    console.log('❌ 无法检查私钥文件，配置文件不存在');
    return false;
  }
  
  try {
    const config = require(configPath);
    const privateKeyPath = path.join(__dirname, '../', config.payment.privateKeyPath);
    
    if (!fs.existsSync(privateKeyPath)) {
      console.log('❌ 私钥文件不存在:', config.payment.privateKeyPath);
      console.log('💡 请从微信支付商户平台下载私钥文件并放置在指定位置');
      return false;
    }
    
    const stats = fs.statSync(privateKeyPath);
    console.log('✅ 私钥文件存在');
    console.log(`   - 文件大小: ${stats.size} 字节`);
    console.log(`   - 修改时间: ${stats.mtime.toLocaleString()}`);
    
    // 检查文件内容格式
    const content = fs.readFileSync(privateKeyPath, 'utf8');
    if (!content.includes('-----BEGIN PRIVATE KEY-----') || !content.includes('-----END PRIVATE KEY-----')) {
      console.log('⚠️  私钥文件格式可能不正确，请确认是否为有效的PEM格式私钥');
      return false;
    }
    
    console.log('✅ 私钥文件格式正确');
    return true;
  } catch (err) {
    console.log('❌ 检查私钥文件失败:', err.message);
    return false;
  }
}

// 检查环境变量
function checkEnvironmentVariables() {
  console.log('\n🌍 检查环境变量...');
  
  const envVars = [
    'WECHAT_APPID',
    'WECHAT_MCHID',
    'WECHAT_SERIAL_NO',
    'WECHAT_APIV3_KEY',
    'WECHAT_NOTIFY_URL',
    'WECHAT_PAY_ENABLED'
  ];
  
  const setVars = [];
  const unsetVars = [];
  
  envVars.forEach(varName => {
    if (process.env[varName]) {
      setVars.push(varName);
    } else {
      unsetVars.push(varName);
    }
  });
  
  if (setVars.length > 0) {
    console.log('✅ 已设置的环境变量:');
    setVars.forEach(varName => {
      const value = process.env[varName];
      const displayValue = varName.includes('KEY') ? '***已设置***' : value;
      console.log(`   - ${varName}: ${displayValue}`);
    });
  }
  
  if (unsetVars.length > 0) {
    console.log('ℹ️  未设置的环境变量:');
    unsetVars.forEach(varName => {
      console.log(`   - ${varName}`);
    });
  }
  
  return setVars.length > 0;
}

// 测试微信支付初始化
function testWechatPayInit() {
  console.log('\n🧪 测试微信支付初始化...');
  
  try {
    const wechatPay = require('../src/utils/wechatPay');
    
    const isEnabled = wechatPay.isWechatPayEnabled();
    
    if (isEnabled) {
      console.log('✅ 微信支付初始化成功，真实支付已启用');
    } else {
      console.log('ℹ️  微信支付未启用，将使用模拟支付');
      console.log('💡 如需启用真实支付，请完善配置并设置 enabled: true');
    }
    
    return true;
  } catch (err) {
    console.log('❌ 微信支付初始化失败:', err.message);
    return false;
  }
}

// 生成配置建议
function generateConfigSuggestions() {
  console.log('\n💡 配置建议:');
  console.log('1. 开发环境建议使用模拟支付 (enabled: false)');
  console.log('2. 生产环境部署前请确保所有配置正确');
  console.log('3. 回调地址必须是公网可访问的HTTPS地址');
  console.log('4. 定期检查微信支付证书有效期');
  console.log('5. 不要将私钥文件提交到版本控制系统');
}

// 主函数
async function main() {
  const results = {
    configFile: checkConfigFile(),
    privateKey: checkPrivateKey(),
    envVars: checkEnvironmentVariables(),
    wechatPayInit: testWechatPayInit()
  };
  
  console.log('\n📊 检查结果汇总:');
  Object.entries(results).forEach(([key, result]) => {
    const status = result ? '✅' : '❌';
    const name = {
      configFile: '配置文件',
      privateKey: '私钥文件',
      envVars: '环境变量',
      wechatPayInit: '微信支付初始化'
    }[key];
    console.log(`${status} ${name}: ${result ? '通过' : '失败'}`);
  });
  
  const allPassed = Object.values(results).every(result => result);
  
  if (allPassed) {
    console.log('\n🎉 所有检查通过！微信支付配置正确。');
  } else {
    console.log('\n⚠️  部分检查未通过，请根据上述提示完善配置。');
  }
  
  generateConfigSuggestions();
}

// 运行测试
main().catch(err => {
  console.error('❌ 测试脚本执行失败:', err);
  process.exit(1);
});
