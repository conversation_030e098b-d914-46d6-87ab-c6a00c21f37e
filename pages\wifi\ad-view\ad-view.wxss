/* pages/wifi/ad-view/ad-view.wxss */
/* 广告观看页面样式 */

.container {
  padding: 30rpx;
  background-color: #f8f9fa;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}

/* 顶部标题 */
.header {
  margin-bottom: 30rpx;
  text-align: center;
}

.title {
  font-size: 40rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.subtitle {
  font-size: 28rpx;
  color: #666;
}

/* WiFi信息 */
.wifi-info {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.wifi-name {
  display: flex;
  align-items: center;
}

.label {
  font-size: 28rpx;
  color: #666;
  margin-right: 10rpx;
  min-width: 150rpx;
}

.value {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

/* 广告容器 */
.ad-container {
  flex: 1;
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 0;
}

.loading-spinner {
  width: 80rpx;
  height: 80rpx;
  border: 6rpx solid rgba(0, 0, 0, 0.1);
  border-top: 6rpx solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  margin-top: 30rpx;
  font-size: 28rpx;
  color: #666;
}

/* 错误状态 */
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 0;
}

.error-text {
  margin-top: 30rpx;
  margin-bottom: 40rpx;
  font-size: 30rpx;
  color: #e74c3c;
  text-align: center;
}

/* 视频广告 */
.video-ad {
  width: 100%;
  position: relative;
  margin-bottom: 30rpx;
}

/* Banner广告 */
.banner-ad {
  width: 100%;
  position: relative;
  margin-bottom: 30rpx;
  border-radius: 12rpx;
  overflow: hidden;
}

.ad-content {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20rpx;
  background: linear-gradient(to top, rgba(0,0,0,0.7), transparent);
}

.ad-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #fff;
  display: block;
  margin-bottom: 6rpx;
}

.ad-desc {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
}

/* 倒计时 */
.countdown {
  margin-top: 20rpx;
  text-align: center;
  font-size: 28rpx;
  color: #666;
  padding: 10rpx 0;
}

/* 连接容器 */
.connect-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 0;
  width: 100%;
}

.connect-text {
  margin-top: 30rpx;
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.connect-subtext {
  margin-top: 10rpx;
  margin-bottom: 40rpx;
  font-size: 28rpx;
  color: #666;
  text-align: center;
}

/* 按钮样式 */
.btn {
  margin-top: 20rpx;
  width: 80%;
  border-radius: 40rpx;
  padding: 20rpx 0;
  font-size: 32rpx;
  text-align: center;
}

.btn-primary {
  background-color: #3498db;
  color: #fff;
  border: none;
}

.btn-default {
  background-color: #f5f5f5;
  color: #333;
  border: 2rpx solid #ddd;
}

/* 底部 */
.footer {
  padding: 30rpx 0;
  text-align: center;
}

.footer-text {
  font-size: 24rpx;
  color: #999;
} 