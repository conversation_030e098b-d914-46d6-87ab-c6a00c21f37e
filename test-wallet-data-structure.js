// 测试钱包数据结构和分润数据
const axios = require('axios');
const jwt = require('jsonwebtoken');

async function testWalletDataStructure() {
  try {
    console.log('🧪 测试钱包数据结构和分润数据...\n');
    
    // 1. 生成测试token
    const testUser = {
      id: 1,
      openid: 'oW20C7mVlW8e3W2AgUGtDTJeAbQU'
    };
    
    const token = jwt.sign(
      testUser,
      'wifi_share_secret_key',
      { expiresIn: '24h' }
    );
    
    console.log('1️⃣ 生成的测试token:', token.substring(0, 50) + '...\n');
    
    // 2. 测试收入统计接口
    console.log('2️⃣ 测试收入统计接口（钱包数据）...');
    try {
      const statsResponse = await axios.get('http://localhost:4000/api/v1/client/income/stats', {
        headers: {
          'Authorization': `Bearer ${token}`
        },
        timeout: 10000
      });
      
      console.log('✅ 收入统计接口成功:');
      console.log('状态码:', statsResponse.status);
      console.log('响应数据:', JSON.stringify(statsResponse.data, null, 2));
      
      // 分析数据结构
      const data = statsResponse.data.data;
      console.log('\n📊 钱包分润数据分析:');
      console.log('='.repeat(50));
      
      // 基础余额信息
      console.log('💰 基础信息:');
      console.log('- 账户余额 (balance):', data.balance || '❌ 缺失');
      console.log('- 总收入 (total_income):', data.total_income || '❌ 缺失');
      console.log('- 今日收入 (today_income):', data.today_income || '❌ 缺失');
      console.log('- 本月收入 (month_income):', data.month_income || '❌ 缺失');
      
      // 分润收益详情
      console.log('\n🎯 分润收益详情:');
      console.log('- WiFi分享收益 (wifi_income):', data.wifi_income || '❌ 缺失');
      console.log('- 团队收益 (team_income/referral_income):', data.team_income || data.referral_income || '❌ 缺失');
      console.log('- 广告流量收益 (ad_income/advertisement_income):', data.ad_income || data.advertisement_income || '❌ 缺失');
      console.log('- 商城订单收益 (goods_income/mall_income):', data.goods_income || data.mall_income || '❌ 缺失');
      
      // 检查是否有嵌套的total对象
      if (data.total) {
        console.log('\n📦 嵌套total对象:');
        console.log('- total.balance:', data.total.balance || '❌ 缺失');
        console.log('- total.wifi_income:', data.total.wifi_income || '❌ 缺失');
        console.log('- total.team_income:', data.total.team_income || '❌ 缺失');
        console.log('- total.ad_income:', data.total.ad_income || '❌ 缺失');
        console.log('- total.goods_income:', data.total.goods_income || '❌ 缺失');
      }
      
      // 检查是否有this_month对象
      if (data.this_month) {
        console.log('\n📅 本月数据对象:');
        console.log('- this_month.income:', data.this_month.income || '❌ 缺失');
        console.log('- this_month.wifi_income:', data.this_month.wifi_income || '❌ 缺失');
        console.log('- this_month.team_income:', data.this_month.team_income || '❌ 缺失');
      }
      
      // 生成前端映射建议
      console.log('\n🔧 前端数据映射建议:');
      console.log('```javascript');
      console.log('incomeStats: {');
      console.log(`  wifi: "${data.wifi_income || data.total?.wifi_income || '0.00'}", // WiFi分享收益`);
      console.log(`  team: "${data.team_income || data.referral_income || data.total?.team_income || '0.00'}", // 团队收益`);
      console.log(`  ads: "${data.ad_income || data.advertisement_income || data.total?.ad_income || '0.00'}", // 广告流量收益`);
      console.log(`  mall: "${data.goods_income || data.mall_income || data.total?.goods_income || '0.00'}" // 商城订单收益`);
      console.log('}');
      console.log('```');
      
    } catch (statsError) {
      console.log('❌ 收入统计接口失败:');
      if (statsError.response) {
        console.log('状态码:', statsError.response.status);
        console.log('错误信息:', JSON.stringify(statsError.response.data, null, 2));
      } else {
        console.log('请求失败:', statsError.message);
      }
    }
    
    // 3. 测试收入明细接口
    console.log('\n3️⃣ 测试收入明细接口...');
    try {
      const detailsResponse = await axios.get('http://localhost:4000/api/v1/client/income/details', {
        headers: {
          'Authorization': `Bearer ${token}`
        },
        params: {
          page: 1,
          limit: 5
        },
        timeout: 10000
      });
      
      console.log('✅ 收入明细接口成功:');
      console.log('状态码:', detailsResponse.status);
      
      const details = detailsResponse.data.data;
      if (details && details.list && details.list.length > 0) {
        console.log('\n📋 收入明细分析:');
        details.list.forEach((item, index) => {
          console.log(`\n明细 ${index + 1}:`);
          console.log('- ID:', item.id);
          console.log('- 金额:', item.amount);
          console.log('- 类型:', item.type);
          console.log('- 标题:', item.title);
          console.log('- 描述:', item.description);
          console.log('- 来源类型:', item.source_type);
          console.log('- 状态:', item.status);
          console.log('- 创建时间:', item.created_at);
        });
        
        // 按类型统计
        const typeStats = {};
        details.list.forEach(item => {
          if (!typeStats[item.type]) {
            typeStats[item.type] = { count: 0, total: 0 };
          }
          typeStats[item.type].count++;
          typeStats[item.type].total += parseFloat(item.amount);
        });
        
        console.log('\n📊 收入类型统计:');
        Object.keys(typeStats).forEach(type => {
          const stats = typeStats[type];
          console.log(`- ${type}: ${stats.count}笔, 总计¥${stats.total.toFixed(2)}`);
        });
      }
      
    } catch (detailsError) {
      console.log('❌ 收入明细接口失败:');
      if (detailsError.response) {
        console.log('状态码:', detailsError.response.status);
        console.log('错误信息:', JSON.stringify(detailsError.response.data, null, 2));
      } else {
        console.log('请求失败:', detailsError.message);
      }
    }
    
    console.log('\n🎯 测试总结:');
    console.log('1. 检查后端API是否返回正确的分润数据字段');
    console.log('2. 确认前端数据映射是否正确');
    console.log('3. 验证收入类型分类是否准确');
    console.log('4. 测试按钮响应和页面跳转功能');
    
  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error.message);
  }
}

// 运行测试
testWalletDataStructure();
