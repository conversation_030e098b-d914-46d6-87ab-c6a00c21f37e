const mysql = require('mysql2/promise');

async function fixWithdrawSystem() {
  let connection;
  
  try {
    // 连接数据库
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: 'wo587129955',
      database: 'mall'
    });
    
    console.log('🔧 修复提现系统...\n');
    
    // 1. 检查并修复表结构
    console.log('1️⃣ 检查表结构...');
    
    // 检查user_withdraw_account表是否存在
    const [accountTable] = await connection.execute(`
      SELECT COUNT(*) as count 
      FROM information_schema.tables 
      WHERE table_schema = 'mall' AND table_name = 'user_withdraw_account'
    `);
    
    if (accountTable[0].count === 0) {
      console.log('创建user_withdraw_account表...');
      await connection.execute(`
        CREATE TABLE user_withdraw_account (
          id int(11) NOT NULL AUTO_INCREMENT COMMENT '账户ID',
          user_id int(11) NOT NULL COMMENT '用户ID',
          account_type varchar(20) NOT NULL COMMENT '账户类型',
          account_name varchar(100) NOT NULL COMMENT '账户名称',
          account_info text NOT NULL COMMENT '账户信息JSON',
          is_default tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否默认',
          is_verified tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已验证',
          status tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态',
          created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
          updated_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
          PRIMARY KEY (id),
          KEY user_id (user_id),
          KEY account_type (account_type)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户提现账户表'
      `);
      console.log('✅ user_withdraw_account表创建成功');
    } else {
      console.log('✅ user_withdraw_account表已存在');
    }
    
    // 2. 插入提现配置数据
    console.log('\n2️⃣ 插入提现配置数据...');
    await connection.execute(`
      INSERT IGNORE INTO withdraw_config (account_type, min_amount, max_amount, daily_limit, fee_type, fee_rate, fee_amount, min_fee, max_fee, process_time, description) VALUES
      ('wechat', 1.00, 20000.00, 5000.00, 1, 0.0060, 0.00, 0.60, 25.00, '实时到账', '微信零钱提现，实时到账'),
      ('alipay', 1.00, 50000.00, 10000.00, 1, 0.0055, 0.00, 0.55, 25.00, '2小时内', '支付宝余额提现，2小时内到账'),
      ('bank_card', 100.00, 50000.00, 20000.00, 1, 0.0100, 0.00, 1.00, 50.00, '1-3个工作日', '银行卡提现，1-3个工作日到账')
    `);
    console.log('✅ 提现配置数据插入成功');
    
    // 3. 插入测试用户提现账户
    console.log('\n3️⃣ 插入测试用户提现账户...');
    await connection.execute(`
      INSERT IGNORE INTO user_withdraw_account (user_id, account_type, account_name, account_info, is_default, is_verified) VALUES
      (1, 'wechat', '微信零钱', '{"openid": "oW20C7mVlW8e3W2AgUGtDTJeAbQU", "nickname": "润生", "real_name": "张润生"}', 1, 1),
      (1, 'alipay', '支付宝账户', '{"account": "138****8888", "real_name": "张润生", "account_type": "phone"}', 0, 0),
      (1, 'bank_card', '工商银行储蓄卡', '{"bank_name": "中国工商银行", "bank_code": "ICBC", "card_number": "6222****1234", "card_holder": "张润生", "bank_branch": "北京分行"}', 0, 0)
    `);
    console.log('✅ 测试用户提现账户插入成功');
    
    // 4. 插入测试提现申请
    console.log('\n4️⃣ 插入测试提现申请...');
    const withdrawNo1 = 'WD' + new Date().toISOString().slice(0,10).replace(/-/g,'') + '001';
    const withdrawNo2 = 'WD' + new Date().toISOString().slice(0,10).replace(/-/g,'') + '002';
    const withdrawNo3 = 'WD' + new Date().toISOString().slice(0,10).replace(/-/g,'') + '003';
    
    await connection.execute(`
      INSERT IGNORE INTO withdraw_application (user_id, withdraw_no, amount, fee, actual_amount, account_id, account_type, account_info, status, apply_time) VALUES
      (1, ?, 100.00, 1.00, 99.00, 1, 'wechat', '{"openid": "oW20C7mVlW8e3W2AgUGtDTJeAbQU", "nickname": "润生"}', 4, DATE_SUB(NOW(), INTERVAL 2 DAY)),
      (1, ?, 200.00, 2.00, 198.00, 2, 'alipay', '{"account": "138****8888", "real_name": "张润生"}', 1, DATE_SUB(NOW(), INTERVAL 1 DAY)),
      (1, ?, 50.00, 0.60, 49.40, 1, 'wechat', '{"openid": "oW20C7mVlW8e3W2AgUGtDTJeAbQU", "nickname": "润生"}', 0, NOW())
    `, [withdrawNo1, withdrawNo2, withdrawNo3]);
    console.log('✅ 测试提现申请插入成功');
    
    // 5. 验证数据
    console.log('\n5️⃣ 验证数据...');
    
    // 显示提现配置
    console.log('💰 提现配置:');
    const [configs] = await connection.execute(`
      SELECT 
        account_type as '提现方式',
        min_amount as '最小金额',
        max_amount as '最大金额',
        daily_limit as '每日限额',
        CONCAT(fee_rate * 100, '%') as '手续费率',
        process_time as '到账时间',
        description as '说明'
      FROM withdraw_config
      ORDER BY account_type
    `);
    console.table(configs);
    
    // 显示用户提现账户
    console.log('\n🏦 用户提现账户:');
    const [accounts] = await connection.execute(`
      SELECT 
        id,
        account_type as '账户类型',
        account_name as '账户名称',
        is_default as '默认',
        is_verified as '已验证',
        status as '状态'
      FROM user_withdraw_account
      WHERE user_id = 1
      ORDER BY is_default DESC, id
    `);
    console.table(accounts);
    
    // 显示提现申请
    console.log('\n📋 提现申请记录:');
    const [applications] = await connection.execute(`
      SELECT 
        id,
        withdraw_no as '提现单号',
        amount as '申请金额',
        fee as '手续费',
        actual_amount as '到账金额',
        account_type as '提现方式',
        CASE status
          WHEN 0 THEN '待审核'
          WHEN 1 THEN '审核通过'
          WHEN 2 THEN '审核拒绝'
          WHEN 3 THEN '处理中'
          WHEN 4 THEN '已完成'
          WHEN 5 THEN '已取消'
        END as '状态',
        apply_time as '申请时间'
      FROM withdraw_application
      WHERE user_id = 1
      ORDER BY apply_time DESC
    `);
    console.table(applications);
    
    // 统计信息
    const [accountCount] = await connection.execute('SELECT COUNT(*) as count FROM user_withdraw_account');
    const [configCount] = await connection.execute('SELECT COUNT(*) as count FROM withdraw_config');
    const [applicationCount] = await connection.execute('SELECT COUNT(*) as count FROM withdraw_application');
    
    console.log('\n📊 数据统计:');
    console.log(`- 提现账户: ${accountCount[0].count} 个`);
    console.log(`- 提现配置: ${configCount[0].count} 项`);
    console.log(`- 提现申请: ${applicationCount[0].count} 条`);
    
    console.log('\n🎉 提现系统修复完成！');
    
  } catch (error) {
    console.error('❌ 修复失败:', error.message);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// 运行修复
fixWithdrawSystem();
