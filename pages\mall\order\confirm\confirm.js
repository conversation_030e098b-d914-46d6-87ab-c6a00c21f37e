// pages/mall/order/confirm/confirm.js
const app = getApp()
const API = require('../../../../config/api')
const { request } = require('../../../../utils/request')
const { showToast, showModal, formatImageUrl } = require('../../../../utils/util')

Page({
  /**
   * 页面的初始数据
   */
  data: {
    orderGoods: [],
    address: null,
    hasDefaultAddress: false,
    totalAmount: 0,
    freight: 0,
    discountAmount: 0,
    finalAmount: 0,
    remark: '',
    paymentMethod: 'wechat',
    couponId: 0,
    loading: true,
    submitting: false,
    isLoggedIn: false,
    fromCart: false,
    // 配送方式相关
    deliveryMethods: [
      {
        id: 1,
        name: '快递配送',
        description: '3-7个工作日送达',
        fee: 10,
        freeShippingThreshold: 99
      },
      {
        id: 2,
        name: '顺丰快递',
        description: '1-3个工作日送达',
        fee: 20,
        freeShippingThreshold: 199
      }
    ],
    selectedDeliveryMethod: {
      id: 1,
      name: '快递配送',
      description: '3-7个工作日送达',
      fee: 10,
      freeShippingThreshold: 99
    }
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    this.setData({
      isLoggedIn: app.globalData.isLoggedIn,
      fromCart: options.from === 'cart'
    })

    if (!this.data.isLoggedIn) {
      showModal({
        title: '提示',
        content: '请先登录',
        showCancel: false,
        success: () => {
          wx.switchTab({
            url: '/pages/user/profile/profile'
          })
        }
      })
      return
    }

    // 获取订单商品数据
    this.getOrderGoods(options)
    
    // 获取默认收货地址
    this.fetchDefaultAddress()
  },

  /**
   * 获取订单商品数据
   */
  getOrderGoods: function (options) {
    console.log('getOrderGoods 开始，options:', options);
    console.log('app.globalData.tempOrderGoods:', app.globalData.tempOrderGoods);

    let orderGoods = []

    // 检查是否有通过URL参数传递的商品数据
    if (options && options.items) {
      try {
        // 解码并解析URL参数中的商品数据
        const itemsStr = decodeURIComponent(options.items);
        orderGoods = JSON.parse(itemsStr);
        console.log('从URL参数获取的商品数据:', orderGoods);
        this.processOrderGoods(orderGoods);
        return;
      } catch (err) {
        console.error('解析URL参数中的商品数据失败:', err);
        this.setData({ loading: false });
      }
    }

    if (this.data.fromCart) {
      // 从购物车进入，获取已选中的购物车商品
      request({
        url: API.cart.list,
        method: 'GET',
        data: { selected: true }
      }).then(res => {
        if (res.code === 0 && res.data) {
          orderGoods = res.data.list || []
          this.processOrderGoods(orderGoods)
        } else {
          this.setData({ loading: false });
          showToast('获取购物车商品失败')
          setTimeout(() => {
            wx.navigateBack()
          }, 1500)
        }
      }).catch(err => {
        console.error('获取购物车商品失败', err)
        this.setData({ loading: false });
        showToast('获取购物车商品失败')
        setTimeout(() => {
          wx.navigateBack()
        }, 1500)
      })
    } else {
      // 从立即购买进入，获取全局临时订单商品
      orderGoods = app.globalData.tempOrderGoods || []
      console.log('从全局数据获取的商品:', orderGoods);
      this.processOrderGoods(orderGoods)
    }
  },

  /**
   * 处理订单商品数据
   */
  processOrderGoods: function (goods) {
    console.log('处理订单商品数据:', goods);

    if (!goods || goods.length === 0) {
      console.log('没有商品数据，设置loading为false');
      this.setData({ loading: false });
      showToast('没有选择商品');
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
      return;
    }

    // 检查商品数据是否已包含完整信息
    if (goods[0] && goods[0].name && goods[0].price) {
      console.log('商品数据已包含完整信息，直接处理');
      // 处理商品图片URL
      goods.forEach(item => {
        if (item.cover) {
          item.cover = formatImageUrl(item.cover);
          console.log('处理后的商品图片URL:', item.cover);
        }
      });
      this.calculateOrderAmount(goods);
    } else {
      console.log('商品数据不完整，需要获取详细信息');
      // 如果商品数据不完整，需要根据商品ID获取详细信息
      this.fetchGoodsDetails(goods);
    }
  },

  /**
   * 获取商品详细信息
   */
  fetchGoodsDetails: function (goods) {
    const promises = goods.map(item => {
      return request({
        url: API.goods.detail,
        method: 'GET',
        data: { id: item.goodsId }
      }).then(res => {
        if (res.code === 0 && res.data) {
          const goodsDetail = res.data;
          const specs = goodsDetail.specifications || [];
          const selectedSpec = specs.find(spec => spec.id === item.specificationId) || specs[0] || {};

          return {
            ...item,
            id: goodsDetail.id,
            name: goodsDetail.name,
            cover: formatImageUrl(goodsDetail.cover),
            price: selectedSpec.price || goodsDetail.price,
            selectedSpec: selectedSpec
          };
        }
        return item;
      }).catch(err => {
        console.error('获取商品详情失败:', err);
        return item;
      });
    });

    Promise.all(promises).then(detailedGoods => {
      console.log('获取到的详细商品信息:', detailedGoods);
      this.calculateOrderAmount(detailedGoods);
    }).catch(err => {
      console.error('获取商品详情失败:', err);
      showToast('获取商品信息失败');
      this.setData({ loading: false });
    });
  },

  /**
   * 计算订单金额
   */
  calculateOrderAmount: function (goods) {
    // 计算总金额
    let totalAmount = 0;
    goods.forEach(item => {
      const price = parseFloat(item.price || 0);
      totalAmount += price * item.quantity;
    });

    // 根据选中的配送方式计算运费
    const selectedMethod = this.data.selectedDeliveryMethod;
    const freight = totalAmount >= selectedMethod.freeShippingThreshold ? 0 : selectedMethod.fee;
    const finalAmount = totalAmount + freight - this.data.discountAmount;

    console.log('订单金额计算:', {
      totalAmount,
      freight,
      finalAmount,
      goods: goods
    });

    this.setData({
      orderGoods: goods,
      totalAmount,
      freight,
      finalAmount,
      loading: false
    });
  },



  /**
   * 获取默认收货地址
   */
  fetchDefaultAddress: function () {
    console.log('开始获取默认地址...');
    request({
      url: API.user.address.default,
      method: 'GET'
    }).then(res => {
      console.log('获取默认地址响应:', res);
      if (res.code === 0 && res.data) {
        console.log('设置默认地址数据:', res.data);
        this.setData({
          address: res.data,
          hasDefaultAddress: true,
          loading: false
        })
      } else {
        console.log('没有默认地址或获取失败');
        this.setData({
          hasDefaultAddress: false,
          loading: false
        })
      }
    }).catch(err => {
      console.error('获取默认地址失败', err)
      this.setData({
        hasDefaultAddress: false,
        loading: false
      })
    })
  },

  /**
   * 选择收货地址
   */
  onSelectAddress: function () {
    console.log('点击了收货地址区域');
    console.log('当前loading状态:', this.data.loading);

    // 如果页面还在加载中，不允许点击
    if (this.data.loading) {
      console.log('页面正在加载中，忽略点击');
      wx.showToast({
        title: '页面加载中...',
        icon: 'loading',
        duration: 1000
      });
      return;
    }

    console.log('跳转到地址选择页面');
    wx.showToast({
      title: '正在跳转...',
      icon: 'loading',
      duration: 1000
    });

    wx.navigateTo({
      url: '/pages/user/address/list?select=true',
      success: function() {
        console.log('成功跳转到地址列表页面');
      },
      fail: function(err) {
        console.error('跳转失败:', err);
        wx.showToast({
          title: '跳转失败',
          icon: 'none'
        });
      }
    })
  },

  /**
   * 设置选中的地址（从地址列表页面回调）
   */
  setAddress: function (address) {
    console.log('设置选中的地址:', address);
    if (!address || !address.id) {
      console.error('选择的地址数据无效:', address);
      showToast('选择的地址信息不完整');
      return;
    }

    this.setData({
      address: address,
      hasDefaultAddress: true
    });
  },

  /**
   * 选择配送方式
   */
  onSelectDeliveryMethod: function (e) {
    const method = e.currentTarget.dataset.method;
    console.log('选择配送方式:', method);

    this.setData({
      selectedDeliveryMethod: method
    });

    // 重新计算运费
    this.calculateOrderAmount(this.data.orderGoods);
  },

  /**
   * 选择支付方式
   */
  onSelectPayment: function (e) {
    this.setData({
      paymentMethod: e.currentTarget.dataset.method
    })
  },

  /**
   * 选择优惠券
   */
  onSelectCoupon: function () {
    showToast('优惠券功能开发中')
  },

  /**
   * 输入备注
   */
  onInputRemark: function (e) {
    this.setData({
      remark: e.detail.value
    })
  },

  /**
   * 提交订单
   */
  onSubmitOrder: function () {
    if (this.data.submitting) return

    console.log('提交订单 - 当前地址数据:', this.data.address);

    if (!this.data.address) {
      showToast('请选择收货地址')
      return
    }

    if (!this.data.address.id) {
      console.error('地址数据缺少ID:', this.data.address);
      showToast('收货地址信息不完整，请重新选择')
      return
    }

    this.setData({ submitting: true })

    // 构建订单数据
    const orderData = {
      addressId: this.data.address.id,
      goods: this.data.orderGoods.map(item => ({
        goodsId: item.goodsId || item.id, // 修复：优先使用goodsId，确保传递正确的商品ID
        quantity: item.quantity,
        specificationId: item.specificationId || 0
      })),
      remark: this.data.remark,
      paymentMethod: this.data.paymentMethod,
      couponId: this.data.couponId,
      fromCart: this.data.fromCart,
      deliveryMethodId: this.data.selectedDeliveryMethod.id,
      freight: this.data.freight
    }

    console.log('提交订单数据:', orderData);

    // 创建订单
    request({
      url: API.order.create,
      method: 'POST',
      data: orderData
    }).then(res => {
      console.log('🎯 订单创建响应:', res)
      if ((res.code === 0 || res.code === 200 || res.success === true) && res.data) {
        const orderId = res.data.orderId
        console.log('🎯 订单ID:', orderId)
        console.log('🎯 最终金额:', this.data.finalAmount)

        // 跳转到支付页面
        const paymentUrl = `/pages/mall/order/payment/payment?id=${orderId}&amount=${this.data.finalAmount}`
        console.log('🎯 跳转URL:', paymentUrl)

        wx.navigateTo({
          url: paymentUrl,
          success: function(res) {
            console.log('🎯 跳转成功:', res)
          },
          fail: function(err) {
            console.error('🎯 跳转失败:', err)
            showToast('跳转支付页面失败: ' + (err.errMsg || '未知错误'))
          }
        })
      } else {
        console.log('🎯 订单创建失败:', res)
        // 显示具体的错误信息
        const errorMsg = res.message || '创建订单失败'
        showToast(errorMsg)
      }
      this.setData({ submitting: false })
    }).catch(err => {
      console.error('创建订单失败', err)
      // 尝试从错误对象中获取具体的错误信息
      let errorMsg = '创建订单失败'
      if (err && err.message) {
        errorMsg = err.message
      } else if (err && typeof err === 'string') {
        errorMsg = err
      }
      showToast(errorMsg)
      this.setData({ submitting: false })
    })
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {
    if (this.data.isLoggedIn) {
      Promise.all([
        this.getOrderGoods(),
        this.fetchDefaultAddress()
      ]).finally(() => {
        wx.stopPullDownRefresh()
      })
    } else {
      wx.stopPullDownRefresh()
    }
  },

  /**
   * 广告加载成功
   */
  onAdLoad: function () {
    console.log('广告加载成功')
  },

  /**
   * 广告加载失败
   */
  onAdError: function (err) {
    console.error('广告加载失败', err)
  }
}) 