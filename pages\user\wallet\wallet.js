// pages/user/wallet/wallet.js
const app = getApp()
const API = require('../../../config/api')
const { request } = require('../../../utils/request')
const { showToast, showModal } = require('../../../utils/util')

Page({
  /**
   * 页面的初始数据
   */
  data: {
    balance: '0.00',
    incomeStats: {
      wifi: '0.00',
      team: '0.00',
      ads: '0.00',
      mall: '0.00'
    },
    transactions: [],
    loading: true,
    isLoggedIn: false,
    page: 1,
    limit: 10,
    hasMore: true
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    console.log('🔧 钱包页面加载，开始初始化...')

    // 直接检查登录状态并获取真实数据
    this.checkLoginStatus()
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    console.log('🔧 页面显示，检查登录状态...')
    const isLoggedIn = app.globalData.isLoggedIn
    const token = wx.getStorageSync('token')

    console.log('当前登录状态:', isLoggedIn)
    console.log('Token存在:', !!token)

    if (isLoggedIn && token) {
      console.log('✅ 已登录，获取钱包数据')
      this.fetchWalletData()
      this.fetchTransactions()
    } else {
      console.log('❌ 未登录，跳转登录页面')
      wx.navigateTo({
        url: '/pages/user/login/login'
      })
    }
  },

  /**
   * 检查登录状态
   */
  checkLoginStatus: function () {
    console.log('🔧 检查登录状态...')
    const isLoggedIn = app.globalData.isLoggedIn
    const userInfo = app.globalData.userInfo
    const token = wx.getStorageSync('token')

    console.log('登录状态:', isLoggedIn)
    console.log('用户信息:', userInfo)
    console.log('Token存在:', !!token)

    this.setData({ isLoggedIn })

    if (!isLoggedIn || !token) {
      console.log('❌ 用户未登录，跳转登录页面')
      showModal({
        title: '提示',
        content: '请先登录',
        showCancel: false,
        success: () => {
          wx.navigateTo({
            url: '/pages/user/login/login'
          })
        }
      })
    } else {
      console.log('✅ 用户已登录，开始获取钱包数据')
      this.fetchWalletData()
      this.fetchTransactions()
    }
  },

  /**
   * 获取钱包数据
   */
  fetchWalletData: function () {
    console.log('🔧 开始获取钱包数据...')
    this.setData({ loading: true })

    // 检查API配置是否存在
    if (!API || !API.income || !API.income.stats) {
      console.error('❌ API配置错误: API.income.stats 不存在')
      showToast('API配置错误')
      this.setData({ loading: false })
      return
    }

    console.log('📡 API地址:', API.income.stats)

    request({
      url: API.income.stats,
      method: 'GET'
    }).then(res => {
      console.log('💰 钱包数据响应:', res)
      console.log('响应类型:', typeof res)
      console.log('响应结构:', JSON.stringify(res, null, 2))

      // 兼容不同的响应格式：code: 0 或 code: 200 或 success: true
      if ((res.code === 0 || res.code === 200 || res.success === true) && res.data) {
        // 适配后端返回的数据结构，关联分润实时数据
        const data = res.data
        console.log('📊 解析的数据:', data)

        const walletData = {
          balance: data.balance || data.total?.balance || '0.00',
          incomeStats: {
            // WiFi分享收益 - 用户分享WiFi获得的收入
            wifi: data.wifi_income || data.total?.wifi_income || '0.00',
            // 团队收益 - 团队分润和推荐奖励收入
            team: data.team_income || data.referral_income || data.total?.team_income || '0.00',
            // 广告流量收益 - 广告点击和展示收入
            ads: data.ad_income || data.advertisement_income || data.total?.ad_income || '0.00',
            // 商城订单收益 - 商品销售分润收入
            mall: data.goods_income || data.mall_income || data.total?.goods_income || '0.00'
          },
          loading: false
        }

        console.log('💾 设置钱包数据:', walletData)
        this.setData(walletData)

        // 检查是否有真实数据
        const hasRealData = parseFloat(walletData.balance) > 0 ||
                           Object.values(walletData.incomeStats).some(val => parseFloat(val) > 0)

        if (hasRealData) {
          console.log('✅ 获取到真实数据')
          showToast('数据更新成功')
        } else {
          console.log('ℹ️ 暂无收入数据')
          showToast('暂无收入数据')
        }

      } else {
        console.error('❌ 钱包数据响应格式错误:', res)
        showToast('数据格式错误')
        this.setData({ loading: false })
      }
    }).catch(err => {
      console.error('❌ 获取钱包数据失败:', err)
      showToast('获取数据失败，请检查网络')
      this.setData({ loading: false })
    })
  },

  /**
   * 设置模拟数据（仅在开发调试时使用）
   */
  setMockData: function() {
    console.log('🎭 API调用失败，设置空数据...')
    const emptyData = {
      balance: '0.00',
      incomeStats: {
        wifi: '0.00',  // WiFi分享收益
        team: '0.00',  // 团队收益
        ads: '0.00',   // 广告流量收益
        mall: '0.00'   // 商城订单收益
      },
      loading: false
    }

    this.setData(emptyData)
    console.log('✅ 空数据设置完成:', emptyData)
    showToast('获取数据失败，请检查网络')
  },

  /**
   * 获取交易记录
   */
  fetchTransactions: function () {
    if (!this.data.hasMore) return
    
    request({
      url: API.income.details,
      method: 'GET',
      data: {
        page: this.data.page,
        limit: this.data.limit
      }
    }).then(res => {
      console.log('💰 收入明细响应:', res)
      // 兼容不同的响应格式：code: 0 或 code: 200 或 success: true
      if ((res.code === 0 || res.code === 200 || res.success === true) && res.data) {
        const newTransactions = res.data.list || []
        const transactions = this.data.page === 1
          ? newTransactions
          : [...this.data.transactions, ...newTransactions]

        this.setData({
          transactions,
          hasMore: newTransactions.length === this.data.limit,
          page: this.data.page + 1
        })
        console.log('✅ 收入明细更新成功，共', newTransactions.length, '条记录')
      } else {
        console.error('❌ 收入明细响应格式错误:', res)
        showToast('获取交易记录失败')
      }
    }).catch(err => {
      console.error('获取交易记录失败', err)
      showToast('获取交易记录失败')
    })
  },

  /**
   * 充值操作
   */
  onRecharge: function () {
    showToast('充值功能开发中')
  },

  /**
   * 提现操作
   */
  onWithdraw: function () {
    console.log('🔧 提现按钮被点击')

    // 检查余额
    if (this.data.balance <= 0) {
      wx.showToast({
        title: '余额不足',
        icon: 'none'
      })
      return
    }

    // 跳转到提现页面
    wx.navigateTo({
      url: '/pages/user/withdraw/withdraw'
    })
  },

  /**
   * 查看明细
   */
  onViewDetails: function () {
    console.log('🔧 明细按钮被点击')

    wx.navigateTo({
      url: '/pages/user/income-details/income-details',
      success: function(res) {
        console.log('✅ 成功跳转到明细页面:', res)
      },
      fail: function(err) {
        console.error('❌ 跳转明细页面失败:', err)
        showToast('页面跳转失败，请重试')
      }
    })
  },

  /**
   * 查看更多交易记录
   */
  onViewMore: function () {
    if (this.data.hasMore) {
      this.fetchTransactions()
    }
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {
    console.log('🔄 用户下拉刷新')
    if (this.data.isLoggedIn) {
      this.setData({ page: 1, hasMore: true })
      Promise.all([
        this.fetchWalletData(),
        this.fetchTransactions()
      ]).finally(() => {
        wx.stopPullDownRefresh()
        console.log('✅ 刷新完成')
      })
    } else {
      console.log('❌ 用户未登录，无法刷新')
      wx.stopPullDownRefresh()
      showToast('请先登录')
    }
  },

  /**
   * 手动刷新数据
   */
  onRefreshData: function() {
    console.log('🔄 手动刷新数据')

    try {
      // 检查登录状态
      const isLoggedIn = app.globalData.isLoggedIn
      const token = wx.getStorageSync('token')

      console.log('登录状态:', isLoggedIn)
      console.log('Token存在:', !!token)

      if (isLoggedIn && token) {
        console.log('✅ 开始刷新数据')

        // 显示加载提示
        wx.showLoading({
          title: '刷新中...',
          mask: true
        })

        // 获取钱包数据
        this.fetchWalletData()

        // 获取交易记录
        if (typeof this.fetchTransactions === 'function') {
          this.fetchTransactions()
        }

        // 延迟隐藏加载提示
        setTimeout(() => {
          wx.hideLoading()
        }, 2000)

      } else {
        console.log('❌ 用户未登录')
        wx.showToast({
          title: '请先登录',
          icon: 'none'
        })
      }

    } catch (error) {
      console.error('刷新数据时发生错误:', error)
      wx.hideLoading()
      wx.showToast({
        title: '刷新失败，请重试',
        icon: 'none'
      })
    }
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {
    if (this.data.isLoggedIn && this.data.hasMore) {
      this.fetchTransactions()
    }
  },

  /**
   * 广告加载成功
   */
  onAdLoad: function () {
    console.log('广告加载成功')
  },

  /**
   * 广告加载失败
   */
  onAdError: function (err) {
    console.error('广告加载失败', err)
  },

  /**
   * 测试刷新按钮（备用方法）
   */
  onTestRefresh: function() {
    console.log('🧪 测试刷新按钮点击')
    wx.showToast({
      title: '刷新按钮正常',
      icon: 'success'
    })
  }
}) 