// services/user.js
// 用户相关服务

const request = require('../utils/request')
const config = require('../config/config')

// 构建API URL
const buildUrl = (path) => {
  return `${config.api.baseUrl}${config.api.clientPath}${path}`
}

/**
 * 用户登录
 * @param {Object} loginData 登录数据
 * @returns {Promise} 登录结果
 */
const login = (loginData) => {
  return request.post(buildUrl('/auth/login'), loginData)
}

/**
 * 处理头像URL
 * @param {String} avatarUrl 原始头像URL
 * @returns {String} 处理后的头像URL
 */
const processAvatarUrl = (avatarUrl) => {
  // 如果没有头像，使用默认头像
  if (!avatarUrl) {
    return '/assets/images/default-avatar.png';
  }

  // 如果是微信的头像URL（这些通常已经失效），使用默认头像
  if (avatarUrl.includes('thirdwx.qlogo.cn') ||
      avatarUrl.includes('wx.qlogo.cn') ||
      avatarUrl.includes('mmopen')) {
    console.log('检测到微信头像URL，使用默认头像');
    return '/assets/images/default-avatar.png';
  }

  // 如果是相对路径，转换为完整URL
  if (!avatarUrl.startsWith('http') && !avatarUrl.startsWith('/')) {
    const baseUrl = config.api.baseUrl || 'http://localhost:4000';
    return `${baseUrl}/${avatarUrl}`;
  }

  return avatarUrl;
};

/**
 * 微信登录（wx.login + getUserProfile 组合方式）
 * @param {String} code 微信登录凭证
 * @param {Object} userInfo 用户信息（可选）
 * @param {Boolean} isDemote 是否为降级登录（用户拒绝授权）
 * @returns {Promise} 登录结果
 */
const wechatLogin = (code, userInfo = null, isDemote = false) => {
  console.log('开始微信登录，code:', code, '，是否提供用户信息:', !!userInfo, '，是否降级登录:', isDemote);
  
  const data = { code };
  
  // 如果提供了用户信息，添加到请求数据中
  if (userInfo) {
    console.log('提供了用户信息:', JSON.stringify(userInfo));
    // 确保所有必要字段都存在
    data.userInfo = {
      nickName: userInfo.nickName || userInfo.nickname || '',
      avatarUrl: userInfo.avatarUrl || userInfo.avatar || '',
      gender: userInfo.gender !== undefined ? userInfo.gender : 0,
      country: userInfo.country || '',
      province: userInfo.province || '',
      city: userInfo.city || ''
    };
    
    console.log('格式化后的用户信息:', JSON.stringify(data.userInfo));
  }
  
  // 如果是降级登录，添加标记
  if (isDemote) {
    data.isDemote = true;
    console.log('标记为降级登录（用户拒绝授权）');
  }
  
  const url = buildUrl('/auth/wechat/login');
  console.log('请求URL:', url);
  console.log('请求数据:', JSON.stringify(data));
  
  return request.post(url, data)
    .then(response => {
      console.log('微信登录成功，服务器返回:', JSON.stringify(response));
      
      // 保存token到本地存储
      if (response && response.data && response.data.token) {
        wx.setStorageSync(config.storageKeys.token, response.data.token);
        
        // 处理用户信息和头像
        if (response.data.user) {
          const userData = response.data.user;
          
          // 确保头像URL是完整的
          if (userData.avatar) {
            // 如果头像不是以http开头，且不是本地资源，则添加域名
            if (!userData.avatar.startsWith('http') && !userData.avatar.startsWith('/assets/')) {
              userData.avatar = userData.avatar.startsWith('/') 
                ? `${config.api.baseUrl}${userData.avatar}`
                : `${config.api.baseUrl}/${userData.avatar}`;
            }
            console.log('处理后的头像URL:', userData.avatar);
            
            // 验证并处理头像URL
            userData.avatar = processAvatarUrl(userData.avatar);
          } else if (userInfo && userInfo.avatarUrl) {
            // 如果后端没有返回头像但前端有，使用前端的
            userData.avatar = userInfo.avatarUrl;
            console.log('使用前端提供的头像:', userData.avatar);
          } else {
            // 设置默认头像
            userData.avatar = '/assets/images/default-avatar.png';
            console.log('使用默认头像');
          }
          
          // 保存处理后的用户信息
          wx.setStorageSync(config.storageKeys.userInfo, userData);
          response.data.user = userData;
        }
      }
      
      return response;
    })
    .catch(error => {
      console.error('微信登录失败:', error);
      throw error;
    });
}

/**
 * 退出登录
 * @returns {Promise} 退出结果
 */
const logout = () => {
  return request.post(buildUrl('/auth/logout'))
}

/**
 * 获取用户信息
 * @returns {Promise} 用户信息
 */
const getUserInfo = () => {
  console.log('开始获取用户信息...');
  const token = wx.getStorageSync(config.storageKeys.token);
  if (!token) {
    console.warn('获取用户信息失败: 未找到token');
    return Promise.reject(new Error('未找到登录凭证'));
  }
  
  console.log(`使用token获取用户信息: ${token.substring(0, 10)}...`);
  
  return request.get(buildUrl('/user/info'), {}, { showError: false })
    .then(response => {
      console.log('获取用户信息成功:', JSON.stringify(response));
      
      // 保存/更新用户信息
      if (response && response.data) {
        // 确保头像URL是完整的
        const userData = response.data;
        if (userData.avatar) {
          // 如果头像不是以http开头，且不是本地资源，则添加域名
          if (!userData.avatar.startsWith('http') && !userData.avatar.startsWith('/assets/')) {
            userData.avatar = userData.avatar.startsWith('/') 
              ? `${config.api.baseUrl}${userData.avatar}`
              : `${config.api.baseUrl}/${userData.avatar}`;
          }
          console.log('处理后的头像URL:', userData.avatar);
          
          // 处理头像URL
          userData.avatar = processAvatarUrl(userData.avatar);
        } else {
          // 使用默认头像
          userData.avatar = '/assets/images/default-avatar.png';
          console.log('使用默认头像');
        }
        
        // 保存处理后的用户信息
        wx.setStorageSync(config.storageKeys.userInfo, userData);
        
        // 更新返回数据
        response.data = userData;
      }
      
      return response;
    })
    .catch(error => {
      console.error('获取用户信息失败:', error);
      throw error;
    });
}

/**
 * 更新用户信息
 * @param {Object} userInfo 用户信息
 * @returns {Promise} 更新结果
 */
const updateUserInfo = (userInfo) => {
  console.log('调用更新用户信息API:', JSON.stringify(userInfo));
  
  // 使用POST方法调用更新接口
  return request.post(buildUrl('/user/update'), userInfo)
    .then(response => {
      console.log('更新用户信息成功:', JSON.stringify(response));
      return response;
    })
    .catch(error => {
      console.error('更新用户信息失败:', error);
      throw error;
    });
}

/**
 * 获取用户钱包信息
 * @returns {Promise} 钱包信息
 */
const getWalletInfo = () => {
  return request.get(buildUrl('/user/wallet'))
}

module.exports = {
  login,
  wechatLogin,
  logout,
  getUserInfo,
  updateUserInfo,
  getWalletInfo,
  processAvatarUrl
}