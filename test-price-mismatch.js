const axios = require('axios');
const jwt = require('jsonwebtoken');
const mysql = require('mysql2/promise');

async function testPriceMismatch() {
  let connection;
  try {
    console.log('🔍 开始调查价格不匹配问题...');
    
    // 连接数据库
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: 'wo587129955',
      database: 'mall'
    });
    
    // 生成JWT token
    const config = require('./config');
    const userId = 2;
    
    const token = jwt.sign(
      { id: userId, openid: 'test_openid_13800138000' },
      config.jwt.secret,
      { expiresIn: config.jwt.expiresIn }
    );
    
    console.log('🔑 生成的token:', token);
    
    // 1. 检查商品价格
    console.log('\n📦 1. 检查商品价格...');
    const [goods] = await connection.execute('SELECT id, title, price, original_price FROM goods WHERE id = 1');
    console.log('商品信息:', goods[0]);
    
    // 2. 模拟订单确认页面的计算
    console.log('\n🧮 2. 模拟订单确认页面的计算...');
    const quantity = 3; // 假设购买3个
    const goodsPrice = parseFloat(goods[0].price);
    const subtotal = goodsPrice * quantity;
    const freight = 10; // 运费
    const discount = 0; // 优惠
    const finalAmount = subtotal + freight - discount;
    
    console.log('单价:', goodsPrice);
    console.log('数量:', quantity);
    console.log('小计:', subtotal);
    console.log('运费:', freight);
    console.log('优惠:', discount);
    console.log('最终金额:', finalAmount);
    
    // 3. 创建订单并检查价格
    console.log('\n📝 3. 创建订单并检查价格...');
    const orderData = {
      addressId: 1,
      goods: [
        {
          goodsId: 1,
          quantity: quantity
        }
      ],
      remark: '价格测试订单',
      paymentMethod: 'wechat',
      couponId: 0,
      fromCart: false
    };
    
    console.log('订单数据:', orderData);
    
    const createOrderResponse = await axios.post('http://localhost:4000/api/v1/client/order/create', orderData, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      timeout: 10000
    });
    
    console.log('📋 订单创建响应:', createOrderResponse.data);
    
    if (createOrderResponse.data.status !== 'success') {
      console.error('❌ 订单创建失败');
      return;
    }
    
    const orderId = createOrderResponse.data.data.orderId;
    console.log('📋 订单ID:', orderId);
    
    // 4. 查询数据库中的订单信息
    console.log('\n🗄️ 4. 查询数据库中的订单信息...');
    const [orders] = await connection.execute('SELECT * FROM orders WHERE id = ?', [orderId]);
    const order = orders[0];
    
    console.log('数据库订单信息:');
    console.log('- 商品金额:', order.goods_amount);
    console.log('- 运费:', order.shipping_fee);
    console.log('- 优惠金额:', order.discount_amount);
    console.log('- 总金额:', order.total_amount);
    
    // 5. 查询订单商品信息
    console.log('\n📦 5. 查询订单商品信息...');
    const [orderGoods] = await connection.execute('SELECT * FROM order_goods WHERE order_id = ?', [orderId]);
    console.log('订单商品信息:', orderGoods);
    
    // 6. 获取订单详情API
    console.log('\n🔍 6. 获取订单详情API...');
    try {
      const orderDetailResponse = await axios.get(`http://localhost:4000/api/v1/client/order/detail/${orderId}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        },
        timeout: 10000
      });
      
      console.log('📋 订单详情API响应:', JSON.stringify(orderDetailResponse.data, null, 2));
    } catch (detailError) {
      console.log('❌ 订单详情API错误:', detailError.response ? detailError.response.data : detailError.message);
    }
    
    // 7. 创建支付订单并检查价格
    console.log('\n💳 7. 创建支付订单并检查价格...');
    try {
      const paymentResponse = await axios.post('http://localhost:4000/api/v1/client/payment/create', {
        orderId: orderId,
        paymentMethod: 'wechat'
      }, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        timeout: 10000
      });
      
      console.log('💳 支付订单创建响应:', JSON.stringify(paymentResponse.data, null, 2));
      
      // 分析价格差异
      console.log('\n🔍 8. 价格差异分析...');
      const dbTotalAmount = parseFloat(order.total_amount);
      const paymentAmount = paymentResponse.data.data.amount;
      
      console.log('数据库总金额:', dbTotalAmount);
      console.log('支付金额:', paymentAmount);
      console.log('价格差异:', Math.abs(dbTotalAmount - paymentAmount));
      
      if (Math.abs(dbTotalAmount - paymentAmount) > 0.01) {
        console.log('❌ 发现价格不匹配！');
        console.log('可能的原因:');
        console.log('1. 订单创建时的价格计算逻辑');
        console.log('2. 支付创建时的价格获取逻辑');
        console.log('3. 数据库中的价格数据');
      } else {
        console.log('✅ 价格匹配正常');
      }
      
    } catch (paymentError) {
      console.log('❌ 支付订单创建错误:', paymentError.response ? paymentError.response.data : paymentError.message);
    }
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    if (error.response) {
      console.error('响应状态:', error.response.status);
      console.error('响应数据:', error.response.data);
    } else {
      console.error('其他错误:', error.code || error.message);
    }
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// 运行测试
testPriceMismatch();
