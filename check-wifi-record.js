// 检查WiFi记录并添加测试数据
const db = require('./src/database');
const { success, error } = require('./src/utils/response');

async function checkAndCreateWifi() {
  try {
    // 初始化数据库连接
    await db.init();
    console.log('数据库连接已建立');
    
    // 检查WiFi表是否存在
    const [tables] = await db.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = DATABASE() 
      AND table_name = 'wifi'
    `);
    
    if (tables.length === 0) {
      console.log('创建wifi表...');
      await db.query(`
        CREATE TABLE wifi (
          id INT PRIMARY KEY AUTO_INCREMENT,
          title VARCHAR(100) NOT NULL,
          name VARCHAR(100) NOT NULL,
          password VARCHAR(100) NOT NULL,
          merchant_name VARCHAR(100) NOT NULL,
          qrcode VARCHAR(255),
          use_count INT DEFAULT 0,
          status TINYINT DEFAULT 1,
          user_id INT NOT NULL,
          created_at DATETIME,
          updated_at DATETIME
        )
      `);
      console.log('wifi表创建完成');
    } else {
      console.log('wifi表已存在');
    }

    // 查询ID为2的WiFi记录
    const [wifiRecord] = await db.query('SELECT * FROM wifi WHERE id = 2');
    console.log('查询ID=2的WiFi记录:', wifiRecord);
    
    // 如果不存在ID为2的记录，创建一个
    if (!wifiRecord || wifiRecord.length === 0) {
      console.log('未找到ID=2的WiFi记录，创建测试数据');
      
      const result = await db.query(`
        INSERT INTO wifi (id, title, name, password, merchant_name, qrcode, use_count, status, user_id, created_at, updated_at)
        VALUES (2, '星巴克WiFi', 'Starbucks_Guest', 'starbucks2024', '星巴克咖啡', '', 456, 1, 1, NOW(), NOW())
        ON DUPLICATE KEY UPDATE
        title = '星巴克WiFi', 
        name = 'Starbucks_Guest', 
        password = 'starbucks2024', 
        merchant_name = '星巴克咖啡',
        status = 1,
        updated_at = NOW()
      `);
      
      console.log('插入结果:', result);
      
      // 查询插入后的记录
      const [newRecord] = await db.query('SELECT * FROM wifi WHERE id = 2');
      console.log('新创建的WiFi记录:', newRecord);
    } else {
      console.log('已存在ID=2的WiFi记录:', wifiRecord);
    }
    
    // 查询所有WiFi记录
    const [allRecords] = await db.query('SELECT * FROM wifi');
    console.log('所有WiFi记录数量:', allRecords.length);
    console.log('所有WiFi记录:', JSON.stringify(allRecords, null, 2));
    
  } catch (err) {
    console.error('执行过程中出错:', err);
  } finally {
    process.exit(0);
  }
}

checkAndCreateWifi(); 