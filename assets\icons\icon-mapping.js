/**
 * 图标映射配置
 * 为缺失的图标提供临时替代方案
 */

// 提现功能图标映射
const withdrawIconMapping = {
  // 微信支付图标 - 临时使用用户图标
  'wechat-pay.png': '/assets/icons/user.png',
  
  // 银行卡图标 - 临时使用购物车图标
  'bank-card.png': '/assets/icons/cart.png',
  
  // 空记录图标 - 临时使用用户占位图标
  'empty-records.png': '/assets/icons/user-placeholder.png',
  
  // 刷新图标 - 临时使用WiFi图标
  'refresh.png': '/assets/icons/wifi.png',
  
  // 选中图标 - 临时使用用户激活图标
  'check-selected.png': '/assets/icons/user-active.png',
  
  // 未选中图标 - 临时使用用户图标
  'check-unselected.png': '/assets/icons/user.png',
  
  // 提现图标 - 临时使用WiFi管理图标
  'withdraw.png': '/assets/icons/wifi-manage.png',
  
  // 充值图标 - 临时使用WiFi创建图标
  'recharge.png': '/assets/icons/wifi-create.png',
  
  // 明细图标 - 临时使用二维码图标
  'details.png': '/assets/icons/qrcode.png'
};

/**
 * 获取图标路径
 * @param {string} iconName - 图标名称
 * @returns {string} 图标路径
 */
function getWithdrawIconPath(iconName) {
  // 如果是完整路径，直接返回
  if (iconName && iconName.startsWith('/')) {
    return iconName;
  }
  
  // 查找映射
  return withdrawIconMapping[iconName] || '/assets/icons/user.png';
}

/**
 * 检查图标是否存在
 * @param {string} iconPath - 图标路径
 * @returns {boolean} 是否存在
 */
function checkIconExists(iconPath) {
  // 这里可以添加图标存在性检查逻辑
  // 目前返回true，表示使用映射的图标
  return true;
}

module.exports = {
  withdrawIconMapping,
  getWithdrawIconPath,
  checkIconExists
};
