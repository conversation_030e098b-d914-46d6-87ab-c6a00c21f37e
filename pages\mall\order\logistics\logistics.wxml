<!--pages/mall/order/logistics/logistics.wxml-->
<view class="container" wx:if="{{!loading && orderInfo && !noLogistics}}">
  <!-- 物流公司信息 -->
  <view class="logistics-header">
    <view class="company-info">
      <text class="company-name">{{orderInfo.logistics_company}}</text>
      <view class="logistics-no">
        <text>运单号：{{orderInfo.logistics_no}}</text>
        <text class="copy-btn" bindtap="onCopyLogisticsNo">复制</text>
      </view>
    </view>
    <view class="contact-actions">
      <button class="contact-btn" bindtap="onContactCourier">联系快递员</button>
      <button class="contact-btn" bindtap="onContactService">联系客服</button>
    </view>
  </view>

  <!-- 收货信息 -->
  <view class="receiver-info">
    <view class="info-row">
      <text class="label">收货人：</text>
      <text class="value">{{orderInfo.receiver_name}} {{orderInfo.receiver_phone}}</text>
    </view>
    <view class="info-row">
      <text class="label">收货地址：</text>
      <text class="value">{{orderInfo.receiver_address}}</text>
    </view>
  </view>

  <!-- 物流轨迹 -->
  <view class="logistics-timeline">
    <view class="timeline-title">物流轨迹</view>
    <view class="timeline-list">
      <view 
        class="timeline-item {{index === 0 ? 'current' : ''}}" 
        wx:for="{{logisticsInfo}}" 
        wx:key="index"
      >
        <view class="timeline-dot"></view>
        <view class="timeline-content">
          <view class="timeline-header">
            <text class="timeline-status">{{item.status}}</text>
            <text class="timeline-time">{{item.time}}</text>
          </view>
          <text class="timeline-desc">{{item.desc}}</text>
          <text class="timeline-location">{{item.location}}</text>
        </view>
      </view>
    </view>
  </view>
</view>

<!-- 加载状态 -->
<view class="loading-container" wx:if="{{loading}}">
  <view class="loading-spinner"></view>
  <text>正在查询物流信息...</text>
</view>

<!-- 无物流信息状态 -->
<view class="no-logistics-container" wx:if="{{!loading && orderInfo && noLogistics}}">
  <view class="order-info-card">
    <view class="order-header">
      <text class="order-title">订单信息</text>
    </view>
    <view class="order-details">
      <view class="detail-row">
        <text class="detail-label">订单号：</text>
        <text class="detail-value">{{orderInfo.orderNo}}</text>
      </view>
      <view class="detail-row">
        <text class="detail-label">订单状态：</text>
        <text class="detail-value status-{{orderInfo.status}}">{{orderInfo.statusText}}</text>
      </view>
    </view>
  </view>

  <view class="no-logistics-info">
    <text class="no-logistics-icon">📦</text>
    <text class="no-logistics-title">{{noLogisticsMessage}}</text>
    <text class="no-logistics-desc">商品发货后，物流信息将在此显示</text>
    <button class="refresh-btn" bindtap="fetchLogisticsInfo">刷新状态</button>
  </view>
</view>

<!-- 错误状态 -->
<view class="error-container" wx:if="{{!loading && !orderInfo}}">
  <text class="error-icon">🚚</text>
  <text class="error-text">暂无物流信息</text>
  <text class="error-desc">请稍后再试或联系客服</text>
  <button class="retry-btn" bindtap="fetchLogisticsInfo">重新查询</button>
</view>
