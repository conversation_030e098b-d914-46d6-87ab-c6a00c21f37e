/* components/qrcode/qrcode.wxss */
/* 二维码组件样式 */

.qrcode-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  padding: 20rpx 0;
}

/* 加载状态 */
.qrcode-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 500rpx;
}

/* 加载覆盖层 */
.qrcode-loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.9);
  z-index: 10;
}

.loading-spinner {
  width: 80rpx;
  height: 80rpx;
  border: 6rpx solid rgba(0, 0, 0, 0.1);
  border-top: 6rpx solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  margin-top: 30rpx;
  font-size: 28rpx;
  color: #666;
}

/* 二维码内容 */
.qrcode-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
}

.qrcode-wrapper {
  position: relative;
  margin: 20rpx 0;
  padding: 40rpx;
  background-color: #fff;
  border-radius: 16rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 85%;
  max-width: 600rpx;
}

/* 标题 */
.qrcode-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #3498db;
  margin-bottom: 20rpx;
  text-align: center;
}

.qrcode-canvas {
  /* 移除绝对定位和隐藏属性 */
  display: block;
  border-radius: 8rpx;
  margin: 20rpx 0;
}

.qrcode-image {
  border-radius: 8rpx;
  margin: 20rpx 0;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
}

/* WiFi信息 */
.wifi-info {
  width: 100%;
  margin-top: 30rpx;
  border-top: 1px solid #eaeaea;
  padding-top: 20rpx;
}

.wifi-info-item {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  margin: 10rpx 0;
  padding: 10rpx 0;
}

.wifi-label {
  font-size: 28rpx;
  color: #666;
  flex: 1;
}

.wifi-value {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
  flex: 2;
  text-align: right;
}

/* 提示信息 */
.qrcode-tips {
  width: 100%;
  margin-top: 20rpx;
  padding-top: 10rpx;
  border-top: 1px dashed #eaeaea;
  text-align: center;
  font-size: 24rpx;
  color: #999;
}

/* 生成错误 */
.qrcode-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 500rpx;
}

.error-text {
  font-size: 30rpx;
  color: #e74c3c;
  margin-bottom: 20rpx;
}

.retry-btn {
  margin-top: 20rpx;
  padding: 12rpx 40rpx;
  background-color: #3498db;
  color: #fff;
  font-size: 28rpx;
  border-radius: 8rpx;
}

/* 备用显示样式 */
.qrcode-fallback {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 20rpx;
  box-sizing: border-box;
}

.fallback-content {
  text-align: center;
  padding: 40rpx 20rpx;
}

.fallback-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #3498db;
  margin-bottom: 30rpx;
}

.fallback-info {
  margin-bottom: 20rpx;
  font-size: 28rpx;
}

.fallback-label {
  color: #666;
  margin-right: 10rpx;
}

.fallback-value {
  color: #333;
  font-weight: bold;
}

.fallback-tip {
  margin-top: 30rpx;
  font-size: 24rpx;
  color: #999;
  font-style: italic;
}