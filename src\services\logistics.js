// src/services/logistics.js
// 物流服务

const axios = require('axios');
const crypto = require('crypto');
const logger = require('../utils/logger');

/**
 * 物流公司配置
 */
const LOGISTICS_COMPANIES = {
  SF: { name: '顺丰速运', code: 'SF', apiCode: 'SF' },
  YTO: { name: '圆通速递', code: 'YTO', apiCode: 'YTO' },
  ZTO: { name: '中通快递', code: 'ZTO', apiCode: 'ZTO' },
  STO: { name: '申通快递', code: 'STO', apiCode: 'STO' },
  YD: { name: '韵达速递', code: 'YD', apiCode: 'YD' },
  JTSD: { name: '极兔速递', code: 'JTSD', apiCode: 'JTSD' },
  JD: { name: '京东物流', code: 'JD', apiCode: 'JD' },
  EMS: { name: '中国邮政', code: 'EMS', apiCode: 'EMS' },
  HTKY: { name: '百世快递', code: 'HTKY', apiCode: 'HTKY' },
  UC: { name: '优速快递', code: 'UC', apiCode: 'UC' }
};

/**
 * 快递鸟API配置
 */
const KDNIAO_CONFIG = {
  baseUrl: 'https://api.kdniao.com',
  appId: process.env.KDNIAO_APP_ID || 'test123456',
  appKey: process.env.KDNIAO_APP_KEY || 'test_key',
  requestType: '1002' // 即时查询
};

/**
 * 生成快递鸟API签名
 */
function generateKdniaoSign(requestData, appKey) {
  const dataToSign = requestData + appKey;
  return crypto.createHash('md5').update(dataToSign, 'utf8').digest('base64');
}

/**
 * 调用快递鸟API查询物流信息
 */
async function queryKdniaoLogistics(shipperCode, logisticCode) {
  try {
    const requestData = JSON.stringify({
      ShipperCode: shipperCode,
      LogisticCode: logisticCode
    });

    const sign = generateKdniaoSign(requestData, KDNIAO_CONFIG.appKey);

    const params = new URLSearchParams({
      RequestData: requestData,
      EBusinessID: KDNIAO_CONFIG.appId,
      RequestType: KDNIAO_CONFIG.requestType,
      DataSign: sign,
      DataType: '2'
    });

    const response = await axios.post(
      `${KDNIAO_CONFIG.baseUrl}/Ebusiness/EbusinessOrderHandle.aspx`,
      params,
      {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        },
        timeout: 10000
      }
    );

    if (response.data.Success) {
      return {
        success: true,
        data: response.data
      };
    } else {
      return {
        success: false,
        error: response.data.Reason || '查询失败'
      };
    }
  } catch (error) {
    logger.error('快递鸟API调用失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * 格式化物流轨迹数据
 */
function formatLogisticsTraces(traces) {
  if (!traces || !Array.isArray(traces)) {
    return [];
  }

  return traces.map(trace => ({
    time: trace.AcceptTime,
    status: getStatusFromDesc(trace.AcceptStation),
    desc: trace.AcceptStation,
    location: trace.Location || '',
    remark: trace.Remark || ''
  })).sort((a, b) => new Date(b.time) - new Date(a.time));
}

/**
 * 从描述中提取状态
 */
function getStatusFromDesc(desc) {
  if (!desc) return '处理中';
  
  if (desc.includes('已签收') || desc.includes('签收')) return '已签收';
  if (desc.includes('派送中') || desc.includes('正在派送')) return '派送中';
  if (desc.includes('到达') || desc.includes('到达目的地')) return '运输中';
  if (desc.includes('发出') || desc.includes('离开')) return '运输中';
  if (desc.includes('收件') || desc.includes('揽收')) return '已揽收';
  
  return '处理中';
}

/**
 * 获取物流公司列表
 */
function getLogisticsCompanies() {
  return Object.values(LOGISTICS_COMPANIES);
}

/**
 * 根据代码获取物流公司信息
 */
function getLogisticsCompanyByCode(code) {
  return LOGISTICS_COMPANIES[code] || null;
}

/**
 * 查询物流信息
 */
async function queryLogistics(companyCode, trackingNumber) {
  try {
    // 获取物流公司信息
    const company = getLogisticsCompanyByCode(companyCode);
    if (!company) {
      return {
        success: false,
        error: '不支持的物流公司'
      };
    }

    // 调用快递鸟API
    const result = await queryKdniaoLogistics(company.apiCode, trackingNumber);
    
    if (result.success) {
      const formattedData = {
        company: company.name,
        trackingNumber: trackingNumber,
        status: result.data.State,
        statusDesc: getLogisticsStatusDesc(result.data.State),
        traces: formatLogisticsTraces(result.data.Traces),
        lastUpdate: new Date().toISOString()
      };

      return {
        success: true,
        data: formattedData
      };
    } else {
      // 如果API调用失败，返回模拟数据
      return getMockLogisticsData(company.name, trackingNumber);
    }
  } catch (error) {
    logger.error('查询物流信息失败:', error);
    // 返回模拟数据作为降级方案
    return getMockLogisticsData('未知快递', trackingNumber);
  }
}

/**
 * 获取物流状态描述
 */
function getLogisticsStatusDesc(state) {
  const statusMap = {
    0: '暂无轨迹信息',
    1: '已揽收',
    2: '在途中',
    3: '已签收',
    4: '问题件'
  };
  return statusMap[state] || '未知状态';
}

/**
 * 获取模拟物流数据（降级方案）
 */
function getMockLogisticsData(companyName, trackingNumber) {
  const mockTraces = [
    {
      time: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString().replace('T', ' ').slice(0, 19),
      status: '派送中',
      desc: '快件正在派送中，请您准备签收（快递员：张师傅，电话：138****5678）',
      location: '北京市朝阳区',
      remark: ''
    },
    {
      time: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString().replace('T', ' ').slice(0, 19),
      status: '运输中',
      desc: '快件到达北京朝阳区分拣中心',
      location: '北京市朝阳区',
      remark: ''
    },
    {
      time: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString().replace('T', ' ').slice(0, 19),
      status: '运输中',
      desc: '快件离开上海分拣中心，发往北京',
      location: '上海市',
      remark: ''
    },
    {
      time: new Date(Date.now() - 48 * 60 * 60 * 1000).toISOString().replace('T', ' ').slice(0, 19),
      status: '已揽收',
      desc: '快件已在上海市浦东新区揽收',
      location: '上海市浦东新区',
      remark: ''
    }
  ];

  return {
    success: true,
    data: {
      company: companyName,
      trackingNumber: trackingNumber,
      status: 2,
      statusDesc: '在途中',
      traces: mockTraces,
      lastUpdate: new Date().toISOString()
    }
  };
}

module.exports = {
  getLogisticsCompanies,
  getLogisticsCompanyByCode,
  queryLogistics,
  LOGISTICS_COMPANIES
};
